<!--
 * @Descripttion: 文件导入
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2022年5月24日11:30:03
 * @LastEditors:
 * @LastEditTime:
-->

<template>
	<slot :open="open">
		<el-button type="primary" plain @click="open">导入</el-button>
	</slot>
	<el-dialog v-model="dialog" title="导入" :width="550" :close-on-click-modal="false" append-to-body destroy-on-close>
		<el-progress v-if="loading" :text-inside="true" :stroke-width="20" :percentage="percentage" style="margin-bottom: 15px;"/>
		<div v-loading="loading">
			<el-upload ref="uploader"
			           drag
			           :auto-upload="false"
			           :multiple="false"
			           :accept="accept"
			           :maxSize="maxSize"
			           :limit="1"
			           :data="data"
			           :show-file-list="false"
			           :on-change="elUploadChange"
			           :on-exceed="elUploadExceed"
			           action=""
			>
				<slot name="uploader">
					<el-icon class="el-icon--upload"><el-icon-upload-filled /></el-icon>
					<div class="el-upload__text">
						将文件拖到此处或 <em>点击选择文件上传</em>
					</div>
				</slot>
				<template #tip>
					<div class="el-upload__tip">
						<template v-if="tip">{{tip}}</template>
						<template v-else>请上传小于或等于 {{maxSize}}M 的 {{accept}} 格式文件</template>
						<p v-if="templateUrl" style="margin-top: 7px;">
							<el-link :href="templateUrl" target="_blank" type="primary" :underline="false">下载导入模板</el-link>
						</p>
					</div>
				</template>
			</el-upload>
			<el-form v-if="$slots.form" inline label-width="100px" label-position="left" style="margin-top: 18px;">
				<slot name="form" :formData="formData"></slot>
			</el-form>
		</div>
	</el-dialog>
</template>

<script>
import XLSX from "xlsx";
export default {
	emits: ['success'],
	props: {
		formatFunction: { type: Function, default: () => { return []} },
		apiObj: { type: Object, default: () => {} },
		data: { type: Object, default: () => {} },
		accept: { type: String, default: ".xls, .xlsx" },
		maxSize: { type: Number, default: 10 },
		tip: { type: String, default: "" },
		templateUrl: { type: String, default: "" }
	},
	data() {
		return {
			dialog: false,
			loading: false,
			percentage: 0,
			formData: {}
		}
	},
	mounted() {

	},
	methods: {
		open(){
			this.dialog = true
			this.formData = {}
		},
		close(){
			this.dialog = false
		},
		before(file){
			const maxSize = file.size / 1024 / 1024 < this.maxSize;
			if (!maxSize) {
				this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`);
				return false;
			}
			this.loading = true
		},

		success(formatData, file){
			this.$refs.uploader.handleRemove(file)
			this.$refs.uploader.clearFiles()
			this.loading = false
			this.percentage = 0
			this.$emit('success', formatData, this.close)
		},
		//文件超出上传限制时提醒
		elUploadExceed() {
			this.$message.warn("最多上传一个文件！")
		},
		//上传文件发生变化
		// eslint-disable-next-line no-unused-vars
		async elUploadChange(file, fileList) {
			let files = {0: file.raw}
			let that = this;
			this.readExcel(files, that);
		},
		//读取上传excel文件能内容
		readExcel(files, that) {
			if (files.length <= 0) {//如果没有文件名
				return false;
			} else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
				this.$message.error('上传格式不正确，请上传xls或者xlsx格式');
				return false;
			}
			const fileReader = new FileReader();
			fileReader.onload = async (ev) => {
				try {
					const data = ev.target.result;
					const workbook = XLSX.read(data, {
						type: 'binary'
					});
					const wsname = workbook.SheetNames;//取第一张表
					const ws = []
					if (wsname.length > 0) {
						wsname.map(r => {
							ws.push(XLSX.utils.sheet_to_json(workbook.Sheets[r]))//生成json表格内容)
						})
					}
					//重写数据
					try {
						//ws[0].splice(0, 1);
						let formatData=that.formatFunction(ws[0]);
						that.success(formatData,files)
					} catch (err) {
						return false;
					}
					this.$refs.uploadCost.value = '';
				} catch (e) {
					return false;
				}
			};
			fileReader.readAsBinaryString(files[0]);
		},
	}
}
</script>

<style>

</style>
