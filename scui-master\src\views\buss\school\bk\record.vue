<template>
  <el-dialog
    title="预览"
    v-model="visible"
    append-to-body="true"
    :width="900"
    destroy-on-close
    @closed="$emit('closed')"
  >
  <div class="fullscreen-modal">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <div class="info-section">
        <span class="question-id">题目编号：{{ currentQuestion.no }}</span>
        <span class="chapter-name">章节：{{ currentQuestion.lessonname}}</span>
      </div>
      <div class="button-group">
 
         <button class="action-btn close" @click="handleClose">关闭</button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="modal-content">
      <!-- 第一列：题目图片 -->
      <div class="column">
        <div class="image-container" :style="{ height: (windowHeight - 100) + 'px' }" @wheel="handleScroll($event, 'left')">
          <img :src="currentQuestion.tm_p" alt="题目图片" />
        </div>
      </div>

      <!-- 第二列：解析图片 -->
      <div class="column">
        <div class="image-container" :style="{ height: (windowHeight - 100) + 'px' }" @wheel="handleScroll($event, 'middle')">
          <img :src="currentQuestion.jx_p" alt="解析图片" />
        </div>
      </div>

      <!-- 第三列：答案图片 -->
      <div class="column">
    	<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort :params="params"
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					
					<el-table-column label="学生" prop="name" width="150" sortable='custom'></el-table-column>
			 
				 	<el-table-column label="作答内容" prop="res" width="150" sortable='custom'></el-table-column>
				 
					<el-table-column label="添加时间" prop="create_date" width="150" sortable='custom'></el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" v-if="scope.row.pic" @click="showimghandle(scope.row, scope.$index)">查看图片
							</el-button>
						
						
					
						</template>
					</el-table-column>

				</scTable>



<el-image-viewer
  v-if="showimg"
  :zoom-rate="1.2"
  @close="closePreview"
  :url-list="imglist"
/>
      </div>
    </div>
  </div>   
    
  
   </el-dialog>
</template>



<script>

export default {
  	emits: ['success', 'closed'],
  data() {
    return {
         visible: false,
           showimg:false,
        imglist:[],
      // 当前题目数据
      currentQuestion: {
        tk:{},
      
        lesson:{}
      },
      params:{tkid:''},
    apiObj:this.$API.bk.recordlist,
      windowHeight: window.innerHeight, 
      // 分割位置状态
      splitPosition: 50,
      isDragging: false,
      startY: 0
    }
  },

  methods: {
     updateWindowHeight() {
      this.windowHeight = window.innerHeight;
    },
    // 拖动处理
    startDrag(e) {
      this.isDragging = true
      this.startY = e.clientY
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
    },

    onDrag(e) {
      if (!this.isDragging) return
      
      const containerHeight = document.querySelector('.split-pane').offsetHeight
      const deltaY = e.clientY - this.startY
      const newPosition = (this.splitPosition * containerHeight + deltaY) / containerHeight * 100
      
      this.splitPosition = Math.min(Math.max(newPosition, 20), 80)
      this.startY = e.clientY
    },
    showimghandle(row,index){
      this.showimg=true;
      console.log(row);
      let pics=row.pic.split(",");
      console.log(pics)
      this.imglist=pics;
    },
    closePreview(){
      this.showimg=false;
    },

    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },

    // 滚动处理
    handleScroll(event, section) {
      event.preventDefault()
      const container = event.currentTarget
      container.scrollTop += event.deltaY
    },

    // 按钮点击处理
    async handleCorrect() {
       let res=await this.$API.score.updateflag.post({id:this.currentQuestion.id,flag:1});
       if(res.code==400){
        this.$message.error(res.message);
        return;
       }else{
        this.$message.success("成功");
       }
       
    },

 async   handleWrong() {
        let res=await this.$API.score.updateflag.post({id:this.currentQuestion.id,flag:0});
       if(res.code==400){
        this.$message.error(res.message);
        return;
       }else{
        this.$message.success("成功");
       }
    },

    async handleNext() {
       let res=await this.$API.score.getnexttk.post();
       if(res.code==400){
        this.$message.error(res.message);
        return;
       }
       this.currentQuestion=res;
    },
	open() {
		 console.log(2322332);
			this.visible = true;
			return this
		},
    handleClose() {
      this.$emit('closed')
    },

     async	setData(data) {

      console.log(data)
			 
			this.currentQuestion=   await this.$API.bk.record.post({id:data.id});
            this.params.tkid=data.id;
            this.$refs.table.refresh();

		},
  },

  mounted() {
      window.addEventListener('resize', this.updateWindowHeight);
    this.updateWindowHeight(); // 初始化高度
  },

 

  beforeUnmount() {
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  }
}
</script>

<style scoped>
.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.top-bar {
  height: 60px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-section {
  display: flex;
  gap: 20px;
  align-items: center;
}

.question-id, .chapter-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 8px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.correct {
  background: #4CAF50;
  color: white;
}

.answer-image {
  width: 100%;
  height: auto;
  display: block;
  margin-bottom: 20px; /* 图片之间的间距 */
}

.answer-image:last-child {
  margin-bottom: 0; /* 最后一张图片不需要底部间距 */
}

.action-btn.wrong {
  background: #f44336;
  color: white;
}

.action-btn.next {
  background: #2196F3;
  color: white;
}

.action-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.modal-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr; /* 三等分列 */
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.column {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.image-container {
  overflow-y: auto;
  padding: 16px;
  position: relative;
}

.image-container img {
  width: 100%;
  height: auto;
  display: block;
  margin-bottom: 20px;
}

.answer-image:last-child {
  margin-bottom: 0;
}

.drag-bar {
  height: 8px;
  background: #e0e0e0;
  cursor: row-resize;
  position: relative;
  transition: background 0.3s ease;
}

.drag-bar:hover {
  background: #bdbdbd;
}

.drag-bar::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 4px;
  background: #9e9e9e;
  border-radius: 2px;
}

/* 自定义滚动条样式 */
.image-container::-webkit-scrollbar {
  width: 6px;
}

.image-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.image-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.image-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.action-btn.close {
  background: #9e9e9e;
  color: white;
}

.action-btn.close:hover {
  background: #757575;
}
</style>