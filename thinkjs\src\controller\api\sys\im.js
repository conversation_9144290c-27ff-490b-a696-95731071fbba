

var socketList = {};
module.exports = class extends  think.Controller {



 async    getbussAction(){
    let table=this.get("table");
    let id=this.get("id");

    const userInfo = await this.session('userInfo');
    let model=this.model(table);

   let pmodel=this.model("project")

    let umodel=this.model("user");
    let buss=await model.where({"id":id}).find();
    let user=null;
    if(!think.isEmpty()){
         user=await umodel.where({aid:buss.create_by2}).find();
    }else{
         user=await umodel.where({id:buss.create_by}).find();
    }
   


    let para={};

 
    if(table=="project"){

        para['flow_name']='项目信息'
        
        para['col1_name']="项目名称";
        para['col1_val']=buss.name;
        para['col2_name']="客户名称";
        cmodel=this.model("customer")
        let customer=await cmodel.where({id:buss.customerid}).find();
        para['col2_val']=customer.name;

        para['col3_name']="预算金额";
        para['col3_val']=buss.money;
        para['col4_name']="项目类型";
        para['col4_val']=buss.type;
        para.user=user.name;
        para.date=buss.create_date;
     
    }

    if(table=="leave"){
        para['flow_name']='请假'
        para['col1_name']="类型";
        para['col1_val']=buss.leave_type;
        para['col2_name']="请假天数";

        para['col2_val']=buss.days;

        para['col3_name']="请假原因";
        para['col3_val']=buss.leave_reason;

        para.user=user.name;
        para.date=buss.create_date;
       
        para.buss=buss;
    
    }

    if(table=="official_seal"){
        para['flow_name']='公章申请'
        para['col1_name']="公章";
        para['col1_val']=buss.seal_name;
        para['col2_name']="使用时间";

        para['col2_val']=buss.use_date;

        para['col3_name']="是否带出";
        para['col3_val']=buss.is_take;

        para['col4_name']="使用说明";
        para['col4_val']=buss.use_reason;

        para.user=user.name;
        para.date=buss.create_date;
     
        para.buss=buss;
       
    }


    if(table=="loan"){
        para['flow_name']='借款'
        para['col1_name']="借款金额";
        para['col1_val']=buss.loan_money;
        para['col2_name']="预计归还";

        para['col2_val']=buss.loan_date;

        para['col3_name']="借款事由";
        para['col3_val']=buss.leave_reason;



        para.user=user.name;
        para.date=buss.create_date;
      
        para.buss=buss;
 
    }



    if(table=="work_overtime"){
        para['flow_name']='加班'
        para['col1_name']="加班日期";
        para['col1_val']=buss.overtime_date;
        para['col2_name']="加班时长（小时）";
        para['col2_val']=buss.overtime_time;
        para['col3_name']="说明";
        para['col3_val']=buss.overtime_note;
        para.user=user.name;
        para.date=buss.create_date;
     
        para.buss=buss;
      
    }




    if(table=="request"){
        para['flow_name']='请示'
        para['col1_name']="类型";
        para['col1_val']=buss.requesttype;
        para['col2_name']="事项标题";
        para['col2_val']=buss.title;
        para['col3_name']="事项内容";
        para['col3_val']=buss.note;
        para.user=user.name;
        para.date=buss.create_date;
     
        para.buss=buss;
    
    }


    if(table=="construction_main"){
        para['flow_name']='请示'
        para['col1_name']="所属项目";

        let t=await pmodel.where({id:buss.project_id}).find();

        para['col1_val']=t.name;
        para['col2_name']="施工负责人";
        para['col2_val']=buss.construction_principal;
        para['col3_name']="施工开始时间";
        para['col3_val']=buss.construction_start_date;

        para['col4_name']="施工费总金额";
        para['col4_val']=buss.construction_money;
        para.user=user.name;
        para.date=buss.create_date;
     
        para.buss=buss;
       
    }



    if(table=="costpay_main"){
        para['flow_name']='报销'
        para['col1_name']="客户";

        let t=await pmodel.where({id:buss.project_id}).find();
        let projectname="无";
        if(!think.isEmpty(t)){
            projectname=t.name;
        }


        para['col1_val']=buss.customer;
        para['col2_name']="项目名称";
        para['col2_val']=projectname;
        para['col3_name']="说明";
        para['col3_val']=buss.note;

        para['col4_name']="总金额";
        para['col4_val']=buss.sum_money;
        para.user=user.name;
        para.date=buss.create_date;
     
        para.buss=buss;
     
    }


    if(table=="paying"){
        para['flow_name']='付款申请'
        para['col1_name']="供应商";
        para['col1_val']=buss.payee;
        para['col2_name']="负责人";
        
       let user2=await umodel.where({aid:buss.leader}).find();
        para['col2_val']=user2.name;

        para['col3_name']="付款金额";
        para['col3_val']=buss.money;

        para.user=user.name;
        para.date=buss.create_date;
      
        
        para.buss=buss;
       
    }

    if(table=="invoice"){
        para['flow_name']='开票'
        para['col1_name']="开票类型";
        para['col1_val']=buss.invoice_type;
        para['col2_name']="开票日期";
      
        para['col2_val']=buss.invoice_date;

        para['col3_name']="开票金额";
        para['col3_val']=buss.invoice_money;
        para['col4_name']="开票单位";
        para['col4_val']=buss.invoice_company;
        para.user=user.name;
        para.date=buss.create_date;   
        para.buss=buss;
       
    }

    if(table=="work_log"){
        para['flow_name']='日志'
        para['col1_name']="客户";
         let customerstr="无"

        if(!think.isEmpty(buss.customer)){
            customerstr=buss.customer
           
        }

        para['col1_val']=customerstr;
        para['col3_name']="填写日期";
      
        para['col3_val']=buss.create_date;

      
        para['col2_name']="日志内容";
        para['col2_val']=buss.situation;
        para.user=user.name;
        para.date=buss.create_date;   
        para.buss=buss;
       
    }

    para.id=id;
    para.table=table;

    return this.json(para);
}



async indexAction() {


    return this.display();
}


async sendAction(self) {
    var socket = self.http.socket;
    let resstr=this.http.data;
    let res=eval('(' + resstr + ')');
    res.data.mine.mine=false;
    res.data.mine.type= "friend";

    if(!think.isEmpty( socketList[res.data.to.id])){
       socketList[res.data.to.id].emit('msg',res.data.mine);
    }else{
        let offmsg={};
        offmsg['id']=think.uuid(32);
        offmsg['touserid']=res.data.to.id;
        offmsg['content']=JSON.stringify(res.data.mine)
        let offmsgmodel=this.model("imoffmsg");
        await offmsgmodel.add(offmsg);
       // console.log("离线消息");
    }

}

async getlistAction() {
    let data={};
    data.code=0;
    data.msg="";
    let cuser={};
    let user = await this.session('userInfo');
    let model=this.model('user');
    let res=await model.where({'id':user.id}).find();
    cuser.username=res.name;
    cuser.id=res.id;
    cuser.status="online";
    cuser.sign=res.sign;
    cuser.avatar=res.avatar;
    data.data={"mine":cuser};

    let ofmodel=this.model("office");
    let offices =await ofmodel.where({"del_flag":0}).select();
    let firends = new Array();
    for(var o of offices){
        let obj={};
        obj.groupname=o.name;
        obj.id=o.id;
        obj.online=0;

        //缓存用户数据
        let users=[];
        const cacheusers = await this.cache('useof_'+o.id);


        if(think.isEmpty(cacheusers)){
             users=await model.where({"office_id":o.id,'del_flag':0}).select();
            await this.cache('useof_'+o.id,users);
        }else{
             users=cacheusers

        }



       

        


        var userlist = new Array();
        for(var u of users){
            let userobj={};
            userobj.username=u.name;
            userobj.id=u.id;
            userobj.avatar=u.avatar;
            userobj.sign=u.sign;
            userobj.status=u.status;
            userlist.push(userobj);

        }
        obj.list=userlist;
        firends.push(obj);
    }

    data.data.friend=firends;


    this.json(data);
}


async updatesignAction(){
    let user = await this.session('userInfo');
    let sign= this.post('sign')
    let model=this.model('user');
    await model.where({'id':user.id}).update({'sign':sign});
    this.json({'errorcode': 0, 'message': "修改成功。", 'resultdata': null, 'type': 1});
}   

async updateoffmsgAction(){

    let offmsgmodel=this.model("imoffmsg");
    let res=await offmsgmodel.where({'id':this.get("id")}).update({'ifread':'1'});
    this.json({'errorcode': 0, 'message': "修改成功。", 'resultdata': null, 'type': 1});
}
async getoffmsgAction(){
    let user = await this.session('userInfo');
    let offmsgmodel=this.model("imoffmsg");
    let res=await offmsgmodel.where({'ifread':'0','touserid':user.id}).select();
    this.json(res);
    
}   


async   openAction(self){
    // var socket = self.http.socket;
    // let user = await this.session('userInfo');
    // let model=this.model('user');
    // await model.where({'id':user.id}).update({'status':'online'});
    // console.log("open======"+user.id);
    // socketList[user.id] = socket;
    
    // this.broadcast('online', {
    //     userid:user.id,
    //     username: user.name,
    //     message: 'online'
    //   });
    console.log('获取客户端 addUser 事件发送的数据', this.wsData);
    console.log('获取当前 WebSocket 对象', this.websocket);
    console.log('判断当前请求是否是 WebSocket 请求', this.isWebsocket);
  }

  async   closeAction(self){
    // var socket = self.http.socket;
    // let user = await this.session('userInfo');
    // console.log(socket);
    // let model=this.model('user');
    // await model.where({'id':user.id}).update({'status':'offline'});
    // console.log("sdfsdf============================ds"+user.id);
    // delete socketList[user.id];
    // this.broadcast('offline', {
    //     userid:user.id,
    //     username: user.name,
    //     message: 'offline'
    //   });
    console.log('获取客户端 addUser 事件发送的数据', this.wsData);
    let user = await this.session('userInfo');
    console.log(user);
  }




  async getoffmsgAction(){
    let user = await this.session('userInfo');
    let model=this.model("imoffmsg");

    let res =await model.where({touserid:user.id,ifread:0}).order("create_date asc").select();

    for(var item of res){

    //    console.log(item)
        let date=new Date(item.create_date)
        item.timestamp=date.getTime();    

        
    }
    
   
    return this.json(res);


  }

  async updatemsgAction(){
    let user = await this.session('userInfo');
    let model=this.model("imoffmsg");
    let id=this.get("id");
    let res =await model.where({id:id,ifread:0}).update({ifread:"1"});
   
    return this.json(res);


  }

  







}