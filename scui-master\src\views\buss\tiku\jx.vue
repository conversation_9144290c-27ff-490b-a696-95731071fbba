<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    :width="800"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-container>
      <el-container>
        <el-header>
          <div class="left-panel">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="add"
            ></el-button>
          </div>
          <div class="right-panel">
            <div class="right-panel-search"></div>
          </div>
        </el-header>
        <el-main class="nopadding">
          <scTable
            ref="table"
            :apiObj="apiObj"

            :params="params"

            @selection-change="selectionChange"
            stripe
            remoteSort
            remoteFilter
          >
            <el-table-column type="selection" width="50"></el-table-column>
   <el-table-column
              label="文件名"
              prop="name"
              width="150"
            ></el-table-column>
            <el-table-column
              label="类型"
              prop="type"
              width="150"
            ></el-table-column>

            <el-table-column
              label="添加时间"
              prop="create_date"
              width="150"
              sortable="custom"
            ></el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              align="right"
              width="140"
            >
              <template #default="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="table_show(scope.row, scope.$index)"
                  >查看
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="table_edit(scope.row, scope.$index)" >编辑
                </el-button>
                <el-popconfirm
                  title="确定删除吗？"
                  @confirm="table_del(scope.row, scope.$index)"
                >
                  <template #reference>
                    <el-button type="text" size="small">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </scTable>
        </el-main>
      </el-container>
    </el-container>

    <save-dialog
      v-if="dialog.save"
      ref="saveDialog"
      @success="handleSuccess"
      @closed="dialog.save = false"
    ></save-dialog>
  </el-dialog>
</template>

<script>
import saveDialog from "./jxform";

export default {
  name: "user",
  components: {
    saveDialog,
  },
  data() {
    return {
      mode: "add",
      titleMap: {
        add: "题目解析管理",
        edit: "编辑",
      },

      visible: false,
      dialog: {
        save: false,
      },
      showGrouploading: false,
      groupFilterText: "",
      group: [],
      apiObj: this.$API.jx.page,

      params:{

        where:{
            tkid:0
        }
      },

      selection: [],
      search: {
        name: null,
      },
      defaultProps: {
        label: "name",
      },
    };
  },
  watch: {
    groupFilterText(val) {
      this.$refs.group.filter(val);
    },
  },
  mounted() {
    
  },
  methods: {
    //添加
    async add() {
      this.dialog.save = true;
      
      this.$nextTick(() => {
        this.$refs.saveDialog.open("add").setData({"tkid":this.params.where.tkid});
      });
    },
    //编辑
    async table_edit(row) {
      this.dialog.save = true;
     
     
      this.$nextTick(() => {
        this.$refs.saveDialog.open("edit").setData(row);
      });
    },
    //查看
    table_show(row) {
      this.dialog.save = true;
      this.$nextTick(() => {
        window.open(row.file);
      });
    },
    //删除
    async table_del(row, index) {
      var reqData = { id: row.id };
      var res = await this.$API.jx.delete.post(reqData);
      if (res.code == 200) {
        //这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功");
      } else {
        this.$alert(res.message, "提示", { type: "error" });
      }
    },

    //表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
  

    //搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    //本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    //显示
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },
    setData(data, mode) {
     
        
     this.params.where.tkid=data.id;
     //this.$refs.table.refresh();
    }

  },
};
</script>

<style>
</style>
