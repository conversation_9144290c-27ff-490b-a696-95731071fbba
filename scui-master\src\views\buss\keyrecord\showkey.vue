<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="1200" destroy-on-close @closed="$emit('closed')">
		<el-container>
		
		<el-container>
			<el-header>
			<el-button type="primary" @click="table_exp">导出</el-button>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :data="data" @selection-change="selectionChange"  stripe 
				>
					<el-table-column type="selection" width="50"></el-table-column>


					 
				
					<el-table-column label="激活码" prop="key" width="200"  ></el-table-column>
					 
					<el-table-column label="发放状态" prop="state" width="100" 
                    
                     >
                     <template #default="scope">
                        <el-tag type="success" v-if="scope.row.state==0">未发放</el-tag>
                        <el-tag type="danger" v-else>已发放</el-tag>
                     </template>
                     </el-table-column>

					<el-table-column label="使用状态" prop="state2" width="100"  >
                     <template #default="scope">
                        <el-tag type="success" v-if="scope.row.state2==0">未使用</el-tag>
						  <el-tag type="info" v-if="scope.row.state2==2">过期</el-tag>
                        <el-tag type="danger" v-if="scope.row.state2==1">已使用</el-tag>
                     </template>
                     </el-table-column>
				 <el-table-column label="绑定账号" prop="login_name" width="180"  ></el-table-column>

              
					
					<el-table-column label="添加时间" prop="create_date" width="150" sortable='create_date'></el-table-column>
				
					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_ff(scope.row, scope.$index)">发放
							</el-button>
						 
						</template>
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>
		<template #footer>
			<el-button @click="visible=false">关闭</el-button>
		
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		const validatePrice = (rule, value, callback) => {
			if (value <= 0) {
				callback(new Error('优惠价必须大于0'));
			} else {
				callback();
			}
		};

		return {
            data:[],
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
		
			mode: "add",
			school:{},
			name:"",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			menuList: null,
			visible: false,
			isSaveing: false,
			//菜单树配置
    form:{},
	
			//表单数据
		
			//验证规则
			rules: {
			 
			
			 
				
				 
			},
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getdata();
	},
	methods: {
     async table_ff(row){
            console.log(row);
             await this.$API.common.util.post('/buss/keyrecord/ffkey',{id:row.id});
                let res=await this.$API.common.util.post('/buss/keyrecord/getkey',{id:this.form.id});
           this.data=res.data;
              //提示成功
              this.$message.success("发放成功");
        },
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		//加载树数据
		async getdata() {
			 
		},
		//表单提交方法
	
		//表单注入数据
	async	setData(data) {
			 console.log("======================");
			this.form=data;
           let res=await this.$API.common.util.post('/buss/keyrecord/getkey',{id:data.id});
           this.data=res.data;
		   this.name=data.title;
           console.log(res);

		},
		table_exp() {
			// Get selected rows or all data if nothing is selected
			const exportData = this.selectedRows?.length > 0 ? this.selectedRows : this.data;
			
			// Define CSV headers
			const headers = [
				'激活码',
				'发放状态',
				'使用状态',
				'绑定账号',
				'添加时间'
			];
			
			// Convert data to CSV format
			let csvContent = headers.join(',') + '\n';
			
			exportData.forEach(row => {
				const rowData = [
					row.key,
					row.state === 0 ? '未发放' : '已发放',
					row.state2 === 0 ? '未使用' : '已使用',
					row.login_name || '',
					row.create_date
				];
				csvContent += rowData.join(',') + '\n';
			});
			
			// Create and trigger download
			const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
			const link = document.createElement('a');
			const url = URL.createObjectURL(blob);
			
			link.setAttribute('href', url);
			link.setAttribute('download', this.name+'_激活码记录.csv');
			link.style.visibility = 'hidden';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	}
}
</script>

<style>
</style>
