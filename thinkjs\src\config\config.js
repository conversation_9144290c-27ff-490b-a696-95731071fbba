

// default config
const ueditor = require('./ueditor.js');
module.exports = {
  workers: 1,
  port: 8361,
  // 不检测登录路由
  ignoreUrl: [
    '/api/sys/sysdict/dict',
    '/api/sys/table/infocolmun'
  ],
  ueditor: ueditor,
  domain:"https://jsjy.hanruisoft.com",



  appid: 'wx19db7a1f925ec299', // 公众号APPID
  secret: 'b1da3d4739676a46f234f6c8896d3155',  
  jsapi: {
    debug: true,
    apiList: [
      'updateAppMessageShareData',
      'updateTimelineShareData',
      'chooseImage',
      'previewImage',
      "scanQRCode"
    ]
  },


  wechat: {
    appid: 'wx19db7a1f925ec299', // 公众号APPID
    secret: 'b1da3d4739676a46f234f6c8896d3155',
    token: 'jsjywechat', // 用于验证消息的令牌，需要在公众号后台配置
    encodingAESKey: 'lvxBRL5LNCKlOl1EvYN3YgH5tepeRWVDMdVCAKOJnsi', // 消息加密密钥，可选
  },



  wxpay: {
    appid: 'wx19db7a1f925ec299', // 公众号APPID
    mch_id: '1706925459', // 商户号
    key: 'kH3uB8yO0jP9rL9uP8oH0jH4sI0vX6zB', // API密钥
    notify_url: 'https://app.jisijy.com/api/wechat/notify/wxpay' // 支付结果通知地址
  },

  alicloud: {
    accessKeyId: 'LTAI5tLeZtNFX9CmwPksB1pq', // 阿里云 AccessKey ID
    accessKeySecret: '******************************', // 阿里云 AccessKey Secret
    signName: '瀚瑞信息', // 短信签名
    templateCode: {
      login: 'SMS_479815122', // 
    }
  },

  tencentcloud:{
  secretId: 'AKIDeEetzFNK5f3gC0fBXklofLrDSXSYbVEr',
  secretKey: 'b5PYfcRCpHZihYFqI5BcOLExjMF2d2Lv',
  sdkAppId: '1400971436',
  signName: '泰安极思信息',
  templateId: {
    register: '2384191',
    login: '2384191',
    resetPassword: '2384191'
  }
}

};
