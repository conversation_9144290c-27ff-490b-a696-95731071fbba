const BaseRest = require('../rest.js');
import toPdf from 'custom-soffice-to-pdf'
module.exports = class extends BaseRest {
  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    where['del_flag'] = 0;
    const model = this.model('file');
    const response = await model.where(where).select();
    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }
  async list2Action() {

    let order = 'create_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    where['del_flag'] = 0;

    where['suffix']=['IN',['png', 'jpg','jpeg']]


    let type=this.get("type");

    if(type=="2"){

      where['suffix']=['IN',['mp4', 'mov','mp3']]

    }

    if(type=="3"){

      where['suffix']=['IN',['mp4', 'mov','pdf','xls','xlsx','doc','docx','rar','zip']]

    }

    if(type=="4"){

      where['suffix']=['IN',['xls','xlsx','doc','docx','ppt','pptx']]

    }
    let user = await this.session('userInfo');
    where["create_by"]=user.id;
    const model = this.model('file');
    const res = await model.where(where).page(page, rows).order(order).countSelect();
    const data = {};

    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';


    return this.json(data);


  }
  async rotateAction(){
    const { Image } = require('image-js');
    let p=think.ROOT_PATH + '/www'+this.get("path");
    let image = await Image.load(p);
     image.rotate(90,null).save(p);

   return  this.json({"name":think.uuid})
}
  async compressAction(){
      let width=this.post("width");
     const { Image } = require('image-js');
      var fs = require("fs");
      var path = require("path");
    let json=this.post("json");
 let list=   JSON.parse(json)
  // resize the image, forcing a width of 200 pixels. The height is computed automatically to preserve the aspect ratio.



    for(var item of list){

      let p= think.ROOT_PATH + '/www'+item.path;


      var file = path.parse(item.path);
      file=file.name;

      let p2=p.replace(file,file+"_compress_"+width);
      let image = await Image.load(p);





       image.resize({ width: width }).save(p2);

        item.compress=item.path.replace(file,file+"_compress_"+width);

    }




    return this.json(list);

  }


  async topdfAction(){
  let name=this.post("name");
  let suffix=this.post("suffix");
  let path=this.post("path");



  let p= think.ROOT_PATH + '/www'+path;


  var fs = require("fs")

        var wordBuffer = fs.readFileSync(p)

        var pdfBuffer = await toPdf(wordBuffer)

        path=path.replace("."+suffix,".pdf");
         fs.writeFileSync(think.ROOT_PATH+"/www"+path, pdfBuffer)





  return this.json(path);

}



};
