import tool from '@/utils/tool';

export function permission(data) {
	let userInfo = tool.data.get("USER_INFO");
	if(!userInfo){
		return false;
	}
	let role = userInfo.role;
	let isHave = role.split(",").includes(data);
	return isHave;
}

export function rolePermission(data) {
	let userInfo = tool.data.get("USER_INFO");
	if(!userInfo){
		return false;
	}
	let role = userInfo.rolePermissions;
	if(!role){
		return false;
	}
	let isHave = role.includes(data);
	return isHave;
}

