<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    fullscreen="true"
    destroy-on-close
    @closed="$emit('closed')"
  >


分值范围
<el-input
        v-model="params.start"
        style="width: 130px; margin-right: 30px"
	/>-<el-input
	 
      
         v-model="params.end"
        style="width: 130px; margin-left: 30px"
	/>
<el-button type="text" size="small" @click="score()">确定
							</el-button>

  <div style="display:flex;height:100%">
   	<el-card shadow="never" style="width:60%; ">
			<el-tabs tab-position="top" >

				<el-tab-pane label="题库题目">
            
		<el-container class="is-vertical">
			<el-header>
				<div class="left-panel">
	
      <el-radio-group v-model="type" aria-label="label position" @change="changestate"> 
        <el-radio-button label="单选"></el-radio-button>
        <el-radio-button label="多选"></el-radio-button>
        <el-radio-button label="填空"></el-radio-button>
        <el-radio-button label="解答"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>


        <el-radio-group v-model="ifjj" aria-label="label position" style="margin-left: 20px;" @change="changjiangjie"> 
        <el-radio-button label="已讲解"></el-radio-button>
        <el-radio-button label="未讲解"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>
	
				</div>
			</el-header>
			<el-main class="nopadding">
                        <el-scrollbar  :style="{ height: height + 'px' }">
				<scTable ref="table1" :apiObj="listApi" row-key="id" :params="params1" stripe
          remoteSort
          remoteFilter
				         @selection-change="selectionChange"   :paginationLayout="'prev, pager, next'">
					 
			

          <el-table-column
            label="编号"
            prop="no"
            width="150" sortable='custom'
             
          ></el-table-column>
		  	  <el-table-column label="类型" prop="type" width="50"></el-table-column> 
		   <el-table-column label="分数" prop="score" width="80" sortable='custom'></el-table-column>
                	<el-table-column label="错题数" prop="num2" width="80" :formatter="strFormat"></el-table-column>    
				
					<el-table-column label="题目" prop="tm" width="300" :formatter="strFormat"></el-table-column>
				 <el-table-column label="操作"    width="150">
						<template #default="scope">
							
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
                            <el-button type="text" size="small" @click="table_jiaru1(scope.row, scope.$index)">加入</el-button>
					 
						 
						</template>
					</el-table-column>
                   
			 
				</scTable>

                  </el-scrollbar>
			</el-main>
		</el-container>

              
                </el-tab-pane>

                <el-tab-pane label="讲解题目">


	<el-container class="is-vertical">
			<el-header>
				<div class="left-panel">
	
      <el-radio-group v-model="type" aria-label="label position" @change="changestate2"> 
        <el-radio-button label="单选"></el-radio-button>
        <el-radio-button label="多选"></el-radio-button>
        <el-radio-button label="填空"></el-radio-button>
        <el-radio-button label="解答"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>

       <el-radio-group v-model="ifjj" aria-label="label position" style="margin-left: 20px;" @change="changjiangjie2"> 
        <el-radio-button label="已讲解"></el-radio-button>
        <el-radio-button label="未讲解"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>
	
				</div>
			</el-header>
			<el-main class="nopadding">
                        <el-scrollbar  :style="{ height: height + 'px' }">
				<scTable ref="table2" :apiObj="listApi2" row-key="id" :params="params2" stripe
          remoteSort
          remoteFilter
				         @selection-change="selectionChange"   :paginationLayout="'prev, pager, next'">
					 
			

          <el-table-column
            label="编号"
            prop="no"
            width="150" sortable='custom'
             
          ></el-table-column>
		  	  <el-table-column label="类型" prop="type" width="50"></el-table-column> 
		   <el-table-column label="分数" prop="score" width="80" sortable='custom'></el-table-column>
                	<el-table-column label="错题数" prop="num2" width="80" :formatter="strFormat"></el-table-column>    
				
					<el-table-column label="题目" prop="tm" width="300" :formatter="strFormat"></el-table-column>
				 <el-table-column label="操作"    width="150">
						<template #default="scope">
							
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
                            <el-button type="text" size="small" @click="table_jiaru2(scope.row, scope.$index)">加入</el-button>
					 
						 
						</template>
					</el-table-column>
                   
			 
				</scTable>

                  </el-scrollbar>
			</el-main>
		</el-container>


                </el-tab-pane>

                 <el-tab-pane label="错题题目">

	<el-container class="is-vertical">
			<el-header>
				<div class="left-panel">
	
      <el-radio-group v-model="type" aria-label="label position" @change="changestate3"> 
        <el-radio-button label="单选"></el-radio-button>
        <el-radio-button label="多选"></el-radio-button>
        <el-radio-button label="填空"></el-radio-button>
        <el-radio-button label="解答"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>

       <el-radio-group v-model="ifjj" aria-label="label position" style="margin-left: 20px;" @change="changjiangjie3"> 
        <el-radio-button label="已讲解"></el-radio-button>
        <el-radio-button label="未讲解"></el-radio-button>
        <el-radio-button label="全部"></el-radio-button>
      </el-radio-group>
	
				</div>
			</el-header>
			<el-main class="nopadding">
                        <el-scrollbar  :style="{ height: height + 'px' }">
				<scTable ref="table3" :apiObj="listApi3" row-key="id" :params="params3" stripe
          remoteSort
          remoteFilter
				         @selection-change="selectionChange"   :paginationLayout="'prev, pager, next'">
					 
			

          <el-table-column
            label="编号"
            prop="no"
            width="150" sortable='custom'
             
          ></el-table-column>
		  	  <el-table-column label="类型" prop="type" width="50"></el-table-column> 
		   <el-table-column label="分数" prop="score" width="80" sortable='custom'></el-table-column>
                	<el-table-column label="错题数" prop="num2" width="80" :formatter="strFormat"></el-table-column>    
				
					<el-table-column label="题目" prop="tm" width="300" :formatter="strFormat"></el-table-column>
				 <el-table-column label="操作"    width="150">
						<template #default="scope">
							
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
                            <el-button type="text" size="small" @click="table_jiaru3(scope.row, scope.$index)">加入</el-button>
					 
						 
						</template>
					</el-table-column>
                   
			 
				</scTable>

                  </el-scrollbar>
			</el-main>
		</el-container>
                </el-tab-pane>

 
            </el-tabs>
    </el-card>

<el-card shadow="never" style="width:40%;margin-left:20px">
			<el-tabs tab-position="top">
                    <el-tab-pane label="已选题目">
<scTable ref="finaltable" :data="datalist"  row-key="id"  stripe
          remoteSort
          remoteFilter
				         @selection-change="selectionChange"   :paginationLayout="'prev, pager, next'">
					 
			

          <el-table-column
            label="编号"
            prop="no"
            width="150" sortable='custom'
             
          ></el-table-column>
		  	  <el-table-column label="类型" prop="type" width="50"></el-table-column> 
		   <el-table-column label="分数" prop="score" width="80" sortable='custom'></el-table-column>
                	<el-table-column label="错题数" prop="num2" width="80" :formatter="strFormat"></el-table-column>    
				
					<el-table-column label="题目" prop="tm" width="300" :formatter="strFormat"></el-table-column>
          <el-table-column label="类型" prop="bktype" width="100"  ></el-table-column>
				 <el-table-column label="操作"  fixed="right" align="right"   width="150">
						<template #default="scope">
								<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
							<el-button type="text" size="small" @click="table_remove(scope.row, scope.$index)">删除
							</el-button>
                           
					 
						 
						</template>
					</el-table-column>
                   
			 
				</scTable>
                </el-tab-pane>
            </el-tabs>
    </el-card>
</div>
    <template #footer>
        <el-button type="success" @click="submit()">保存并发布</el-button>
        <el-button @click="tmpsubmit()">暂存</el-button>
      <el-button @click="close()">取 消</el-button>
    </template>



<el-image-viewer
  v-if="showimg"
  :zoom-rate="1.2"
  @close="closePreview"
  :url-list="imglist"
/>



  </el-dialog>


  <record-dialog v-if="dialog.record" ref="recordDialog"  :append-to-body="true"   @closed="dialog.record=false"></record-dialog>

</template>




<script>
 import recordDialog    from './record'
export default {
  components: {
    recordDialog
  },
  emits: ["success", "closed"],
  data() {
    return {
        imglist:[],
        showimg:false,
        ifjj:"",
           options: {
                inline:true,
                title: false,
                navbar: true,
                toolbar: { //自定义下方按钮哪个要哪个不要
                    rotateLeft: true,
                    rotateRight: true,
                    zoomIn: true,
                    zoomOut: true,
                    reset: true,
                },
            },
        lessonid:0,
      height: "",
      rowsytle: {
        height: "",
      },
      mode: "add",
      listApi: this.$API.bk.tkpage,
      listApi2: this.$API.bk.tkpage2,
      listApi3: this.$API.bk.tkpage3,
      titleMap: {
        add: "备课",
        show: "备课",
      },
      type:"",
      datalist:[],
        id:0,
      params:{"start":"",
        end:""
      },
      dialog: {
        record: false
      },
      visible: false,
      isSaveing: false,
 

      form: {
    
      },
      rules: {
      
      },
      params1:{},

       params2:{},
          params3:{}
   


    };
  },
  mounted() {
    
  },
  created() {
    this.height = document.documentElement.clientHeight - 400;
     
    this.rowsytle.height = this.height + "px";
  },
  methods: {
closePreview (){
  this.imglist= []
  this.showimg=false
},

score(){
    this.table1();

     this.table2();

this.table3();
},

changjiangjie(){


 this.table1();

},

changjiangjie2(){


 this.table2();
},

changjiangjie3(){


 this.table3();
},


table_remove(row){
this.removeFromList(row.id);

},
    close() {
      this.$emit("success", this.form, this.mode);
      this.visible = false;
    },
   strFormat(row, column, cellValue) {
      if (!cellValue) return ''
      if (cellValue.length > 30) {       //最长固定显示10个字符
        return cellValue.slice(0, 30) + '...'
      }
      return cellValue
    },
 

	table_jiaru1(row) {
		
			this.$nextTick(() => {
        row['bktype']="教师抽取";
				//this.datalist.push(row);
                this.addToListIfNotExists(row)
			})
		},


table_jiaru2(row) {
		
			this.$nextTick(() => {
        row['bktype']="讲解";

				//this.datalist.push(row);
                this.addToListIfNotExists(row)
			})
		},


    table_jiaru3(row) {
		
			this.$nextTick(() => {
				//this.datalist.push(row);
        row['bktype']="错题";
                this.addToListIfNotExists(row)
			})
		},

    async table1(){
        this.params1={lessonid: this.lessonid};
        if(this.params.start>0){

            this.params1.start=this.params.start;
        }


if(this.type&&this.type!="全部"){

            this.params1.type=this.type;
        }


if(this.ifjj&&this.ifjj!="全部"){

            this.params1.ifjj=this.ifjj;
        }

         if(this.params.end>0){

            this.params1.end=this.params.end;
        }
    this.$nextTick(()=>{   this.$refs.table1.upData({
				where: JSON.stringify(this.params1)
			})} ) 
       
       
    },




        async table2(){
        this.params2={lessonid: this.lessonid};
        if(this.params.start>0){
            this.params2.start=this.params.start;
        }


if(this.type&&this.type!="全部"){
            this.params2.type=this.type;
        }

if(this.ifjj&&this.ifjj!="全部"){

            this.params2.ifjj=this.ifjj;
        }

         if(this.params.end>0){

            this.params2.end=this.params.end;
        }
    this.$nextTick(()=>{   this.$refs.table2.upData({
				where: JSON.stringify(this.params2)
			})} ) 
       
       
    },



    
        async table3(){
        this.params3={lessonid: this.lessonid};
        if(this.params.start>0){
            this.params3.start=this.params.start;
        }


  if(this.type&&this.type!="全部"){

            this.params3.type=this.type;
        }

        if(this.ifjj&&this.ifjj!="全部"){

            this.params3.ifjj=this.ifjj;
        }

         if(this.params.end>0){

            this.params3.end=this.params.end;
        }
    this.$nextTick(()=>{   this.$refs.table3.upData({
				where: JSON.stringify(this.params3)
			})} ) 
       
       
    },


    //显示
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },



changestate(){

 
	this.table1();
   

	 
},


 removeFromList(id) {
  let index = this.datalist.findIndex(item => item.id === id);
  if (index !== -1) {
    this.datalist.splice(index, 1);
    
  } else {
    
  }
}
,

 addToListIfNotExists(newItem) {
  let isIdExists = this.datalist.some(item => item.id === newItem.id);
  
  if (!isIdExists) {
    this.datalist.push(newItem);
  
  } else {
    this.$alert("该题已选择", "提示", { type: "error" }); 
  }
},


changestate2(){

 

    this.table2();

	 
},


changestate3(){

 

     this.table3();
	 
},

    //获取字典列表
   
    //表单提交方法
 async   tmpsubmit() {
      if (this.datalist.length>0) {
        
       
            this.isSaveing = true;
 console.log("sadfasdfs");
            console.log(this.datalist);

          const newArray = this.datalist.map(item => ({
                      id: item.id,
                      bktype: item.bktype
                  }));

            let res = await this.$API.bk.tmpsubmit.post({id:this.id,ids:JSON.stringify({"list":newArray})});
            this.isSaveing = false;
            if (res.state == 1) {
                 this.$emit("success",{}, this.mode);
              this.visible = false;
              this.$message.success("操作成功");
            } else {
              this.$alert(res.msg, "提示", { type: "error" });
            }
          
      
      }else{
        this.$alert("不能为空", "提示", { type: "error" });

      }
    },

table_show(row){
   
 this.dialog.record=true;
 	this.$nextTick(() => {
				this.$refs.recordDialog.open().setData(row);
			})


},

     async   submit() {
      if (this.datalist.length>0) {
        
       
            this.isSaveing = true;


     const newArray = this.datalist.map(item => ({
                      id: item.id,
                      bktype: item.bktype
                  }));

           

            let res = await this.$API.bk.submit.post({id:this.id,ids:JSON.stringify({"list":newArray})});
            this.isSaveing = false;
            if (res.state == 1) {
                 this.$emit("success",{}, this.mode);
              this.visible = false;
              this.$message.success("操作成功");
            } else {
              this.$alert(res.msg, "提示", { type: "error" });
            }
          
      
      }else{
        this.$alert("不能为空", "提示", { type: "error" });

      }
    },
    //表单注入数据
  async  setData(data ) {
        this.id=data.id;

        this.datalist=await this.$API.bk.getlist.get({id:data.id});
        this.lessonid=data.lesson;
        this.table1();
        this.table2();
         this.table3();
    
    },
  },
};
</script>

<style>
.el-dialog__body{

    height: 80%;
}

</style>
