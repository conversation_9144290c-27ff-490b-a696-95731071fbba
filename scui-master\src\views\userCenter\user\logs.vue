<template>
	<el-card shadow="never" header="最近10次操作记录">
		<scTable ref="table" :data="data" height="auto" paginationLayout="total, prev, pager, next" hideDo>
			<sc-table-column label="序号" type="index"></sc-table-column>
			<sc-table-column label="业务名称" prop="title" min-width="150"></sc-table-column>
			<sc-table-column label="IP" prop="remote_addr" width="150"></sc-table-column>
			<sc-table-column label="结果" prop="status" width="150">
				<template #default="scope">
				<el-tag type="success">	{{ scope.row.status == 200 ? "成功" : "失败" }}</el-tag>
				</template>
			</sc-table-column>
			<sc-table-column label="操作时间" prop="create_date" width="150"></sc-table-column>

		</scTable>
	</el-card>
</template>

<script>
export default {
	data() {
		return {
			data: [
				
			]
		}
	},
	methods: {
		async getData() {
			let userInfo = this.$TOOL.data.get("USER_INFO");
			let log = await this.$API.log.page.get({ pageSize: 10, where: JSON.stringify({ create_by: userInfo.id }) });
			this.data = log.data;
		}
	},
	async created() {
		await this.getData()
	},
}
</script>

<style>
</style>
