import config from "@/config";
import http from "@/utils/request";

const log = {
	page: {
		url: `${config.API_URL}/sys/syslog/page`,
		name: "日志列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	info: {
		url: `${config.API_URL}/sys/syslog/info`,
		name: "日志详情",
		post: async function (data) {
			return await http.post(this.url, this.name,data);
		}
	}
};

export default log;
