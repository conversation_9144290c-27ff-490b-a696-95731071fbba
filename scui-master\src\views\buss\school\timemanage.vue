<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    :width="1200"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-container>
      <el-container>
        <el-header>
          <div class="left-panel">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="add"
            ></el-button>
          </div>
          <div class="right-panel">
            <div class="right-panel-search"></div>
          </div>
        </el-header>
        <el-main class="nopadding">
          <scTable
            ref="table"
            :apiObj="apiObj"

            :params="params"

            @selection-change="selectionChange"
            stripe
            remoteSort
            remoteFilter
          >
            <el-table-column type="selection" width="50"></el-table-column>

            <el-table-column
              label="学校"
              prop="schoolname"
              width="150"
            ></el-table-column>


            <el-table-column
              label="时长"
              prop="num"
              width="150"
            ></el-table-column>


            
            <el-table-column
              label="类型"
              prop="type"
              width="150"
            ></el-table-column>

            <el-table-column
              label="说明"
              prop="remarks"
              width="150"
            ></el-table-column>

            <el-table-column
              label="添加时间"
              prop="create_date"
              width="150"
              sortable="custom"
            ></el-table-column>
            
          </scTable>
        </el-main>
      </el-container>
    </el-container>

     <time-form v-if="dialog.save"
      ref="timeForm"
      @success="handleSuccess"
      @closed="dialog.save = false">
      
     </time-form>
  </el-dialog>
</template>

<script>
import timeForm from "./timeform";

export default {
 
  components: {
    timeForm,
  },
  data() {
    return {
      schoolid:0,
      mode: "add",
      titleMap: {
        add: "上传",
        edit: "编辑",
      },

      visible: false,
      dialog: {
        save: false,
      },
      showGrouploading: false,
      groupFilterText: "",
      group: [],
      apiObj: this.$API.school.timerecord,

      params:{

        where:{
            schoolid:0
        }
      },

      selection: [],
      search: {
        name: null,
      },
      defaultProps: {
        label: "name",
      },
    };
  },
  watch: {
    groupFilterText(val) {
      this.$refs.group.filter(val);
    },
  },
  mounted() {
    
  },
  methods: {
    //添加
    async add() {
      this.dialog.save = true;
      
      
      this.$nextTick(() => {
        this.$refs.timeForm.open("add").setData({"schoolid":this.schoolid});
      });
    },

  
     

    //表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
  

    //搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    //本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    //显示
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },
    setData(data, mode) {
      this.schoolid=data.schoolid;
      this.params.where.schoolid=this.schoolid;
      
     this.$refs.table.refresh();
    }

  },
};
</script>

<style>
</style>
