const axios = require('axios');

module.exports = class extends think.Controller {
  /**
   * 创建公众号菜单
   */
  async createAction() {
    // 获取access_token
    let accessToken;
    try {
      accessToken = await this.getWechatAccessToken();
    } catch (error) {
      return this.fail('获取access_token失败: ' + error.message);
    }
    
    // 菜单定义 - 简化菜单结构，避免可能的格式问题
    const menuData = {
      button: [
        {
          name: "课程服务",
          type: "view",
          url: "https://app.jisijy.com/wx/index"
        },
        {
          name: "关于我们",
          type: "click",
          key: "ABOUT_US"
        },
        {
          name: "联系客服",
          type: "click",
          key: "CONTACT_US"
        }
      ]
    };
    
    // 调用微信接口创建菜单
    try {
      // 先尝试查询当前菜单
      const getMenuUrl = `https://api.weixin.qq.com/cgi-bin/menu/get?access_token=${accessToken}`;
      const menuResponse = await axios.get(getMenuUrl);
      console.log('当前菜单信息:', JSON.stringify(menuResponse.data));
      
      // 创建新菜单
      const url = `https://api.weixin.qq.com/cgi-bin/menu/create?access_token=${accessToken}`;
      console.log('发送的菜单数据:', JSON.stringify(menuData));
      
      const response = await axios.post(url, menuData);
      console.log('微信返回结果:', JSON.stringify(response.data));
      
      if (response.data && response.data.errcode === 0) {
        return this.success('创建菜单成功');
      } else {
        // 检查公众号类型
        const infoUrl = `https://api.weixin.qq.com/cgi-bin/get_current_selfmenu_info?access_token=${accessToken}`;
        const infoResponse = await axios.get(infoUrl);
        console.log('公众号菜单配置信息:', JSON.stringify(infoResponse.data));
        
        return this.fail('创建菜单失败: ' + JSON.stringify(response.data));
      }
    } catch (error) {
      console.error('请求错误详情:', error);
      return this.fail('创建菜单请求失败: ' + error.message);
    }
  }
  
  /**
   * 获取微信公众号接口调用凭证
   */
  async getWechatAccessToken() {
    // 获取配置的appid和secret
    const { appid, secret } = think.config();
    
    console.log('使用的AppID:', appid);
    
    // 检查是否已有缓存的token
    let accessToken = await this.cache('wechat_access_token');
    
    if (!accessToken) {
      // 从微信服务器获取新的token
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
      
      try {
        const response = await axios.get(url);
        console.log('获取token返回:', JSON.stringify(response.data));
        
        if (response.data && response.data.access_token) {
          accessToken = response.data.access_token;
          // 缓存token（微信token有效期为7200秒，这里设置7000秒）
          await this.cache('wechat_access_token', accessToken, {
            timeout: 7000 * 1000
          });
        } else {
          throw new Error('获取access_token失败: ' + JSON.stringify(response.data));
        }
      } catch (error) {
        console.error('获取access_token错误详情:', error);
        think.logger.error('获取微信access_token失败:', error);
        throw error;
      }
    }
    
    return accessToken;
  }
  
  /**
   * 删除当前菜单
   */
  async deleteMenuAction() {
    try {
      const accessToken = await this.getWechatAccessToken();
      const url = `https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=${accessToken}`;
      
      const response = await axios.get(url);
      
      if (response.data && response.data.errcode === 0) {
        return this.success('删除菜单成功');
      } else {
        return this.fail('删除菜单失败: ' + JSON.stringify(response.data));
      }
    } catch (error) {
      return this.fail('删除菜单请求失败: ' + error.message);
    }
  }
  
  /**
   * 获取公众号信息
   */
  async getInfoAction() {
    try {
      const accessToken = await this.getWechatAccessToken();
      
      // 获取公众号基本信息
      const infoUrl = `https://api.weixin.qq.com/cgi-bin/get_current_selfmenu_info?access_token=${accessToken}`;
      const infoResponse = await axios.get(infoUrl);
      
      return this.success(infoResponse.data);
    } catch (error) {
      return this.fail('获取公众号信息失败: ' + error.message);
    }
  }
};