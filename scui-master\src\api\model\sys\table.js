import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/sys/table/page`,
		name: "表格数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/sys/table/add`,
		name: "表格数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/sys/table/save`,
		name: "表格数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/sys/table/info`,
		name: "表格数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/sys/table/delete`,
		name: "删除表格数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	infoColmun:{
		url: `${config.API_URL}/sys/table/infocolmun`,
		name: "查询表格列数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	updateColmun:{
		url: `${config.API_URL}/sys/table/updatecolmun`,
		name: "修改表格列数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
