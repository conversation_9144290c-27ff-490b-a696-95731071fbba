const BaseRest = require('./rest.js');
const Decimal = require("decimal.js");
module.exports = class extends BaseRest {
  //查询待办事项


  async techserviceAction(){
      let sql="select h.date,h.weekcn,ifnull(t.num,0) as num  from sys_holidays h  left JOIN oa_teachservice_count t on h.date=t.d where  date >= DATE(NOW()) + INTERVAL -7 DAY AND date < DATE(NOW()) + INTERVAL 0 DAY order by t.d asc "
      let model = this.model('user');


    let res=await model.query(sql);

   return this.json()

  }

  async daibanAction() {
    const userInfo = await this.session('userInfo');

    let rolemodel = this.model('user_role');
    let roleres = await rolemodel.alias("c").join(['sys_role r on c.role_id=r.id ']).where({"user_id": userInfo.id}).select();
    let roleid = 0;

    if (!think.isEmpty(roleres)) {
      roleid = roleres[0].aid;
    }
    let sql = "SELECT  count(*) as num FROM `sys_wf_run_process` `f` INNER JOIN `sys_wf_flow` `w` ON `f`.`run_flow`=`w`.`id` INNER JOIN `sys_wf_run` `r` ON `f`.`run_id`=`r`.`id`  INNER JOIN  sys_wf_flow_process  p on p.id=r.run_flow_process WHERE ( (f.auto_person != 5 and FIND_IN_SET(" + userInfo.aid + ",f.sponsor_ids)) or(f.auto_person=5 and FIND_IN_SET(" + roleid + ",f.sponsor_ids))  )  and  `f`.`status` = '0' AND `r`.`status` = '0' ";

    let model = this.model("user");

    let sql2 = "select ifnull(sum(unread),0) as num from sys_muser where userid=" + userInfo.aid;


    let res = await model.query(sql);

    let res2 = await model.query(sql2);

    let sql3 = "select ifnull(count(*),0) as num from oa_remind where date_sub(curdate(), interval 7 day) <= date(date)";

    let res3 = await model.query(sql3);

    return this.json({"count1": res[0].num, "count2": res2[0].num, "count3": res3[0].num})


  }


  async officedataAction() {
    const search = this.post("search") ? this.post("search") : {office_id: null, date: null}
    const holidaysModel = this.model('holidays');
    const userInfo = await this.session('userInfo');
    var moment = require('moment');
    let model = this.model("user");

    let officeid = 0;
    if (!think.isEmpty(search.office_id)) {
      let officeids = await model.query(" SELECT getLeafNodeList(" + parseInt(search.office_id) + ") AS office_ids");
      officeid = officeids[0].office_ids;
    }

    let beginDate = '';
    let endDate = '';
    if (!think.isEmpty(search.date)) {
      beginDate = search.date[0];
      endDate = search.date[1];
    }

    //外部收款 金额合计
    let sql = "select ifnull(sum(incoming_money),0) as num     from finance_incoming  where  incoming_source='收入' and office_id in (" + officeid + ") AND incoming_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0 AND  status=2 AND incoming_class!='预付款'";

    //外部预付款 金额合计
    let sql0 = "select ifnull(sum(incoming_money),0) as num     from finance_incoming  where  incoming_source='收入' and office_id in (" + officeid + ") AND incoming_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0 AND status=2  AND incoming_class='预付款'";

    let res0 = await model.query(sql0);
    let res = await model.query(sql);

    //礼品 金额合计
    let sql111 = "select ifnull(sum(share_money),0) as num FROM sys_repository_use_share WHERE share_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND share_office_id in (" + officeid + ")";
    let res111 = await model.query(sql111);

    //内部收入合计
    let sql2 = "select ifnull(sum(inside_money),0) as num  from finance_inside where inside_source='收入' and office_id in (" + officeid + ") AND inside_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0";
    let res2 = await model.query(sql2);

    //外部支出合计
    let sql30 = "select ifnull(sum(incoming_money),0) as num     from finance_incoming  where  incoming_source='支出' and office_id in (" + officeid + ") AND incoming_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0";

    let res30 = await model.query(sql30);
    //内部支出合计

    let sql3 = "select ifnull(sum(inside_money),0) as num  from finance_inside where inside_source='支出' and office_id in (" + officeid + ") AND inside_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0";
    let res3 = await model.query(sql3);
    //报销数据
    let sql4 = "SELECT IFNULL(sum(sum_money),0) as num  from sys_costpay_main where office_id in (" + officeid + ") and `status`=2  and iscommon=2  AND pay_date BETWEEN '" + beginDate + "' AND" +
      " '" + endDate + "'" + " AND del_flag=0";

    let res4 = await model.query(sql4);


    let sql6 = "select ifnull(sum(share_money),0) as num FROM finance_share_detail WHERE share_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND share_office_id in (" + officeid + ")";

    let res6 = await model.query(sql6);


    // 获取当前工作日
    let workData = await holidaysModel.where({
      date: ['between', beginDate, endDate],
      del_flag: 0
    }).select();
    let workResult = {
      work: 0,
      rest: 0,
      holidays: 0
    };
    workData.map(item => {
      if (item.type == "0") {
        workResult.work++;
      } else if (item.type == "1") {
        workResult.rest++;
      } else {
        workResult.holidays++;
      }
    });
    let sql7 = "select ifnull(sum(days),0) as num FROM sys_leave WHERE start_time BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND office_id in (" + officeid + ") AND status= 2";

    let res7 = await model.query(sql7);
//加班
    let sql8 = "select ifnull(sum(overtime_time),0) as num FROM sys_work_overtime WHERE overtime_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND office_id in (" + officeid + ") AND" +
      " status= 2";

    let res8 = await model.query(sql8);

    sql = "select * from sys_office where id in (" + officeid + ")";

    let offices = await model.query(sql);
    let worknum = 0;
    for (var o of offices) {

      let t = parseInt(o.people_number + o.people_outside) * parseInt(workResult.work)

      worknum = worknum + t;
    }
    let hk = 0;
    let sql9 = "select ifnull(sum(arrival_number*inside_money),0) as num FROM pay_place_order_detail WHERE del_flag = 0 and needbuy=1 and bussid is null and arrive=0";

    let sql10 = "select ifnull(sum(arrival_number*arrival_money),0) as num FROM pay_place_order_detail WHERE del_flag = 0 and needbuy=1 and bussid is not null and arrive=0";
    let res9 = await model.query(sql9);
    let res10 = await model.query(sql10);

    hk = parseFloat(res9[0]['num']) + parseFloat(res10[0]['num']);

    let sql11 = "select ifnull(sum(money2),0) as num FROM finance_pay_request WHERE del_flag = 0 and ifclose = 0"
    let res11 = await model.query(sql11);


    //查询付款流程未结束的
    let sql17 = "select ifnull(sum(money),0) as num  from sys_paying where `status`<2 and del_flag='0'"
    let res17 = await model.query(sql17);


    let sql12 = "SELECT ifnull(SUM( d.income + a.sum1 + b.sum2 ),0) AS num FROM sys_billdetail d LEFT JOIN ( SELECT ifnull(SUM( money * number ),0) AS sum1, bill_id FROM sys_billcost WHERE" +
      " del_flag = 0 GROUP BY bill_id ) a ON a.bill_id = d.bill_id LEFT JOIN ( SELECT ifnull(SUM( money * number * params ),0) AS sum2, bill_id FROM sys_billoutsource WHERE del_flag = 0 GROUP BY" +
      " bill_id ) b ON b.bill_id = d.bill_id WHERE d.del_flag = 0 AND d.STATUS != 2 AND d.bill_settlement = '验收结算' AND d.office_id in (" + officeid + ")"
    let res12 = await model.query(sql12);


    let sql13 = "SELECT ifnull( SUM( i.inside_money ), 0 ) AS num FROM finance_inside i WHERE i.del_flag = 0 AND i.inside_type='订货' AND i.inside_source='支出' AND i.inside_date BETWEEN '"+ beginDate +"' AND '"+ endDate+"' AND i.office_id IN (" + officeid + ")";
    let res13 = await model.query(sql13);

    //内部支出合计

    let sql14 = "select ifnull(sum(inside_money),0) as num  from finance_inside where inside_source='支出' and office_id in (" + officeid + ") AND inside_date BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " AND del_flag=0 AND is_cost=1";
    let res14 = await model.query(sql14);


    let sql15 = "SELECT ifnull( SUM( d.arrival_money*d.arrival_number ), 0 ) as num FROM sys_billdetail b LEFT JOIN pay_place_order_main m ON m.bill_id = b.bill_id LEFT JOIN pay_place_order_detail d ON m.id = d.place_id WHERE b.bill_settlement = '验收结算'  AND b.`status` = 1 AND b.del_flag = '0' AND m.del_flag = '0' AND d.del_flag = '0' AND d.needbuy = 1 AND d.arrive = 1";
    let res15 = await model.query(sql15);
    //服务库存合计
    let sql16="\n" +
      "SELECT ifnull( SUM(a.monthMoney),0) AS num FROM (SELECT\n" +
      "\t\t*,\n" +
      "CASE\n" +
      "\t\tWHEN\n" +
      "\t\t'"+beginDate+"' <= share_start_date AND '"+endDate+"' >= share_end_date \n" +
      "\t\tTHEN  (PERIOD_DIFF(DATE_FORMAT(share_end_date,\"%Y%m\"),DATE_FORMAT(share_start_date,\"%Y%m\"))+1)* share_period\n" +
      "\t\tWHEN\n" +
      "\t\t'"+beginDate+"' >= share_start_date AND '"+endDate+"' >= share_end_date \n" +
      "\t\tTHEN  (PERIOD_DIFF(DATE_FORMAT(share_end_date,\"%Y%m\"),DATE_FORMAT('"+beginDate+"',\"%Y%m\"))+1)* share_period\n" +
      "\t\tWHEN\n" +
      "\t\t'"+beginDate+"' <= share_start_date AND '"+endDate+"' <= share_end_date \n" +
      "\t\tTHEN  (PERIOD_DIFF(DATE_FORMAT('"+endDate+"',\"%Y%m\"),DATE_FORMAT(share_start_date,\"%Y%m\"))+1)* share_period\n" +
      "\t\tWHEN\n" +
      "\t\t'"+beginDate+"' >= share_start_date AND '"+endDate+"' <= share_end_date \n" +
      "\t\tTHEN  (PERIOD_DIFF(DATE_FORMAT('"+endDate+"',\"%Y%m\"),DATE_FORMAT('"+beginDate+"',\"%Y%m\"))+1)*share_period\n" +
      "\t\tEND AS monthMoney\n" +
      "\tFROM\n" +
      "\t\tfinance_incoming_share \n" +
      "WHERE\n" +
      "\tshare_start_date <= '"+endDate+"' AND share_end_date >= '"+beginDate+"' AND share_office_id in (" + officeid + ") ) a";
    let res16 = await model.query(sql16);
    return this.json({
      //外部收入
      "wbsr": parseFloat(res[0]['num']),
      //外部预付款收入
      "yfk": parseFloat(res0[0]['num']),
      //内部收入
      "nbsr": parseFloat(res2[0]['num']),
      //外部支出
      "wbzc": parseFloat(res30[0]['num']),
      //内部支出
      "nbzc": parseFloat(res3[0]['num']),
      //报销
      "bx": parseFloat(res4[0]['num']),
      "lp": parseFloat(res111[0]['num']),
      //公摊
      'gt': parseFloat(res6[0]['num']),
      'worknum': parseFloat((worknum * 8 + res8[0]['num'] - res7[0]['num'] * 8) / 8),
      //未到货货款
      hk: hk,
      xm: parseFloat(res12[0]['num']),
      yf: Decimal(res11[0]['num']).add(Decimal(res17[0]['num'])).toNumber(),
      dhhk: parseFloat(res13[0]['num']),
      cbzh: parseFloat(res14[0]['num']),
      cbsg: parseFloat(res15[0]['num']),
      fwkc: parseFloat(res16[0]['num'])
    });
  }





  async chart1Action() {
    const search = this.post("search") ? this.post("search") : {office_id: null, date: null}
    let beginDate = '';

    let officeid = 0;
    let endDate = '';
    if (!think.isEmpty(search.date)) {
      beginDate = search.date[0];
      endDate = search.date[1];
    }

    let model = this.model("user");
    if (!think.isEmpty(search.office_id)) {
      let officeids = await model.query(" SELECT getLeafNodeList(" + parseInt(search.office_id) + ") AS office_ids");
      officeid = officeids[0].office_ids;
    }

    let sql = "select inside_type as name,sum(inside_money) as value from finance_inside where inside_date   BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " and office_id   in (" + officeid + ") and del_flag=0 group by inside_type "
    let res = await model.query(sql);


    this.json(res);
  }


  async chart2Action() {
    const search = this.post("search") ? this.post("search") : {office_id: null, date: null}
    let beginDate = '';

    let officeid = 0;
    let endDate = '';
    if (!think.isEmpty(search.date)) {
      beginDate = search.date[0];
      endDate = search.date[1];
    }

    let model = this.model("user");
    if (!think.isEmpty(search.office_id)) {
      let officeids = await model.query(" SELECT getLeafNodeList(" + parseInt(search.office_id) + ") AS office_ids");
      officeid = officeids[0].office_ids;
    }

    let sql = "select o.name as name ,o.id as office_id ,t.`value` from (select pay_office_id,sum(inside_money) as value from finance_inside where  inside_date  BETWEEN '" + beginDate + "' AND '" + endDate + "'" + " and office_id   in (" + officeid + ") and del_flag=0 group by pay_office_id ) as t join sys_office o on o.id=t.pay_office_id and o.del_flag=0"
    let res = await model.query(sql);


    this.json(res);


  }


}
