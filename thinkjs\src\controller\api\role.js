const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {
  async listAction() {
    const page = this.get('page');
    const rows = this.get('pageSize');
    const keyword = this.get('keyword');
    const model = this.model('role');
    const where = {del_flag: 0};
    if (!think.isEmpty(keyword)) {
      where['name'] = ['like', '%' + keyword + '%'];
    }
    const res = await model.alias('c').page(page, rows).where(where).order('sort asc').countSelect();
    const data = {};

    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';

    return this.json(data);
  }

  async selectAction() {
    const model = this.model('role');
    const where = {del_flag: 0};
    const res = await model.where(where).select();

    return this.json(res);
  }

  async removeAction() {
    const id = this.get('id');

    const model = this.model('role');
    await model.where({'id': id}).update({'del_flag': 1});

    return this.json({'code': 200});
  }

  async saveAction() {
    const model = this.model('role');
    const data = this.post();

    if (think.isEmpty(data.id)) {
      data.id = think.uuid();
      await model.add(data);
    } else {
      await model.where({'id': data.id}).update(data);
    }

    this.json({code: 200});
  }

  async savepAction() {
    const data = this.post();

    const model = this.model('role_menu');
    const role = this.model('role');
    await role.where({id: data.id}).update({data_scope: data.data_scope, data_office_scope: data.data_office_scope});
    await model.where({'role_id': data.id}).delete();
    const res = [];
    for (var one of data.menus) {
      const tmp = {};
      tmp['menu_id'] = one;
      tmp['role_id'] = data.id;
      res.push(tmp);
    }
    await model.addMany(res);

    this.json({code: 200});
  }

  async getmenusAction() {
    const model = this.model('role_menu');

    const res = await model.where({'role_id': this.post('id')}).select();

    const obj = [];

    for (var one of res) {
      obj.push(one['menu_id']);
    }

    return this.json({'list': obj});
  }
};
