module.exports = class extends think.Controller {

       //处理到期的题
       async processAction() {
        let model=this.model("tk_user");

        
        let sql="update sys_tk_user set state=0 where now() >=yxq";
        await model.execute(sql);





        let model2=this.model("bk")

     //   await model.execute("UPDATE sys_bk SET state = 3 WHERE skdate < NOW()")
       await model.execute("UPDATE sys_bk SET state = 3 WHERE skdate <= DATE_SUB(NOW(), INTERVAL 2 HOUR) and state=2")

        await model.execute("UPDATE sys_bk SET state = 4 WHERE skdate <= NOW() and state=3")

      let list =await model.query("SELECT * FROM buss_key where yxq <now() and state2=0");

      for(let tmp of list){
        let keyrecord  =await this.model("keyrecord").where({"id":tmp.keyrecord}).find();

        await this.model("school").where({"id":keyrecord.schoolid}).increment("canusetime",keyrecord.usetime);
        await this.model("key").where({"id":tmp.id}).update({"state2":2});



      }






        return this.json("ok")


    }


    async processscoreAction(){
        let model=this.model("student");

        let model2=this.model("userdaysinfo");

        let users=await model.where({"del_flag":0}).select();

        for(let u of users ){ 
            if(!think.isEmpty(u.level)){
                let lessones=await this.model("lesson").where({"type":u.level,"parent_id":0,"del_flag":0}).field("id").select();

                for(let l of lessones){
                

                    let para={};

                    para.uid=u.id;
                    para.lessonid=l.id;
                    para.create_date=think.datetime();

                    let ifhave=await this.model("userdaysinfo").where(para).find();


                    if(think.isEmpty(ifhave)){

                        let child=await this.model("lesson").where({"parent_id":l.id,"del_flag":0}).field("id").select();
                        if(!think.isEmpty(child)){

                            let tmpsql="SELECT ifnull(avg(score),50)  as score  FROM sys_lesson_user where userid="+u.id+" and lessonid  in ("+child.map(item => item.id).join(',')+")"
                            let avg=await model.query(tmpsql);
                            para.score=avg[0]['avg']
                        }else{
                            para.score=50;
                        }
                       
    
                        await this.model("userdaysinfo").add(para);
                    }


                  

                }





            }




        }



        return this.json("ok")


    }




 


  async caclscoreAction() {
    
    let model=this.model("student");
    let users=await model.where({"del_flag":0}).select();
    let model2=this.model("userdaysinfo");

    for(let u of users){
        const scores = await this.calculateUserAllScores(u.id);
      console.log(scores)
      let arr=[];
      for(let s of scores){
        arr.push({
            uid:u.id,
            lessonid:s.lessonId,
            lessonname:s.name,
            score:s.score,
            create_date:think.datetime("YYYY-MM-DD")    
        })
      }

      console.log("insert",arr);

      await model2.where({"uid":u.id,"create_date":think.datetime("YYYY-MM-DD")}).delete();
      await model2.addMany(arr);



    }
    return this.json("ok");
    
   
  }


  async calculateUserAllScores(userId) {
    // 获取所有根科目（parent_id = 0）
    const rootLessons = await this.model('lesson')
      .where({
        parent_id: 0,
        del_flag: '0'
      })
      .select();

    const results = [];
    
    // 计算每个根科目的成绩
    for (const rootLesson of rootLessons) {
      const score = await this.calculateSubjectScore(userId, rootLesson.id);
      results.push({
        lessonId: rootLesson.id,
        name: rootLesson.name,
        score: Math.round(score)
      });
    }

    return results;
  }

  /**
   * 递归计算科目成绩
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<number>} 返回计算后的成绩
   */
  async calculateSubjectScore(userId, lessonId) {
    // 获取当前科目的所有子科目
    const children = await this.model('lesson')
      .where({
        parent_id: lessonId,
        del_flag: '0'
      })
      .select();

    // 如果没有子科目，说明是最底层科目，直接从 lesson_user 表获取成绩
    if (think.isEmpty(children)) {
      const userScore = await this.model('lesson_user')
        .where({
          userid: userId,
          lessonid: lessonId
        })
        .field('score')
        .find();
      
      return userScore.score || 50; // 如果没有成绩记录，返回默认分数50
    }

    // 如果有子科目，递归计算所有子科目的成绩
    let totalScore = 0;
    let validChildren = 0;

    for (const child of children) {
      const childScore = await this.calculateSubjectScore(userId, child.id);
      totalScore += childScore;
      validChildren++;
    }

    // 返回子科目的平均分
    return validChildren > 0 ? totalScore / validChildren : 50;
  }

  /**
   * 获取完整的科目成绩树
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<Object>} 返回包含成绩的科目树
   */
  async getScoreTree(userId, lessonId) {
    // 获取当前科目信息
    const lesson = await this.model('lesson')
      .where({
        id: lessonId,
        del_flag: '0'
      })
      .find();

    if (think.isEmpty(lesson)) {
      return null;
    }

    // 计算当前科目成绩
    const score = await this.calculateSubjectScore(userId, lessonId);

    // 获取子科目
    const children = await this.model('lesson')
      .where({
        parent_id: lessonId,
        del_flag: '0'
      })
      .select();

    const result = {
      id: lesson.id,
      name: lesson.name,
      score: Math.round(score),
      children: []
    };

    // 递归获取子科目的成绩树
    if (!think.isEmpty(children)) {
      for (const child of children) {
        const childTree = await this.getScoreTree(userId, child.id);
        if (childTree) {
          result.children.push(childTree);
        }
      }
    }

 

    return result;
  }

  /**
   * 获取成绩树
   */
  async treeAction() {
    try {
      const userId = 3;
      const lessonId = this.post('lessonId'); // 可选，不提供则返回所有根科目的树

      if (!userId) {
        return this.fail('请提供用户ID');
      }

      if (lessonId) {
        // 获取指定科目的成绩树
        const tree = await this.getScoreTree(userId, lessonId);
        return this.success(tree);
      } else {
        // 获取所有根科目的成绩树
        const rootLessons = await this.model('lesson')
          .where({
            parent_id: '0',
            del_flag: '0'
          })
          .select();

        const forest = [];
        for (const root of rootLessons) {
          const tree = await this.getScoreTree(userId, root.id);
          if (tree) {
            forest.push(tree);
          }
        }

       // console.log(forest);
        return this.success(forest);
      }
    } catch (err) {
      think.logger.error('Score tree calculation error:', err);
      return this.fail(err.message);
    }
  }











   async processvipAction(){
    let auto = think.service('auto');
    await auto.autoDeductVipDays();



    return this.json(1);

}

  async processschoolAction(){


    await this.model("school").update({"todaycost":0});

    return this.json(1);



  }


    async processorderAction(){
      try {
        // 获取30分钟前的时间
        const thirtyMinutesAgo = think.datetime(Date.now() - 30 * 60 * 1000);
        
        // 删除超过30分钟未支付的订单
        const result = await this.model('order').where({
          pay_status: 0,
          create_time: ['<', thirtyMinutesAgo]
        }).update({"del_flag":1});

        think.logger.info(`已删除 ${result} 条超时未支付订单`);
        
        return this.json({
          code: 200,
          message: `成功删除${result}条超时订单`,
          data: {
            deleted: result
          }
        });

      } catch (error) {
        think.logger.error('删除超时订单失败:', error);
        return this.json({
          code: 500, 
          message: '删除超时订单失败: ' + error.message
        });
      }

      
    }

async processyxAction(){
  try {
    // 获取所有有扫码记录但用户yxid为空的情况
    const scanRecords = await this.model('yx_scan')
      .alias('scan')
      .join('buss_student student ON scan.openid = student.openid')
      .where({
        'student.del_flag': 0,
        'student.yxid': ['exp', 'IS NULL OR student.yxid = 0 OR student.yxid = ""']
      })
      .field('scan.openid, scan.yx_id, student.id as student_id, student.yxid')
      .select();

    if (think.isEmpty(scanRecords)) {
      return this.json({
        code: 200,
        message: '没有需要更新的用户记录',
        data: {
          updated: 0
        }
      });
    }

    let updatedCount = 0;

    // 批量更新用户的yxid
    for (const record of scanRecords) {
      if (record.yx_id && record.student_id) {
        await this.model('student')
          .where({
            id: record.student_id,
            del_flag: 0
          })
          .update({
            yxid: record.yx_id
          });
        
        updatedCount++;
        
        think.logger.info(`更新用户yxid: 学生ID=${record.student_id}, openid=${record.openid}, yxid=${record.yx_id}`);
      }
    }

    return this.json({
      code: 200,
      message: `成功更新${updatedCount}条用户记录`,
      data: {
        updated: updatedCount,
        total: scanRecords.length
      }
    });

  } catch (error) {
    think.logger.error('更新用户yxid失败:', error);
    return this.json({
      code: 500,
      message: '更新用户yxid失败: ' + error.message,
      data: {
        updated: 0
      }
    });
  }
}


}