'use strict';
export default class extends think.Controller  {
    async loginAction() {
        return this.display();
    }
    async logoutAction() {
        //退出登录
        let is_login = await
            this.islogin();
        if (is_login) {
            await this.session('regInfo', null);
            this.redirect('/project/public/signin');
        } else {
            this.redirect('/project/public/signin');
        }
    }
    async regAction(){
        let model3 = this.model("admin/office");
        let offices = await model3.where({ "del_flag": 0, "parent_id": 1 }).select()
        this.assign("offices",offices);
        return this.display();


        this.display();
    }
    async islogin() {
        let user = await
            this.session('regInfo');
        let res = think.isEmpty(user) ? false : true;
        return res;
    }
    
    async dologinAction() {

  

        let model = this.model("admin/reginfo");
    
        let data = await model.where({phone: this.post('phone'),password:this.post('pass'),'del_flag':0}).find();

        if(think.isEmpty(data)){

          

            this.json({state:"error",msg:'手机号或密码错误'});
            
        }else{
            let token=think.uuid();
            await model.where({"id":data.id}).update({token:token});
            data.token=token;
            await this.session('regInfo', data);

            let logmodel=this.model("admin/loginlog");
            let logdata={};
            logdata.id=think.uuid();
            logdata.create_date=think.datetime();
            logdata.no=data.phone;
            logdata.ip=this.ip();
            await logmodel.add(logdata);

            this.json({state:"success",message:''});
        }

        

    }



    async checkphoneAction(){

        let phone=this.post("phone");
        let model=this.model("admin/reginfo");
        let res1=await model.where({"phone":phone,"del_flag":0}).select();

        if(think.isEmpty(res1)){
            this.json({state:false});
        }else{
            this.json({state:false});
        }

    }

    async sendsmsAction(){


        let model3 = this.model("admin/reginfo");
        let res3=await model3.where({"phone":this.post("phone")}).find();

        if(!think.isEmpty(res3)){

           return this.json({"state":"error","msg":"该手机号已注册"});
        }


        let model=this.model("admin/sms");
        let tel=this.post("phone");
        console.log(this.post())

        let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 5 MINUTE) and mobile =%s";

        

        let sqls = model.parseSql(sql, tel);

      

        let res= await model.query(sqls);
        console.log(res);

        if(res.length==0){

            var charactors="1234567890";

            var value='',i,j;
            
            for(j=1;j<=4;j++){
            
            i = parseInt(10*Math.random()); 　
            
            value = value + charactors.charAt(i);
            
            }
    
         
          await  model.add({"mobile":tel,"code":value,create_date:think.datetime()});
    
            var request = require('request');
            request.post({url:'https://dfsns.market.alicloudapi.com/data/send_sms', form:{
                phone_number:tel,
                content:"code:"+value+"",
                template_id:"TPL_09299"
    
    
            },
            headers: {
                "Authorization": "APPCODE 3aa153d853e14c8db5325543b07dfbd7",
            },
        }, function(error, response, body) {
            if (!error && response.statusCode == 200) {
                console.log(body)
    
                let res=JSON.parse(body);
                
            }
    
            });
    


        }
        return this.json({"state":"success","msg":"已发送"});
    }





    async sendsmssAction(){


        let model3 = this.model("admin/reginfo");
        let res3=await model3.where({"phone":this.post("phone")}).find();

        if(think.isEmpty(res3)){

           return this.json({"state":"error","msg":"系统未查询到该手机号的注册用户"});
        }


        let model=this.model("admin/sms");
        let tel=this.post("phone");
        console.log(this.post())

        let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 5 MINUTE) and mobile =%s";

        

        let sqls = model.parseSql(sql, tel);

      

        let res= await model.query(sqls);
        console.log(res);

        if(res.length==0){

            var charactors="1234567890";

            var value='',i,j;
            
            for(j=1;j<=4;j++){
            
            i = parseInt(10*Math.random()); 　
            
            value = value + charactors.charAt(i);
            
            }
    
         
          await  model.add({"mobile":tel,"code":value,create_date:think.datetime()});
    
            var request = require('request');
            request.post({url:'https://dfsns.market.alicloudapi.com/data/send_sms', form:{
                phone_number:tel,
                content:"code:"+value+"",
                template_id:"TPL_09299"
    
    
            },
            headers: {
                "Authorization": "APPCODE 3aa153d853e14c8db5325543b07dfbd7",
            },
        }, function(error, response, body) {
            if (!error && response.statusCode == 200) {
                console.log(body)
    
                let res=JSON.parse(body);
                
            }
    
            });
    


        }
        return this.json({"state":"success","msg":"已发送"});
    }

    async getqyinfoAction(){

        let code=this.get("no");
        let mode=this.model("admin/qyinfo");
        let res=await mode.where({"code":code}).find();
        this.json(res);
    }

    async logoutAction(){
        //退出登录
      
        await this.session('regInfo', null);

        
        return this.redirect('/tsb/public/login');
    }
    async doregAction(){


        let model3 = this.model("admin/reginfo");
        let res3=await model3.where({"phone":this.post("phone")}).find();

        if(!think.isEmpty(res3)){

           return this.json({"state":"error","msg":"该手机号已注册"});
        }


        let smscode=this.post("code");

        let model2=this.model("admin/sms");


        let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 5 MINUTE) and mobile =%s  and code =%s";

        

        let sqls = model2.parseSql(sql, this.post("phone"),smscode);


        


      

        let res= await model2.query(sqls);

        if(res.length>0){

            let data={};
            let model=this.model("admin/reginfo");
         
            data.phone=this.post("phone");
            let pass=this.post("pass");
            let repass=this.post("repass");
            if(think.isEmpty(pass)||think.isEmpty(repass)||pass!=repass){
                this.json({"state":"error","msg":"密码格式错误"});
            }else{
                data.password=this.post("repass");
    
                data.id=think.uuid();
                data.create_date=think.datetime();
        
                await model.add(data);
        
        
                this.json({"state":"success"});
            }
           

        }else{
            this.json({"state":"error","msg":"您输入的短信验证码错误，请检查输入！"});

        }

        
    }


    async checknameAction(){


    }


    async forgetAction(){

        this.display();
    }

    

    async checkmobileAction(){
        
        let model = this.model("admin/reginfo");
        let res=await model.where({"no":this.get("no"),"tel":this.get("tel")}).find();

        if(think.isEmpty(res)){

            this.json({"state":0});
        }else{
            this.json({"state":1});

        }


    }




    async dorepassAction(){


        let model3 = this.model("admin/reginfo");
        let res3=await model3.where({"phone":this.post("phone")}).find();

        if(think.isEmpty(res3)){

           return this.json({"state":"error","msg":"系统未查询到该手机号的注册用户"});
        }


        let smscode=this.post("code");

        let model2=this.model("admin/sms");


        let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 3 MINUTE) and mobile =%s  and code =%s";

        

        let sqls = model2.parseSql(sql, this.post("phone"),smscode);

      

        let res= await model2.query(sqls);

        if(res.length>0){





            let data={};
            let model=this.model("admin/reginfo");
            
           
        

            data.phone=this.post("phone");
       
            data.del_flag='0';
    
           
    
            await model.where(data).update({"password":this.post("pass")})
    
    
            this.json({"state":"success"});

        }else{
            this.json({"state":"error","msg":"您输入的验证码错误，请检查输入！"});

        }

        
    }


}