
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {


    async seeAction(){
            let model=this.model("tk_record");
          const user = await this.session('userInfo');
          let res=await this.model("tk_record").alias("t").join("sys_tk tk on tk.id=t.tkid")
          .where({"t.userid":user.id,"t.ifsee":0,"t.ifcheck":1}).field("t.*,tk.no,tk.type").select();

        
         
         // await model.where({"userid":user.id,"ifsee":0,ifcheck:1}).update({ifsee: 1});

          let data={};
          data.count=res.length;
          data.data=res;
          return this.json(data);




        
    }
    
    
    async seeinfoAction(){
            let id=this.post("id")
         let model=this.model("tk_record");
          const user = await this.session('userInfo');
          let res=await this.model("tk_record").alias("t").join("sys_tk tk on tk.id=t.tkid")
          .where({"t.id":id}).field("t.*,tk.jx_p,tk.tm_p").select();

        
         
         // await model.where({"userid":user.id,"ifsee":0,ifcheck:1}).update({ifsee: 1});

          let data={};
          data.count=res.length;
          data.data=res;
          return this.json(data);

        
        
    }
    
    
    async okseeAction(){
        let id=this.post("id");
        const user = await this.session('userInfo');
        let model=this.model("tk_record");
         await model.where({"userid":user.id,"ifsee":0,ifcheck:1,"id":id}).update({ifsee: 1});
        return this.json("ok")
    }

    //做题总数
     async indexnumAction(){
        let model=this.model("tk_record");
        
        const user = await this.session('userInfo');
        let res=await model.where({"userid":user.id}).count();


        // let sql="SELECT COUNT(DISTINCT create_date) as num FROM sys_userdaysinfo  WHERE uid = "+user.id;

        // let num2=0;
        // let res2=await model.query(sql);
        // if(!think.isEmpty(res2)){
        //     num2=res2[0]['num']


        // }

        let dayrecord=this.model("dayrecord");
        let count=await dayrecord.where({uid:user.id}).count();

        let count3=await this.model("tk_record").where({"userid":user.id,"ifsee":0,"ifcheck":1}).count();


        let data={};

 

        data.count1=res;
        data.count2=count;
        data.count3=count3;
        return this.json(data);


     }



     async lessonscoreAction(){
        let model2=this.model("userdaysinfo");
        const user = await this.session('userInfo');
        let res=await model2.where({"uid":user.id,"create_date":think.datetime("YYYY-MM-DD")}).select();
   

        for(let i=0;i<res.length;i++){
          let tmp=await this.model("vip").where({"userid":user.id,"lessson":res[i].lessonid}).order("enddate desc").find();
          if(tmp){
            if(tmp.enddate&&tmp.enddate>think.datetime("YYYY-MM-DD")  ){
              res[i].enddate=think.datetime(tmp.enddate,"YYYY-MM-DD");
            }else{
              res[i].enddate="已过期";
            }
            

          }else{
            res[i].enddate="未购买";
          }


        }
      


        return this.json(res);
    
     }


     async lessoninfoAction(){
        let model=this.model("lesson");
        let id=this.post("id");
        let lesson=await model.where({"id":id}).find();
            
         

          const user = await this.session('userInfo');
        let model2 = this.model("userdaysinfo");
        
        // Get the last 15 days dates
        let dates = [];
        for (let i = 0; i < 15; i++) {
            let date = think.datetime(new Date(Date.now() - i * 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
            dates.push(date);
        }
        
        // Get existing records
        let res = await model2.where({
            uid: user.id,
            lessonid:lesson.id,
            create_date: ['IN', dates]
        }).select();
        
        // Create a map of existing scores
        let scoreMap = {};
        res.forEach(record => {
            scoreMap[record.create_date] = record;
        });
        
        // Insert missing dates into database
        let insertData = [];
        dates.forEach(date => {
            if (!scoreMap[date]) {
                insertData.push({
                    uid: user.id,
                    create_date: date,
                    score: 0,
                    lessonid:lesson.id,
                    lessonname:lesson.name,
                    create_time: think.datetime()
                   
                });
            }
        });
        
        if (insertData.length > 0) {
            // Batch insert missing records
            await model2.addMany(insertData);
            
            // Get complete updated records
            res = await model2.where({
                uid: user.id,
                lessonid:lesson.id,
                create_date: ['IN', dates]
            }).select();
        }

        lesson.history=res;
        let tree=null;

      
            const userId = user.id;
            const lessonId = lesson.id; // 可选，不提供则返回所有根科目的树
       
      

              // 获取指定科目的成绩树
              tree = await this.getScoreTree(userId, lessonId);
             
        
              lesson.tree=tree;
              console.log(tree);









        return this.json(lesson);
     }

      
  async calculateUserAllScores(userId) {
    // 获取所有根科目（parent_id = 0）
    const rootLessons = await this.model('lesson')
      .where({
        parent_id: 0,
        del_flag: '0'
      })
      .select();

    const results = [];
    
    // 计算每个根科目的成绩
    for (const rootLesson of rootLessons) {
      const score = await this.calculateSubjectScore(userId, rootLesson.id);
      results.push({
        lessonId: rootLesson.id,
        name: rootLesson.name,
        score: Math.round(score)
      });
    }

    return results;
  }

  /**
   * 递归计算科目成绩
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<number>} 返回计算后的成绩
   */
  async calculateSubjectScore(userId, lessonId) {
    // 获取当前科目的所有子科目
    const children = await this.model('lesson')
      .where({
        parent_id: lessonId,
        del_flag: '0'
      })
      .select();

    // 如果没有子科目，说明是最底层科目，直接从 lesson_user 表获取成绩
    if (think.isEmpty(children)) {
      const userScore = await this.model('lesson_user')
        .where({
          userid: userId,
          lessonid: lessonId
        })
        .field('score')
        .find();
      
      return userScore.score || 50; // 如果没有成绩记录，返回默认分数50
    }

    // 如果有子科目，递归计算所有子科目的成绩
    let totalScore = 0;
    let validChildren = 0;

    for (const child of children) {
      const childScore = await this.calculateSubjectScore(userId, child.id);
      totalScore += childScore;
      validChildren++;
    }

    // 返回子科目的平均分
    return validChildren > 0 ? totalScore / validChildren : 50;
  }

  /**
   * 获取完整的科目成绩树
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<Object>} 返回包含成绩的科目树
   */
  async getScoreTree(userId, lessonId) {
    // 获取当前科目信息
    const lesson = await this.model('lesson')
      .where({
        id: lessonId,
        del_flag: '0'
      })
      .find();

    if (think.isEmpty(lesson)) {
      return null;
    }

    // 计算当前科目成绩
    const score = await this.calculateSubjectScore(userId, lessonId);

    // 获取子科目
    const children = await this.model('lesson')
      .where({
        parent_id: lessonId,
        del_flag: '0'
      })
      .select();

    const result = {
      id: lesson.id,
      name: lesson.name,
      expanded: false,
      score: Math.round(score),
      children: []
    };

    // 递归获取子科目的成绩树
    if (!think.isEmpty(children)) {
      for (const child of children) {
        const childTree = await this.getScoreTree(userId, child.id);
        if (childTree) {
          result.children.push(childTree);
        }
      }
    }

 

    return result;
  }

  /**
   * 获取成绩树
   */
  async treeAction() {
    try {
      const userId = 3;
      const lessonId = this.post('lessonId'); // 可选，不提供则返回所有根科目的树

      if (!userId) {
        return this.fail('请提供用户ID');
      }

      if (lessonId) {
        // 获取指定科目的成绩树
        const tree = await this.getScoreTree(userId, lessonId);
        return this.success(tree);
      } else {
        // 获取所有根科目的成绩树
        const rootLessons = await this.model('lesson')
          .where({
            parent_id: '0',
            del_flag: '0'
          })
          .select();

        const forest = [];
        for (const root of rootLessons) {
          const tree = await this.getScoreTree(userId, root.id);
          if (tree) {
            forest.push(tree);
          }
        }

       // console.log(forest);
        return this.success(forest);
      }
    } catch (err) {
      think.logger.error('Score tree calculation error:', err);
      return this.fail(err.message);
    }
  }

}