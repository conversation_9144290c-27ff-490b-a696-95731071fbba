<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="广告名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入广告名称" clearable></el-input>
					</el-form-item>
				</el-col>

                <el-col :span="12">
					<el-form-item label="广告状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio :label="1">启用</el-radio>
							<el-radio :label="0">禁用</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				
				<el-col :span="24">
					<el-form-item label="广告图片" prop="image">
						<!-- 替换为sc-upload组件 -->
						<sc-upload v-model="form.image" title="广告图片" :cropper="true" :compress="1" 
                                   :aspectRatio="16 / 9" :apiObj="uploadApi"></sc-upload>
						<div class="el-form-item-msg">建议上传16:9比例的图片，最佳尺寸为1200x675像素</div>
					</el-form-item>
				</el-col>
				
				<el-col :span="24">
					<el-form-item label="广告内容" prop="content">
						<el-input type="textarea" v-model="form.content" rows="4" placeholder="请输入广告内容描述"></el-input>
					</el-form-item>
				</el-col>
				
				<el-col :span="12">
					<el-form-item label="允许点击" prop="clickable">
						<el-switch v-model="form.clickable" active-text="是" inactive-text="否"></el-switch>
					</el-form-item>
				</el-col>
				
				<el-col :span="24">
					<el-form-item label="外链地址" prop="link_url" :disabled="!form.clickable">
						<el-input v-model="form.link_url" placeholder="请输入外链地址" :disabled="!form.clickable" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增广告',
				edit: '编辑广告',
				show: '查看广告'
			},
			visible: false,
			isSaveing: false,
			
			// 图片上传API
			uploadApi: this.$API.advertisement.upload,
			
			// 表单数据
			form: {
				name: "",
				status: 1,
				content: "",
				image: "",
				clickable: false,
				link_url: ""
			},
			
			// 验证规则
			rules: {
				name: [
					{required: true, message: '请输入广告名称', trigger: 'blur'},
					{min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}
				],
				content: [
					{required: true, message: '请输入广告内容', trigger: 'blur'}
				],
				image: [
					{required: true, message: '请上传广告图片', trigger: 'change'}
				],
				link_url: [
					{
						required: true, 
						message: '请输入外链地址', 
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (this.form.clickable && !value) {
								callback(new Error('允许点击时外链地址不能为空'));
							} else {
								callback();
							}
						}
					},
					{
						validator: (rule, value, callback) => {
							if (this.form.clickable && value && !/^https?:\/\//.test(value)) {
								callback(new Error('外链地址必须以http://或https://开头'));
							} else {
								callback();
							}
						},
						trigger: 'blur'
					}
				]
			}
		}
	},
	watch: {
		// 监听clickable变化，自动验证link_url
		'form.clickable'(val) {
			if (this.$refs.dialogForm) {
				this.$nextTick(() => {
					this.$refs.dialogForm.validateField('link_url');
				});
			}
		}
	},
	methods: {
		// 显示对话框
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			this.resetForm();
			return this;
		},
		
		// 表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					
					try {
						// 提交表单
						const res = await this.$API.advertisement.save.post(this.form);
						
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode);
							this.visible = false;
							this.$message.success("操作成功");
						} else {
							this.$alert(res.message, "提示", {type: 'error'});
						}
					} catch (error) {
						console.error('保存广告失败', error);
						this.$alert('保存广告失败', "提示", {type: 'error'});
					} finally {
						this.isSaveing = false;
					}
				} else {
					return false;
				}
			});
		},
		
		// 表单注入数据
		setData(data) {
			this.form = {
				id: data.id,
				name: data.name,
				status: data.status,
				content: data.content,
				image: data.image || "",
				clickable: data.clickable || false,
				link_url: data.link_url || "",
			};
			
			return this;
		},
		
		// 重置表单
		resetForm() {
			if (this.$refs.dialogForm) {
				this.$refs.dialogForm.resetFields();
			}
			this.form = {
				name: "",
				status: 1,
				content: "",
				image: "",
				clickable: false,
				link_url: ""
			};
		}
	}
}
</script>

<style scoped>
.el-form-item-msg {
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}
</style>