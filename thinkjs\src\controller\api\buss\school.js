const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {


  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const learnLogModel = this.model('school');
    where['p.del_flag'] = 0;
    
    //let dataScopeWhere = await this.dataScope('p');

    const response = await learnLogModel
      .alias('p')
      .field('p.*,u.name AS create_name,u2.name as mastername')
      .join('sys_user u ON p.`create_by`=u.`id`')
      .join('sys_user u2 ON p.`uid`=u2.`id`')
      
      .page(page, rows).where(where)
      .order('p.create_date desc').countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }

  async infoAction(){

    let respData = {};
    let id = this.post('id') ? this.post('id') : null;
    const userInfo = await this.session('userInfo');
    console.log("==================",id);
    if (id==0||think.isEmpty(id)) {
      console.log(userInfo);
        let school=await think.model('school').where({uid:userInfo.id}).find();
        console.log(school);
        id=school.id
    }


    console.log(id);

 



    
    const model = this.model('school');
    respData=   await model.where({id: id}).find();
    let usermodel = this.model("user");
    let master= await usermodel.where({"id": respData.uid}).field("id,name").find();
    respData.master=master;
    return this.json(respData);
  }


  


  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('school');
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const model = this.model('school');
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      await model.add(await this.addData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    } else {
      await model.where({id: id}).update(await this.updateData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('school');
    where['p.del_flag'] = 0;
    const response = await model
      .alias('p')
      .field('p.*')
      .where(where)
      .select();
    respData = {
      code: 200,
      data: response,
      message: ''
    };
    return this.json(respData);
  }


  async gzhAction() {
    let respData = {};
    const model = this.model('school');
    let id = this.post('id') ? this.post('id') : null;
    const userInfo = await this.session('userInfo');
    if (think.isEmpty(id)||id==0) {
        let school=await model.where({uid:userInfo.id}).find();
        id=school.id
    }


    await model.where({id: id}).update({"gzhconfig":JSON.stringify(this.post())});

        await think.config(id+'_wechat', JSON.stringify(this.post()));
    respData = {
      code: 200,
      data: {},
      message: '成功'
    };
    return this.json(respData);
  }

  async getwxpayAction(){
    let id=this.post("id")
    const model = this.model('school');

    const userInfo = await this.session('userInfo');
    if (think.isEmpty(id)||id==0) {
        let school=await model.where({uid:userInfo.id}).find();
        id=school.id
    }


    let res=await model.where({"id":id}).find();
    let data={};

    console.log(res);
    if(!think.isEmpty(res)){
      data=JSON.parse(res['wxpayconfig'])
    }


    
  let  respData = {
      code: 200,
      data: data,
      message: '成功'
    };

    return this.json(respData)

  }

  async wxpayAction() 
    {
      try {
        const data = this.post();
        
        
        const userInfo = await this.session('userInfo');
        if (think.isEmpty(data.id)||data.id==0) {
            let school=await this.model('school').where({uid:userInfo.id}).find();
            data.id=school.id
        }
        
        // 验证必要参数
        if (!data.appid || !data.mch_id || !data.key || !data.notify_url) {
          return this.fail('请完善微信支付参数');
        }
        
        // 查询学校是否存在
        const school = await this.model('school').where({ id: data.id }).find();
        if (think.isEmpty(school)) {
          return this.fail('学校不存在');
        }
        
        // 将支付配置转为JSON字符串保存
        const payconfig = JSON.stringify({
          appid: data.appid,
          mch_id: data.mch_id,
          key: data.key,
          notify_url: data.notify_url,
          remarks: data.remarks || '',
          cert_path: data.cert_path || ''
        });

        think.config(data.id+'_wxpay', payconfig);
        console.log(data);
        console.log(payconfig);
        
        // 更新学校的支付配置
        await this.model('school').where({ id: data.id }).update({ "wxpayconfig": payconfig });
        
        return this.success(true, '保存成功');
      } catch (error) {
        think.logger.error('保存微信支付配置失败:', error);
        return this.fail('保存微信支付配置失败: ' + error.message);
      }
    }
  


  async savetimeAction() {
    let data=this.post();
    let model=this.model("timerecord");
    
    if(data.type=="增加"){
      await this.model("school").where({id:data.schoolid}).increment("canusetime",data.num);
    }else{
      await this.model("school").where({id:data.schoolid}).decrement("canusetime",data.num);
    }
    data.create_date=think.datetime();


    await model.add(data);
    return this.json({code:200,data:{},message:"成功"});




  }
  async timerecordAction() {

    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};

    const model = this.model('timerecord');
    

    const response = await model
    .alias('p')
    .field('p.*,s.name as schoolname')
    .join('buss_school s ON p.`schoolid`=s.`id`')
   
    
    .page(page, rows).where(where)
    .order('p.create_date desc').countSelect();

  respData = {
    code: 200,
    count: response.count,
    data: response.data,
    message: ''
  };
  return this.json(respData);
   
  }

  async listselectAction() {
    let order = 'create_date desc';
    

    const keyword = this.get('keyword');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('school');
    const where = {del_flag: 0};
    const userInfo = await this.session('userInfo');

    if(userInfo.name!="系统管理员"){
      where["uid"]=userInfo.id;
    }


    if (!think.isEmpty(keyword)) {
      where['name'] = ['like', '%' + keyword + '%'];
    }
    const res = await model.alias('c').where(where).order(order).select();


    return this.json(res);
  }


  async getschoolAction(){
    let respData = {};
    const model = this.model('school');
    let userInfo = await this.session('userInfo');
    const response = await model.alias('c').where({del_flag:0,uid:userInfo.id}).find();
  
    return this.json(response);
  }
 
};
