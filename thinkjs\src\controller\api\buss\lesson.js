const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {


  async removeAction() {
    let id = this.post("id");


  

    let model = this.model("lesson")
    let model2 = this.model("tk")



    let res=await model.where({parent_id:id,del_flag:0}).find();
    if(!think.isEmpty(res)){


      return this.json({"state": 0});
    }

    let res2=await model2.where({lessonid:id,del_flag:0}).find();
    if(!think.isEmpty(res2)){


      return this.json({"state": 0});
    }
    if(!think.isEmpty(id)){
      await model.where({"id": id}).update({"del_flag": 1});
    }
   

    return this.json({"state": 1});
  }


  async getbyidAction() {
    let id = this.post("id");

    let model = this.model("lesson")
    let res = await model.where({"id": id}).find();

    return this.json(res);


  }
  async listAction() {
    let where = this.post("where")?this.post("where"):null;

    let model = this.model("lesson")
    let res = await model.where(where).select();

    return this.json({
      code:200,
      data:res,
      msg: "Success"
    });


  }


  async saveAction() {
    let model = this.model("lesson");
    let data = this.post();
    // Object.assign(data,data.meta);


    data.parent_id = data.parent;

    data.ifgl = data.ifgl ? 1 : 0;
    let parent ={};
    if(!think.isEmpty(data.parent)){

      parent  = await model.where({"id": data.parent}).find();
    }else{
      data.parent_id=0;
      parent={id:0,parent_ids:0};
    }
  
    

    if (think.isEmpty(data.id)) {

      let id = await model.add(data);

      await model.where({"id": id}).update({"parent_ids": parent.parent_ids + "," + id});

    } else {
      let id = await model.where({"id": data.id}).update(data);
      await model.where({"id": data.id}).update({"parent_ids": parent.parent_ids + "," + data.id});
    }
    return this.json({state: 1})


  }

  async tree2Action() {

    let model = this.model("lesson");
    let usermodel = this.model("user");
    let where = this.get("where") ? JSON.parse(this.get("where")) : {}
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();

    let tree  = this.transTreeArray(data,'id','parent_id');

    this.json(tree);
  }

  async treeAction() {

    let model = this.model("lesson");
    let usermodel = this.model("user");
    let where = this.get("where") ? JSON.parse(this.get("where")) : {}
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();


    var tree = [];
    for (var t of data) {
      if (t.parent_id == '0') {
        let obj = {};
        obj.id = t.id;
        obj.sort = t.sort;
        obj.name = t.name;
        obj.title = t.name;
        obj.level = t.level;
        obj.num1=t.num1;
        obj.num2=t.num2;
        obj.people_number = t.people_number;
        obj.people_outside = t.people_outside;
        obj.code=t.code;
        obj.path = t.path;
        obj.ifgl = t.ifgl === 1;
        obj.component = t.component;
        let meta = {};
        meta.title = t.name;
        meta.icon = t.icon;
        meta.type = t.type;
        obj.type = t.type

        obj.meta = meta;

        await this.getchild(obj, data);
        tree.push(obj);

      }

    }
    this.json(tree);

  }


  async getchild(tree, menus) {
    tree.hasChildren = false;
    let usermodel = this.model("user");
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id) {
        let obj = {};
        obj.id = t.id;
       obj.code=t.code;
        obj.name = t.name;
        obj.title = t.name;
        obj.sort = t.sort;
        obj.num1=t.num1;
        obj.num2=t.num2;
        obj.people_number = t.people_number;
        obj.people_outside = t.people_outside;
        obj.level = t.level;
        obj.path = t.path;
        obj.type = t.type
        obj.ifgl = t.ifgl === 1;
        obj.component = t.component;
        let meta = {};
        meta.title = t.name;
        meta.icon = t.icon;
        meta.type = t.type;

        obj.meta = meta;
        await this.getchild(obj, menus);
        childs.push(obj);
      }
    }
    tree.children = childs;


  }

  async dictAction() {

  }






  async treebyschoolAction() {
    const userInfo = await this.session('userInfo');
    let model = this.model("lesson");
    let usermodel = this.model("user");
    let where = this.get("where") ? JSON.parse(this.get("where")) : {}

    let mode2=this.model("user_lesson");

    let resss=await mode2.where({"user_id":userInfo.id}).select();


    let arr=[];
    if(!think.isEmpty(resss)){

      for(let obj of resss){
        arr.push(obj.lession_id);
      }


  

    }


    where.del_flag = 0;
  
    let datescope=this.get("datescope");

    let sqlwhere=" DATE(create_date) = CURDATE()";

    if(datescope=="7d"){

      sqlwhere="  create_date >= CURRENT_DATE - INTERVAL 7 DAY";
    }

    if(datescope=="30d"){

      sqlwhere="  create_date >= CURRENT_DATE - INTERVAL 30 DAY";
    }

    let data = await model.alias("c").join("left join ( select sum(num2) as num2,sum(num3) as num3 ,lessonid from sys_lesson_school where schoolid="+userInfo.schoolid+" and "+sqlwhere+"  GROUP BY  lessonid) ls on ls.lessonid=c.id").where(where).order(["sort asc"]).field("c.*,ifnull(ls.num2,0) as errnum,ifnull(ls.num3,0) as jjnum").select();
    // for(var item of data){
   
    //   let jj=await this.model("jj").where({"tkid":item.id,"schoolid":userInfo.id}).where(sqlwhere).find();
    //   if(think.isEmpty(jj)){

    //     data['jjnum']=0;

    //   }else{
    //     data['jjnum']=jj.num
    //   }
     


    // }

    var tree = [];
    for (var t of data) {
      if (t.parent_id == '0') {
        console.log("=============",t.id);
        console.log("=============",arr);
        if (arr.includes( t.id)) {
          

          let obj = {};
          obj.id = t.id;
          obj.sort = t.sort;
          obj.name = t.name;
          obj.title = t.name+" 错："+t.errnum+" 讲"+t.jjnum;
          obj.level = t.level;
          obj.num1=t.num1;
          obj.num2=t.num2;
          obj.errnum=t.errnum;
          obj.people_number = t.people_number;
          obj.people_outside = t.people_outside;
          obj.code=t.code;
          obj.path = t.path;
          obj.ifgl = t.ifgl === 1;
          obj.component = t.component;
          let meta = {};
          meta.title = t.name;
          meta.icon = t.icon;
          meta.type = t.type;
          obj.type = t.type
  
          obj.meta = meta;
  
          await this.getchildbyschool(obj, data);
          tree.push(obj);


        }

        

      }

    }
    this.json(tree);

  }




  async getchildbyschool(tree, menus) {
    tree.hasChildren = false;
    let usermodel = this.model("user");
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id) {
        let obj = {};
        obj.id = t.id;
       obj.code=t.code;
        obj.name = t.name;
        obj.title =  t.name+" 错："+t.errnum+" 讲"+t.jjnum;
        obj.sort = t.sort;
        obj.num1=t.num1;
        obj.num2=t.num2;
        obj.errnum=t.errnum;
        obj.people_number = t.people_number;
        obj.people_outside = t.people_outside;
        obj.level = t.level;
        obj.path = t.path;
        obj.type = t.type
        obj.ifgl = t.ifgl === 1;
        obj.component = t.component;
        let meta = {};
        meta.title = t.name;
        meta.icon = t.icon;
        meta.type = t.type;

        obj.meta = meta;
        await this.getchildbyschool(obj, menus);
        childs.push(obj);
      }
    }
    tree.children = childs;


  }





async  getselectAction(){
  let model = this.model("lesson");

  let where = this.get("where") ? JSON.parse(this.get("where")) : {}

    where.type=2;
    where.id=[">",1];
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();

    return this.json(data);


}

  async  billselectAction(){
    let model = this.model("lesson");

    let where = this.post("where") ? this.post("where"): {}

    let data = await model.where(where).order(["sort asc"]).select();

    return this.json(data);


  }


  async  lessonselect2Action(){

    let tree=[];

    let nj=["小学","初中","高中"];
    let i=1;
    for(let item of nj){
      let res1=await this.model("lesson").where({"type":item,parent_id:0,"del_flag":0}).order(["sort asc"]).select();
    
      let one={}
      one.id=i;
      one.leaf=false;
      one.title=item;
      if(res1.length>0){
        one.hasChildren=true;
      }else{
        one.hasChildren=false;
      }
      one.children=[];
      for(let item2 of res1){
        let two={}
        two.id=item2.id;
        two.leaf=true;
        two.title=item2.name;
        two.hasChildren=false;
        one.children.push(two);
      }
      i++;
    
      tree.push(one);
    }
    
    
    
        return this.json(tree);
    
  }


  async lessonbynjAction(){
    let model = this.model("lesson");
    let where = this.post("where") ? this.post("where"): {}
    let data = await model.where(where).order(["sort asc"]).select();
    return this.json(data);
  }
}
