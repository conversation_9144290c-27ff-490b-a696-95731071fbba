const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {
  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const model = this.model('log');
    const response = await model.fieldReverse('body').page(page, rows).where(where).order('update_date desc').countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      msg: ''
    };
    return this.json(respData);
  }

  async infoAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('log');
    const response = await model.where(where).find();
    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }
};
