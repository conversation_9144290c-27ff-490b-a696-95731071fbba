module.exports = class extends think.Controller {
    
    
    async clsAction(){
        
        let model=this.model("lesson_user");
        await model.where({"userid":3}).delete();
        
        
        
        
            let model2=this.model("tk_user");
        await model2.where({"userid":3}).delete();
        
        
        let model3=this.model("tk_record");
        await model3.where({"userid":3}).delete();
        
    return this.json(1)
        
        
        
    }
    
    async indexAction(){
        
        let testti=await this.cache("testti");
        let zsti=await this.cache("zsti");
        
        this.assign("testti",testti);
        this.assign("zsti",zsti);
        console.log(zsti)
         let tkmodel = this.model("tk");
       


       
        
        for(var item of zsti){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
        
        let lessonid =await this.cache("lessonid");
        
        let userid=3
        let lmodel=this.model("lesson")
        let lesson=await lmodel.where({"id":lessonid}).find();
        
        this.assign("lesson",lesson);
        
      let curti=  await this.cache("curtino");
      
      let ti=await tkmodel.where({"id":curti}).find();
       this.assign("ti",ti);
       
        let model2 = this.model("lesson_user");
       
    
        
           let restmp = await model2.where({ "lessonid": lessonid, "userid":3 }).find();
           
           console.log("===============");
           console.log(restmp);
           console.log("===============");
        let score=restmp.score
        
         this.assign("score",score);
        
     
     //   let list1 = await this.getnodo(lessonid, 3, score, ", parseInt(config.tinum1))
        let where=""
       if(restmp.tisetup>=1 &&restmp.tisetup<=4){
           where="(type ='单选' or type='多选')";
           
       }
       
       if(restmp.tisetup>=5 &&restmp.tisetup<=6){
           where="(type ='填空' )";
           
       }
       
       
           if(restmp.tisetup==7){
           where="(type ='解答' )";
           
       }
       
       
 
        let config=this.config("ti")
        let score1 = parseInt(score) - parseInt(config.score1);
        let score2 = parseInt(score) + parseInt(config.score1);

        let sql = "SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid + " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY  times asc  " ;


        let model = this.model("tk");
        let list1 = await model.query(sql);
        
        
        this.assign("title1",'分值段1未出过：'+score1+"-"+score2 )
        
              
        for(var item of list1){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
        
        
        
          let sql2 = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql(list1)+" and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  " ;

            let list2 = await model.query(sql2);
            this.assign("title2",'分值段1 错题库里已到期的：'+score1+"-"+score2 )
            console.log("========", sql2);
             
        
        
        
    
         for(var item of list2){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
           this.assign("list2",list2);
        
          
        
         this.assign("title3",'分值段1 对题库里已到期的：'+score1+"-"+score2 )
           
            let sql3 = "SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql([...list1,...list2])+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY   times asc  " ;

            let list3 = await model.query(sql3);

            console.log("========", sql3);
        
        
        
        
        
        for(var item of list3){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
        
        
        
        
        
           this.assign("list3",list3);
           
           
           
           
           
           
           
        score1 = parseInt(score) - parseInt(config.score2);
        score2 = parseInt(score) + parseInt(config.score2);
           
         //分段二  
         let sql11 = "SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid + " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY  times asc  " ;


       
        let list11 = await model.query(sql11);
        
        
        this.assign("title11",'分值段2未出过：'+score1+"-"+score2 )   
           
    for(var item of list11){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
           
        this.assign("list11",list11 )   
           
        
        
        
        
        
        
        
           let sql12 = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql(list1)+" and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  " ;

            let list12 = await model.query(sql12);
            this.assign("title12",'分值段2 错题库里已到期的：'+score1+"-"+score2 )
            
             
        
        
        
    
         for(var item of list12){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
           this.assign("list12",list12);
        
        
        
            this.assign("title13",'分值段2 对题库里已到期的：'+score1+"-"+score2 )
           
            let sql13 = "SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql([...list1,...list2])+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY   times asc  " ;

            let list13 = await model.query(sql13);

            
        
        
        
        
        
        for(var item of list13){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
        
        
        
        
        
           this.assign("list13",list13);
        
        
        
        
        
        
        
        
           score1 = parseInt(score) - parseInt(config.score3);
        score2 = parseInt(score) + parseInt(config.score3);
           
         //分段二  
         let sql21 = "SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid + " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY  times asc  " ;


       
        let list21 = await model.query(sql21);
        
        
        this.assign("title21",'分值段3未出过：'+score1+"-"+score2 )   
           
    for(var item of list21){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
           
        this.assign("list21",list21 )   
           
        
        
        
        
        
        
        
           let sql22 = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql(list1)+" and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  " ;

            let list22 = await model.query(sql22);
            this.assign("title22",'分值段3 错题库里已到期的：'+score1+"-"+score2 )
            
             
        
        
        
    
         for(var item of list22){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
           this.assign("list22",list22);
        
        
        
         this.assign("title23",'分值段2 对题库里已到期的：'+score1+"-"+score2 )
           
            let sql23 = "SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + where + " and lessonid=" + lessonid +this.processinsql([...list1,...list2])+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY   times asc  " ;

            let list23 = await model.query(sql23);

            
        
        
        
        
        
        for(var item of list23){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
        
        
        
        
        
           this.assign("list23",list23);
        
        
        
        
        
        
        
        
        
        
        
        
           
           
           
           
           
           
            let sql33="select tk.*,u.yxq from sys_tk as tk join sys_tk_user u on u.tkid=tk.id  where u.state=1 and u.userid=3";
        
        let listbuke= await tkmodel.query(sql33);
        
        
          for(var item of listbuke){
            let tires = await tkmodel.where({ id: item.id }).find();
            item.times=tires.times;
            let sql='select flag ,count(*) as num from sys_tk_record where tkid="'+item.id+'" GROUP by flag';
            let model=this.model("tk_record");
            let record=await model.query(sql);
            
            for(var tmp of record){
                if(tmp.flag==1){
                    
                    item['rightnum']=tmp.num;
                }else{
                    
                      item['errornum']=tmp.num;
                }
                
                
            }
            
        }
        
       
        
        
        let  allti=await tkmodel.where({lessonid:lesson.id,del_flag:0}).order("score desc").select();
        
        
       this.assign("allti",allti);
        
         this.assign("bukelist",listbuke);
      
        
        this.assign("list1",list1);
       
   
        
        return this.display();
    }
    
        processinsql(tilist){
    let sql=" and id not in ( "

    let arr=[];
    for(let a of tilist){
        arr.push(a.id)

    }
    sql=sql+" "+ `'${arr.join("', '")}'`
    sql=sql+" )"
    return sql;


   }
    
     async getnodo(lessonid, userid, score, type, neednum) {


        let config=this.config("ti")
        let score1 = parseInt(score) - parseInt(config.score1);
        let score2 = parseInt(score) + parseInt(config.score1);

        let sql = "SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid + " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY  times asc  " ;


        let model = this.model("tk");
        let tilist = await model.query(sql);
        console.log("===============", sql)
        //获取错题库
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            let sql2 = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+" and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  " ;

            let tilist2 = await model.query(sql2);

            console.log("========", sql2);
            tilist = tilist.concat(tilist2);
        }

        //获取对题库
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            let sql3 = "SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY   times asc  " ;

            let tilist3 = await model.query(sql3);

            console.log("========", sql3);
            tilist = tilist.concat(tilist3);
        }


        //二段分值

        score1 = parseInt(score) - parseInt(config.score2);
        score2 = parseInt(score) + parseInt(config.score2);


        //未做题
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY   times asc  " ;
          let  tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }




        //错题库

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  " ;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY  times asc  " ;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }



        //三段分值

        score1 = parseInt(score) - parseInt(config.score3);
        score2 = parseInt(score) + parseInt(config.score3);


        //未做题
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY   times asc  " ;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }




        //错题库

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc  ";;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY  times asc  " ;;
            let    tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }
        
        
        


        return tilist;



    }

    
    
 
    
    
}