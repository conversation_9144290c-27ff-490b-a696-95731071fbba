import config from "@/config";
import http from "@/utils/request";

const wtfk = {

	page: {
		url: `${config.API_URL}/buss/wtfk/page`,
		name: "数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

    delete: {
		url: `${config.API_URL}/buss/wtfk/delete`,
		name: "删除",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}

};

export default wtfk;
