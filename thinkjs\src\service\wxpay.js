const crypto = require('crypto');
const axios = require('axios');
const xml2js = require('xml2js');

module.exports = class extends think.Service {
  constructor() {
    super();
    // 微信支付配置
    this.config = {
      appid: think.config('wxpay.appid'), // 微信公众号或小程序AppID
      mch_id: think.config('wxpay.mch_id'), // 商户号
      key: think.config('wxpay.key'), // API密钥
      notify_url: think.config('wxpay.notify_url'), // 支付结果通知地址
      trade_type: 'JSAPI', // 交易类型，小程序或公众号支付为JSAPI
      body: '会员购买', // 商品描述
      spbill_create_ip: '127.0.0.1' // 终端IP，可以在Controller中动态传入
    };
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} - 随机字符串
   */
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成签名
   * @param {object} params - 参数对象
   * @returns {string} - 签名
   */
  generateSign(params) {
    // 1. 对参数按照key=value的格式，并按照参数名ASCII字典序排序
    const sortedParams = Object.keys(params).sort().reduce((result, key) => {
      if (params[key] !== undefined && params[key] !== '' && key !== 'sign') {
        result.push(`${key}=${params[key]}`);
      }
      return result;
    }, []);

    // 2. 拼接API密钥
    sortedParams.push(`key=${this.config.key}`);
    const stringSignTemp = sortedParams.join('&');

    // 3. MD5加密并转为大写
    return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
  }

  /**
   * 将对象转换为XML
   * @param {object} obj - 对象
   * @returns {string} - XML字符串
   */
  objectToXml(obj) {
    let xml = '<xml>';
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        xml += `<${key}>${obj[key]}</${key}>`;
      }
    }
    xml += '</xml>';
    return xml;
  }

  /**
   * 将XML转换为对象
   * @param {string} xml - XML字符串
   * @returns {Promise<object>} - 对象
   */
  async xmlToObject(xml) {
    return new Promise((resolve, reject) => {
      xml2js.parseString(xml, { explicitArray: false, trim: true }, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result.xml);
        }
      });
    });
  }

  /**
   * 统一下单
   * @param {object} orderData - 订单数据
   * @returns {Promise<object>} - 下单结果
   */
  async unifiedOrder(orderData) {
    try {

      let schoolid=orderData.schoolid;
      let wxpayconfig=think.config(schoolid+'_wxpay');

      // 1. 构建请求参数
      const params = {
        appid: wxpayconfig.appid,
        mch_id: wxpayconfig.mch_id,
        nonce_str: this.generateNonceStr(),
        body: orderData.goodsname,
        out_trade_no: orderData.orderNo,
        total_fee: Math.round(orderData.totalAmount * 100), // 金额转为分
        spbill_create_ip: orderData.ip,
        notify_url: wxpayconfig.notify_url,
        trade_type: "JSAPI",
        openid: orderData.openid // 用户的openid，JSAPI支付必须传
      };

      console.log("params=============",params);

      // 2. 生成签名
      params.sign = this.generateSign(params);
      console.log("params.sign=============",params.sign);


      // 3. 将参数转换为XML
      const xmlData = this.objectToXml(params);
      console.log("xmlData=============",xmlData);


      // 4. 发送请求
      const response = await axios.post('https://api.mch.weixin.qq.com/pay/unifiedorder', xmlData, {
        headers: { 'Content-Type': 'text/xml' }
      });

      // 5. 解析响应
      const result = await this.xmlToObject(response.data);

      // 6. 验证结果
      if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
        // 7. 返回支付参数
        return {
          appId: this.config.appid,
          timeStamp: Math.floor(Date.now() / 1000).toString(),
          nonceStr: result.nonce_str,
          package: `prepay_id=${result.prepay_id}`,
          signType: 'MD5',
          paySign: '',
          prepay_id: result.prepay_id
        };
      } else {
        throw new Error(result.return_msg || result.err_code_des || '下单失败');
      }
    } catch (error) {
      think.logger.error('微信支付统一下单失败', error);
      throw error;
    }
  }

  /**
   * 生成JSAPI支付参数
   * @param {object} orderData - 订单数据
   * @returns {Promise<object>} - 支付参数
   */
  async createPayParams(orderData) {
    try {
      // 1. 统一下单
      const payParams = await this.unifiedOrder(orderData);

      // 2. 生成支付签名
      const signParams = {
        appId: payParams.appId,
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType
      };
      payParams.paySign = this.generateSign(signParams);

      // 3. 返回支付参数
      return payParams;
    } catch (error) {
      think.logger.error('生成支付参数失败', error);
      throw error;
    }
  }

  /**
   * 查询订单
   * @param {string} orderNo - 订单号
   * @returns {Promise<object>} - 查询结果
   */
  async queryOrder(orderNo) {
    try {
      // 1. 构建请求参数
      const params = {
        appid: this.config.appid,
        mch_id: this.config.mch_id,
        out_trade_no: orderNo,
        nonce_str: this.generateNonceStr()
      };

      // 2. 生成签名
      params.sign = this.generateSign(params);

      // 3. 将参数转换为XML
      const xmlData = this.objectToXml(params);

      // 4. 发送请求
      const response = await axios.post('https://api.mch.weixin.qq.com/pay/orderquery', xmlData, {
        headers: { 'Content-Type': 'text/xml' }
      });

      // 5. 解析响应
      const result = await this.xmlToObject(response.data);

      // 6. 返回结果
      return result;
    } catch (error) {
      think.logger.error('查询订单失败', error);
      throw error;
    }
  }

  /**
   * 关闭订单
   * @param {string} orderNo - 订单号
   * @returns {Promise<object>} - 关闭结果
   */
  async closeOrder(orderNo) {
    try {
      // 1. 构建请求参数
      const params = {
        appid: this.config.appid,
        mch_id: this.config.mch_id,
        out_trade_no: orderNo,
        nonce_str: this.generateNonceStr()
      };

      // 2. 生成签名
      params.sign = this.generateSign(params);

      // 3. 将参数转换为XML
      const xmlData = this.objectToXml(params);

      // 4. 发送请求
      const response = await axios.post('https://api.mch.weixin.qq.com/pay/closeorder', xmlData, {
        headers: { 'Content-Type': 'text/xml' }
      });

      // 5. 解析响应
      const result = await this.xmlToObject(response.data);

      // 6. 返回结果
      return result;
    } catch (error) {
      think.logger.error('关闭订单失败', error);
      throw error;
    }
  }

  /**
   * 申请退款
   * @param {object} refundData - 退款数据
   * @returns {Promise<object>} - 退款结果
   */
  async refund(refundData) {
    try {
      let schoolid = refundData.schoolid;
      let wxpayconfig = think.config(schoolid + '_wxpay');
      
      if (typeof wxpayconfig === 'string') {
        wxpayconfig = JSON.parse(wxpayconfig);
      }
      
      console.log('退款数据:', refundData);
      
      // 读取证书文件
      const fs = require('fs');
      const certFile = think.ROOT_PATH + '/www' + wxpayconfig.cert_path;
      
      if (!fs.existsSync(certFile)) {
        return {
          code: 500,
          message: '证书文件不存在: ' + certFile,
          data: null
        };
      }
      
      // 构建请求参数
      const params = {
        appid: wxpayconfig.appid,
        mch_id: wxpayconfig.mch_id,
        nonce_str: this.generateNonceStr(),
        out_trade_no: refundData.orderNo,
        out_refund_no: 'RF' + Date.now(), // 使用时间戳生成唯一退款单号
        total_fee: Math.round(refundData.totalAmount * 100),
        refund_fee: Math.round(refundData.refundAmount * 100),
        refund_desc: refundData.refundReason || '用户申请退款'
      };
      
      // 生成签名
      params.sign = this.generateSign(params);
      
      // 将参数转换为XML
      const xmlData = this.objectToXml(params);
      
      // 使用 Node.js 内置的 HTTPS 模块发送请求
      return new Promise((resolve, reject) => {
        const https = require('https');
        const certContent = fs.readFileSync(certFile);
        
        const options = {
          hostname: 'api.mch.weixin.qq.com',
          port: 443,
          path: '/secapi/pay/refund',
          method: 'POST',
          pfx: certContent,
          passphrase: wxpayconfig.mch_id,
          headers: {
            'Content-Type': 'text/xml',
            'Content-Length': Buffer.byteLength(xmlData)
          }
        };
        
        const req = https.request(options, (res) => {
          let data = '';
          
          res.on('data', (chunk) => {
            data += chunk;
          });
          
          res.on('end', async () => {
            try {
              const result = await this.xmlToObject(data);
              console.log('退款响应:', result);
              
              if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
                resolve({
                  code: 200,
                  message: '退款成功',
                  data: result
                });
              } else {
                resolve({
                  code: 500,
                  message: result.err_code_des || result.return_msg || '退款失败',
                  data: result
                });
              }
            } catch (error) {
              console.error('解析响应失败:', error);
              resolve({
                code: 500,
                message: '解析响应失败: ' + error.message,
                data: null
              });
            }
          });
        });
        
        req.on('error', (error) => {
          console.error('请求失败:', error);
          resolve({
            code: 500,
            message: '请求失败: ' + error.message,
            data: null
          });
        });
        
        req.write(xmlData);
        req.end();
      });
      
    } catch (error) {
      console.error('申请退款失败:', error);
      return {
        code: 500,
        message: error.message || '退款处理异常',
        data: null
      };
    }
  }

  /**
   * 验证支付结果通知
   * @param {string} xmlData - 通知XML数据
   * @returns {Promise<object>} - 验证结果
   */
  async verifyNotify(xmlData) {
    try {
      // 1. 解析XML数据
      const result = await this.xmlToObject(xmlData);

      // 2. 验证签名
      const sign = result.sign;
      delete result.sign;
      const calculatedSign = this.generateSign(result);

      // 3. 验证结果
      if (sign === calculatedSign) {
        return {
          verified: true,
          data: result
        };
      } else {
        return {
          verified: false,
          message: '签名验证失败'
        };
      }
    } catch (error) {
      think.logger.error('验证支付通知失败', error);
      throw error;
    }
  }


  //退款
  
}
