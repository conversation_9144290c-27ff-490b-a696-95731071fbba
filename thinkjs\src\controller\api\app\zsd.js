
const BaseRest = require('./rest.js');
const path = require('path');
module.exports = class extends BaseRest {

    async listAction(){
        let model=this.model("zsd")
        let lessonid=this.post("lessonid")
        const page = this.post('page') ? this.post('page') : 1;
        const rows = this.post('pageSize') ? this.post('pageSize') : 20;

        let res=await model.where({del_flag:"0",lessonid:lessonid}).page(page, rows)
        .order("create_date desc")
        .countSelect();
        let data={}
        data.code = 200;
        data.count = res.count;
        data.data = res.data;
        data.msg = '';


  
 
    return this.json(data);
     
     


    }


    async infoAction(){
        let model=this.model("zsd")
        let id=this.post("id")
        let res=await model.where({id:id}).find();

  // 检查是否有 m3u8 版本
  if (res.video && res.video.trim()) {
    try {
      // 获取原始 MP4 文件路径信息
      const videoPath = res.video;
      
      // 提取文件名（不含扩展名）
      const basename = path.basename(videoPath, path.extname(videoPath));
      
      // 构建 HLS 目录路径
      const dirPath = path.join(path.dirname(videoPath), 'zsd', basename);
      const absoluteDirPath = path.join(think.ROOT_PATH, 'www', dirPath);
      
      // 构建 m3u8 文件路径
      const m3u8Path = path.join(dirPath, 'index.m3u8');
      const absoluteM3u8Path = path.join(absoluteDirPath, 'index.m3u8');
      
      // 检查目录和 m3u8 文件是否存在
      const dirExists = await think.isDirectory(absoluteDirPath);
      const fileExists = await think.isFile(absoluteM3u8Path);
      
      if (dirExists && fileExists) {
        // 将相对路径添加到响应中
        res.videoM3u8 = '/' + m3u8Path.replace(/\\/g, '/');
        
        console.log('找到 HLS 版本:', res.videoM3u8);
      } else {
        console.log('未找到 HLS 版本，目录:', absoluteDirPath, '文件:', absoluteM3u8Path);
      }
    } catch (error) {
      console.error('检查 HLS 版本时出错:', error);
    }
  }
  



        return this.json(res);


    }



}