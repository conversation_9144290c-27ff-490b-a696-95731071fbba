<template>
	<el-dialog title="公众号参数设置" v-model="visible" :width="800" destroy-on-close @closed="$emit('closed')">
		<el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="top" v-loading="loading">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="公众号APPID" prop="appid">
						<el-input v-model="form.appid" placeholder="请输入公众号APPID" clearable maxlength="50" show-word-limit></el-input>
						<div class="el-form-item-msg">在微信公众平台-开发-基本配置中获取</div>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="AppSecret" prop="secret">
						<el-input v-model="form.secret" type="password" placeholder="请输入AppSecret" clearable maxlength="100" show-password></el-input>
						<div class="el-form-item-msg">在微信公众平台-开发-基本配置中获取，请妥善保管</div>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="Token令牌" prop="token">
						<el-input v-model="form.token" placeholder="请输入Token令牌" clearable maxlength="32"></el-input>
						<div class="el-form-item-msg">用于验证消息的令牌，由英文或数字组成，长度为3-32字符</div>
					</el-form-item>
				</el-col>
<el-col :span="12">
						<el-form-item label="消息加密密钥">
							<el-input v-model="form.encodingAESKey" placeholder="请输入EncodingAESKey（可选）" clearable maxlength="43"></el-input>
							<div class="el-form-item-msg">消息加密密钥，43位字符组成，可选配置</div>
						</el-form-item>
					</el-col>

			
				<el-col :span="24">
					<el-form-item label="服务器地址(URL)">
						<el-input :value="serverUrl" readonly>
							<template #append>
								<el-button @click="copyToClipboard(serverUrl)">
									<el-icon><el-icon-copy-document /></el-icon>
									复制
								</el-button>
							</template>
						</el-input>
						<div class="el-form-item-msg">请将此地址配置到微信公众平台-开发-基本配置-服务器配置中</div>
					</el-form-item>
				</el-col>

				<el-col :span="24">
					<el-form-item label="备注说明">
						<el-input v-model="form.remarks" type="textarea" :autosize="{minRows: 3, maxRows: 6}" placeholder="请输入备注说明"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button type="primary" @click="submit" :loading="isSaving">保 存</el-button>
		 
		</template>
	</el-dialog>
</template>

<script>
export default {
	name: 'gzh',
	emits: ['success', 'closed'],
	data() {
		return {
			visible: false,
			loading: false,
			isSaving: false,
			isTesting: false,
			//表单数据
			form: {
                id:0,
				appid: '',
				secret: '',
				token: '',
				encodingAESKey: '',
				enabled: false,
				autoReply: true,
				remarks: ''
			},
			//验证规则
			rules: {
				appid: [
					{ required: true, message: '请输入公众号APPID', trigger: 'blur' },
					{ pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'APPID格式不正确', trigger: 'blur' }
				],
				secret: [
					{ required: true, message: '请输入AppSecret', trigger: 'blur' },
					{ min: 32, max: 32, message: 'AppSecret长度必须为32位', trigger: 'blur' }
				],
				token: [
					{ required: true, message: '请输入Token令牌', trigger: 'blur' },
					{ min: 3, max: 32, message: 'Token长度为3-32个字符', trigger: 'blur' },
					{ pattern: /^[a-zA-Z0-9]+$/, message: 'Token只能由英文字母和数字组成', trigger: 'blur' }
				]
			}
		}
	},
	computed: {
		serverUrl() {
			return `${window.location.origin}/we/wechat/${this.form.id}`
		}
	},
	methods: {
		//显示弹窗
		open() {
			this.visible = true;
			this.init();
			return this;
		},

		async init() {
			this.loading = true
			try {
				// 加载微信配置
				let res = await this.$API.common.getconfig.post({ id: 'wechat' })
				if (res.data) {
					this.form = { ...this.form, ...JSON.parse(res.data) }
				}
			} catch (error) {
				this.$message.error('加载配置失败：' + error.message)
			} finally {
				this.loading = false
			}
		},

		//表单提交方法
		submit() {
			this.$refs.form.validate(async (valid) => {
				if (valid) {
					this.isSaving = true
					try {
						// 处理开关状态
						const formData = { ...this.form }
					 
                    let res = await this.$API.common.util.post(
                            `/buss/school/gzh`,
                            this.form
                        );
                        console.log(res)
                        if(res.code == 200){
                            this.$emit('success', this.form)
                            this.visible = false
                            this.$message.success("保存成功")
                        }
                                    

					 
					} catch (error) {
						this.$message.error('保存失败：' + error.message)
					} finally {
						this.isSaving = false
					}
				} else {
					return false
				}
			})
		},

		 

		async copyToClipboard(text) {
			try {
				await navigator.clipboard.writeText(text)
				this.$message.success('已复制到剪贴板')
			} catch (error) {
				// 降级方案
				const textArea = document.createElement('textarea')
				textArea.value = text
				document.body.appendChild(textArea)
				textArea.select()
				document.execCommand('copy')
				document.body.removeChild(textArea)
				this.$message.success('已复制到剪贴板')
			}
		},
        setData(data){

            if(data.gzhconfig){
                this.form = JSON.parse(data.gzhconfig)
            }else{
                this.form = { ...this.form, ...data }
            }

           
        }
          
	}
}
</script>

<style scoped>
.el-form-item-msg {
	color: #909399;
	font-size: 12px;
	line-height: 1.5;
	margin-top: 4px;
}

::v-deep(.el-form-item__label) {
	font-weight: 500;
}
</style>
