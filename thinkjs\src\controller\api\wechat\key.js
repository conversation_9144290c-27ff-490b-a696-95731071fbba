
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {

    async verifyAction(){
        let code = this.post("code");
        let model = this.model("key");
        let key = await model.where({key:code,"del_flag":0}).find();

       
        if(think.isEmpty(key)){
            
            return this.fail(301,"激活码不存在");
        }

        let userinfo = await this.session("userInfo");
        

        let keyrecordmodel=this.model("keyrecord");
        let keyrecord=await keyrecordmodel.where({id:key.keyrecord,"del_flag":0}).find();


        if(think.isEmpty(keyrecord)){
            return this.fail(301,"激活码不存在");
        }


        if(keyrecord.schoolid!=userinfo.school){
            return this.fail(301,"激活码不属于当前学校");
        }

      


        if(key.state2 == 1){
            return this.fail(301,"激活码已使用");
        }

        if(keyrecord.yxq<think.datetime()){
            return this.fail(301,"激活码已过期");
        }
   


        let sql="select kt.id,kt.allow_multiple_use from buss_key k join sys_keyrecord kr on kr.id=k.keyrecord join buss_keytype kt on kt.id=kr.type where kr.del_flag='0' and  k.uid="+userinfo.id+" and k.state=1 and k.state2=1"

        let res=await this.model("key").query(sql);

        if(res.length>0){
            for(let i=0;i<res.length;i++){
                if(res[i].allow_multiple_use==0){
                   if(res[i].id==keyrecord.type){
                    return this.fail(301,"该类型激活码不允许同一个账号多次使用！");
                   }
                }
            }
        }





        await model.where({id:key.id}).update({state:1,state2:1,"uid":userinfo.id});
  


 
      
        
        let userModel=this.model("student");
        let user=await userModel.where({id:userinfo.id}).find();



        let vipService = think.service('vip');

        let lessson=await this.model("lesson").where({"type":keyrecord.nj,parent_id:0}).order(["sort asc"]).field("id,name").select();
        for(let item of lessson){
    
    
    
            let vipres=     await vipService.addVipRecord({
                userid: userinfo.id,
                lessson: item.id,
                nj: keyrecord.nj,
                allday: keyrecord.usetime,
                orderid: "0",
                type:"key",
                code:code
                });
    
    
    
    
        }

        // if(user.type=="免费"){

            

        //     let date1    =new Date();
            
        //     let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
          

        //      await userModel.where({id:userinfo.id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});
        // }else{
        //     let date1    =new Date(user.todate);
        //     if(date1<new Date()){
        //         date1=new Date();
        //     }
        //     let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
        //     await userModel.where({id:userinfo.id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});

        // }
        return this.success({duration:keyrecord.usetime});
    }
}
