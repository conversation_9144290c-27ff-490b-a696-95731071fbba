const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {






    async pageAction() {
        let respData = {};
        const page = this.get('page') ? this.get('page') : 1;
        const rows = this.get('pageSize') ? this.get('pageSize') : 20;
        const where =  {};
   

          if(this.get('orderNo')){
            where['p.order_no'] = ['like',`%${this.get('orderNo')}%`];
          } 

          if(this.get('yxid')){
            where['u.yxid'] = ['=',this.get('yxid')];
          }

          if(this.get('phone')){
            where['u.phone'] = ['like',`%${this.get('phone')}%`];
          }  

          if(this.get('payStatus')){
            where['p.pay_status'] = ['=',this.get('payStatus')];
          }

          if(this.get('orderStatus')){
            where['p.order_status'] = ['=',this.get('orderStatus')];
          }

          if(this.get('startDate')){
            where['p.create_time'] = ['>=',this.get('startDate')];
          }

          if(this.get('endDate')){
            where['p.create_time'] = ['<=',this.get('endDate')];
          } 
          




        let name = this.get('name') ? this.get('name') : '';
        if(name){
            where['p.name'] = ['like',`%${name}%`];
        }

    let isConsumed=this.get('isConsumed');
      if(isConsumed==1){
          where['v.available_days'] = 0;
      }else if(isConsumed==2){
        where['v.available_days'] = ['>',0];
      }

        const learnLogModel = this.model('order');
        where['p.del_flag'] = 0;
        
        //let dataScopeWhere = await this.dataScope('p');
    
        const response = await learnLogModel
          .alias('p')
          .field('p.*,u.name AS uname,u.phone AS uphone,v.useday,v.available_days,v.allday,sp.name as productname,yx.name as yxname')
          .join(["left join buss_student u ON p.`user_id`=u.`id`"])
          .join(["left join sys_vip v ON v.orderid=p.id"])
          .join(["left join sys_product sp ON sp.id=p.product_id"])
          .join(["left join sys_yx yx ON yx.id=u.yxid"])
    
          
          .page(page, rows).where(where)
          .order('p.create_time desc').countSelect();
    
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          message: ''
        };
        return this.json(respData);
      }





      async removeAction(){
        let respData = {};
        const id = this.post('id');
        const orderModel = this.model('order');
        const result = await orderModel.where({id:id}).update({del_flag:1});
        return this.success('删除成功');
      }


      async refundAction(){

          const refundData = this.post();


         let refund={};
       

          let model=this.model('order');
          let result=await model.where({id:refundData.orderId}).find();



          if(result){
           
            refund.schoolid=result.schoolid;
            refund.orderNo=result.order_no;
            refund.totalAmount=result.pay_amount;
            refund.refundAmount=refundData.refundAmount;
            refund.refundReason=refundData.refundDesc;
            refund.refundNo="TK_"+refundData.order_no;
            const wxpayService = think.service('wxpay');
            let refundres=await wxpayService.refund(refund);
            console.log("result=============",refundres);



            if(refundres.code==200){

              let vipmodel=this.model('vip');
              let vipresult=await vipmodel.where({orderid:refundData.orderId}).update({"del_flag":1});
              await model.where({id:refundData.orderId}).update({"pay_status":2,"order_status":3});
              return this.success('退款成功');
            }else{
              return this.error('退款失败');
            }



            return this.success('退款成功');
          }else{
            return this.error('退款失败');
          }







        
        return this.success('退款成功');
      }



 
}