const BaseRest = require('../rest.js');

module.exports = class extends BaseRest {









    async pageAction() {
        let respData = {};
        const page = this.get('page') ? this.get('page') : 1;
        const rows = this.get('pageSize') ? this.get('pageSize') : 20;
        const where =   {};


        let name = this.get('name') ? this.get('name') : '';
        if(name){
            where['p.name'] = ['like',`%${name}%`];
        }
        const learnLogModel = this.model('product');
        where['p.del_flag'] = 0;
        
        //let dataScopeWhere = await this.dataScope('p');
    
        const response = await learnLogModel
          .alias('p')
          .field('p.*,u.name AS uname,l.name AS lessonname,s.name AS schoolname')
          .join(["left join sys_user u ON p.`create_user`=u.`id`"])
          .join(["left join sys_lesson l ON p.`lesson`=l.`id`"])
          .join(["left join buss_school s ON p.`schoolid`=s.`id`"])
          
          .page(page, rows).where(where)
          .order('p.create_time desc').countSelect();
    
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          message: ''
        };
        return this.json(respData);
      }









      async saveAction() {
        let respData = {};
        const model = this.model('product');
        const id = this.post('id') ? this.post('id') : null;
        const userInfo = await this.session('userInfo');
        let data = this.post();

        let mode2=this.model("lesson");
        let lesson=await mode2.where({"id":data.lesson}).find();
        data.nj=lesson.type;

       
        data.create_user = userInfo.id;
        if (think.isEmpty(id)) {
          data.schoolid=userInfo.schoolid;
          await model.add(await this.addDataNoId(data));
          respData = {
            code: 200,
            state:1,
            data: {},
            message: '成功'
          };
        } else {
          await model.where({id: id}).update(await this.updateData(this.post()));
          respData = {
            code: 200,
            state:1,
            data: {},
            message: '成功'
          };
        }
        return this.json(respData);
      }
 


      async deleteAction() {
        let respData = {};
        const id = this.post('id') ? this.post('id') : null;
        if (think.isEmpty(id)) {
          respData = {
            code: 400,
            data: {},
            message: '缺少必要的参数'
          };
        } else {
          const model = this.model('product');
          await model.where({id: id}).update({del_flag:1});
          respData = {
            code: 200,
            data: {},
            message: '成功'
          };
        }
        return this.json(respData);
      }
  
  
};
