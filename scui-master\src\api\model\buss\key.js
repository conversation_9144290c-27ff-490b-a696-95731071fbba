import config from "@/config";
import http from "@/utils/request";

const key = {
	page: {
		url: `${config.API_URL}/buss/key/page`,
		name: "菜单",
		get: async function () {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url);

		}
	}
	, save: {
		url: `${config.API_URL}/buss/key/save`,
		name: "保存",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}
	, remove: {
		url: `${config.API_URL}/buss/key/delete`,
		name: "删除",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}
}

export default key;
