const BaseRest = require('../rest.js');
const axios = require('axios');
module.exports = class extends BaseRest {
  /**
   * 获取菜单列表
   */
  async listAction() {
    try {
      let schoolId = this.get('school_id');
       

      const userInfo = await this.session('userInfo');
      if (schoolId==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          schoolId=school.id
      }


      // 查询菜单列表
      const menus = await this.model('school_wechatmenu')
        .where({ school_id: schoolId })
        .order('parent_id ASC, sort_order ASC')
        .select();

      return this.success(menus);
    } catch (error) {
      think.logger.error('获取菜单列表失败:', error);
      return this.fail('获取菜单列表失败: ' + error.message);
    }
  }

  /**
   * 保存菜单
   */
  async saveAction() {
    try {
      let data = this.post();

      console.log(data);
    


      
      const userInfo = await this.session('userInfo');
      if (data.school_id==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          data.school_id=school.id
      }

      // 验证菜单数据
      if (!data.name) {
        return this.fail('菜单名称不能为空');
      }

      // 验证菜单类型相关字段
      if (data.menu_type === 1 && !data.url) {
        return this.fail('跳转链接不能为空');
      } else if (data.menu_type === 2 && !data.menu_key) {
        return this.fail('事件KEY不能为空');
      } else if (data.menu_type === 3 && (!data.appid || !data.pagepath)) {
        return this.fail('小程序APPID和页面路径不能为空');
      }
      
      // 如果没有设置排序值，设置一个默认值
      if (data.sort_order === undefined || data.sort_order === null) {
        // 获取同级菜单中的最大排序值
        const maxOrder = await this.model('school_wechatmenu')
          .where({
            school_id: data.school_id,
            parent_id: data.parent_id || 0
          })
          .max('sort_order');
        
        // 设置为最大值+10，方便后续插入新菜单
        data.sort_order = (maxOrder || 0) + 10;
      }

      // 保存或更新菜单
      let result;
      if (data.id) {
        let res=await this.model('school_wechatmenu').where({ id: data.id }).find();
        if(!think.isEmpty(res)){

            result = await this.model('school_wechatmenu').where({ id: data.id }).update({
                name: data.name,
                menu_type: data.menu_type,
                url: data.url || '',
                menu_key: data.menu_key || '',
                appid: data.appid || '',
                pagepath: data.pagepath || '',
                sort_order: data.sort_order || 0,
                status: data.status || 1
              });

        }else{
            result = await this.model('school_wechatmenu').add({
                school_id: data.school_id,
                parent_id: data.parent_id || 0,
                name: data.name,
                menu_type: data.menu_type || 1,
                url: data.url || '',
                menu_key: data.menu_key || '',
                appid: data.appid || '',
                pagepath: data.pagepath || '',
                sort_order: data.sort_order || 0,
                status: data.status || 1
              });

        }
        // 更新
     
      } else {
        // 新增
        result = await this.model('school_wechatmenu').add({
          school_id: data.school_id,
          parent_id: data.parent_id || 0,
          name: data.name,
          menu_type: data.menu_type || 1,
          url: data.url || '',
          menu_key: data.menu_key || '',
          appid: data.appid || '',
          pagepath: data.pagepath || '',
          sort_order: data.sort_order || 0,
          status: data.status || 1
        });
      }

      return this.success(result);
    } catch (error) {
      think.logger.error('保存菜单失败:', error);
      return this.fail('保存菜单失败: ' + error.message);
    }
  }

  /**
   * 批量保存菜单
   */
  async batchSaveAction() {
    try {
      let { school_id, menus } = this.post();
      if ( !menus || !Array.isArray(menus)) {
        return this.fail('参数错误');
      }


      
      const userInfo = await this.session('userInfo');
      if (school_id==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          school_id=school.id
      }

      // 开启事务
      const model = this.model('school_wechatmenu');
      await model.startTrans();

      try {
        // 删除该学校的所有菜单
        await model.where({ school_id }).delete();

        // 批量插入新菜单
        if (menus.length > 0) {
          await model.addMany(menus.map(menu => ({
            ...menu,
            school_id
          })));
        }

        // 提交事务
        await model.commit();
        return this.success(true, '保存成功');
      } catch (error) {
        // 回滚事务
        await model.rollback();
        throw error;
      }
    } catch (error) {
      think.logger.error('批量保存菜单失败:', error);
      return this.fail('批量保存菜单失败: ' + error.message);
    }
  }

  /**
   * 删除菜单
   */
  async deleteAction() {
    try {
      let { id, school_id } = this.post();
      if (!id  ) {
        return this.fail('参数错误');
      }


      
      const userInfo = await this.session('userInfo');
      if (school_id==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          school_id=school.id
      }

      // 开启事务
      const model = this.model('school_wechatmenu');
      await model.startTrans();

      try {
        // 删除菜单
        await model.where({ id, school_id }).delete();

        // 如果是一级菜单，同时删除其下的所有子菜单
        await model.where({ parent_id: id, school_id }).delete();

        // 提交事务
        await model.commit();
        return this.success(true, '删除成功');
      } catch (error) {
        // 回滚事务
        await model.rollback();
        throw error;
      }
    } catch (error) {
      think.logger.error('删除菜单失败:', error);
      return this.fail('删除菜单失败: ' + error.message);
    }
  }

  /**
   * 更新菜单排序
   */
  async updateSortAction() {
    try {
      let { id, school_id, sort_order } = this.post();
      
      // 验证必要参数
      if (!id || !school_id || sort_order === undefined) {
        return this.fail('参数错误');
      }


      
      const userInfo = await this.session('userInfo');
      if (school_id==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          school_id=school.id
      }

      // 查询菜单是否存在
      const menu = await this.model('school_wechatmenu').where({ id, school_id }).find();
      if (think.isEmpty(menu)) {
        return this.fail('菜单不存在');
      }

      // 更新排序值
      await this.model('school_wechatmenu').where({ id }).update({ sort_order });
      
      // 获取更新后的菜单
      const updatedMenu = await this.model('school_wechatmenu').where({ id }).find();
      
      return this.success(updatedMenu, '更新排序成功');
    } catch (error) {
      think.logger.error('更新菜单排序失败:', error);
      return this.fail('更新菜单排序失败: ' + error.message);
    }
  }

  /**
   * 发布菜单到微信公众号
   */
  async publishAction() {
    try {
      let { school_id, menu_data } = this.post();
      if (!school_id || !menu_data) {
        return this.fail('参数错误');
      }


      const userInfo = await this.session('userInfo');
      if (think.isEmpty(school_id)||school_id==0) {
        let school=await think.model('school').where({uid:userInfo.id}).find();
          school_id=school.id
      }

      // 解析菜单数据
      let menuData;
      try {
        console.log(menu_data);
        menuData = JSON.parse(menu_data);

    


      } catch (e) {
        return this.fail('菜单数据格式错误');
      }

      // 获取公众号配置
      const schoolModel = this.model('school');
      const school = await schoolModel.where({ id: school_id }).find();
      if (think.isEmpty(school)) {
        return this.fail('学校不存在');
      }

      let gzhconfig;
      try {
        gzhconfig = JSON.parse(school.gzhconfig || '{}');
      } catch (e) {
        return this.fail('公众号配置错误');
      }

      if (!gzhconfig.appid || !gzhconfig.secret) {
        return this.fail('公众号配置不完整，请先配置公众号参数');
      }

      // 获取微信访问令牌
      const accessToken = await this.getWechatAccessToken(gzhconfig);
      if (!accessToken) {
        return this.fail('获取微信访问令牌失败');
      }

      // 调用微信接口创建菜单
      const result = await this.createWechatMenu(accessToken, menuData);

    

      return this.success(result);
    } catch (error) {
      think.logger.error('发布菜单失败:', error);
      return this.fail('发布菜单失败: ' + error.message);
    }
  }

  /**
   * 获取微信访问令牌
   */
  async getWechatAccessToken(gzhconfig) {
    // 获取配置的appid和secret
    const { appid, secret } = gzhconfig;
    
    // 检查是否已有缓存的token
    let accessToken = await this.cache('wechat_access_token');
    
    if (!accessToken) {
      // 从微信服务器获取新的token
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
      
      try {
        const response = await axios.get(url);
        
        if (response.data && response.data.access_token) {
          accessToken = response.data.access_token;
          // 缓存token（微信token有效期为7200秒，这里设置7000秒）
          await this.cache('wechat_access_token', accessToken, {
            timeout: 7000 * 1000
          });
        } else {
          throw new Error('获取access_token失败: ' + JSON.stringify(response.data));
        }
      } catch (error) {
        think.logger.error('获取微信access_token失败:', error);
        throw error;
      }
    }
    
    return accessToken;
  }
  

  /**
   * 调用微信接口创建菜单
   */
  async createWechatMenu(accessToken, menuData) {
    try {
      const url = `https://api.weixin.qq.com/cgi-bin/menu/create?access_token=${accessToken}`;
      const response = await axios.post(url,menuData);
      
      const result = await response.data;
      if (result.errcode !== 0) {
        throw new Error(result.errmsg || '创建菜单失败');
      }
      
      return result;
    } catch (error) {
      think.logger.error('调用微信接口创建菜单失败:', error);
      throw error;
    }
  }
};