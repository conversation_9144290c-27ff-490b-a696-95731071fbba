module.exports = class extends think.Model {
  // 配置关联关系
  get relation() {
    return {
      column: { // 主表和自表的关联关系
        type: think.Model.HAS_MANY,
        key: 'id',
        fKey: 'table_id',
        relation: false,
        where: {
          del_flag: 0
        },
        order: 'no ASC'
      }
    };
  }
  setRelationPage(flag) {
    return this.setRelation(flag).countSelect();
  }

  getRelationWherePage(model, where) {
    return this.setRelation(model, where).countSelect();
  }

  setRelationList(flag) {
    return this.setRelation(flag).select();
  }
  setRelationFind(flag) {
    return this.setRelation(flag).find();
  }
};
