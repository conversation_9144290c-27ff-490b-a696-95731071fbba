module.exports = class extends think.Controller {


    async listAction() {
        try {
          const adModel = this.model('advertisement');
          const data = await adModel
            .field('id, name, content, image, clickable, link_url')
            .where({
              status: 1,
              del_flag: 0
            })
            .order('create_time desc')
            .select();
          
          return this.success(data);
        } catch (err) {
          return this.fail(500, '获取广告数据失败');
        }
      }
    

}