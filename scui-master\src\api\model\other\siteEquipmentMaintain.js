import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/site/equipment_maintain/page`,
		name: "设备维护数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/site/equipment_maintain/add`,
		name: "设备维护数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/site/equipment_maintain/save`,
		name: "设备维护数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/site/equipment_maintain/info`,
		name: "设备维护数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/site/equipment_maintain/delete`,
		name: "删除设备维护数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
