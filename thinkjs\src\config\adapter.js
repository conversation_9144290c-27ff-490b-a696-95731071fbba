const fileCache = require('think-cache-file');
const nunjucks = require('think-view-nunjucks');
const fileSession = require('think-session-file');
const mysql = require('think-model-mysql');
const {Console, File, DateFile} = require('think-logger3');
const path = require('path');
const isDev = think.env === 'development';
const JWTSession = require('think-session-jwt');
const ws = require('think-websocket-ws');
const socketio = require('think-websocket-socket.io');
/**
 * cache adapter config
 * @type {Object}
 */

exports.cache = {
  type: 'file',
  common: {
    timeout: 24 * 60 * 60 * 1000 // millisecond
  },
  file: {
    handle: fileCache,
    cachePath: path.join(think.ROOT_PATH, 'runtime/cache'), // absoulte path is necessarily required
    pathDepth: 1,
    gcInterval: 24 * 60 * 60 * 1000 // gc interval
  }
};

/**
 * model adapter config
 * @type {Object}
 */
exports.model = {
  type: 'mysql',
  common: {
    logConnect: isDev,
    logSql: isDev,
    logger: msg => think.logger.info(msg)
  },
  mysql: {
    handle: mysql,
    database: 'jsjy2',
    prefix: 'sys_',
    encoding: 'utf8mb4',
    host: '127.0.0.1',
    port: '3306',
    user: 'root2',
    password: '123456',
    dateStrings: true
    // host: '127.0.0.1',
    // port: '3308',
    // user: 'root',
    // password: 'mysql',
    // dateStrings: true
  },
  sequel: {
    logConnect: false,
    database: 'jsjy',
    user: 'root',
    password: 'root',
    options: {
      host: '127.0.0.1',
      port: 3308,
      dialect: 'mysql',
      logging: false,
      timezone: '+08:00',
      dialectOptions: {
        options: {
          // 你的 tedious 参数
          useUTC: false,
          dateFirst: 1,
          // 单独的录入时间格式化参数
          charset: 'utf8mb4',
          dateStrings: true,
          typeCast: true
        },
        multipleStatements: true
      },
      define: {
        createdAt: 'create_date',
        updatedAt: 'update_date',
        deletedAt: 'del_flag',
        omitNull: true,
        paranoid: true,
        freezeTableName: false
      }
    }
  }
};

/**
 * session adapter config
 * @type {Object}
 */
exports.session = {
  type: 'jwt',
  common: {
    cookie: {
      name: 'jsjy'
    }
  },
  jwt: {
    handle: JWTSession,
    secret: 'secret', // secret is reqired
    tokenType: 'header', // ['query', 'body', 'header', 'cookie'], 'cookie' is default
    tokenName: 'authorization', // if tokenType not 'cookie', this will be token name, 'jwt' is default
    sign: {
      expiresIn: 60 * 60 * 12
    },
    verify: {
      // verify options is not required
    },
    verifyCallback: any => any // default verify fail callback
  }
};

/**
 * view adapter config
 * @type {Object}
 */
exports.view = {
  type: 'nunjucks',
  common: {
    viewPath: path.join(think.ROOT_PATH, 'view'),
    sep: '_',
    extname: '.html'
  },
  nunjucks: {
    handle: nunjucks
  }
};

/**
 * logger adapter config
 * @type {Object}
 */
exports.logger = {
  type: isDev ? 'console' : 'dateFile',
  console: {
    handle: Console
  },
  file: {
    handle: File,
    backups: 10, // max chunk number
    absolute: true,
    maxLogSize: 50 * 1024, // 50M
    filename: path.join(think.ROOT_PATH, 'logs/app.log')
  },
  dateFile: {
    handle: DateFile,
    level: 'ALL',
    absolute: true,
    pattern: '-yyyy-MM-dd',
    alwaysIncludePattern: true,
    filename: path.join(think.ROOT_PATH, 'logs/app.log')
  }
};

/**
 * websocket 模块
 * @type {{common: {}, type: string, ws: {path: string, messages: [{addUser: string, close: string, open: string}], handle: ThinkWebsocketWs}}}
 */
// exports.websocket = {
//   type: 'ws',
//   common: {
//     // common config
//   },
//   ws: {
//     handle: ws,
//     path: '/ws',
//     messages: [{
//       close: '/ws/close',
//       open: '/ws/open',
//       addUser: '/ws/addUser'
//     }]
//   }
// };
exports.websocket = {
  type: 'socketio',
  common: {
    // common config
  },
  socketio: {
    handle: socketio,
    allowOrigin: null,  // 默认所有的域名都允许访问
    path: '/socket.io',             // 默认 '/socket.io'
    adapter: null,                  // 默认无 adapter
    messages: {
      open: '/websocket/im/open',
      close: '/websocket/im/close',     // 关闭连接时处理的 Action
      addUser: '/websocket/im/addUser'
    }
  }
};
