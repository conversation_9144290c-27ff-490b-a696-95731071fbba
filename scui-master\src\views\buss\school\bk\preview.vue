<template>
  <el-dialog
    title="预览"
    v-model="visible"
    append-to-body="true"
    :width="900"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-container class="layout">
    <div>

       <div
                
                v-for="(item, index) in imglist"
                :key="index + 'sd'"
            >
            {{index+1}}
                <img :src="item.tm_p" alt="" />
                 
            </div>

    </div>
    </el-container>

    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="mode != 'show'"
        type="primary"
        :loading="isSaveing"
        @click="select()"
        >选择</el-button
      >
    </template>
  </el-dialog>
</template>

<script>

export default {

  emits: ["success", "closed"],

  data() {
    return {
        filelist:[],
         isSaveing:false,
       imglist:[],
      dialog: {
        save: false,
      },
  
      mode: "add",
      
      visible: false,
     
    };
  },
  mounted() {},
  methods: {
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },

async  setData(data) {
       console.log(data);

      this.imglist = await this.$API.bk.getlist.get({id:data.id});
      console.log(this.imglist)
       
    },
  
  },
};
</script>

