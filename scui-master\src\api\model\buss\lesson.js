import config from "@/config";
import http from "@/utils/request";

const lesson = {
	officeList: {
		url: `${config.API_URL}/buss/lesson/list`,
		name: "部门树",
		post: async function (data) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name,data);

		}
	},
	tree: {
		url: `${config.API_URL}/buss/lesson/tree2`,
		name: "树2",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	},
	list: {
		url: `${config.API_URL}/buss/lesson/tree`,
		name: "树",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	},

	listbyschool: {
		url: `${config.API_URL}/buss/lesson/treebyschool`,
		name: "树",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	},

	billselect: {
		url: `${config.API_URL}/buss/lesson/billselect`,
		name: "下拉",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name,params);

		}
	},
	select: {
		url: `${config.API_URL}/buss/lesson/getselect`,
		name: "下拉",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	}
	, save: {
		url: `${config.API_URL}/buss/lesson/save`,
		name: "保存",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}, remove: {
		url: `${config.API_URL}/buss/lesson/remove`,
		name: "删除部门",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},
	getbyid: {
		url: `${config.API_URL}/office/getbyid`,
		name: "删除部门",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}


};

export default lesson;
