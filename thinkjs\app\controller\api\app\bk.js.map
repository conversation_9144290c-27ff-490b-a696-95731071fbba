{"version": 3, "sources": ["..\\..\\..\\..\\src\\controller\\api\\app\\bk.js"], "names": ["BaseRest", "require", "module", "exports", "tabsAction", "model", "user", "session", "res", "where", "level", "field", "select", "json", "teacherAction", "id", "post", "console", "log", "find", "password", "domain", "config", "think", "isEmpty", "skinfo", "replace", "jisuanAction", "datetime", "data", "userid", "lasttime", "model2", "schoolid", "school", "del_flag", "state", "fbdate", "model3", "item", "tmp", "bkid", "uid", "tklist", "map", "tkid", "errorlist", "flag", "Date", "jjlist", "score", "lessonid", "lesson", "split", "max", "min", "scorelist", "para", "length", "iferror", "ifjj", "ifscore", "create_date", "errorid", "join", "jjid", "scoreid", "add", "listAction", "page", "rows", "alias", "order", "countSelect", "lessonname", "code", "count", "msg", "infoAction", "listtmAction", "res2", "for<PERSON>ach", "includes", "error", "jj", "countflagAction", "delete", "countAction"], "mappings": ";;AACA,MAAMA,WAAWC,QAAQ,WAAR,CAAjB;AACAC,OAAOC,OAAP,GAAiB,cAAcH,QAAd,CAAuB;;AAE9BI,cAAN,GAAkB;AAAA;;AAAA;AACf,gBAAIC,QAAM,MAAKA,KAAL,CAAW,QAAX,CAAV;AACA,kBAAMC,OAAO,MAAM,MAAKC,OAAL,CAAa,UAAb,CAAnB;;AAEA,gBAAIC,MAAI,MAAMH,MAAMI,KAAN,CAAY,EAAC,QAAOH,KAAKI,KAAb,EAAmB,YAAW,CAA9B,EAAgC,aAAY,CAA5C,EAAZ,EAA4DC,KAA5D,CAAkE,SAAlE,EAA6EC,MAA7E,EAAd;;AAIA,mBAAO,MAAKC,IAAL,CAAUL,GAAV,CAAP;AARe;AAajB;;AAGKM,iBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAIT,QAAM,OAAKA,KAAL,CAAW,MAAX,CAAV;AACA,gBAAIU,KAAG,OAAKC,IAAL,CAAU,IAAV,CAAP;AACAC,oBAAQC,GAAR,CAAYH,EAAZ;AACA,gBAAIP,MAAI,MAAMH,MAAMI,KAAN,CAAY,EAAC,MAAKM,EAAN,EAAS,YAAW,CAApB,EAAZ,EAAoCJ,KAApC,CAA0C,GAA1C,EAA+CQ,IAA/C,EAAd;AACA,mBAAOX,IAAIY,QAAX;;AAEA,gBAAIC,SAAO,OAAKC,MAAL,CAAY,QAAZ,CAAX;AACA,gBAAG,CAACC,MAAMC,OAAN,CAAchB,IAAIiB,MAAlB,CAAJ,EAA8B;;AAE1BjB,oBAAIiB,MAAJ,GAAWjB,IAAIiB,MAAJ,CAAWC,OAAX,CAAmB,wBAAnB,EAA8C,QAAOL,MAAO,gBAA5D,CAAX;AACH;;AAGD,mBAAO,OAAKR,IAAL,CAAUL,GAAV,CAAP;AAdiB;AAepB;;AAGEmB,gBAAN,GAAoB;AAAA;;AAAA;AACjB,kBAAMrB,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIF,QAAM,OAAKA,KAAL,CAAW,cAAX,CAAV;AACA,gBAAIuB,WAASL,MAAMK,QAAN,EAAb;AACD,gBAAIC,OAAK,MAAMxB,MAAMI,KAAN,CAAY,EAACqB,QAAOxB,KAAKS,EAAb,EAAZ,EAA8BI,IAA9B,EAAf;AACA,gBAAG,CAACI,MAAMC,OAAN,CAAcK,IAAd,CAAJ,EAAwB;AACvBD,2BAASC,KAAKE,QAAd;AACA;;AAEDH,uBAASL,MAAMK,QAAN,CAAeA,QAAf,EAAwB,YAAxB,CAAT;;AAGC,gBAAII,SAAO,OAAK3B,KAAL,CAAW,IAAX,CAAX;AACA,gBAAIG,MAAI,MAAMwB,OAAOvB,KAAP,CAAa,EAACwB,UAAS3B,KAAK4B,MAAf,EAAsBC,UAAS,CAA/B,EAAiCC,OAAM,CAAvC,EAAyCC,QAAO,EAAC,MAAKT,QAAN,EAAhD,EAAb,EAA+EhB,MAA/E,EAAd;;AAGA,gBAAI0B,SAAO,OAAKjC,KAAL,CAAW,SAAX,CAAX;AACA,iBAAI,IAAIkC,IAAR,IAAgB/B,GAAhB,EAAoB;AAChB,oBAAIgC,MAAI,MAAMF,OAAO7B,KAAP,CAAa,EAACgC,MAAKF,KAAKxB,EAAX,EAAc2B,KAAIpC,KAAKS,EAAvB,EAAb,EAAyCI,IAAzC,EAAd;AACA,oBAAGI,MAAMC,OAAN,CAAcgB,GAAd,CAAH,EAAsB;AAClB;AACA,wBAAIG,SAAO,MAAM,OAAKtC,KAAL,CAAW,SAAX,EAAsBI,KAAtB,CAA4B,EAACgC,MAAKF,KAAKxB,EAAX,EAA5B,EAA4CJ,KAA5C,CAAkD,MAAlD,EAA0DC,MAA1D,EAAjB;AACA+B,6BAAOA,OAAOC,GAAP,CAAW;AAAA,+BAAML,KAAKM,IAAX;AAAA,qBAAX,CAAP;;AAGA,wBAAIC,YAAW,MAAM,OAAKzC,KAAL,CAAW,WAAX,EAAwBI,KAAxB,CAA8B,EAACqB,QAAOxB,KAAKS,EAAb,EAAgBgC,MAAK,CAArB,EAAuBF,MAAK,CAAC,IAAD,EAAMF,MAAN,CAA5B,EAA9B,EACpBlC,KADoB,CACd,qBAAqBc,MAAMK,QAAN,CAAe,IAAIoB,IAAJ,EAAf,EAA2B,YAA3B,CAArB,GAAgE,GADlD,EACuDrC,KADvD,CAC6D,MAD7D,EACqEC,MADrE,EAArB;;AAGZ;;AAEQ,wBAAIqC,SAAO,MAAM,OAAK5C,KAAL,CAAW,WAAX,EAAwBI,KAAxB,CAA8B,EAACiC,KAAIpC,KAAKS,EAAV,EAAa8B,MAAK,CAAC,IAAD,EAAMF,MAAN,CAAlB,EAA9B,EAAgEhC,KAAhE,CAAsE,MAAtE,EAA8EC,MAA9E,EAAjB;AACR;;;AAGQ,wBAAIsC,QAAM,MAAM,OAAK7C,KAAL,CAAW,aAAX,EAA0BI,KAA1B,CAAgC,EAACqB,QAAOxB,KAAKS,EAAb,EAAgBoC,UAAS,CAAC,IAAD,EAAMZ,KAAKa,MAAL,CAAYC,KAAZ,CAAkB,GAAlB,CAAN,CAAzB,EAAhC,EAAyF1C,KAAzF,CAA+F,OAA/F,EAAwGQ,IAAxG,EAAhB;;AAGA,wBAAGI,MAAMC,OAAN,CAAc0B,KAAd,CAAH,EAAwB;AACpBA,gCAAM,EAAN;AACH,qBAFD,MAEK;AACDA,gCAAMA,MAAMA,KAAZ;AACH;;AAGD,wBAAII,MAAIJ,QAAM,EAAd;AACA,wBAAIK,MAAIL,QAAM,EAAd;;AAEA,wBAAIM,YAAU,MAAM,OAAKnD,KAAL,CAAW,IAAX,EAAiBI,KAAjB,CAAuB,EAAC0C,UAAS,CAAC,IAAD,EAAMZ,KAAKa,MAAL,CAAYC,KAAZ,CAAkB,GAAlB,CAAN,CAAV,EAAwCH,OAAM,CAAC,SAAD,EAAW,CAACK,GAAD,EAAKD,GAAL,CAAX,CAA9C,EAAoE,MAAK,CAAC,IAAD,EAAMX,MAAN,CAAzE,EAAvB,EAAgHhC,KAAhH,CAAsH,IAAtH,EAA4HC,MAA5H,EAApB;;AAGA,wBAAI6C,OAAK,EAAT;AACAA,yBAAKhB,IAAL,GAAUF,KAAKxB,EAAf;AACA,wBAAG+B,UAAUY,MAAV,GAAiB,CAApB,EAAsB;AAClBD,6BAAKE,OAAL,GAAa,CAAb;AACH,qBAFD,MAEK;AACDF,6BAAKE,OAAL,GAAa,CAAb;AACH;AACD,wBAAGV,OAAOS,MAAP,GAAc,CAAjB,EAAmB;AACfD,6BAAKG,IAAL,GAAU,CAAV;AACH,qBAFD,MAEK;AACDH,6BAAKG,IAAL,GAAU,CAAV;AACH;AACD,wBAAGJ,UAAUE,MAAV,GAAiB,CAApB,EAAsB;AAClBD,6BAAKI,OAAL,GAAa,CAAb;AACH,qBAFD,MAEK;AACDJ,6BAAKI,OAAL,GAAa,CAAb;AACH;;AAEDJ,yBAAKrB,KAAL,GAAW,CAAX;AACAqB,yBAAKf,GAAL,GAASpC,KAAKS,EAAd;AACA0C,yBAAKK,WAAL,GAAiBvC,MAAMK,QAAN,EAAjB;AACA6B,yBAAKM,OAAL,GAAajB,UAAUF,GAAV,CAAc;AAAA,+BAAML,KAAKM,IAAX;AAAA,qBAAd,EAA+BmB,IAA/B,CAAoC,GAApC,CAAb;AACAP,yBAAKQ,IAAL,GAAUhB,OAAOL,GAAP,CAAW;AAAA,+BAAML,KAAKM,IAAX;AAAA,qBAAX,EAA4BmB,IAA5B,CAAiC,GAAjC,CAAV;AACAP,yBAAKS,OAAL,GAAaV,UAAUZ,GAAV,CAAc;AAAA,+BAAML,KAAKxB,EAAX;AAAA,qBAAd,EAA6BiD,IAA7B,CAAkC,GAAlC,CAAb;AACA,0BAAM1B,OAAO6B,GAAP,CAAWV,IAAX,CAAN;;AAEIlB,yBAAKH,KAAL,GAAW,CAAX;AACH;AACJ;AA9EgB;AAmFpB;;AAEKgC,cAAN,GAAkB;AAAA;;AAAA;AACd,gBAAI/D,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,gBAAI8C,WAAS,OAAKnC,IAAL,CAAU,UAAV,CAAb;AACA,kBAAMV,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACAU,oBAAQC,GAAR,CAAYZ,IAAZ;;AAEAW,oBAAQC,GAAR,CAAY,OAAKF,IAAL,EAAZ;;AAEA,gBAAIP,QAAM,EAAV;;AAEAA,kBAAM,YAAN,IAAoBH,KAAK4B,MAAzB;AACAzB,kBAAM,YAAN,IAAoB,GAApB;AACAA,kBAAM,SAAN,IAAiB,CAAC,IAAD,EAAM,CAAN,CAAjB;AACAA,kBAAM,WAAN,IAAmB0C,QAAnB;AACA,kBAAMkB,OAAO,OAAKrD,IAAL,CAAU,MAAV,IAAoB,OAAKA,IAAL,CAAU,MAAV,CAApB,GAAwC,CAArD;AACA,kBAAMsD,OAAO,OAAKtD,IAAL,CAAU,UAAV,IAAwB,OAAKA,IAAL,CAAU,UAAV,CAAxB,GAAgD,EAA7D;AACA,gBAAI2C,UAAQ,OAAK3C,IAAL,CAAU,SAAV,CAAZ;;AAEA,gBAAIoB,QAAM,OAAKpB,IAAL,CAAU,OAAV,CAAV;AACA,gBAAG,CAACO,MAAMC,OAAN,CAAcY,KAAd,CAAJ,EAAyB;AACrB,oBAAGA,SAAO,CAAV,EAAY;;AAER3B,0BAAM,SAAN,IAAiB,CAAjB;AACH,iBAHD,MAGM,IAAG2B,SAAO,CAAV,EAAY;;AAEb3B,0BAAM,SAAN,IAAiB,CAAC,IAAD,EAAM,KAAN,CAAjB;AACJ;AAGJ;;AAED,gBAAG,CAACc,MAAMC,OAAN,CAAcmC,OAAd,CAAD,IAAyBA,WAAS,CAArC,EAAuC;;AAEnClD,sBAAM,YAAN,IAAoB,CAApB;AACH;AACC,gBAAImD,OAAK,OAAK5C,IAAL,CAAU,MAAV,CAAT;;AAEF,gBAAG,CAACO,MAAMC,OAAN,CAAcoC,IAAd,CAAD,IAAsBA,QAAM,CAA/B,EAAiC;;AAE7BnD,sBAAM,SAAN,IAAiB,CAAjB;AACH;;AAMD,gBAAID,MAAI,MAAMH,MAAMkE,KAAN,CAAY,IAAZ,EAAkB9D,KAAlB,CAAwBA,KAAxB,EAA+B4D,IAA/B,CAAoCA,IAApC,EAA0CC,IAA1C,EACbN,IADa,CACR,uCADQ,EAEbA,IAFa,CAER,0CAFQ,EAEoCrD,KAFpC,CAE0C,kGAF1C,EAGb6D,KAHa,CAGP,oBAHO,EAIbC,WAJa,EAAd;AAKA,gBAAI5C,OAAK,EAAT;;AAIA,iBAAI,IAAIU,IAAR,IAAgB/B,IAAIqB,IAApB,EAAyB;AACjBU,qBAAKmC,UAAL,GAAiBnC,KAAKmC,UAAL,CAAgBhD,OAAhB,CAAwB,IAAxB,EAA8B,MAA9B,CAAjB;AAGP;;AAIDG,iBAAK8C,IAAL,GAAY,GAAZ;AACA9C,iBAAK+C,KAAL,GAAapE,IAAIoE,KAAjB;AACA/C,iBAAKA,IAAL,GAAYrB,IAAIqB,IAAhB;AACAA,iBAAKgD,GAAL,GAAW,EAAX;;AAKJ,mBAAO,OAAKhE,IAAL,CAAUgB,IAAV,CAAP;AAvEkB;AA4EjB;;AAGSiD,cAAN,GAAkB;AAAA;;AAAA;AACd,gBAAIzE,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,gBAAIU,KAAG,OAAKC,IAAL,CAAU,IAAV,CAAP;AACA,gBAAIR,MAAI,MAAMH,MAAMkE,KAAN,CAAY,GAAZ,EAAiBP,IAAjB,CAAsB,yBAAtB,EAAiDvD,KAAjD,CAAuD,EAACgC,MAAK1B,EAAN,EAAvD,EAAkEJ,KAAlE,CAAwE,mBAAxE,EAA6FC,MAA7F,EAAd;;AAGA,mBAAO,OAAKC,IAAL,CAAUL,GAAV,CAAP;AANc;AASjB;AACKuE,gBAAN,GAAoB;AAAA;;AAAA;AACnB,gBAAOvE,MAAI,MAAM,OAAKH,KAAL,CAAW,SAAX,EAAsBkE,KAAtB,CAA4B,GAA5B,EAAiCP,IAAjC,CAAsC,yBAAtC,EAAiEvD,KAAjE,CAAuE,EAAC,QAAO,OAAKO,IAAL,CAAU,IAAV,CAAR,EAAvE,EAAiGL,KAAjG,CAAuG,iBAAvG,EAA0HC,MAA1H,EAAjB;AACG,gBAAIG,KAAG,OAAKC,IAAL,CAAU,IAAV,CAAP;AACA,gBAAKV,OAAK,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAhB;AACA,gBAAIyE,OAAK,MAAM,OAAK3E,KAAL,CAAW,SAAX,EAAsBI,KAAtB,CAA4B,EAACgC,MAAK1B,EAAN,EAAS2B,KAAIpC,KAAKS,EAAlB,EAA5B,EAAmDI,IAAnD,EAAf;;AAEA,gBAAG,CAACI,MAAMC,OAAN,CAAcwD,IAAd,CAAJ,EAAwB;AACpB,oBAAIlC,YAAUkC,KAAKjB,OAAL,CAAaV,KAAb,CAAmB,GAAnB,CAAd;AACA,oBAAIJ,SAAO+B,KAAKf,IAAL,CAAUZ,KAAV,CAAgB,GAAhB,CAAX;AACA,oBAAIG,YAAUwB,KAAKd,OAAL,CAAab,KAAb,CAAmB,GAAnB,CAAd;;AAEA7C,oBAAIyE,OAAJ,CAAY,gBAAM;AACd,wBAAGnC,UAAUoC,QAAV,CAAmB3C,KAAKxB,EAAxB,CAAH,EAA+B;AAC3BwB,6BAAK4C,KAAL,GAAW,CAAX;AACH;AACD,wBAAGlC,OAAOiC,QAAP,CAAgB3C,KAAKxB,EAArB,CAAH,EAA4B;AACxBwB,6BAAK6C,EAAL,GAAQ,CAAR;AACH;AACD,wBAAG5B,UAAU0B,QAAV,CAAmB3C,KAAKxB,EAAxB,CAAH,EAA+B;AAC3BwB,6BAAKW,KAAL,GAAW,CAAX;AACH;AACJ,iBAVD;AAWH;;AAQD,mBAAO,OAAKrC,IAAL,CAAU,EAACgB,MAAKrB,GAAN,EAAV,CAAP;AA9BgB;AA+BnB;;AAEK6E,mBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAM/E,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIF,QAAM,OAAKA,KAAL,CAAW,cAAX,CAAV;AACA,gBAAIwB,OAAK,EAAT;AACAA,iBAAKC,MAAL,GAAYxB,KAAKS,EAAjB;AACAc,iBAAKE,QAAL,GAAcR,MAAMK,QAAN,EAAd;AACA,kBAAMvB,MAAMI,KAAN,CAAY,EAACqB,QAAOxB,KAAKS,EAAb,EAAZ,EAA8BuE,MAA9B,EAAN;AACA,kBAAMjF,MAAM8D,GAAN,CAAUtC,IAAV,CAAN;AACA,mBAAO,OAAKhB,IAAL,CAAU,EAAC8D,MAAK,GAAN,EAAUE,KAAI,EAAd,EAAiBhD,MAAK,EAAtB,EAAV,CAAP;AARmB;AAStB;;AAEK0D,eAAN,GAAmB;AAAA;;AAAA;;AAEf,kBAAMjF,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIF,QAAM,OAAKA,KAAL,CAAW,cAAX,CAAV;AACA,gBAAIuB,WAASL,MAAMK,QAAN,EAAb;AACD,gBAAIC,OAAK,MAAMxB,MAAMI,KAAN,CAAY,EAACqB,QAAOxB,KAAKS,EAAb,EAAZ,EAA8BI,IAA9B,EAAf;AACA,gBAAG,CAACI,MAAMC,OAAN,CAAcK,IAAd,CAAJ,EAAwB;AACvBD,2BAASC,KAAKE,QAAd;AACA;AACD,gBAAIC,SAAO,OAAK3B,KAAL,CAAW,IAAX,CAAX;AACA,gBAAIG,MAAI,MAAMwB,OAAOvB,KAAP,CAAa,EAACwB,UAAS3B,KAAK4B,MAAf,EAAsBC,UAAS,CAA/B,EAAiCC,OAAM,CAAvC,EAAyCC,QAAO,EAAC,MAAKT,QAAN,EAAhD,EAAb,EAA+EgD,KAA/E,EAAd;AACA,mBAAO,OAAK/D,IAAL,CAAU,EAAC8D,MAAK,GAAN,EAAUE,KAAI,EAAd,EAAiBhD,MAAK,EAAC+C,OAAMpE,GAAP,EAAtB,EAAV,CAAP;AAXgB;AAgBlB;;AA9QmC,CAAxC", "file": "..\\..\\..\\..\\src\\controller\\api\\app\\bk.js", "sourcesContent": ["\r\nconst BaseRest = require('./rest.js');\r\nmodule.exports = class extends BaseRest {\r\n\r\n    async tabsAction(){\r\n       let model=this.model(\"lesson\");\r\n       const user = await this.session('userInfo');\r\n\r\n       let res=await model.where({\"type\":user.level,\"del_flag\":0,\"parent_id\":0}).field(\"id,name\").select();\r\n\r\n\r\n\r\n       return this.json(res);\r\n\r\n\r\n\r\n\r\n    }\r\n\r\n\r\n    async teacherAction(){\r\n        let model=this.model(\"user\");\r\n        let id=this.post(\"id\");\r\n        console.log(id);\r\n        let res=await model.where({\"id\":id,\"del_flag\":0}).field(\"*\").find();\r\n        delete res.password;\r\n\r\n        let domain=this.config(\"domain\");\r\n        if(!think.isEmpty(res.skinfo)){\r\n\r\n            res.skinfo=res.skinfo.replace(/src=\"\\/static\\/upload/g, `src=\"${domain}/static/upload`);\r\n        }\r\n    \r\n    \r\n        return this.json(res);\r\n    }\r\n    \r\n\r\n async jisuanAction(){\r\n    const user = await this.session('userInfo');\r\n    let model=this.model(\"bk_countflag\");\r\n    let datetime=think.datetime();\r\n   let data=await model.where({userid:user.id}).find();\r\n   if(!think.isEmpty(data)){\r\n    datetime=data.lasttime;\r\n   }\r\n\r\n   datetime=think.datetime(datetime,\"YYYY-MM-DD\");\r\n\r\n\r\n    let model2=this.model(\"bk\");\r\n    let res=await model2.where({schoolid:user.school,del_flag:0,state:2,fbdate:{\">=\":datetime}}).select();\r\n\r\n\r\n    let model3=this.model(\"bk_user\");\r\n    for(let item of res){\r\n        let tmp=await model3.where({bkid:item.id,uid:user.id}).find();\r\n        if(think.isEmpty(tmp)){\r\n            //计算错题\r\n            let tklist=await this.model(\"bk_list\").where({bkid:item.id}).field(\"tkid\").select();\r\n            tklist=tklist.map(item=>item.tkid);\r\n            \r\n\r\n            let errorlist =await this.model(\"tk_record\").where({userid:user.id,flag:0,tkid:[\"in\",tklist]})\r\n            .where(\"create_date >= '\" + think.datetime(new Date(), 'YYYY-MM-01') + \"'\").field(\"tkid\").select();\r\n\r\n//计算讲解\r\n\r\n        let jjlist=await this.model(\"jj_record\").where({uid:user.id,tkid:[\"in\",tklist]}).field(\"tkid\").select();\r\n//计算分数段\r\n\r\n\r\n        let score=await this.model(\"lesson_user\").where({userid:user.id,lessonid:[\"in\",item.lesson.split(\",\")]}).field(\"score\").find();\r\n      \r\n\r\n        if(think.isEmpty(score)){\r\n            score=50;\r\n        }else{\r\n            score=score.score;\r\n        }\r\n\r\n\r\n        let max=score+10\r\n        let min=score-10\r\n\r\n        let scorelist=await this.model(\"tk\").where({lessonid:[\"in\",item.lesson.split(\",\")],score:[\"between\",[min,max]],\"id\":[\"in\",tklist]}).field(\"id\").select();\r\n\r\n\r\n        let para={};\r\n        para.bkid=item.id;\r\n        if(errorlist.length>0){\r\n            para.iferror=1;\r\n        }else{\r\n            para.iferror=0;\r\n        }\r\n        if(jjlist.length>0){\r\n            para.ifjj=1;\r\n        }else{\r\n            para.ifjj=0;\r\n        }\r\n        if(scorelist.length>0){\r\n            para.ifscore=1;\r\n        }else{\r\n            para.ifscore=0;\r\n        }\r\n\r\n        para.state=0;\r\n        para.uid=user.id;\r\n        para.create_date=think.datetime();\r\n        para.errorid=errorlist.map(item=>item.tkid).join(\",\");\r\n        para.jjid=jjlist.map(item=>item.tkid).join(\",\");\r\n        para.scoreid=scorelist.map(item=>item.id).join(\",\");\r\n        await model3.add(para);\r\n\r\n            item.state=1;\r\n        }\r\n    }\r\n    \r\n\r\n\r\n\r\n}\r\n\r\nasync listAction(){\r\n    let model=this.model(\"bk_user\")\r\n    let lessonid=this.post(\"lessonid\")\r\n    const user = await this.session('userInfo');\r\n    console.log(user);\r\n    \r\n    console.log(this.post())\r\n    \r\n    let where={};\r\n \r\n    where['c.schoolid']=user.school;\r\n    where['c.del_flag']='0';\r\n    where['c.state']=[\"!=\",1];\r\n    where['toplesson']=lessonid;\r\n    const page = this.post('page') ? this.post('page') : 1;\r\n    const rows = this.post('pageSize') ? this.post('pageSize') : 20;\r\n    let iferror=this.post(\"iferror\")\r\n    \r\n    let state=this.post(\"state\");\r\n    if(!think.isEmpty(state)){\r\n        if(state==1){\r\n            \r\n            where[\"c.state\"]=1\r\n        }else if(state==2){\r\n            \r\n             where[\"c.state\"]=[\"in\",\"2,3\"]\r\n        }\r\n        \r\n        \r\n    }\r\n    \r\n    if(!think.isEmpty(iferror)&&iferror==1){\r\n        \r\n        where[\"bu.iferror\"]=1\r\n    }\r\n      let ifjj=this.post(\"ifjj\")\r\n    \r\n    if(!think.isEmpty(ifjj)&&ifjj==1){\r\n        \r\n        where[\"bu.ifjj\"]=1\r\n    }\r\n    \r\n    \r\n    \r\n\r\n \r\n    let res=await model.alias(\"bu\").where(where).page(page, rows)\r\n    .join(\"left join sys_bk as c on c.id=bu.bkid\")\r\n    .join(\"left join sys_user t on t.id=c.teacherid\").field(\"bu.iferror,bu.ifjj,bu.ifscore,c.*,t.name,t.avatar,DATE_FORMAT(c.create_date, '%m-%d %H:%i') as d\")\r\n    .order(\"c.create_date desc\")\r\n    .countSelect();\r\n    let data={}\r\n\r\n\r\n\r\n    for(let item of res.data){\r\n            item.lessonname= item.lessonname.replace(/,/g, '<br>');\r\n            \r\n        \r\n    }\r\n\r\n\r\n\r\n    data.code = 200;\r\n    data.count = res.count;\r\n    data.data = res.data;\r\n    data.msg = '';\r\n\r\n\r\n\r\n\r\nreturn this.json(data);\r\n \r\n \r\n\r\n\r\n}\r\n\r\n\r\n    async infoAction(){\r\n        let model=this.model(\"bk_list\")\r\n        let id=this.post(\"id\")\r\n        let res=await model.alias(\"c\").join(\"sys_tk t on t.id=c.tkid\").where({bkid:id}).field(\"c.*,t.jx_p,t.tm_p\").select();\r\n\r\n\r\n        return this.json(res);\r\n\r\n\r\n    }\r\n    async listtmAction(){\r\n     let    res=await this.model(\"bk_list\").alias(\"c\").join(\"sys_tk t on t.id=c.tkid\").where({\"bkid\":this.post(\"id\")}).field(\"c.*,t.no,t.type\").select();\r\n        let id=this.post(\"id\");\r\n        let  user=await this.session(\"userInfo\");\r\n        let res2=await this.model(\"bk_user\").where({bkid:id,uid:user.id}).find();\r\n        \r\n        if(!think.isEmpty(res2)){\r\n            let errorlist=res2.errorid.split(\",\");\r\n            let jjlist=res2.jjid.split(\",\");\r\n            let scorelist=res2.scoreid.split(\",\");\r\n\r\n            res.forEach(item=>{\r\n                if(errorlist.includes(item.id)){\r\n                    item.error=1;\r\n                }\r\n                if(jjlist.includes(item.id)){\r\n                    item.jj=1;\r\n                }\r\n                if(scorelist.includes(item.id)){\r\n                    item.score=1;\r\n                }\r\n            })\r\n        }\r\n\r\n\r\n\r\n\r\n\r\n \r\n      \r\n        return this.json({data:res});\r\n    }\r\n\r\n    async countflagAction(){\r\n        const user = await this.session('userInfo');\r\n        let model=this.model(\"bk_countflag\");\r\n        let data={};\r\n        data.userid=user.id;\r\n        data.lasttime=think.datetime();\r\n        await model.where({userid:user.id}).delete();\r\n        await model.add(data);\r\n        return this.json({code:200,msg:\"\",data:{}});\r\n    }\r\n\r\n    async countAction(){\r\n\r\n        const user = await this.session('userInfo');\r\n        let model=this.model(\"bk_countflag\");\r\n        let datetime=think.datetime();\r\n       let data=await model.where({userid:user.id}).find();\r\n       if(!think.isEmpty(data)){\r\n        datetime=data.lasttime;\r\n       } \r\n       let model2=this.model(\"bk\");\r\n       let res=await model2.where({schoolid:user.school,del_flag:0,state:2,fbdate:{\">=\":datetime}}).count();\r\n       return this.json({code:200,msg:\"\",data:{count:res}});\r\n\r\n\r\n\r\n\r\n    }\r\n\r\n}"]}