import config from "@/config";
import http from "@/utils/request";

const advertisement = {
 
	page: {
		url: `${config.API_URL}/buss/advertisement/page`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
    save: {
		url: `${config.API_URL}/buss/advertisement/save`,
		name: "保存",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},  
    delete: {
		url: `${config.API_URL}/buss/advertisement/delete`,
		name: "删除",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},
	upload: {
		url: `${config.API_URL}/common/upload`,
		name: "文件上传",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
    update: {
		url: `${config.API_URL}/buss/advertisement/update`,
		name: "更新",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},
    batchDel: {
		url: `${config.API_URL}/buss/advertisement/batchDel`,
		name: "批量删除",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},
    changeStatus: {
		url: `${config.API_URL}/buss/advertisement/changeStatus`,
		name: "修改状态",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	}
}

export default advertisement;