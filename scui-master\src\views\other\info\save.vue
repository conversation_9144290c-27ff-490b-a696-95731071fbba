<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/10 15:27:13
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="《代销合同》签订时间" prop="contract_date">
					<el-date-picker
						v-model="form.contract_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="《代销合同》签订人" prop="contract_name">
					<el-input v-model="form.contract_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="《销售许可证》发证日期" prop="sell_date">
					<el-date-picker
						v-model="form.sell_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="《销售许可证》有效期限" prop="sell_valid">
					<el-input v-model="form.sell_valid" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="销售保证金额" prop="sell_money">
					<el-input v-model="form.sell_money" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="销售保证金姓名" prop="sell_money_name">
					<el-input v-model="form.sell_money_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="销售保证金时间" prop="sell_money_date">
					<el-date-picker
						v-model="form.sell_money_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",

				contract_date: "",

				contract_name: "",

				sell_date: "",

				sell_valid: "",

				sell_money: "",

				sell_money_name: "",

				sell_money_date: "",

			},
			//验证规则
			rules: {

				contract_date: [
					{required: true, message: '请输入《代销合同》签订时间'}
				],

				contract_name: [
					{required: true, message: '请输入《代销合同》签订人'}
				],

				sell_date: [
					{required: true, message: '请输入《销售许可证》发证日期'}
				],

				sell_valid: [
					{required: true, message: '请输入《销售许可证》有效期限'}
				],

				sell_money: [
					{required: true, message: '请输入销售保证金额'}
				],

				sell_money_name: [
					{required: true, message: '请输入销售保证金姓名'}
				],

				sell_money_date: [
					{required: true, message: '请输入销售保证金时间'}
				],

			},
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
