


<template>


<el-main>
		<el-card shadow="never">
			<el-tabs tab-position="top">

				<el-tab-pane label="上课设置">
	<el-form :model="form" :rules="rules"  ref="dialogForm" label-width="200px" >
			
		
	
    

      <el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="上课信息" prop="skinfo">
					
					 <vue-ueditor-wrap  style="width:800px"   v-model="form.skinfo" editor-id="xiumi-demo-01" :config="editorConfig"
                        @ready="ueReady"></vue-ueditor-wrap>
					</el-form-item>
				</el-col>
			
			</el-row>


            		<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>

   

            	
 
		</el-form>
				</el-tab-pane>
            </el-tabs>
        </el-card>
</el-main>

	
 
	
	



    <dialog1
      v-if="asynDialog1"
      ref="dialog1"
      @success="handleSuccess2"
      @closed="asynDialog1 = false"
    ></dialog1>

    <dialog2
      v-if="asynDialog2"
      ref="dialog2"
      @success="handleSuccess3"
      @recive="recive"
      @closed="asynDialog2 = false"
    ></dialog2>
    <up-dialog
      v-if="dialog.upload"
      ref="upDialog"
      @success="handleSuccess"
      @closed="dialog.upload = false"
    ></up-dialog>
     
</template>

<script>
import upDialog from "@/views/buss/school/upload";
import { defineAsyncComponent } from "vue";
	export default {
         components: {
      
        dialog1: defineAsyncComponent(() => import("@/views/buss/school/uploadfile")),
        dialog2: defineAsyncComponent(() => import("@//views/buss/school/file")),
        upDialog,
    },
		emits: ['success', 'closed'],
		data() {
			return {
                 uploadApi:this.$API.common.upload,
                 ue: null,
            asynDialog1: false,
            asynDialog2: false,
            activeName: "first",
            dialog: {
        upload: false,
      },
    
            editorConfig: null,
				mode: "add",
				titleMap: {
					add: '新增',
					edit: '编辑',
					show: '查看'
				},
				visible: false,
				isSaveing: false,
				//表单数据
				form: {
				 
					skinfo:"",
                  
					 
				},
				//验证规则
				rules: {
					 
				 
			 
                   
					
				},
				//所需数据选项
				groups: [],
				groupsProps: {
				value: "id",
				multiple: false,
				label: "name",
				checkStrictly: true
				}
			}
		},
		mounted() {
            this.setData();
			
		},
        created() {
        this.init();

    },
		methods: {

             recive(obj, type, flag) {

            var html = "";
            if (type == 1) {
                if (flag) {
                    obj.forEach((item, index) => {
                        console.log(index);
                        var js_target = " target='_blank'";
                        var picAllPath = item.path;

                        var js_hyperlink = " href='" + this.$CONFIG.FRONT_URL + item.path + "'";
                        if (item.compress) {
                            html +=
                                '<a "' +
                                js_hyperlink +
                                js_target +
                                '"><img style="border:none;" src="' + this.$CONFIG.FRONT_URL +
                                item.compress +
                                '"/></a>';
                        } else {
                            html +=
                                '<img style="border:none;max-width:1000px" src="' + this.$CONFIG.FRONT_URL +
                                picAllPath +
                                '"/>';
                        }
                        html += "<br/	>";
                    });

                    this.ue.execCommand("inserthtml", html);
                } else {
                    if (obj[0]) {
                        if (obj[0].compress) {
                            this.form.img = this.$CONFIG.FRONT_URL + obj[0].compress;
                        } else {
                            this.form.img = this.$CONFIG.FRONT_URL + obj[0].path;
                        }
                    }
                }
            }

            if (type == 2) {
                if (flag) {
                    obj.forEach((item, index) => {
                        console.log(index);

                        html +=
                            '<div style="text-align:center"><center><video controls="controls" src="' + this.$CONFIG.FRONT_URL +
                            item.path +
                            '"';

                        html += ' width="90%"';

                        html += ' height="90%"';

                        html += "></video></center>";
                        html +=
                            '<p style="text-align: center; line-height: 33px; font-size: 14px;">\n' +
                            '    <span style="text-align: center; color: rgb(84, 141, 212); font-size: 13.33px;">点击播放视频</span><strong><span style="letter-spacing: 3px; font-family: 方正小标宋简体; font-size: 29px;"><span style="font-family: 方正小标宋简体;"></span></span></strong><br/>\n' +
                            "</p></div>";

                        html += "<br/	>";
                    });

                    this.ue.execCommand("inserthtml", html);
                } else {
                    if (obj[0]) {
                        this.form.video = this.$CONFIG.FRONT_URL + obj[0].path;
                    }
                }
            }

            if (type == 3) {
                if (flag) {
                    var img = "";

                    obj.forEach((item, index) => {
                        console.log(index);
                        if (item.suffix == "pdf") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_pdf.gif">';
                        } else if (item.suffix == "doc" || item.suffix == "docx") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_doc.gif">';
                        } else if (item.suffix == "xls" || item.suffix == "xlsx") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_xls.gif">';
                        } else if (item.suffix == "ppt" || item.suffix == "pptx") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_ppt.gif">';
                        } else if (item.suffix == "txt") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_txt.gif">';
                        } else if (item.suffix == "zip" || item.suffix == "rar") {
                            img =
                                '<img style="border:none;" src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_rar.gif">';
                        } else {
                            img =
                                '<img src="' +
                                this.$CONFIG.FRONT_URL +
                                '/ueditor/dialogs/attachment/filetypeimages/icon_default.png">';
                        }

                        html +=
                            '<a href="' + this.$CONFIG.FRONT_URL + item.path + '">' + img + item.file_name + "</a>";
                        html += "<br/	>";
                    });

                    this.ue.execCommand("inserthtml", html);
                }

            }

            if (type == 4) {


                if (obj[0]) {
                    this.form.officeurl = this.$CONFIG.FRONT_URL + obj[0].path;
                }
            }
            else {
                if (obj[0]) {
                    this.form.file = this.$CONFIG.FRONT_URL + obj[0].path;
                }
            }
        },

  openfiledialog(flag, type) {
            this.asynDialog2 = true;
            this.flag = flag;
            this.$nextTick(() => {
                this.$refs.dialog2.open().setData({ flag: flag, type: type });
            });
        },

        uploadimg() {
            this.dialog.upload = true;

            this.$nextTick(() => {
                this.$refs.upDialog.open("add");
            });
        },

        uploadfile(type) {
            this.asynDialog1 = true;

            this.$nextTick(() => {
                this.$refs.dialog1.open(type);
            });
        },

        handleSuccess(obj) {
            this.form.img = obj.img;
        },

        handleSuccess2(obj) {
            if (obj.type == "video") {
                this.form.video = obj.file;
            } else {
                this.form.file = obj.file;
            }
        },

        init() {
                


                    this.editorConfig = {
                        UEDITOR_HOME_URL: window.location.origin +'/admin/UEditor/', // 访问 UEditor 静态资源的根路径，可参考常见问题1
                        serverUrl: '/api/common/uploadue', // 服务端接口（这个地址是我为了方便各位体验文件上传功能搭建的临时接口，请勿在生产环境使用！！！）};
                        catchRemoteImageEnable: true, // 抓取远程图片
                        
                    };
                    this.editorDependencies = [
                        'ueditor.config.js',
                        'ueditor.all.min.js'
                    ]

                
                },
            ueReady(editorInstance) {
                        let that = this;
                        this.ue = editorInstance;
                        editorInstance.commands["newupload"] = {
                            execCommand: function () {
                                that.openfiledialog(true, 1);
                            },
                            queryCommandState: function () { },
                        };

                        editorInstance.commands["fileupload"] = {
                            execCommand: function () {
                                that.openfiledialog(true, 3);
                            },
                            queryCommandState: function () { },
                        };

                        editorInstance.commands["newvideo"] = {
                            execCommand: function () {
                                that.openfiledialog(true, 2);
                            },
                            queryCommandState: function () { },
                        };

                        editorInstance.setHeight(
                        500
                        );
                    },

          
			//显示
			open(mode='add'){
				this.mode = mode;
				this.visible = true;
				return this
			},
		
			//表单提交方法
			submit(){
				this.$refs.dialogForm.validate(async (valid) => {
					if (valid) {
						this.isSaveing = true;
		
				 
						 var res = await this.$API.teacher.skinfo.post(this.form);
						 this.isSaveing = false;
					if(res.code == 200){
							this.$emit('success', this.form, this.mode)
							this.visible = false;
							this.$message.success("操作成功")
						}else{
							this.$alert(res.message, "提示", {type: 'error'})
						}

			 

						
					}
				})
			},
			//表单注入数据
			async setData( ){
			 
				
				 
				let resp = await this.$API.teacher.myinfo.post( )
			 
				
					Object.assign(this.form, resp)
				
			}
		}
	}
</script>

<style>
</style>
