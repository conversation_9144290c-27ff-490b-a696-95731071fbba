<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="登录账号" prop="login_name">
						<el-input v-model="form.login_name" placeholder="用于登录系统" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="姓名" prop="name">
						<el-input v-model="form.name" placeholder="请输入完整的真实姓名" clearable></el-input>
					</el-form-item>
				</el-col>
		
			</el-row>
			<el-row :gutter="20" v-if="mode=='add'">
				<el-col :span="12">
					<el-form-item label="登录密码" prop="password">
						<el-input type="password" v-model="form.password" clearable show-password></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="确认密码" prop="password2">
						<el-input type="password" v-model="form.password2" clearable show-password></el-input>
					</el-form-item>
				</el-col>
			</el-row>


			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="所属学校" prop="schoolid">
						<el-cascader
							v-model="form.schoolid"
							:options="schoolList"
							:props="schoolProps"
							:show-all-levels="false"
							clearable
							style="width: 100%;"
						></el-cascader>
					</el-form-item>
				</el-col>
			</el-row>


           <el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="负责学科课程" prop="lesson">
						<el-cascader v-model="form.lesson" :options="groups" :props="groupsProps" :show-all-levels="false"
						             clearable style="width: 100%;"></el-cascader>
					</el-form-item>
				</el-col>
			</el-row>
	
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
			mode: "add",
			titleMap: {
				add: '新增教师',
				edit: '编辑教师',
				show: '查看'
			},
			schoolList: null,
			visible: false,
			isSaveing: false,
			//菜单树配置
			schoolProps: {
				value: "id",
				emitPath: false,
				label: "name",
				checkStrictly: true,
			},
			//表单数据
			form: {
				id: "",
				login_name: "",
				name: "",
				group: "",
				schoolid: "",
				user_level: "",
			},
			//验证规则
			rules: {
				login_name: [
					{required: true, message: '请输入登录账号'}
				],
				name: [
					{required: true, message: '请输入真实姓名'}
				],
				schoolid: [
					{required: true, message: '请选择所属学校'}
				],
				password: [
					{required: true, message: '请输入登录密码'},
					{
						validator: (rule, value, callback) => {
							if (this.form.password2 !== '') {
								this.$refs.dialogForm.validateField('password2');
							}
							callback();
						}
					}
				],
				password2: [
					{required: true, message: '请再次输入密码'},
					{
						validator: (rule, value, callback) => {
							if (value !== this.form.password) {
								callback(new Error('两次输入密码不一致!'));
							} else {
								callback();
							}
						}
					}
				]
				
			},
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		//加载树数据
		async getGroup() {
			
			var lesson = await this.$API.teacher.lesson.get();
			this.groups = lesson;

			this.schoolList= await this.$API.school.listselect.get();
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					var res = await this.$API.teacher.save.post(this.form);
					this.isSaveing = false;
					if (res.code == 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			this.form.id = data.id
			this.form.login_name = data.login_name
			this.form.name = data.name
		 	this.form.lesson=data.lesson;
			
			this.form.schoolid = parseInt(data.schoolid);
			//this.menuList = data.office;


		}
	}
}
</script>

<style>
</style>
