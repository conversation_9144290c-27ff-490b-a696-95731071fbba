import config from "@/config";
import http from "@/utils/request";

export default {
	getOpenId: {
		url: `${config.API_URL}/public/getopenid`,
		name: "获取openid",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	getChildOffice: {
		url: `${config.API_URL}/public/getchildoffice`,
		name: "获取子部门",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	getDingUser: {
		url: `${config.API_URL}/public/getdinguser`,
		name: "获取钉钉用户信息",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
}
