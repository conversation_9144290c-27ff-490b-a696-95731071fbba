<template>
  <el-container>
    <el-container>
      <el-header class="head">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="学科:" prop="parent">
              <el-cascader
                v-model="form.parent"
                :options="menuList"
                :props="menuProps"
                :show-all-levels="false"
                @change="selectkm"
                clearable
              ></el-cascader>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="最大编号:" prop="maxnum"
              ><el-text class="mx-1" type="primary">{{
                maxnum
              }}</el-text></el-form-item
            ></el-col
          >

          <el-col :span="6"> </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6"
            ><el-form-item label="知识点:" prop="maxnum">
              <el-button-group>
                <el-button type="info">图片{{ num }}</el-button>
                <el-button type="info"> 视频 {{ num2 }} </el-button>
              </el-button-group></el-form-item
            ></el-col
          >
          <el-col :span="6"
            ><el-button type="primary" icon="el-icon-plus" @click="zsd"
              >上传知识点</el-button
            ></el-col
          >
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-plus" @click="uploadzip"
              >上传压缩包</el-button
            >
          </el-col>

          <el-col :span="6">
            <el-button type="primary" icon="el-icon-delete" @click="batch_del"
              >批量删除</el-button
            >
          </el-col>
        </el-row>
      </el-header>
      <el-main class="nopadding">
        <scTable
          ref="table"
          :apiObj="apiObj"
		  :params="params"
          @selection-change="selectionChange"
          stripe
          remoteSort
          remoteFilter
        >
          <el-table-column type="selection" width="50"></el-table-column>
         

          <el-table-column
            label="编号"
            prop="no"
            width="150"  sortable='custom'
            
          ></el-table-column>
           <el-table-column label="类型" prop="type" width="150"></el-table-column> 
             <el-table-column
            label="评分"
            prop="score"
            width="150"  sortable='custom'
          ></el-table-column>
          <el-table-column label="题目" prop="tm" width="550"></el-table-column>
        

          <el-table-column label="操作" fixed="right" align="right" width="140">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="table_edit(scope.row, scope.$index)"
                >编辑
              </el-button>
              <el-popconfirm
                title="确定删除吗？" v-if="isadmin"
                @confirm="table_del(scope.row, scope.$index)"
              >
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
  </el-container>

  <upload-dialog
    v-if="dialog.upload"
    ref="uploadDialog"
    @success="handleSuccess"
    @closed="dialog.upload = false"
  ></upload-dialog>

  <edit-dialog
    v-if="dialog.edit"
    ref="editDialog"
    @success="handleSuccess"
    @closed="handleSuccess2"
  ></edit-dialog>

  <zsd-dialog
    v-if="dialog.zsd"
    ref="zsdDialog"
    @success="handleSuccess"
    @closed="handleSuccess2"
  ></zsd-dialog>

</template>

<script>
import uploadDialog from "./upload";
import editDialog from "./save";
import zsdDialog from "./zsd";
export default {
  name: "user",
  components: {
    uploadDialog,
    editDialog,
	zsdDialog
  },
  data() {
    return {
      isadmin:false,
      dialog: {
        edit: false,
        upload: false,
		zsd:false
      },
      maxnum: 0,
      num: 0,
      num2: 0,
	  params:{
		where:{}

	  },
      form: {
        parent: "",
      },
      menuList: [],

      showGrouploading: false,
      groupFilterText: "",
      group: [],
      apiObj: this.$API.tk.page2,
      selection: [],
      search: {
        name: null,
      },
      defaultProps: {
        label: "name",
      },
      menuProps: {
        value: "id",
        emitPath: false,
        label: "title",
        checkStrictly: true,
      },
    };
  },
  watch: {
    groupFilterText(val) {
      this.$refs.group.filter(val);
    },
  },
  async mounted() {
    var res = await this.$API.lesson.list.get();
    this.menuList = res;

let user= await this.$API.user.currinfo.post();
		if(user.data.login_name=="admin"){
			this.isadmin=true;
		}

    this.getGroup();
  },
  methods: {
    //添加

	async zsd(){
		this.dialog.zsd = true;
		if(this.form.parent>0){

			this.$nextTick(() => {
				this.$refs.zsdDialog.open().setData({"lessonid":this.form.parent});
			});
		}else{
			this.$alert("请先选择学科", "提示", { type: 'error' })

		}


   
	},

  async batch_del(){
				this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？`, '提示', {
					type: 'warning'
				}).then(async () => {
					const loading = this.$loading();
					this.selection.forEach(async(item) =>{
				
							  

                var res = await this.$API.tk.remove.post({id:item.id});
						
					})

         this.$refs.table.refresh();
					loading.close();
					this.$message.success("操作成功")
				}).catch(() => {

				})
			},
    async add() {
      this.dialog.save = true;
      //	let row={};

      //	row.office = office
      this.$nextTick(() => {
        this.$refs.saveDialog.open("add");
      });
    },
    //编辑
    async table_edit(row) {
      this.dialog.edit = true;

      this.$nextTick(() => {
        this.$refs.editDialog.open().setData(row);
      });
    },
    //查看

    //删除
    async table_del(row, index) {
      var reqData = { id: row.id };
      var res = await this.$API.tk.remove.post(reqData);
      if (res.code == 200) {
        //这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功");
      } else {
        this.$alert(res.message, "提示", { type: "error" });
      }
    },

    //表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    //加载树数据
    async getGroup() {
      var res = await this.$API.role.select.get();
      this.showGrouploading = false;
      ///res.data.unshift(allNode);
      this.group = res;
    },
    //树过滤
    groupFilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    //树点击事件
    groupClick(data) {
      var params = {
        roleid: data.id,
      };
      this.$refs.table.upData(params);
    },
    //搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },

    uploadzip() {
      this.dialog.upload = true;
      this.$nextTick(() => {
        this.$refs.uploadDialog.open().setData({ lessonid: this.form.parent });
      });
    },
    //本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    handleSuccess2() {
      this.dialog.edit = false;
      this.$refs.table.refresh();
    },

    async selectkm(data) {
      let res = await this.$API.tk.getnum.get({ lessonid: data });

	  let numres=await this.$API.zsd.num.get({ lessonid: data });
      this.num=numres.num
	  this.num2=numres.num2
      this.maxnum = res.num;
	  this.params.where.lessonid=data;
	  this.$refs.table.refresh();
    },

    
  },
};
</script>

<style>
.head {
  height: 110px;
  align-items: normal !important;
  display: inline !important;
}
</style>
