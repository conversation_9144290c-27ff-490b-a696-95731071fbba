<template>
	<el-dialog
		title="角色权限设置"
		v-model="visible"
		:width="500"
		destroy-on-close
		@closed="$emit('closed')"
		id="create-ticket"
	>
		<el-tabs tab-position="top">
			<el-tab-pane label="菜单权限">
				<div class="treeMain">
					<el-tree
						ref="menu"
						node-key="id"
						:data="menu.list"
						:default-checked-keys="menu.checked"
						:default-expand-all="true"
						:props="menu.props"
						show-checkbox
						check-strictly=true
					></el-tree>
				</div>
			</el-tab-pane>

			<el-tab-pane label="数据权限">
				<el-form label-width="100px" label-position="left">
					<el-form-item label="选择数据权限">
						<sc-select v-model="dataScope" placeholder="请选择"
						           :selectConfig="selectConfig.roleDataScope"
						           clearable dic="roleDataScope"
						           filterable></sc-select>
						<div class="el-form-item-msg">用于控制角色可以看到的数据权限</div>
					</el-form-item>
					<el-form-item label="选择部门">
						<div class="treeMain">
							<el-tree
								ref="office"
								node-key="id"
								:data="office.list"
								:default-checked-keys="office.checked"
								:default-expand-all="true"
								:props="office.props"
								show-checkbox
							></el-tree>
						</div>
					</el-form-item>
				</el-form>
			</el-tab-pane>
		</el-tabs>
		<template #footer>
			<el-button @click="visible = false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()"
			>保 存
			</el-button
			>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ["success", "closed"],
	data() {
		return {
			replaceFields:{children:'children', title:'name', key:'id', value: 'id' },
			value:null,
			roleid: "",
			visible: false,
			isSaveing: false,
			dataScope: 1,
			show:{
				office:false
			},
			menu: {
				list: [],
				checked: ["system", "user"],
				props: {
					label: (data) => {
						return data.title;
					}
				}
			},
			office: {
				list: [],
				checked:[],
				props: {
					label: (data) => {
						return data.name;
					}
				}
			},
			dashboard: "0",
			//下拉选择配置
			selectConfig: {
				roleDataScope: {
					label: 'name',
					value: 'key'
				}
			}
		};
	},
	mounted() {
		this.getMenu();
		this.getOffice();
	},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		dataChange(data) {
			if (data==2) {
				this.show.office=true;
			}
			else {
				this.show.office=false;
			}
		},
		async submit() {

			this.isSaveing = true;
			let dadata_office_scope=""
			this.$refs.office.getCheckedKeys().map(r=>{
				dadata_office_scope+=r+","
			})
			let data = {
				id: this.roleid,
				menus: this.$refs.menu.getCheckedKeys(),
				data_scope: this.dataScope,
				data_office_scope:dadata_office_scope
			};
			var res = await this.$API.role.savep.post(data);

			if (res.code == 200) {
				this.$emit("success", this.form, this.mode);
				this.visible = false;
				this.$message.success("操作成功");
			} else {
				this.$alert(res.message, "提示", {type: "error"});
			}
		},
		async getMenu() {
			var res = await this.$API.menu.list.get();
			this.menu.list = res;
		},

		async getOffice() {
			var res = await this.$API.office.tree.get()
			this.office.list = res;
		},

		async setData(data) {
			this.roleid = data.id;
			this.dataScope = data.data_scope;
			if (data.data_scope==2){
				this.show.office=true;
			}
			var res = await this.$API.role.getmenus.post({id: this.roleid});
			this.menu.checked = res.list;
			data.data_office_scope= data.data_office_scope.substring(0, data.data_office_scope.lastIndexOf(','));
			this.office.checked =data.data_office_scope.split(",");
		}
	}
};
</script>

<style scoped>
.treeMain {
	height: 280px;
	overflow: auto;
	border: 1px solid #dcdfe6;
	margin-bottom: 10px;
}
</style>
