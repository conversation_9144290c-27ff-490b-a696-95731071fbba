import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/site/equipment_repair/page`,
		name: "设备维修数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/site/equipment_repair/add`,
		name: "设备维修数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/site/equipment_repair/save`,
		name: "设备维修数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/site/equipment_repair/info`,
		name: "设备维修数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/site/equipment_repair/delete`,
		name: "删除设备维修数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
