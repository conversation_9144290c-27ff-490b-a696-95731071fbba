<template>
    <el-dialog :title="titleMap[mode]" v-model="visible" :width="800" destroy-on-close @closed="$emit('closed')">
        <el-form :model="form" :rules="rules" ref="dialogForm" label-width="80px" label-position="left">

       
			 
            <el-col :span="24">
            <el-form-item label="图片" prop="pic">
                <sc-upload-file v-model="form.pic" title="题干照片"  
					:apiObj="uploadApi"  :limit="1"  :maxSize="100">
                    <el-button type="primary" icon="el-icon-upload">上传图片</el-button>
                </sc-upload-file>
		

            </el-form-item>

        </el-col>


        </el-form>
        <template #footer>
            <el-button @click="visible = false">取 消</el-button>
            <el-button type="primary" :loading="isSaveing" @click="submit()">保存</el-button>
        </template>
    </el-dialog>
   


 

</template>




<script>
export default {
    emits: ['success', 'closed'],
    data() {
        return {
            mode: "add",
            titleMap: {
                add: '上传',
                edit: '编辑'
            },
            btnloading:false,
            visible: false,
            isSaveing: false,
            menuList: [],
            fileViewList: [],
            uploadApi: this.$API.common.upload,

            ifprocess:false,
            form: {
                id: "",
                name: "",
                video:"",
                pic:"",
                lessonid:0
            },
            rules: {
                name: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],

               
            },
            dict: [],
            dicProps: {
                value: "id",
                label: "name",
                checkStrictly: true,
                emitPath: false
            },
            props: {
                menu: {
                    type: Object, default: () => {
                    }
                }
            }
            ,
            selectConfig: {
				 
				score: {
					label: 'name',
					value: 'name'
				},
			},
            menuProps: {
                value: "id",
                emitPath: false,
                label: "title",
                checkStrictly: true
            },
        }
    },
    mounted() {
      
    },
    methods: {

   
        fileSuccess(response) {
            const suffix = response.data.file_name.substr(
                response.data.file_name.lastIndexOf(".") + 1
            ); // 文件后缀
            this.fileViewList.push({
                suffix: suffix,
                name: response.data.file_name,
                url: response.data.src,
                new_name: response.data.new_name,
                id: response.data.new_name,
            });
            this.$message.success(`文件上传成功`);
            this.ifprocess=false;
            return false;
        },
        beforeRemove(file) {
            this.fileViewList.map((r, index) => {
                if (r.name == file.name) {
                    this.form.files = this.form.files.replace(
                        "/static/upload/" + file.name + ",",
                        ""
                    );
                    this.fileViewList.splice(index, 1);
                }
            });
        },
      

        //显示
        open(mode = 'add') {
            this.mode = mode;
            this.visible = true;
            return this;
        },
    
        //表单提交方法
        submit() {

           
                this.$refs.dialogForm.validate(async (valid) => {
                if (valid) {
                    this.isSaveing = true;

                    let res = await this.$API.tk.updatepic.post(this.form);
                    this.isSaveing = false;
                    if (res.state == 1) {
                       
                        this.$emit('success', this.form, this.mode)
                        this.visible = false;
                        this.$message.success("操作成功")
                    } else {
                        this.$alert(res.msg, "提示", { type: 'error' })
                    }
                }
            })
           
        },
        //表单注入数据
        setData(data, mode) {
             
            Object.assign(this.form, data)
         
             
        }
    }
}
</script>

<style></style>
