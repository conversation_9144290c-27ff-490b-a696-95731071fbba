<template>
  <el-dialog
    :title="titleMap[params.type]"
    v-model="visible"
    append-to-body="true"
    :width="900"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-container class="layout">
      <el-container>
        <el-main class="nopadding">
          <scTable
            ref="table"
            :apiObj="apiObj"
            :hideDo="true"
            :params="params"
            @currentChange="change"
            @selection-change="selectionChange"
            stripe
            style="height:600px;"
            remoteSort
            remoteFilter
          >
            <el-table-column type="selection" width="50" v-if="flag"></el-table-column>

            <el-table-column
              label="文件名称"
              prop="file_name"
              width="250"
            ></el-table-column>
            <el-table-column
              label="类型"
              prop="suffix"
              width="50"
            ></el-table-column>
            <el-table-column
              label="上传时间"
              prop="create_date"
              width="150"
              sortable="custom"
            ></el-table-column>
          </scTable>
        </el-main>

        <el-aside
          style="
            width: 300px;
            border-left: 1px solid #e6e6e6;
            text-align: center;
          "
        >
          <el-upload
            class="upload-demo"
            drag
            :show-file-list="false"
            :http-request="request"
            :on-success="success"
            :accept="accept"
            multiple
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">注：上传文件不超过 300M</div>
            </template>
          </el-upload>
<hr>
          <el-image
            v-if="ifshow"
            :src="prewurl"
            ref="pre"
            :fit="fit"
            style="width: 200px; height: 200px; padding-top: 15px"
          >
            <template #error>
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </template>
          </el-image>

          <el-button
            v-if="!ifshow"
            type="primary"
            @click="down()"
            style="margin-top: 15px"
            >下载<i class="el-icon-paperclip el-icon--right"></i
          ></el-button>


          <el-row  v-if="params.type==2">
              <el-col :span="24">
  <video :src="videoUrl"  controls  class="video" 
         width="200" style="width:200px"></video>
              </el-col> 
          </el-row>
        

 <el-row v-if="params.type==1">

     <el-col :span="6">

        <p style="padding-top: 5px;">  图片尺寸</p>
  </el-col>
     <el-col :span="10">

        
  
<el-select v-model="value" placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select></el-col>
</el-row>


<el-row style="    margin-top: 15px;" v-if="value==2">
       <el-col :span="6">

      <p style="padding-top: 5px;">  固定宽度</p>
  </el-col>
  <el-col :span="8">
        <el-input v-model="width" ></el-input>
  </el-col>
   <el-col :span="2">

        <p style="padding-top: 5px;">  px</p>
   </el-col>
</el-row>
 


        </el-aside>
      </el-container>
    </el-container>

    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="mode != 'show'"
        type="primary"
        :loading="isSaveing"
        @click="select()"
        >选择</el-button
      >
    </template>
  </el-dialog>
</template>

<script>
import Editor from '../../vab/editor.vue';
export default {
  components: {Editor},
  emits: ["success", "closed"],

  data() {
    return {
        flag:true,
        filelist:[],
       isSaveing:false,
        videoUrl:"",
        value:"1",
        width:800,
        	titleMap: {
				1: '图片上传',
				2: '视频上传',
				3: '附件上传',
        	4: 'office文件上传'
			},
        accept:".jpg,.png,.jpeg",
         options: [
          {
            value: '1',
            label: '原图尺寸',
          },
          {
            value: '2',
            label: '指定尺寸',
          },
         
        ],
      ifshow: true,
      customerid: "",
      dialog: {
        save: false,
      },
      prewurl: "http://1.c",
      downurl: "",
      mode: "add",
      apiObj: this.$API.file.list2,
      visible: false,
      params: {
        type: "1",
      },
    };
  },
  mounted() {},
  methods: {
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },

    request(param) {
      let apiObj = this.$API.common.upload;

      const data = new FormData();
      var file = param.file;
      data.append("file", file);
      apiObj
        .post(data)
        .then((res) => {
          param.onSuccess(res);
        })
        .catch((err) => {
          param.onError(err);
        });
    },

    change(obj) {
        if(!this.flag){
            this.selection=[];
            this.selection.push(obj);
        }   

      if (obj) {
        if (
          obj.suffix == "jpg" ||
          obj.suffix == "png" ||
          obj.suffix == "jpeg"
        ) {
          this.prewurl = obj.path;
          this.ifshow = true;
        } else {
          this.downurl = obj.path;
          this.ifshow = false;
        }

        if (
          obj.suffix == "mp4" ||
          obj.suffix == "mov" 
        ) {
           
         this.videoUrl=obj.path;
        }



      }
    },
    //表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    //搜索
   async select() {


      if(this.selection){
	this.isSaveing = true;
        
      if(this.params['type']==4){
        console.log({"path":this.selection.path,name:this.selection.id,suffix:this.selection.suffix})
      let res=     await this.$API.file.topdf.post({"path":this.selection[0].path,name:this.selection[0].id,suffix:this.selection[0].suffix});
      this.selection[0].path2=this.selection[0].path;
      this.selection[0].path=res;
    	this.isSaveing = false;
        
        

          this.$emit("recive", this.selection,this.params.type,this.flag);
         

           this.$emit('closed')
          return ;
        }

        if(this.value=="1"){
            this.$emit("recive", this.selection,this.params.type,this.flag);
        }else{

					this.isSaveing = true;

          let res=       await this.$API.file.compress.post({"width":this.width,"json":JSON.stringify(this.selection)});
          
					this.isSaveing = false;

         this.$emit("recive", res,this.params.type,this.flag);
        
        }
        this.$emit('closed')
      }else{
        this.$alert("未选中文件", "提示", { type: "error" });

      }

      
      
      
    },
    down() {
      let a = document.createElement("a");
      a.href = this.downurl;
      a.target = "_blank";
      a.click();
    },

    upsearch() {
      this.$refs.table.upData(this.search);
    },
    //本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    success() {
      
      this.$refs.table.upData(this.search);
    },
    //表单注入数据
    async setData(data) {
      this.flag = data.flag;
      this.params['type']=data.type;
      if(data.type=="1"){
          this.accept=".png,.jpg,.jpeg"

      }
      if(data.type=="2"){
          this.accept=".mp4,.mov,.mp3"
      }
        if(data.type=="3"){
          this.accept=".mp4,.mov,.mp3,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar"
      }

       if(data.type=="4"){
          this.accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,"
      }
    },
  },
};
</script>
<style  lang="less">
.upload-demo .el-upload-dragger {
  width: 260px !important;
}
</style>
