module.exports = class extends think.Controller {

    async indexAction(){


    }

    async tongjiAction(){
        const user = await this.session('userInfo');
        let model=this.model("tk");
        let sql="SELECT	DATE_FORMAT( a.timeDay, '%m-%d' ) AS day,  IFNULL( b.count, 0) AS count FROM( SELECT  curdate() AS timeDay UNION ALL  SELECT  date_sub(curdate(), INTERVAL 1 DAY ) AS timeDay  UNION ALL	SELECT  date_sub(curdate(),INTERVAL 2 DAY) AS timeDay	 UNION ALL	SELECT  date_sub( curdate(), INTERVAL 3 DAY ) AS timeDay UNION ALL	SELECT		date_sub( curdate(), INTERVAL 4 DAY ) AS timeDay		UNION ALL	SELECT		date_sub( curdate(), INTERVAL 5 DAY ) AS timeDay 	UNION ALL	SELECT date_sub( curdate(), INTERVAL 6 DAY ) AS timeDay ) a LEFT JOIN (	SELECT		date( d.create_date ) AS day,		count(1) AS count FROM sys_tk_record d  WHERE d.userid="+user.id+"	GROUP BY date( d.create_date ) 	) b ON a.timeDay = b.day ORDER BY	day";
        let res=await model.query(sql);

        let categoriesarr=[],dateary=[];

        for(let i of res){

            categoriesarr.push(i.count);
            dateary.push(i.day);
        }

        let ress = {
            categories: dateary,
            series: [{
                name: "练习量",
                data: categoriesarr
            }]
        };

        console.log(ress)

        this.json(ress);

    }

    async regAction(){
        let smscode=this.post("code");

    let model2=this.model("sms");


    let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 3 MINUTE) and mobile =%s and code =%s";

    

    let sqls = model2.parseSql(sql, this.post("mobile"),smscode);

   

    let res= await model2.query(sqls);

    if(res.length>0){

      let data={};
      let model=this.model("student");
    
      data.phone=this.post("mobile");
      data.del_flag=0;
      data.create_date=think.datetime();
      data.password=think.md5(this.post("repassword"));
  
      data.id=think.uuid();
      data.type="试用账号"
      const currentDate = new Date();



      data.todate=think.datetime(currentDate.getTime() + 6 * 24 * 60 * 60 * 1000)
      await model.add(data);
  
  
      this.json({"state":1});

    }else{
      this.json({"state":0,"msg":"您输入的验证码错误，请检查输入！"});

    }


    }

    async loginAction() {
         
    
        let model = this.model('student');
    
        const res = await model.where({'del_flag': 0, 'phone': this.post("phone"), 'password': this.post("password")}).find();
        if (!think.isEmpty(res)) {

            const user = Object.assign({}, res, {'userName': res['name']});
            const token = await this.session('userInfo', user);
            user['token'] = token;
            user['state'] = 1;
            console.log("=======================");
            console.log(typeof user.todate);
            console.log("=======================");
            
            let expiryDate = new Date( user.todate);
          const now = new Date();

  if (isNaN(expiryDate.getTime())) {
     user.type="会员已到期"
  }

  const timeDiff = expiryDate - now;
  if (timeDiff <=0) {
      
       user.type="会员已到期"
  }
  if(timeDiff>0){
      
  let day= Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
  user.type=user.type+" ：剩余"+day+"天";
      
  }


            
            
            return this.json({state:1,data:user})

        }else{

            return this.json({state:0,data:null})

        }
    }

    async getschoolAction(){

        let model=this.model("school");
        let res=await model.where({"del_flag":0}).field("id,name as text").select();
        return this.json(res);
    }
    async getuserAction(){

        const user = await this.session('userInfo');
        console.log(user);
        let model=this.model("student");
        const res = await model.where({'del_flag': 0, "id":user.id}).find();


        let smodel=this.model("school")
        let school=await smodel.where({"id":res.school}).find();
        res['schoolname']=school.name;

        return this.json(res);


    }

    async updateinfoAction(){

        const user = await this.session('userInfo');
        console.log(user);
        let model=this.model("student");
        await model.where({id:user.id}).update({level:this.post("level"),name:this.post("name"),city:this.post("city"),sex:this.post("sex"),school:this.post("school"),ifok:1});

        return this.json({state:1})
    }

}