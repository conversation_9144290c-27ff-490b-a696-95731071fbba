{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeStyle as _normalizeStyle, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\";\n\nconst _hoisted_1 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"题干\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_2 = {\n  style: {\n    \"height\": \"50%\",\n    \"overflow\": \"auto\",\n    \"border\": \"1px solid #ddd\",\n    \"margin-bottom\": \"10px\"\n  }\n};\nconst _hoisted_3 = {\n  style: {\n    \"height\": \"50%\",\n    \"overflow\": \"auto\",\n    \"border\": \"1px solid #ddd\"\n  }\n};\n\nconst _hoisted_4 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"解析\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_5 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"答案\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_6 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"单选题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_7 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_8 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"多选题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_9 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_10 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"填空题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_11 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_12 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"解答题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_13 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_14 = {\n  style: {\n    \"position\": \"absolute\",\n    \"right\": \"60px\",\n    \"top\": \"42px\"\n  }\n};\n\nconst _hoisted_15 = /*#__PURE__*/_createTextVNode(\"上一题\");\n\nconst _hoisted_16 = /*#__PURE__*/_createTextVNode(\"下一题\");\n\nconst _hoisted_17 = /*#__PURE__*/_createTextVNode(\"取 消\");\n\nconst _hoisted_18 = /*#__PURE__*/_createTextVNode(\"取 消\");\n\nconst _hoisted_19 = /*#__PURE__*/_createTextVNode(\" 保存裁剪 \");\n\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_image = _resolveComponent(\"el-image\");\n\n  const _component_el_card = _resolveComponent(\"el-card\");\n\n  const _component_el_col = _resolveComponent(\"el-col\");\n\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n\n  const _component_el_button = _resolveComponent(\"el-button\");\n\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n\n  const _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n\n  const _component_el_row = _resolveComponent(\"el-row\");\n\n  const _component_el_form = _resolveComponent(\"el-form\");\n\n  const _component_sc_cropper = _resolveComponent(\"sc-cropper\");\n\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n\n  return _openBlock(), _createBlock(_component_el_dialog, {\n    title: $data.titleMap[$data.mode],\n    modelValue: $data.visible,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.visible = $event),\n    fullscreen: \"true\",\n    \"destroy-on-close\": \"\",\n    onClosed: _cache[6] || (_cache[6] = $event => _ctx.$emit('closed'))\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[2] || (_cache[2] = $event => $options.close())\n    }, {\n      default: _withCtx(() => [_hoisted_17]),\n      _: 1\n      /* STABLE */\n\n    })]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $data.form,\n      height: $data.height,\n      rules: $data.rules,\n      ref: \"dialogForm\",\n      \"label-width\": \"80px\",\n      \"label-position\": \"left\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_row, {\n        style: _normalizeStyle($data.rowsytle)\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: _normalizeStyle($data.rowsytle)\n          }, {\n            header: _withCtx(() => [_hoisted_1]),\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n              style: {\n                \"width\": \"100%\",\n                \"cursor\": \"pointer\"\n              },\n              src: $data.form.tm_p,\n              \"zoom-rate\": 1.2,\n              \"max-scale\": 7,\n              \"min-scale\": 0.2,\n              fit: \"contain\",\n              onClick: $options.openCropper\n            }, null, 8\n            /* PROPS */\n            , [\"src\", \"zoom-rate\", \"min-scale\", \"onClick\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n              style: {\n                \"width\": \"100%\",\n                \"cursor\": \"pointer\"\n              },\n              src: $data.form.tm_p_new,\n              \"zoom-rate\": 1.2,\n              \"max-scale\": 7,\n              \"min-scale\": 0.2,\n              fit: \"contain\"\n            }, null, 8\n            /* PROPS */\n            , [\"src\", \"zoom-rate\", \"min-scale\"])])]),\n            _: 1\n            /* STABLE */\n\n          }, 8\n          /* PROPS */\n          , [\"style\"])]),\n          _: 1\n          /* STABLE */\n\n        }), _createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: {\n              \"height\": \"100%\"\n            }\n          }, {\n            header: _withCtx(() => [_hoisted_4]),\n            default: _withCtx(() => [_createVNode(_component_el_image, {\n              style: {\n                \"height\": \"100%\"\n              },\n              src: $data.form.jx_p,\n              \"zoom-rate\": 1.2,\n              \"max-scale\": 7,\n              \"min-scale\": 0.2,\n              fit: \"cover\"\n            }, null, 8\n            /* PROPS */\n            , [\"src\", \"zoom-rate\", \"min-scale\"])]),\n            _: 1\n            /* STABLE */\n\n          })]),\n          _: 1\n          /* STABLE */\n\n        }), _createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: {\n              \"height\": \"100%\"\n            }\n          }, {\n            header: _withCtx(() => [_hoisted_5]),\n            default: _withCtx(() => [_createVNode(_component_el_row, {\n              style: {\n                \"height\": \"33%\"\n              }\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_col, null, {\n                default: _withCtx(() => [_hoisted_6, _createVNode(_component_el_form_item, {\n                  label: \"答案\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n                    modelValue: $data.form.ans,\n                    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.form.ans = $event)\n                  }, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.dxlist, item => {\n                      return _openBlock(), _createBlock(_component_el_radio, {\n                        key: item.value,\n                        label: item.value\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(item.value), 1\n                        /* TEXT */\n                        )]),\n                        _: 2\n                        /* DYNAMIC */\n\n                      }, 1032\n                      /* PROPS, DYNAMIC_SLOTS */\n                      , [\"label\"]);\n                    }), 128\n                    /* KEYED_FRAGMENT */\n                    ))]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"modelValue\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-plus\",\n                    onClick: $options.dxadd,\n                    circle: \"\"\n                  }, null, 8\n                  /* PROPS */\n                  , [\"onClick\"]), _createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savedx2\n                  }, {\n                    default: _withCtx(() => [_hoisted_7]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_8, _createVNode(_component_el_form_item, {\n                  label: \"答案\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_checkbox_group, {\n                    modelValue: $data.form.ans2,\n                    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.form.ans2 = $event)\n                  }, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.duoxuanlist, item => {\n                      return _openBlock(), _createBlock(_component_el_checkbox, {\n                        key: item.value,\n                        label: item.value\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(item.value), 1\n                        /* TEXT */\n                        )]),\n                        _: 2\n                        /* DYNAMIC */\n\n                      }, 1032\n                      /* PROPS, DYNAMIC_SLOTS */\n                      , [\"label\"]);\n                    }), 128\n                    /* KEYED_FRAGMENT */\n                    ))]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"modelValue\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-plus\",\n                    onClick: _ctx.duoxuanadd,\n                    circle: \"\"\n                  }, null, 8\n                  /* PROPS */\n                  , [\"onClick\"]), _createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.saveduoxuan2\n                  }, {\n                    default: _withCtx(() => [_hoisted_9]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_10, _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savetk\n                  }, {\n                    default: _withCtx(() => [_hoisted_11]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_12, _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savejd2\n                  }, {\n                    default: _withCtx(() => [_hoisted_13]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                })]),\n                _: 1\n                /* STABLE */\n\n              })]),\n              _: 1\n              /* STABLE */\n\n            })]),\n            _: 1\n            /* STABLE */\n\n          })]),\n          _: 1\n          /* STABLE */\n\n        })]),\n        _: 1\n        /* STABLE */\n\n      }, 8\n      /* PROPS */\n      , [\"style\"])]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"model\", \"height\", \"rules\"]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_button, {\n      icon: \"el-icon-back\",\n      onClick: $options.before\n    }, {\n      default: _withCtx(() => [_hoisted_15]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"onClick\"]), _createVNode(_component_el_button, {\n      icon: \"el-icon-right\",\n      onClick: $options.next\n    }, {\n      default: _withCtx(() => [_hoisted_16]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"onClick\"])]), _createVNode(_component_el_dialog, {\n      title: \"题干图片裁剪\",\n      modelValue: $data.cropperVisible,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.cropperVisible = $event),\n      width: \"600px\",\n      \"destroy-on-close\": \"\",\n      \"append-to-body\": \"\"\n    }, {\n      footer: _withCtx(() => [_createVNode(_component_el_button, {\n        onClick: _cache[3] || (_cache[3] = $event => $data.cropperVisible = false)\n      }, {\n        default: _withCtx(() => [_hoisted_18]),\n        _: 1\n        /* STABLE */\n\n      }), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $options.saveCroppedImage,\n        loading: $data.uploadLoading\n      }, {\n        default: _withCtx(() => [_hoisted_19]),\n        _: 1\n        /* STABLE */\n\n      }, 8\n      /* PROPS */\n      , [\"onClick\", \"loading\"])]),\n      default: _withCtx(() => [$data.cropperVisible ? (_openBlock(), _createBlock(_component_sc_cropper, {\n        key: 0,\n        src: $data.cropperImageSrc,\n        compress: 0.8,\n        aspectRatio: NaN,\n        ref: \"cropper\"\n      }, null, 8\n      /* PROPS */\n      , [\"src\", \"compress\"])) : _createCommentVNode(\"v-if\", true)]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"modelValue\"])]),\n    _: 1\n    /* STABLE */\n\n  }, 8\n  /* PROPS */\n  , [\"title\", \"modelValue\"]);\n}", "map": {"version": 3, "mappings": ";;gCAoBcA,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;;EAIGE,KAAiF,EAAjF;IAAA;IAAA;IAAA;IAAA;EAAA;;;EAYAA,KAA4D,EAA5D;IAAA;IAAA;IAAA;EAAA;;;gCAeHF,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;gCAiBAA,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;gCAOGA,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;iDAiBM;;gCAQNF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;iDAkBM;;iCAKVF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAGQ;;iCAIRF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAMQ;;;EAcXA,KAEK,EAFL;IAAA;IAAA;IAAA;EAAA;;;kDAIa;;kDAGA;;kDAIW;;kDAmBiB;;kDACkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApLnFC,aAyLYC,oBAzLZ,EAyLY;IAxLTC,KAAK,EAAEC,eAASA,UAAT,CAwLE;gBAvLDA,aAuLC;+DAvLDA,gBAAOC,OAuLN;IAtLVC,UAAU,EAAC,MAsLD;IArLV,sBAqLU;IApLTC,QAAM,sCAAEC,WAAK,QAAL,CAAF;EAoLG,CAzLZ;IA+JaC,MAAM,WACf,MAA2C,CAA3CC,aAA2CC,oBAA3C,EAA2C;MAA/BC,OAAK,sCAAEC,gBAAF;IAA0B,CAA3C;wBAA4B,MAAG;;;;KAA/B,CAA2C,CAD5B;sBAxJjB,MA4IU,CA5IVH,aA4IUI,kBA5IV,EA4IU;MA3IPC,KAAK,EAAEX,UA2IA;MA1IPY,MAAM,EAAEZ,YA0ID;MAzIPa,KAAK,EAAEb,WAyIA;MAxIRc,GAAG,EAAC,YAwII;MAvIR,eAAY,MAuIJ;MAtIR,kBAAe;IAsIP,CA5IV;wBAQE,MAmIS,CAnITR,aAmISS,iBAnIT,EAmIS;QAnIAnB,KAAK,kBAAEI,cAAF;MAmIL,CAnIT;0BACE,MA8BS,CA9BTM,aA8BSU,iBA9BT,EA8BS;UA9BAC,IAAI,EAAE;QA8BN,CA9BT,EAAgB;4BACd,MA4BU,CA5BVX,aA4BUY,kBA5BV,EA4BU;YA5BAtB,KAAK,kBAAEI,cAAF;UA4BL,CA5BV;YACamB,MAAM,WACf,MAEM,CAFNC,UAEM,CAHS;8BAKjB,MAUM,CAVN1B,oBAUM,KAVN,cAUM,CATJY,aAQEe,mBARF,EAQE;cAPAzB,KAAoC,EAApC;gBAAA;gBAAA;cAAA,CAOA;cANC0B,GAAG,EAAEtB,WAAKuB,IAMX;cALC,aAAW,GAKZ;cAJC,aAAW,CAIZ;cAHC,aAAW,GAGZ;cAFAC,GAAG,EAAC,SAEJ;cADChB,OAAK,EAAEC;YACR,CARF;;YAAA,+CASI,CAVN,CAUM,EAENf,oBASM,KATN,cASM,CARJY,aAOEe,mBAPF,EAOE;cANAzB,KAAoC,EAApC;gBAAA;gBAAA;cAAA,CAMA;cALC0B,GAAG,EAAEtB,WAAKyB,QAKX;cAJC,aAAW,GAIZ;cAHC,aAAW,CAGZ;cAFC,aAAW,GAEZ;cADAD,GAAG,EAAC;YACJ,CAPF;;YAAA,oCAQI,CATN,CAFM;;;;WAhBR;;UAAA,YA4BU,EA7BI;;;;QAAA,CAAhB,CA8BS,EACTlB,aAgBSU,iBAhBT,EAgBS;UAhBAC,IAAI,EAAE;QAgBN,CAhBT,EAAgB;4BACd,MAcU,CAdVX,aAcUY,kBAdV,EAcU;YAdDtB,KAAoB,EAApB;cAAA;YAAA;UAcC,CAdV,EAA6B;YAChBuB,MAAM,WACf,MAEM,CAFNO,UAEM,CAHS,CADU;8BAM3B,MAOE,CAPFpB,aAOEe,mBAPF,EAOE;cANAzB,KAAoB,EAApB;gBAAA;cAAA,CAMA;cALC0B,GAAG,EAAEtB,WAAK2B,IAKX;cAJC,aAAW,GAIZ;cAHC,aAAW,CAGZ;cAFC,aAAW,GAEZ;cADAH,GAAG,EAAC;YACJ,CAPF;;YAAA,oCAOE,EAbyB;;;;UAAA,CAA7B,CAcU,EAfI;;;;QAAA,CAAhB,CADS,EAkBTlB,aAiFSU,iBAjFT,EAiFS;UAjFAC,IAAI,EAAE;QAiFN,CAjFT,EAAgB;4BACd,MA+EU,CA/EVX,aA+EUY,kBA/EV,EA+EU;YA/EDtB,KAAoB,EAApB;cAAA;YAAA;UA+EC,CA/EV,EAA6B;YAChBuB,MAAM,WACf,MAEM,CAFNS,UAEM,CAHS,CADU;8BAM3B,MAoES,CApETtB,aAoESS,iBApET,EAoES;cApEDnB,KAAmB,EAAnB;gBAAA;cAAA;YAoEC,CApET,EAA2B;gCACzB,MAkES,CAlETU,aAkESU,iBAlET,EAkES,IAlET,EAkES;kCAhEN,MAAuC,CAAvCa,UAAuC,EAExCvB,aAUiBwB,uBAVjB,EAUiB;kBAVHC,KAAK,EAAC;gBAUH,CAVjB,EAAwB;oCACpB,MAQiB,CARjBzB,aAQiB0B,yBARjB,EAQiB;gCARQhC,WAAKiC,GAQb;+EARQjC,WAAKiC,MAAGhC;kBAQhB,CARjB;sCAEI,MAAsB,oBADxBiC,oBAMWC,SANX,EAMW,IANX,EAMWC,YALMpC,YAKN,EALFqC,IAAc,IAAV;2CADbxC,aAMWyC,mBANX,EAMW;wBAJRC,GAAG,EAAEF,IAAI,CAACG,KAIF;wBAHRT,KAAK,EAAEM,IAAI,CAACG;sBAGJ,CANX;0CAKE,MAAgB,mCAAbH,IAAI,CAACG,QAAK;wBAAA;yBAAG;;;;uBALlB;;sBAAA;qBAMW,CANX;;oBAAA,CACwB;;;;mBAF1B;;kBAAA,iBAQiB,EATG;;;;gBAAA,CAAxB,CAFwC,EAatClC,aAMewB,uBANf,EAMe;kBANDC,KAAK,EAAC;gBAML,CANf,EAAsB;oCACpB,MAAuD,CAAvDzB,aAAuDC,oBAAvD,EAAuD;oBAA5CkC,IAAI,EAAC,cAAuC;oBAAvBjC,OAAK,EAAEC,cAAgB;oBAATiC,MAAM,EAAN;kBAAS,CAAvD;;kBAAA,cAAuD,EAEvDpC,aAECC,oBAFD,EAEC;oBAFUkC,IAAI,EAAC,eAEf;oBAFgCjC,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAFuD,EADnC;;;;gBAAA,CAAtB,CAbsC,EAsBtCH,aAAcqC,qBAAd,CAtBsC,EAyBvCC,UAzBuC,EA4BtCtC,aAUewB,uBAVf,EAUe;kBAVDC,KAAK,EAAC;gBAUL,CAVf,EAAwB;oCACtB,MAQoB,CARpBzB,aAQoBuC,4BARpB,EAQoB;gCARQ7C,WAAK8C,IAQb;+EARQ9C,WAAK8C,OAAI7C;kBAQjB,CARpB;sCAEI,MAA2B,oBAD7BiC,oBAMcC,SANd,EAMc,IANd,EAMcC,YALGpC,iBAKH,EALLqC,IAAmB,IAAf;2CADbxC,aAMckD,sBANd,EAMc;wBAJXR,GAAG,EAAEF,IAAI,CAACG,KAIC;wBAHXT,KAAK,EAAEM,IAAI,CAACG;sBAGD,CANd;0CAKE,MAAgB,mCAAbH,IAAI,CAACG,QAAK;wBAAA;yBAAG;;;;uBALlB;;sBAAA;qBAMc,CANd;;oBAAA,CAC6B;;;;mBAF/B;;kBAAA,iBAQoB,EATE;;;;gBAAA,CAAxB,CA5BsC,EAuCtClC,aAMewB,uBANf,EAMe;kBANDC,KAAK,EAAC;gBAML,CANf,EAAsB;oCACpB,MAA4D,CAA5DzB,aAA4DC,oBAA5D,EAA4D;oBAAjDkC,IAAI,EAAC,cAA4C;oBAA5BjC,OAAK,EAAEJ,eAAqB;oBAATsC,MAAM,EAAN;kBAAS,CAA5D;;kBAAA,cAA4D,EAE5DpC,aAECC,oBAFD,EAEC;oBAFUkC,IAAI,EAAC,eAEf;oBAFgCjC,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAF4D,EADxC;;;;gBAAA,CAAtB,CAvCsC,EA+CtDH,aAAcqC,qBAAd,CA/CsD,EAgD3CK,WAhD2C,EAiDxC1C,aAIewB,uBAJf,EAIe;kBAJDC,KAAK,EAAC;gBAIL,CAJf,EAAsB;oCACpB,MAEC,CAFDzB,aAECC,oBAFD,EAEC;oBAFUkC,IAAI,EAAC,eAEf;oBAFgCjC,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAEC,EAHmB;;;;gBAAA,CAAtB,CAjDwC,EAsDtDH,aAAcqC,qBAAd,CAtDsD,EAuD3CM,WAvD2C,EA0DxC3C,aAKewB,uBALf,EAKe;kBALDC,KAAK,EAAC;gBAKL,CALf,EAAsB;oCAEpB,MAEC,CAFDzB,aAECC,oBAFD,EAEC;oBAFUkC,IAAI,EAAC,eAEf;oBAFgCjC,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAEC,EAJmB;;;;gBAAA,CAAtB,CA1DwC,EAgEjC;;;;cAAA,CAlET,CAkES,EAnEgB;;;;YAAA,CAA3B,CAoES,EA1EkB;;;;UAAA,CAA7B,CA+EU,EAhFI;;;;QAAA,CAAhB,CAlBS;;;;OA/BX;;MAAA,YAmIS;;;;KA3IX;;IAAA,+BA4IU,EAEVf,oBASM,KATN,eASM,CANAY,aAEWC,oBAFX,EAEW;MAFAkC,IAAI,EAAC,cAEL;MAFqBjC,OAAK,EAAEC;IAE5B,CAFX;wBACa,MAAG;;;;KADhB;;IAAA,cAMA,EAHYH,aAEDC,oBAFC,EAED;MAFYkC,IAAI,EAAC,eAEjB;MAFkCjC,OAAK,EAAEC;IAEzC,CAFC;wBACC,MAAG;;;;KADJ;;IAAA,cAGZ,CATN,CAFU,EAiBVH,aAoBYR,oBApBZ,EAoBY;MAnBVC,KAAK,EAAC,QAmBI;kBAlBDC,oBAkBC;iEAlBDA,uBAAcC,OAkBb;MAjBViD,KAAK,EAAC,OAiBI;MAhBV,sBAgBU;MAfV;IAeU,CApBZ;MAca7C,MAAM,WACf,MAA0D,CAA1DC,aAA0DC,oBAA1D,EAA0D;QAA9CC,OAAK,sCAAER,uBAAc,KAAhB;MAAyC,CAA1D;0BAA2C,MAAG;;;;OAA9C,CAA0D,EAC1DM,aAEYC,oBAFZ,EAEY;QAFD4C,IAAI,EAAC,SAEJ;QAFe3C,OAAK,EAAEC,yBAEtB;QAFyC2C,OAAO,EAAEpD;MAElD,CAFZ;0BAA6E,MAE7E;;;;OAFA;;MAAA,yBAD0D,CAD3C;wBAPjB,MAME,CADMA,sCALRH,aAMEwD,qBANF,EAME;cAAA;QALC/B,GAAG,EAAEtB,qBAKN;QAJCsD,QAAQ,EAAE,GAIX;QAHCC,WAAW,EAAEC,GAGd;QAFA1C,GAAG,EAAC;MAEJ,CANF;;MAAA,2DAME;;;;KAbJ;;IAAA,iBAjBU;;;;GAnJZ;;EAAA", "names": ["_createElementVNode", "class", "style", "_createBlock", "_component_el_dialog", "title", "$data", "$event", "fullscreen", "onClosed", "_ctx", "footer", "_createVNode", "_component_el_button", "onClick", "$options", "_component_el_form", "model", "height", "rules", "ref", "_component_el_row", "_component_el_col", "span", "_component_el_card", "header", "_hoisted_1", "_component_el_image", "src", "tm_p", "fit", "tm_p_new", "_hoisted_4", "jx_p", "_hoisted_5", "_hoisted_6", "_component_el_form_item", "label", "_component_el_radio_group", "ans", "_createElementBlock", "_Fragment", "_renderList", "item", "_component_el_radio", "key", "value", "icon", "circle", "_component_el_divider", "_hoisted_8", "_component_el_checkbox_group", "ans2", "_component_el_checkbox", "_hoisted_10", "_hoisted_12", "width", "type", "loading", "_component_sc_cropper", "compress", "aspectRatio", "NaN"], "sourceRoot": "", "sources": ["C:\\jsjy\\jsjy\\scui-master\\src\\views\\buss\\tiku\\save.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"titleMap[mode]\"\n    v-model=\"visible\"\n    fullscreen=\"true\"\n    destroy-on-close\n    @closed=\"$emit('closed')\"\n  >\n    <el-form\n      :model=\"form\"\n      :height=\"height\"\n      :rules=\"rules\"\n      ref=\"dialogForm\"\n      label-width=\"80px\"\n      label-position=\"left\"\n    >\n      <el-row :style=\"rowsytle\">\n        <el-col :span=\"8\">\n          <el-card :style=\"rowsytle\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>题干</span>\n              </div>\n            </template>\n            <div style=\"height: 50%; overflow: auto; border: 1px solid #ddd; margin-bottom: 10px;\">\n              <el-image\n                style=\"width: 100%; cursor: pointer\"\n                :src=\"form.tm_p\"\n                :zoom-rate=\"1.2\"\n                :max-scale=\"7\"\n                :min-scale=\"0.2\"\n                fit=\"contain\"\n                @click=\"openCropper\"\n              />\n            </div>\n\n            <div style=\"height: 50%; overflow: auto; border: 1px solid #ddd;\">\n              <el-image\n                style=\"width: 100%; cursor: pointer\"\n                :src=\"form.tm_p_new\"\n                :zoom-rate=\"1.2\"\n                :max-scale=\"7\"\n                :min-scale=\"0.2\"\n                fit=\"contain\"\n              />\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>解析</span>\n              </div>\n            </template>\n            <el-image\n              style=\"height: 100%\"\n              :src=\"form.jx_p\"\n              :zoom-rate=\"1.2\"\n              :max-scale=\"7\"\n              :min-scale=\"0.2\"\n              fit=\"cover\"\n            />\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>答案</span>\n              </div>\n            </template>\n            <el-row style=\"height: 33%\">\n              <el-col>\n\n                 <span style=\"font-size:18px\">单选题</span>\n\n                <el-form-item label=\"答案\">\n                    <el-radio-group v-model=\"form.ans\">\n                      <el-radio\n                        v-for=\"item in dxlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"dxadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"savedx2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n\n                  <el-divider />\n\n\n                 <span style=\"font-size:18px\">多选题</span>\n\n                \n                  <el-form-item label=\"答案\">\n                    <el-checkbox-group v-model=\"form.ans2\">\n                      <el-checkbox\n                        v-for=\"item in duoxuanlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-checkbox>\n                    </el-checkbox-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"duoxuanadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"saveduoxuan2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n  <el-divider />\n             <span style=\"font-size:18px\">填空题</span>\n                <el-form-item label=\"\">\n                  <el-button icon=\"el-icon-check\" @click=\"savetk\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n  <el-divider />\n             <span style=\"font-size:18px\">解答题</span>\n\n        \n                <el-form-item label=\"\">\n                 \n                  <el-button icon=\"el-icon-check\" @click=\"savejd2\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n         \n\n        \n          </el-card>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <div  style=\"    position: absolute;\n    right: 60px;\n    top: 42px;\">\n          <el-button icon=\"el-icon-back\" @click=\"before\"\n                      >上一题</el-button\n                    >\n                      <el-button icon=\"el-icon-right\" @click=\"next\"\n                      >下一题</el-button\n                    >\n    </div>\n    <template #footer>\n      <el-button @click=\"close()\">取 消</el-button>\n    </template>\n\n    <!-- 裁剪对话框 -->\n    <el-dialog \n      title=\"题干图片裁剪\" \n      v-model=\"cropperVisible\" \n      width=\"600px\" \n      destroy-on-close\n      append-to-body\n    >\n      <sc-cropper \n        :src=\"cropperImageSrc\" \n        :compress=\"0.8\" \n        :aspectRatio=\"NaN\"\n        ref=\"cropper\"\n        v-if=\"cropperVisible\"\n      />\n      <template #footer>\n        <el-button @click=\"cropperVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveCroppedImage\" :loading=\"uploadLoading\">\n          保存裁剪\n        </el-button>\n      </template>\n    </el-dialog>\n  </el-dialog>\n</template>\n\n\n\n\n<script>\nimport scCropper from '@/components/scCropper'\n\nexport default {\n  emits: [\"success\", \"closed\"],\n  components: {\n    scCropper\n  },\n  data() {\n    return {\n      array: [1],\n      height: \"\",\n      rowsytle: {\n        height: \"\",\n      },\n      mode: \"add\",\n      titleMap: {\n        add: \"上传\",\n        edit: \"编辑\",\n      },\n      url: \"/static/upload/26b6ea56-7049-425a-a659-64defef541bf/t4.png\",\n      btnloading: false,\n      visible: false,\n      isSaveing: false,\n      menuList: [],\n      fileViewList: [],\n      uploadApi: this.$API.common.upload,\n\n      ifprocess: false,\n      dxlist: [{ value: \"A\" }, { value: \"B\" }, { value: \"C\" }, { value: \"D\" }],\n\n      duoxuanlist: [\n        { value: \"A\" },\n        { value: \"B\" },\n        { value: \"C\" },\n        { value: \"D\" },\n      ],\n\n      cropperVisible: false,\n      cropperImageSrc: '',\n      uploadLoading: false,\n      form: {\n        id: \"\",\n        tm_p: \"\",\n        jx_p: \"\",\n        score: \"\",\n        tm_p_new: \"\",\n        ans: \"\",\n        ans2: [],\n        value: [],\n        lessonid: 0,\n      },\n      rules: {\n        name: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n\n        parent: [{ required: true, trigger: \"blur\", message: \"请选择\" }],\n      },\n      dict: [],\n      dicProps: {\n        value: \"id\",\n        label: \"name\",\n        checkStrictly: true,\n        emitPath: false,\n      },\n      props: {\n        menu: {\n          type: Object,\n          default: () => {},\n        },\n      },\n      selectConfig: {\n        score: {\n          label: \"name\",\n          value: \"name\",\n        },\n      },\n      menuProps: {\n        value: \"id\",\n        emitPath: false,\n        label: \"title\",\n        checkStrictly: true,\n      },\n    };\n  },\n  mounted() {\n    this.getDic();\n    this.getOffice();\n  },\n  created() {\n    this.height = document.documentElement.clientHeight - 180;\n\n    this.rowsytle.height = this.height + \"px\";\n  },\n  methods: {\n    async savedx() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savedx2() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        await this.next();\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan2() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        await  this.next();\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savetk() {\n      await this.$API.tk.savetk.post({ id: this.form.id, ans: \"\" });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n    async savetk2() {\n      if (!this.$TOOL.isEmpty(this.form.value)) {\n        await this.$API.tk.savetk.post({\n          id: this.form.id,\n          ans: JSON.stringify(this.form.value),\n        });\n\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请填写正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    close() {\n      this.$emit(\"success\", this.form, this.mode);\n      this.visible = false;\n    },\n\n    async savejd() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      console.log(\"====\", this.form.value);\n      this.$message.success(\"保存成功\");\n    },\n    async savejd2() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n\n    async getnext() {\n      let next = await this.$API.tk.getnext2.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num\n      });\n      if (next.length == 0) {\n        this.$alert(\"本分类无待处理内容\", \"提示\", { type: \"error\" });\n      } else {\n        this.form.ans = \"\";\n        this.array = [1];\n        this.form.value = [];\n        this.form.id = next[0].id;\n        this.form.tm_p = next[0].tm_p;\n        this.form.jx_p = next[0].jx_p;\n        this.form.num=next[0].num\n      }\n    },\n\nasync before(){\n\n\n         let next = await this.$API.tk.before.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    async next(){\n\n\n         let next = await this.$API.tk.next.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    dxadd() {\n      console.log(this.form.ans);\n      // 生成新元素的value值\n      const newValue = String.fromCharCode(65 + this.dxlist.length);\n\n      // 添加新元素\n      this.dxlist.push({ value: newValue });\n    },\n    async process() {\n      this.btnloading = true;\n      let res = await this.$API.tk.process.post({ file: this.form.zip });\n      this.form.buss_id = res.data.buss_id;\n      this.$message.success(\"解析完成\");\n      this.ifprocess = true;\n      this.btnloading = false;\n    },\n    fileSuccess(response) {\n      const suffix = response.data.file_name.substr(\n        response.data.file_name.lastIndexOf(\".\") + 1\n      ); // 文件后缀\n      this.fileViewList.push({\n        suffix: suffix,\n        name: response.data.file_name,\n        url: response.data.src,\n        new_name: response.data.new_name,\n        id: response.data.new_name,\n      });\n      this.$message.success(`文件上传成功`);\n      this.ifprocess = false;\n      return false;\n    },\n    beforeRemove(file) {\n      this.fileViewList.map((r, index) => {\n        if (r.name == file.name) {\n          this.form.files = this.form.files.replace(\n            \"/static/upload/\" + file.name + \",\",\n            \"\"\n          );\n          this.fileViewList.splice(index, 1);\n        }\n      });\n    },\n    async getOffice() {\n      var res = await this.$API.lesson.list.get();\n      this.menuList = res;\n    },\n\n    addtk() {\n      this.array.push(1); //通过添加array的值，增加input的个数\n    },\n    del(index) {\n      this.form.value.splice(index, 1); //先删除form中value对应索引的值\n      this.array.splice(index, 1); //然后删除array对应索引的值，实现点击删除按钮，减少input框效果\n    },\n\n    //显示\n    open(mode = \"add\") {\n      this.mode = mode;\n      this.visible = true;\n      return this;\n    },\n    //获取字典列表\n    async getDic() {\n      var res = await this.$API.dict.list.get();\n      this.dict = res.data;\n    },\n    //表单提交方法\n    submit() {\n      if (this.ifprocess) {\n        this.$refs.dialogForm.validate(async (valid) => {\n          if (valid) {\n            this.isSaveing = true;\n\n            let res = await this.$API.tk.saveimp.post(this.form);\n            this.isSaveing = false;\n            if (res.state == 1) {\n              this.$emit(\"success\", this.form, this.mode);\n              this.visible = false;\n              this.$message.success(\"操作成功\");\n            } else {\n              this.$alert(res.msg, \"提示\", { type: \"error\" });\n            }\n          }\n        });\n      } else {\n        this.$alert(\"请先解析压缩包内容！\", \"提示\", { type: \"error\" });\n      }\n    },\n    //表单注入数据\n    setData(data, mode) {\n      //可以和上面一样单个注入，也可以像下面一样直接合并进去\n      this.titleMap.edit = \"题号：\" + data.no+\"类型：\"+data.type;\n      this.form.ans = \"\";\n      this.form.ans2 = [];\n      this.form.value = [];\n      this.array = [1];\n      Object.assign(this.form, data);\n\n   \n\n      if (data.type == \"单选\") {\n        console.log(data);\n        this.form.ans = data.ans;\n      }\n      if (data.type == \"多选\") {\n        this.form.ans2 = data.ans.split(\",\");\n      }\n    },\n\n    // 打开裁剪器\n    openCropper() {\n      if (this.form.tm_p) {\n        this.cropperImageSrc = this.form.tm_p;\n        this.cropperVisible = true;\n      } else {\n        this.$message.warning('没有题干图片可以裁剪');\n      }\n    },\n\n    // 保存裁剪后的图片\n    async saveCroppedImage() {\n      if (!this.$refs.cropper) {\n        this.$message.error('裁剪器未初始化');\n        return;\n      }\n\n      this.uploadLoading = true;\n      try {\n        // 获取裁剪后的文件\n        this.$refs.cropper.getCropFile(async (file) => {\n          try {\n            // 创建FormData上传\n            const formData = new FormData();\n            formData.append('file', file);\n            \n            // 上传到服务器\n            const response = await this.$API.common.upload.post(formData);\n\n            console.log(response);\n            \n            if (response && response.data && response.data.src) {\n              // 更新题干图片路径\n              this.form.tm_p_new = response.data.src;\n              \n\n  await this.$API.common.util.post('/sys/tk/updatetmnew',{id:this.form.id,tm_p_new:this.form.tm_p_new});\n\n              this.cropperVisible = false;\n            } else {\n              this.$message.error('上传失败，请重试');\n            }\n          } catch (error) {\n            console.error('上传失败:', error);\n            this.$message.error('上传失败: ' + (error.message || '未知错误'));\n          } finally {\n            this.uploadLoading = false;\n          }\n        }, 'cropped_image.jpg', 'image/jpeg');\n      } catch (error) {\n        console.error('裁剪失败:', error);\n        this.$message.error('裁剪失败: ' + (error.message || '未知错误'));\n        this.uploadLoading = false;\n      }\n    },\n  },\n};\n</script>\n\n<style></style>\n"]}, "metadata": {}, "sourceType": "module"}