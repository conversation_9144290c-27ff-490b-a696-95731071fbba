{"version": 3, "sources": ["..\\..\\..\\src\\controller\\api\\dingshi.js"], "names": ["module", "exports", "think", "Controller", "processAction", "model", "sql", "execute", "model2", "list", "query", "tmp", "keyrecord", "where", "find", "schoolid", "increment", "usetime", "id", "update", "json", "processscoreAction", "users", "select", "u", "isEmpty", "level", "lessones", "field", "l", "para", "uid", "lessonid", "create_date", "datetime", "ifhave", "child", "tmpsql", "map", "item", "join", "avg", "score", "add", "caclscoreAction", "scores", "calculateUserAllScores", "console", "log", "arr", "s", "push", "lessonId", "lessonname", "name", "delete", "addMany", "userId", "rootLessons", "parent_id", "del_flag", "results", "<PERSON><PERSON><PERSON><PERSON>", "calculateSubjectScore", "Math", "round", "children", "userScore", "userid", "totalScore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childScore", "getScoreTree", "lesson", "result", "childTree", "treeAction", "post", "fail", "tree", "success", "forest", "root", "err", "logger", "error", "message", "processvipAction", "auto", "service", "autoDeductVipDays", "processschoolAction", "processorderAction", "thirtyMinutesAgo", "Date", "now", "pay_status", "create_time", "info", "code", "data", "deleted", "processyxAction", "scanRecords", "alias", "updated", "updatedCount", "record", "yx_id", "student_id", "yxid", "openid", "total", "length"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,UAApB,CAA+B;;AAEzC;AACMC,eAAN,GAAsB;AAAA;;AAAA;AACrB,UAAIC,QAAM,MAAKA,KAAL,CAAW,SAAX,CAAV;;AAGA,UAAIC,MAAI,kDAAR;AACA,YAAMD,MAAME,OAAN,CAAcD,GAAd,CAAN;;AAMA,UAAIE,SAAO,MAAKH,KAAL,CAAW,IAAX,CAAX;;AAEH;AACE,YAAMA,MAAME,OAAN,CAAc,0FAAd,CAAN;;AAEC,YAAMF,MAAME,OAAN,CAAc,+DAAd,CAAN;;AAEF,UAAIE,OAAM,MAAMJ,MAAMK,KAAN,CAAY,sDAAZ,CAAhB;;AAEA,WAAI,IAAIC,GAAR,IAAeF,IAAf,EAAoB;AAClB,YAAIG,YAAY,MAAM,MAAKP,KAAL,CAAW,WAAX,EAAwBQ,KAAxB,CAA8B,EAAC,MAAKF,IAAIC,SAAV,EAA9B,EAAoDE,IAApD,EAAtB;;AAEA,cAAM,MAAKT,KAAL,CAAW,QAAX,EAAqBQ,KAArB,CAA2B,EAAC,MAAKD,UAAUG,QAAhB,EAA3B,EAAsDC,SAAtD,CAAgE,YAAhE,EAA6EJ,UAAUK,OAAvF,CAAN;AACA,cAAM,MAAKZ,KAAL,CAAW,KAAX,EAAkBQ,KAAlB,CAAwB,EAAC,MAAKF,IAAIO,EAAV,EAAxB,EAAuCC,MAAvC,CAA8C,EAAC,UAAS,CAAV,EAA9C,CAAN;AAID;;AAOC,aAAO,MAAKC,IAAL,CAAU,IAAV,CAAP;AAnCqB;AAsCxB;;AAGKC,oBAAN,GAA0B;AAAA;;AAAA;AACtB,UAAIhB,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;;AAEA,UAAIG,SAAO,OAAKH,KAAL,CAAW,cAAX,CAAX;;AAEA,UAAIiB,QAAM,MAAMjB,MAAMQ,KAAN,CAAY,EAAC,YAAW,CAAZ,EAAZ,EAA4BU,MAA5B,EAAhB;;AAEA,WAAI,IAAIC,CAAR,IAAaF,KAAb,EAAoB;AAChB,YAAG,CAACpB,MAAMuB,OAAN,CAAcD,EAAEE,KAAhB,CAAJ,EAA2B;AACvB,cAAIC,WAAS,MAAM,OAAKtB,KAAL,CAAW,QAAX,EAAqBQ,KAArB,CAA2B,EAAC,QAAOW,EAAEE,KAAV,EAAgB,aAAY,CAA5B,EAA8B,YAAW,CAAzC,EAA3B,EAAwEE,KAAxE,CAA8E,IAA9E,EAAoFL,MAApF,EAAnB;;AAEA,eAAI,IAAIM,CAAR,IAAaF,QAAb,EAAsB;;AAGlB,gBAAIG,OAAK,EAAT;;AAEAA,iBAAKC,GAAL,GAASP,EAAEN,EAAX;AACAY,iBAAKE,QAAL,GAAcH,EAAEX,EAAhB;AACAY,iBAAKG,WAAL,GAAiB/B,MAAMgC,QAAN,EAAjB;;AAEA,gBAAIC,SAAO,MAAM,OAAK9B,KAAL,CAAW,cAAX,EAA2BQ,KAA3B,CAAiCiB,IAAjC,EAAuChB,IAAvC,EAAjB;;AAGA,gBAAGZ,MAAMuB,OAAN,CAAcU,MAAd,CAAH,EAAyB;;AAErB,kBAAIC,QAAM,MAAM,OAAK/B,KAAL,CAAW,QAAX,EAAqBQ,KAArB,CAA2B,EAAC,aAAYgB,EAAEX,EAAf,EAAkB,YAAW,CAA7B,EAA3B,EAA4DU,KAA5D,CAAkE,IAAlE,EAAwEL,MAAxE,EAAhB;AACA,kBAAG,CAACrB,MAAMuB,OAAN,CAAcW,KAAd,CAAJ,EAAyB;;AAErB,oBAAIC,SAAO,+EAA6Eb,EAAEN,EAA/E,GAAkF,qBAAlF,GAAwGkB,MAAME,GAAN,CAAU;AAAA,yBAAQC,KAAKrB,EAAb;AAAA,iBAAV,EAA2BsB,IAA3B,CAAgC,GAAhC,CAAxG,GAA6I,GAAxJ;AACA,oBAAIC,MAAI,MAAMpC,MAAMK,KAAN,CAAY2B,MAAZ,CAAd;AACAP,qBAAKY,KAAL,GAAWD,IAAI,CAAJ,EAAO,KAAP,CAAX;AACH,eALD,MAKK;AACDX,qBAAKY,KAAL,GAAW,EAAX;AACH;;AAGD,oBAAM,OAAKrC,KAAL,CAAW,cAAX,EAA2BsC,GAA3B,CAA+Bb,IAA/B,CAAN;AACH;AAKJ;AAMJ;AAKJ;;AAID,aAAO,OAAKV,IAAL,CAAU,IAAV,CAAP;AAzDsB;AA4DzB;;AAQGwB,iBAAN,GAAwB;AAAA;;AAAA;;AAEtB,UAAIvC,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,UAAIiB,QAAM,MAAMjB,MAAMQ,KAAN,CAAY,EAAC,YAAW,CAAZ,EAAZ,EAA4BU,MAA5B,EAAhB;AACA,UAAIf,SAAO,OAAKH,KAAL,CAAW,cAAX,CAAX;;AAEA,WAAI,IAAImB,CAAR,IAAaF,KAAb,EAAmB;AACf,cAAMuB,SAAS,MAAM,OAAKC,sBAAL,CAA4BtB,EAAEN,EAA9B,CAArB;AACF6B,gBAAQC,GAAR,CAAYH,MAAZ;AACA,YAAII,MAAI,EAAR;AACA,aAAI,IAAIC,CAAR,IAAaL,MAAb,EAAoB;AAClBI,cAAIE,IAAJ,CAAS;AACLpB,iBAAIP,EAAEN,EADD;AAELc,sBAASkB,EAAEE,QAFN;AAGLC,wBAAWH,EAAEI,IAHR;AAILZ,mBAAMQ,EAAER,KAJH;AAKLT,yBAAY/B,MAAMgC,QAAN,CAAe,YAAf;AALP,WAAT;AAOD;;AAEDa,gBAAQC,GAAR,CAAY,QAAZ,EAAqBC,GAArB;;AAEA,cAAMzC,OAAOK,KAAP,CAAa,EAAC,OAAMW,EAAEN,EAAT,EAAY,eAAchB,MAAMgC,QAAN,CAAe,YAAf,CAA1B,EAAb,EAAsEqB,MAAtE,EAAN;AACA,cAAM/C,OAAOgD,OAAP,CAAeP,GAAf,CAAN;AAID;AACD,aAAO,OAAK7B,IAAL,CAAU,IAAV,CAAP;AA5BsB;AA+BvB;;AAGK0B,wBAAN,CAA6BW,MAA7B,EAAqC;AAAA;;AAAA;AACnC;AACA,YAAMC,cAAc,MAAM,OAAKrD,KAAL,CAAW,QAAX,EACvBQ,KADuB,CACjB;AACL8C,mBAAW,CADN;AAELC,kBAAU;AAFL,OADiB,EAKvBrC,MALuB,EAA1B;;AAOA,YAAMsC,UAAU,EAAhB;;AAEA;AACA,WAAK,MAAMC,UAAX,IAAyBJ,WAAzB,EAAsC;AACpC,cAAMhB,QAAQ,MAAM,OAAKqB,qBAAL,CAA2BN,MAA3B,EAAmCK,WAAW5C,EAA9C,CAApB;AACA2C,gBAAQV,IAAR,CAAa;AACXC,oBAAUU,WAAW5C,EADV;AAEXoC,gBAAMQ,WAAWR,IAFN;AAGXZ,iBAAOsB,KAAKC,KAAL,CAAWvB,KAAX;AAHI,SAAb;AAKD;;AAED,aAAOmB,OAAP;AArBmC;AAsBpC;;AAED;;;;;;AAMME,uBAAN,CAA4BN,MAA5B,EAAoCL,QAApC,EAA8C;AAAA;;AAAA;AAC5C;AACA,YAAMc,WAAW,MAAM,OAAK7D,KAAL,CAAW,QAAX,EACpBQ,KADoB,CACd;AACL8C,mBAAWP,QADN;AAELQ,kBAAU;AAFL,OADc,EAKpBrC,MALoB,EAAvB;;AAOA;AACA,UAAIrB,MAAMuB,OAAN,CAAcyC,QAAd,CAAJ,EAA6B;AAC3B,cAAMC,YAAY,MAAM,OAAK9D,KAAL,CAAW,aAAX,EACrBQ,KADqB,CACf;AACLuD,kBAAQX,MADH;AAELzB,oBAAUoB;AAFL,SADe,EAKrBxB,KALqB,CAKf,OALe,EAMrBd,IANqB,EAAxB;;AAQA,eAAOqD,UAAUzB,KAAV,IAAmB,EAA1B,CAT2B,CASG;AAC/B;;AAED;AACA,UAAI2B,aAAa,CAAjB;AACA,UAAIC,gBAAgB,CAApB;;AAEA,WAAK,MAAMlC,KAAX,IAAoB8B,QAApB,EAA8B;AAC5B,cAAMK,aAAa,MAAM,OAAKR,qBAAL,CAA2BN,MAA3B,EAAmCrB,MAAMlB,EAAzC,CAAzB;AACAmD,sBAAcE,UAAd;AACAD;AACD;;AAED;AACA,aAAOA,gBAAgB,CAAhB,GAAoBD,aAAaC,aAAjC,GAAiD,EAAxD;AAjC4C;AAkC7C;;AAED;;;;;;AAMME,cAAN,CAAmBf,MAAnB,EAA2BL,QAA3B,EAAqC;AAAA;;AAAA;AACnC;AACA,YAAMqB,SAAS,MAAM,OAAKpE,KAAL,CAAW,QAAX,EAClBQ,KADkB,CACZ;AACLK,YAAIkC,QADC;AAELQ,kBAAU;AAFL,OADY,EAKlB9C,IALkB,EAArB;;AAOA,UAAIZ,MAAMuB,OAAN,CAAcgD,MAAd,CAAJ,EAA2B;AACzB,eAAO,IAAP;AACD;;AAED;AACA,YAAM/B,QAAQ,MAAM,OAAKqB,qBAAL,CAA2BN,MAA3B,EAAmCL,QAAnC,CAApB;;AAEA;AACA,YAAMc,WAAW,MAAM,OAAK7D,KAAL,CAAW,QAAX,EACpBQ,KADoB,CACd;AACL8C,mBAAWP,QADN;AAELQ,kBAAU;AAFL,OADc,EAKpBrC,MALoB,EAAvB;;AAOA,YAAMmD,SAAS;AACbxD,YAAIuD,OAAOvD,EADE;AAEboC,cAAMmB,OAAOnB,IAFA;AAGbZ,eAAOsB,KAAKC,KAAL,CAAWvB,KAAX,CAHM;AAIbwB,kBAAU;AAJG,OAAf;;AAOA;AACA,UAAI,CAAChE,MAAMuB,OAAN,CAAcyC,QAAd,CAAL,EAA8B;AAC5B,aAAK,MAAM9B,KAAX,IAAoB8B,QAApB,EAA8B;AAC5B,gBAAMS,YAAY,MAAM,OAAKH,YAAL,CAAkBf,MAAlB,EAA0BrB,MAAMlB,EAAhC,CAAxB;AACA,cAAIyD,SAAJ,EAAe;AACbD,mBAAOR,QAAP,CAAgBf,IAAhB,CAAqBwB,SAArB;AACD;AACF;AACF;;AAID,aAAOD,MAAP;AA3CmC;AA4CpC;;AAED;;;AAGME,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACF,cAAMnB,SAAS,CAAf;AACA,cAAML,WAAW,OAAKyB,IAAL,CAAU,UAAV,CAAjB,CAFE,CAEsC;;AAExC,YAAI,CAACpB,MAAL,EAAa;AACX,iBAAO,OAAKqB,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI1B,QAAJ,EAAc;AACZ;AACA,gBAAM2B,OAAO,MAAM,OAAKP,YAAL,CAAkBf,MAAlB,EAA0BL,QAA1B,CAAnB;AACA,iBAAO,OAAK4B,OAAL,CAAaD,IAAb,CAAP;AACD,SAJD,MAIO;AACL;AACA,gBAAMrB,cAAc,MAAM,OAAKrD,KAAL,CAAW,QAAX,EACvBQ,KADuB,CACjB;AACL8C,uBAAW,GADN;AAELC,sBAAU;AAFL,WADiB,EAKvBrC,MALuB,EAA1B;;AAOA,gBAAM0D,SAAS,EAAf;AACA,eAAK,MAAMC,IAAX,IAAmBxB,WAAnB,EAAgC;AAC9B,kBAAMqB,OAAO,MAAM,OAAKP,YAAL,CAAkBf,MAAlB,EAA0ByB,KAAKhE,EAA/B,CAAnB;AACA,gBAAI6D,IAAJ,EAAU;AACRE,qBAAO9B,IAAP,CAAY4B,IAAZ;AACD;AACF;;AAEF;AACC,iBAAO,OAAKC,OAAL,CAAaC,MAAb,CAAP;AACD;AACF,OAhCD,CAgCE,OAAOE,GAAP,EAAY;AACZjF,cAAMkF,MAAN,CAAaC,KAAb,CAAmB,+BAAnB,EAAoDF,GAApD;AACA,eAAO,OAAKL,IAAL,CAAUK,IAAIG,OAAd,CAAP;AACD;AApCgB;AAqClB;;AAYMC,kBAAN,GAAwB;AAAA;;AAAA;AACvB,UAAIC,OAAOtF,MAAMuF,OAAN,CAAc,MAAd,CAAX;AACA,YAAMD,KAAKE,iBAAL,EAAN;;AAIA,aAAO,OAAKtE,IAAL,CAAU,CAAV,CAAP;AANuB;AAQ1B;;AAEOuE,qBAAN,GAA2B;AAAA;;AAAA;;AAGzB,YAAM,OAAKtF,KAAL,CAAW,QAAX,EAAqBc,MAArB,CAA4B,EAAC,aAAY,CAAb,EAA5B,CAAN;;AAEA,aAAO,OAAKC,IAAL,CAAU,CAAV,CAAP;AALyB;AAS1B;;AAGOwE,oBAAN,GAA0B;AAAA;;AAAA;AACxB,UAAI;AACF;AACA,cAAMC,mBAAmB3F,MAAMgC,QAAN,CAAe4D,KAAKC,GAAL,KAAa,KAAK,EAAL,GAAU,IAAtC,CAAzB;;AAEA;AACA,cAAMrB,SAAS,MAAM,QAAKrE,KAAL,CAAW,OAAX,EAAoBQ,KAApB,CAA0B;AAC7CmF,sBAAY,CADiC;AAE7CC,uBAAa,CAAC,GAAD,EAAMJ,gBAAN;AAFgC,SAA1B,EAGlB1E,MAHkB,CAGX,EAAC,YAAW,CAAZ,EAHW,CAArB;;AAKAjB,cAAMkF,MAAN,CAAac,IAAb,CAAmB,OAAMxB,MAAO,WAAhC;;AAEA,eAAO,QAAKtD,IAAL,CAAU;AACf+E,gBAAM,GADS;AAEfb,mBAAU,OAAMZ,MAAO,OAFR;AAGf0B,gBAAM;AACJC,qBAAS3B;AADL;AAHS,SAAV,CAAP;AAQD,OApBD,CAoBE,OAAOW,KAAP,EAAc;AACdnF,cAAMkF,MAAN,CAAaC,KAAb,CAAmB,WAAnB,EAAgCA,KAAhC;AACA,eAAO,QAAKjE,IAAL,CAAU;AACf+E,gBAAM,GADS;AAEfb,mBAAS,eAAeD,MAAMC;AAFf,SAAV,CAAP;AAID;AA3BuB;AA8BzB;;AAECgB,iBAAN,GAAuB;AAAA;;AAAA;AACrB,UAAI;AACF;AACA,cAAMC,cAAc,MAAM,QAAKlG,KAAL,CAAW,SAAX,EACvBmG,KADuB,CACjB,MADiB,EAEvBhE,IAFuB,CAElB,sDAFkB,EAGvB3B,KAHuB,CAGjB;AACL,8BAAoB,CADf;AAEL,0BAAgB,CAAC,KAAD,EAAQ,kDAAR;AAFX,SAHiB,EAOvBe,KAPuB,CAOjB,iEAPiB,EAQvBL,MARuB,EAA1B;;AAUA,YAAIrB,MAAMuB,OAAN,CAAc8E,WAAd,CAAJ,EAAgC;AAC9B,iBAAO,QAAKnF,IAAL,CAAU;AACf+E,kBAAM,GADS;AAEfb,qBAAS,aAFM;AAGfc,kBAAM;AACJK,uBAAS;AADL;AAHS,WAAV,CAAP;AAOD;;AAED,YAAIC,eAAe,CAAnB;;AAEA;AACA,aAAK,MAAMC,MAAX,IAAqBJ,WAArB,EAAkC;AAChC,cAAII,OAAOC,KAAP,IAAgBD,OAAOE,UAA3B,EAAuC;AACrC,kBAAM,QAAKxG,KAAL,CAAW,SAAX,EACHQ,KADG,CACG;AACLK,kBAAIyF,OAAOE,UADN;AAELjD,wBAAU;AAFL,aADH,EAKHzC,MALG,CAKI;AACN2F,oBAAMH,OAAOC;AADP,aALJ,CAAN;;AASAF;;AAEAxG,kBAAMkF,MAAN,CAAac,IAAb,CAAmB,kBAAiBS,OAAOE,UAAW,YAAWF,OAAOI,MAAO,UAASJ,OAAOC,KAAM,EAArG;AACD;AACF;;AAED,eAAO,QAAKxF,IAAL,CAAU;AACf+E,gBAAM,GADS;AAEfb,mBAAU,OAAMoB,YAAa,OAFd;AAGfN,gBAAM;AACJK,qBAASC,YADL;AAEJM,mBAAOT,YAAYU;AAFf;AAHS,SAAV,CAAP;AASD,OAnDD,CAmDE,OAAO5B,KAAP,EAAc;AACdnF,cAAMkF,MAAN,CAAaC,KAAb,CAAmB,aAAnB,EAAkCA,KAAlC;AACA,eAAO,QAAKjE,IAAL,CAAU;AACf+E,gBAAM,GADS;AAEfb,mBAAS,iBAAiBD,MAAMC,OAFjB;AAGfc,gBAAM;AACJK,qBAAS;AADL;AAHS,SAAV,CAAP;AAOD;AA7DoB;AA8DtB;;AAhb+C,CAAhD", "file": "..\\..\\..\\src\\controller\\api\\dingshi.js", "sourcesContent": ["module.exports = class extends think.Controller {\r\n\r\n       //处理到期的题\r\n       async processAction() {\r\n        let model=this.model(\"tk_user\");\r\n\r\n        \r\n        let sql=\"update sys_tk_user set state=0 where now() >=yxq\";\r\n        await model.execute(sql);\r\n\r\n\r\n\r\n\r\n\r\n        let model2=this.model(\"bk\")\r\n\r\n     //   await model.execute(\"UPDATE sys_bk SET state = 3 WHERE skdate < NOW()\")\r\n       await model.execute(\"UPDATE sys_bk SET state = 3 WHERE skdate <= DATE_SUB(NOW(), INTERVAL 2 HOUR) and state=2\")\r\n\r\n        await model.execute(\"UPDATE sys_bk SET state = 4 WHERE skdate <= NOW() and state=3\")\r\n\r\n      let list =await model.query(\"SELECT * FROM buss_key where yxq <now() and state2=0\");\r\n\r\n      for(let tmp of list){\r\n        let keyrecord  =await this.model(\"keyrecord\").where({\"id\":tmp.keyrecord}).find();\r\n\r\n        await this.model(\"school\").where({\"id\":keyrecord.schoolid}).increment(\"canusetime\",keyrecord.usetime);\r\n        await this.model(\"key\").where({\"id\":tmp.id}).update({\"state2\":2});\r\n\r\n\r\n\r\n      }\r\n\r\n\r\n\r\n\r\n\r\n\r\n        return this.json(\"ok\")\r\n\r\n\r\n    }\r\n\r\n\r\n    async processscoreAction(){\r\n        let model=this.model(\"student\");\r\n\r\n        let model2=this.model(\"userdaysinfo\");\r\n\r\n        let users=await model.where({\"del_flag\":0}).select();\r\n\r\n        for(let u of users ){ \r\n            if(!think.isEmpty(u.level)){\r\n                let lessones=await this.model(\"lesson\").where({\"type\":u.level,\"parent_id\":0,\"del_flag\":0}).field(\"id\").select();\r\n\r\n                for(let l of lessones){\r\n                \r\n\r\n                    let para={};\r\n\r\n                    para.uid=u.id;\r\n                    para.lessonid=l.id;\r\n                    para.create_date=think.datetime();\r\n\r\n                    let ifhave=await this.model(\"userdaysinfo\").where(para).find();\r\n\r\n\r\n                    if(think.isEmpty(ifhave)){\r\n\r\n                        let child=await this.model(\"lesson\").where({\"parent_id\":l.id,\"del_flag\":0}).field(\"id\").select();\r\n                        if(!think.isEmpty(child)){\r\n\r\n                            let tmpsql=\"SELECT ifnull(avg(score),50)  as score  FROM sys_lesson_user where userid=\"+u.id+\" and lessonid  in (\"+child.map(item => item.id).join(',')+\")\"\r\n                            let avg=await model.query(tmpsql);\r\n                            para.score=avg[0]['avg']\r\n                        }else{\r\n                            para.score=50;\r\n                        }\r\n                       \r\n    \r\n                        await this.model(\"userdaysinfo\").add(para);\r\n                    }\r\n\r\n\r\n                  \r\n\r\n                }\r\n\r\n\r\n\r\n\r\n\r\n            }\r\n\r\n\r\n\r\n\r\n        }\r\n\r\n\r\n\r\n        return this.json(\"ok\")\r\n\r\n\r\n    }\r\n\r\n\r\n\r\n\r\n \r\n\r\n\r\n  async caclscoreAction() {\r\n    \r\n    let model=this.model(\"student\");\r\n    let users=await model.where({\"del_flag\":0}).select();\r\n    let model2=this.model(\"userdaysinfo\");\r\n\r\n    for(let u of users){\r\n        const scores = await this.calculateUserAllScores(u.id);\r\n      console.log(scores)\r\n      let arr=[];\r\n      for(let s of scores){\r\n        arr.push({\r\n            uid:u.id,\r\n            lessonid:s.lessonId,\r\n            lessonname:s.name,\r\n            score:s.score,\r\n            create_date:think.datetime(\"YYYY-MM-DD\")    \r\n        })\r\n      }\r\n\r\n      console.log(\"insert\",arr);\r\n\r\n      await model2.where({\"uid\":u.id,\"create_date\":think.datetime(\"YYYY-MM-DD\")}).delete();\r\n      await model2.addMany(arr);\r\n\r\n\r\n\r\n    }\r\n    return this.json(\"ok\");\r\n    \r\n   \r\n  }\r\n\r\n\r\n  async calculateUserAllScores(userId) {\r\n    // 获取所有根科目（parent_id = 0）\r\n    const rootLessons = await this.model('lesson')\r\n      .where({\r\n        parent_id: 0,\r\n        del_flag: '0'\r\n      })\r\n      .select();\r\n\r\n    const results = [];\r\n    \r\n    // 计算每个根科目的成绩\r\n    for (const rootLesson of rootLessons) {\r\n      const score = await this.calculateSubjectScore(userId, rootLesson.id);\r\n      results.push({\r\n        lessonId: rootLesson.id,\r\n        name: rootLesson.name,\r\n        score: Math.round(score)\r\n      });\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * 递归计算科目成绩\r\n   * @param {number} userId 用户ID\r\n   * @param {number} lessonId 科目ID\r\n   * @returns {Promise<number>} 返回计算后的成绩\r\n   */\r\n  async calculateSubjectScore(userId, lessonId) {\r\n    // 获取当前科目的所有子科目\r\n    const children = await this.model('lesson')\r\n      .where({\r\n        parent_id: lessonId,\r\n        del_flag: '0'\r\n      })\r\n      .select();\r\n\r\n    // 如果没有子科目，说明是最底层科目，直接从 lesson_user 表获取成绩\r\n    if (think.isEmpty(children)) {\r\n      const userScore = await this.model('lesson_user')\r\n        .where({\r\n          userid: userId,\r\n          lessonid: lessonId\r\n        })\r\n        .field('score')\r\n        .find();\r\n      \r\n      return userScore.score || 50; // 如果没有成绩记录，返回默认分数50\r\n    }\r\n\r\n    // 如果有子科目，递归计算所有子科目的成绩\r\n    let totalScore = 0;\r\n    let validChildren = 0;\r\n\r\n    for (const child of children) {\r\n      const childScore = await this.calculateSubjectScore(userId, child.id);\r\n      totalScore += childScore;\r\n      validChildren++;\r\n    }\r\n\r\n    // 返回子科目的平均分\r\n    return validChildren > 0 ? totalScore / validChildren : 50;\r\n  }\r\n\r\n  /**\r\n   * 获取完整的科目成绩树\r\n   * @param {number} userId 用户ID\r\n   * @param {number} lessonId 科目ID\r\n   * @returns {Promise<Object>} 返回包含成绩的科目树\r\n   */\r\n  async getScoreTree(userId, lessonId) {\r\n    // 获取当前科目信息\r\n    const lesson = await this.model('lesson')\r\n      .where({\r\n        id: lessonId,\r\n        del_flag: '0'\r\n      })\r\n      .find();\r\n\r\n    if (think.isEmpty(lesson)) {\r\n      return null;\r\n    }\r\n\r\n    // 计算当前科目成绩\r\n    const score = await this.calculateSubjectScore(userId, lessonId);\r\n\r\n    // 获取子科目\r\n    const children = await this.model('lesson')\r\n      .where({\r\n        parent_id: lessonId,\r\n        del_flag: '0'\r\n      })\r\n      .select();\r\n\r\n    const result = {\r\n      id: lesson.id,\r\n      name: lesson.name,\r\n      score: Math.round(score),\r\n      children: []\r\n    };\r\n\r\n    // 递归获取子科目的成绩树\r\n    if (!think.isEmpty(children)) {\r\n      for (const child of children) {\r\n        const childTree = await this.getScoreTree(userId, child.id);\r\n        if (childTree) {\r\n          result.children.push(childTree);\r\n        }\r\n      }\r\n    }\r\n\r\n \r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * 获取成绩树\r\n   */\r\n  async treeAction() {\r\n    try {\r\n      const userId = 3;\r\n      const lessonId = this.post('lessonId'); // 可选，不提供则返回所有根科目的树\r\n\r\n      if (!userId) {\r\n        return this.fail('请提供用户ID');\r\n      }\r\n\r\n      if (lessonId) {\r\n        // 获取指定科目的成绩树\r\n        const tree = await this.getScoreTree(userId, lessonId);\r\n        return this.success(tree);\r\n      } else {\r\n        // 获取所有根科目的成绩树\r\n        const rootLessons = await this.model('lesson')\r\n          .where({\r\n            parent_id: '0',\r\n            del_flag: '0'\r\n          })\r\n          .select();\r\n\r\n        const forest = [];\r\n        for (const root of rootLessons) {\r\n          const tree = await this.getScoreTree(userId, root.id);\r\n          if (tree) {\r\n            forest.push(tree);\r\n          }\r\n        }\r\n\r\n       // console.log(forest);\r\n        return this.success(forest);\r\n      }\r\n    } catch (err) {\r\n      think.logger.error('Score tree calculation error:', err);\r\n      return this.fail(err.message);\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n   async processvipAction(){\r\n    let auto = think.service('auto');\r\n    await auto.autoDeductVipDays();\r\n\r\n\r\n\r\n    return this.json(1);\r\n\r\n}\r\n\r\n  async processschoolAction(){\r\n\r\n\r\n    await this.model(\"school\").update({\"todaycost\":0});\r\n\r\n    return this.json(1);\r\n\r\n\r\n\r\n  }\r\n\r\n\r\n    async processorderAction(){\r\n      try {\r\n        // 获取30分钟前的时间\r\n        const thirtyMinutesAgo = think.datetime(Date.now() - 30 * 60 * 1000);\r\n        \r\n        // 删除超过30分钟未支付的订单\r\n        const result = await this.model('order').where({\r\n          pay_status: 0,\r\n          create_time: ['<', thirtyMinutesAgo]\r\n        }).update({\"del_flag\":1});\r\n\r\n        think.logger.info(`已删除 ${result} 条超时未支付订单`);\r\n        \r\n        return this.json({\r\n          code: 200,\r\n          message: `成功删除${result}条超时订单`,\r\n          data: {\r\n            deleted: result\r\n          }\r\n        });\r\n\r\n      } catch (error) {\r\n        think.logger.error('删除超时订单失败:', error);\r\n        return this.json({\r\n          code: 500, \r\n          message: '删除超时订单失败: ' + error.message\r\n        });\r\n      }\r\n\r\n      \r\n    }\r\n\r\nasync processyxAction(){\r\n  try {\r\n    // 获取所有有扫码记录但用户yxid为空的情况\r\n    const scanRecords = await this.model('yx_scan')\r\n      .alias('scan')\r\n      .join('buss_student student ON scan.openid = student.openid')\r\n      .where({\r\n        'student.del_flag': 0,\r\n        'student.yxid': ['exp', 'IS NULL OR student.yxid = 0 OR student.yxid = \"\"']\r\n      })\r\n      .field('scan.openid, scan.yx_id, student.id as student_id, student.yxid')\r\n      .select();\r\n\r\n    if (think.isEmpty(scanRecords)) {\r\n      return this.json({\r\n        code: 200,\r\n        message: '没有需要更新的用户记录',\r\n        data: {\r\n          updated: 0\r\n        }\r\n      });\r\n    }\r\n\r\n    let updatedCount = 0;\r\n\r\n    // 批量更新用户的yxid\r\n    for (const record of scanRecords) {\r\n      if (record.yx_id && record.student_id) {\r\n        await this.model('student')\r\n          .where({\r\n            id: record.student_id,\r\n            del_flag: 0\r\n          })\r\n          .update({\r\n            yxid: record.yx_id\r\n          });\r\n        \r\n        updatedCount++;\r\n        \r\n        think.logger.info(`更新用户yxid: 学生ID=${record.student_id}, openid=${record.openid}, yxid=${record.yx_id}`);\r\n      }\r\n    }\r\n\r\n    return this.json({\r\n      code: 200,\r\n      message: `成功更新${updatedCount}条用户记录`,\r\n      data: {\r\n        updated: updatedCount,\r\n        total: scanRecords.length\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    think.logger.error('更新用户yxid失败:', error);\r\n    return this.json({\r\n      code: 500,\r\n      message: '更新用户yxid失败: ' + error.message,\r\n      data: {\r\n        updated: 0\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n\r\n}"]}