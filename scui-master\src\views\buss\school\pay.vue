<template>
	<el-dialog title="微信支付参数设置" v-model="visible" :width="800" destroy-on-close @closed="$emit('closed')">
		<el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="top" v-loading="loading">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="公众号APPID" prop="appid">
						<el-input v-model="form.appid" placeholder="请输入公众号APPID" clearable maxlength="50" show-word-limit></el-input>
						<div class="el-form-item-msg">在微信公众平台-开发-基本配置中获取</div>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="商户号" prop="mch_id">
						<el-input v-model="form.mch_id" placeholder="请输入微信支付商户号" clearable maxlength="20"></el-input>
						<div class="el-form-item-msg">在微信支付商户平台获取</div>
					</el-form-item>
				</el-col>

				<el-col :span="24">
					<el-form-item label="API密钥" prop="key">
						<el-input v-model="form.key" type="password" placeholder="请输入API密钥" clearable maxlength="100" show-password></el-input>
						<div class="el-form-item-msg">在微信支付商户平台-API安全中设置，请妥善保管</div>
					</el-form-item>
				</el-col>

				<el-col :span="24">
					<el-form-item label="支付结果通知地址" prop="notify_url">
						<el-input v-model="form.notify_url" placeholder="请输入支付结果通知地址">
							<template #append>
								<el-button @click="copyToClipboard(form.notify_url)" v-if="form.notify_url">
									<el-icon><el-icon-copy-document /></el-icon>
									复制
								</el-button>
							</template>
						</el-input>
						<div class="el-form-item-msg">微信支付结果通知接收地址，例如：https://您的域名/api/wechat/notify/wxpay</div>
					</el-form-item>
				</el-col>

				<el-col :span="24">
					<el-form-item label="支付证书" prop="cert_path">
					     <sc-upload-file v-model="form.cert_path" title="证书"  
					:apiObj="uploadApi"  :limit="1"  >
                    <el-button type="primary" icon="el-icon-upload">上传证书</el-button>
                </sc-upload-file>
		
						 
						 
						<div class="el-form-item-msg">在微信支付商户平台下载的证书文件,用于退款等操作</div>
					</el-form-item>
				</el-col>

				<el-col :span="24">
					<el-form-item label="备注说明">
						<el-input v-model="form.remarks" type="textarea" :autosize="{minRows: 3, maxRows: 6}" placeholder="请输入备注说明"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button type="primary" @click="submit" :loading="isSaving">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	name: 'pay',
	emits: ['success', 'closed'],
	data() {
		return {
			visible: false,
			loading: false,
			isSaving: false,
			//表单数据
			form: {
                id: 0,
				appid: '',
				mch_id: '',
				key: '',
				notify_url: '',
				remarks: '',
				cert_path: ''
			},
			 uploadApi: this.$API.common.upload,
			//验证规则
			rules: {
				appid: [
					{ required: true, message: '请输入公众号APPID', trigger: 'blur' },
					{ pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'APPID格式不正确', trigger: 'blur' }
				],
				mch_id: [
					{ required: true, message: '请输入微信支付商户号', trigger: 'blur' },
					{ pattern: /^\d{10}$/, message: '商户号格式不正确（10位数字）', trigger: 'blur' }
				],
				key: [
					{ required: true, message: '请输入API密钥', trigger: 'blur' },
					{ min: 6, message: 'API密钥长度不能少于6位', trigger: 'blur' }
				],
				notify_url: [
					{ required: true, message: '请输入支付结果通知地址', trigger: 'blur' },
					{ pattern: /^https?:\/\//, message: '通知地址必须以http://或https://开头', trigger: 'blur' }
				],
				cert_path: [
					{ required: true, message: '请上传支付证书', trigger: 'change' }
				]
			}
		}
	},
	methods: {
		// 证书上传前的验证
		beforeCertUpload(file) {
			const isPfx = file.type === '' && file.name.endsWith('.pfx');
			if (!isPfx) {
				this.$message.error('请上传.pfx格式的证书文件！');
			}
			const isLt2M = file.size / 1024 / 1024 < 2;
			if (!isLt2M) {
				this.$message.error('证书文件大小不能超过 2MB!');
			}
			return isPfx && isLt2M;
		},
		
		// 证书上传成功
		handleCertSuccess(res, file) {
			if (res.code === 200) {
				this.form.cert_path = res.data;
				this.$message.success('证书上传成功');
			} else {
				this.$message.error(res.message || '证书上传失败');
			}
		},

		//显示弹窗
		open() {
			this.visible = true;
			
			return this;
		},

		async init() {
			this.loading = true;
			try {
				// 加载微信支付配置
				let res = await this.$API.common.util.post(`/buss/school/getwxpay`,
							this.form);
				if (res.data) {
					this.form = { ...this.form, ...res.data };
				}
			} catch (error) {
				this.$message.error('加载配置失败：' + error.message);
			} finally {
				this.loading = false;
			}
		},

		//表单提交方法
		submit() {
			this.$refs.form.validate(async (valid) => {
				if (valid) {
					this.isSaving = true;
					try {
						// 保存配置
						let res = await this.$API.common.util.post(
							`/buss/school/wxpay`,
							this.form
						);
						
						if (res.code == 200) {
							this.$emit('success', this.form);
							this.visible = false;
							this.$message.success("保存成功");
						}
					} catch (error) {
						this.$message.error('保存失败：' + error.message);
					} finally {
						this.isSaving = false;
					}
				} else {
					return false;
				}
			});
		},

		async copyToClipboard(text) {
			try {
				await navigator.clipboard.writeText(text);
				this.$message.success('已复制到剪贴板');
			} catch (error) {
				// 降级方案
				const textArea = document.createElement('textarea');
				textArea.value = text;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
				this.$message.success('已复制到剪贴板');
			}
		},
		
		setData(data) {
            console.log(data);
			if (data.wxpayconfig) {
				this.form = JSON.parse(data.wxpayconfig);
			} else {
				this.form = { ...this.form, ...data };
			}
            this.init();
		}
	}
}
</script>

<style scoped>
.el-form-item-msg {
	color: #909399;
	font-size: 12px;
	line-height: 1.5;
	margin-top: 4px;
}

::v-deep(.el-form-item__label) {
	font-weight: 500;
}
</style>