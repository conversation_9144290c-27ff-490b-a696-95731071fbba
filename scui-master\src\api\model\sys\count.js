import config from "@/config";
import http from "@/utils/request";

const count = {
	officedata: {
		url: `${config.API_URL}/count/officedata`,
		name: "模块数据",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},
	chart1: {
		url: `${config.API_URL}/count/chart1`,
		name: "报表数据1",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},

	chart2: {
		url: `${config.API_URL}/count/chart2`,
		name: "报表数据1",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},

}


export default count;
