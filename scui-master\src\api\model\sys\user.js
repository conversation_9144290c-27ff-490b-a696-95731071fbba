import config from "@/config";
import http from "@/utils/request";

const user = {

	menu: {
		url: `${config.API_URL}/user/menu`,
		name: "登录获取用户菜单",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	login: {
		url: `${config.API_URL}/public/login`,
		name: "登录获取用户菜单和权限,全部权限",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	select: {
		url: `${config.API_URL}/user/select`,
		name: "用户选择列表",
		get: async function () {
			return await http.get(this.url);
		}
	},
	list: {
		url: `${config.API_URL}/user/list`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	listselect: {
		url: `${config.API_URL}/user/listselect`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	save: {
		url: `${config.API_URL}/user/save`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	del: {
		url: `${config.API_URL}/user/remove`,
		name: "删除用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	// resetpass: {
	// 	url: `${config.API_URL}/user/resetpass`,
	// 	name: "重置密码",
	// 	post: async function (params = {}) {
	// 		return await http.post(this.url, this.name, params);
	// 	}
	// },
	personal: {
		url: `${config.API_URL}/sys/user/info`,
		name: "获取用户登录信息",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

	currinfo: {
		url: `${config.API_URL}/sys/user/currinfo`,
		name: "获取当前用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},



	teacher: {
		url: `${config.API_URL}/sys/user/teacher`,
		name: "获取老师列表",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

	

	display:{
		url: `${config.API_URL}/sys/user/savedisplay`,
		name: "修改显示",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

	getqr: {
		url: `${config.API_URL}/sys/user/getqr`,
		name: "获取用户qr",
		get: async function (data = {}) {
			return await http.get(this.url, this.name, data);
		}
	},

	daiban: {
		url: `${config.API_URL}/count/daiban`,
		name: "待办数据统计",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	personalSavePassWord: {
		url: `${config.API_URL}/sys/user/savepassword`,
		name: "更改个人密码",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	personalSaveInfo: {
		url: `${config.API_URL}/sys/user/saveinfo`,
		name: "更改个人信息",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	dict: {
		url: `${config.API_URL}/sys/user/dict`,
		name: "个人信息字典",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

	savefilter:{
		url: `${config.API_URL}/sys/user/savefilter`,
		name: "保存过滤信息",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	getfilter:{
		url: `${config.API_URL}/sys/user/getfilter`,
		name: "读取过滤信息",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	}

	,
	delfilter:{
		url: `${config.API_URL}/sys/user/delfilter`,
		name: " 删除过滤信息",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	}
}
export default user;
