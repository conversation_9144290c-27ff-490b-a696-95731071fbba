import config from "@/config";
import http from "@/utils/request";

const dict = {
	list: {
		url: `${config.API_URL}/sys/sysdicttree/list`,
		name: "字典分类列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	save: {
		url: `${config.API_URL}/sys/sysdicttree/save`,
		name: "字典分类保存",
		post: async function (data) {
			//{where：{id：1}}
			return await http.post(this.url, this.name, data);
		}
	},
	add: {
		url: `${config.API_URL}/sys/sysdicttree/add`,
		name: "字典分类新增",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/sys/sysdicttree/delete`,
		name: "字典分类删除",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	infoPage: {
		url: `${config.API_URL}/sys/sysdict/page`,
		name: "字典列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	infoList: {
		url: `${config.API_URL}/sys/sysdict/list`,
		name: "字典列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	infoAdd: {
		url: `${config.API_URL}/sys/sysdict/add`,
		name: "字典添加",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	infoSave: {
		url: `${config.API_URL}/sys/sysdict/save`,
		name: "字典更新",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	infoDelete: {
		url: `${config.API_URL}/sys/sysdict/delete`,
		name: "字典删除",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	infoDict: {
		url: `${config.API_URL}/sys/sysdict/dict`,
		name: "字典分类",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	}
};

export default dict;
