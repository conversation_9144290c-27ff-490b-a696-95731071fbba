import config from "@/config";
import http from "@/utils/request";

const school = {
	page: {
		url: `${config.API_URL}/buss/school/page`,
		name: "菜单",
		get: async function () {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url);

		}
	}
	, save: {
		url: `${config.API_URL}/buss/school/save`,
		name: "保存",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}


	, info: {
		url: `${config.API_URL}/buss/school/info`,
		name: "保存",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}
	, remove: {
		url: `${config.API_URL}/buss/school/delete`,
		name: "删除",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

	listselect: {
		url: `${config.API_URL}/buss/school/listselect`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	timerecord: {
		url: `${config.API_URL}/buss/school/timerecord`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	}
}

export default school;
