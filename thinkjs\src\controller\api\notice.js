const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {

    async getwebAction(){
        let model=this.model("notice");
        const user = await this.session('userInfo');
        let res=await model.where({"touserid":user.id,state:0,"sendtype":3}).select();
        this.json(res);


      }

      async updateoneAction(){
        let id=this.get("id");
        let model=this.model("notice");
        const user = await this.session('userInfo');
        await model.where({"id":id,touserid:user.id}).update({"state":3});

        this.json({state:200});

      }

      async updateallAction(){

        let model=this.model("notice");
        const user = await this.session('userInfo');
        await model.where({touserid:user.id,state:0}).update({"state":3});

        this.json({state:200});

      }



}
