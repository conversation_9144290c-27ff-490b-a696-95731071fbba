module.exports = class extends think.Sequel {

  constructor(...props) {
    super(...props);
  }
  get schema() {
    return {
      attributes: {
        id: {
          type: think.Sequel.Sequel.INTEGER(11),
          primaryKey: true
        },
        no: think.Sequel.Sequel.STRING(128),
        project_id: think.Sequel.Sequel.STRING(128),
        name: think.Sequel.Sequel.STRING(128),
        area: think.Sequel.Sequel.STRING(128),
        note: think.Sequel.Sequel.TEXT,
        start_date: think.Sequel.Sequel.DATEONLY,
        end_date: think.Sequel.Sequel.DATEONLY,
        verify_status: think.Sequel.Sequel.STRING(64),
        verify_rate: think.Sequel.Sequel.STRING(64),
        sum_money: think.Sequel.Sequel.DECIMAL(10, 2),
        create_by: think.Sequel.Sequel.STRING(64),
        update_by: think.Sequel.Sequel.STRING(64)
      },
      options: {
        tableName: 'oa_costpay_main11'
      }
    };
  }


  // get schema() {
  //   return {
  //     attributes: {
  //       id: {
  //         type: think.Sequel.Sequel.BIGINT,
  //         primaryKey: true
  //       },
  //       teamId: think.Sequel.Sequel.BIGINT, // belongsTo, 为当前模型添加外键
  //       name: think.Sequel.Sequel.STRING(255),
  //     },
  //     options: {
  //       timestamps: false,
  //       freezeTableName: true,
  //       tableName: 'think_player',
  //     }
  //   }
  // }
};
