<template>

	<el-row>

		<el-button
			v-if="!foldBtnHidden  && fieldList.length > count"
			type="text"
			@click="showAll"
			class="search"
			style="margin-left: 6px;"
		>{{ icon === 'open' ? '收起搜索' : '展开搜索项' }}
			<i :class="icon === 'open' ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"/>
		</el-button>

	</el-row>

	<el-row style="flex-basis: 100%;">
		<div class="content" style="width: 100% !important;">
			<template v-for="(item, index) in fieldList" :key="index">
				<el-col :span="7.5">
					<div
						v-show="!foldBtnHidden && icon === 'close' ? index < count : true"
					>
						<div class="item-style">
								<span class="label-style" :style="{width: width.labelWidth + 'px'}" style="margin-top: auto;
    margin-bottom: auto;" >{{
										item.label
									}}:</span>
							<!-- 普通输入框 -->
							<el-input
								v-if="item.type === 'input'"
								v-model.trim="defaultData[item.value]"
								:size="item.size?item.size:size"
								:style="{width: width.itemWidth + 'px'}"
								:type="item.type"
								:disabled="item.disabled"
								:placeholder="getPlaceholder(item)"
								@blur="handleEvent($event, item.value)"
							/>
							<!-- 日期/时间 -->
							<el-date-picker
								v-if="item.type === 'date'"
								v-model="defaultData[item.value]"
								:size="item.size?item.size:size"
								:style="{width: width.itemWidth + 'px'}"
								:type="item.dateType"
								:disabledDate="item.disabledDate"
								:value-format="item.valueFormat"
								:clearable="item.clearable"
								:disabled="item.disabled"
								start-placeholder="开始时间"
								end-placeholder="结束时间"
								:placeholder="getPlaceholder(item)"
								@change="handleEvent($event, item.value, 'change')"
							/>
							<!-- 选择框 -->
							<el-select
								v-if="item.type === 'select'"
								v-model="defaultData[item.value]"
								:size="item.size?item.size:size"
								:style="{width: width.itemWidth + 'px'}"
								:disabled="item.disabled"
								:clearable="item.clearable?item.clearable:true"
								:filterable="item.filterable"
								:multiple="item.multiple"
								:placeholder="getPlaceholder(item)"
								@change="handleEvent($event, item.value, 'change')"
							>
								<el-option
									v-for="childItem in item.list"
									:key="childItem.id"
									:label="childItem.name"
									:value="childItem.id"
									:disabled="childItem.disabled"
								/>
							</el-select>
							<!--带sc下拉框-->
							<sc-select v-if="item.type === 'scSelect'"
							           v-model="defaultData[item.value]"
							           :style="{width: width.itemWidth + 'px'}"
							           :params="item.params"
							           :size="item.size?item.size:size"
							           :apiObj="item.apiObj"
							           :selectConfig="item.selectConfig"
							           :dic="item.dic"
							           :clearable="item.clearable?item.clearable:true"
							           :method="item.method"
							           :multiple="item.multiple"
							           :filterable="item.filterable"
							           :disabled="item.disabled"
							           @change="handleEvent($event, item.value, 'change')">
							</sc-select>
							<!--级联-->
							<el-cascader
								v-if="item.type === 'cascader'"
								v-model="defaultData[item.value]"
								:options="item.list"
								:props="item.props"
								:show-all-levels="item.showAllLevels"
								:clearable="item.clearable"
							></el-cascader>
							<!--下拉树-->
							<el-tree-select
								ref="treeSelect"
								v-if="item.type === 'treeSelect'"
								v-model="defaultData[item.value]"
								:data="item.list"
								:check-strictly="item.checkStrictly"
								:accordion="item.accordion"
								:filterable="item.filterable"
								:props="item.menuProps"
								:multiple="item.multiple"
								:size="item.size?item.size:size"
								@change="handleEvent($event, item.value, 'change')"
							/>
							<!-- 计数器 -->
							<el-input-number
								v-if="item.type === 'inputNumber'"
								v-model="defaultData[item.value]"
								:size="item.size?item.size:size"
								:style="{width: width.itemWidth + 'px'}"
								:min="item.min"
								:max="item.max"
								@change="handleEvent($event, item.value, 'change')"
							/>
						</div>
					</div>
				</el-col>
			</template>

			<el-col :span="7.5" v-show="icon==='open'">
				<div class="item-style">
					<span class="label-style" :style="{width: width.labelWidth + 'px'}"></span>
					<el-button
						:type="btnStyle[0].type"
						:size="btnStyle[0].size?btnStyle[0].size:size"
						:plain="btnStyle[0].plain"
						:round="btnStyle[0].round"
						:icon="btnStyle[0].icon"
						:disabled="btnStyle[0].disabled"
						@click="handleFilter"
					>
						{{ btnStyle[0].text }}
					</el-button>
					<el-button
						:type="btnStyle[1].type"
						:size="btnStyle[1].size?btnStyle[0].size:size"
						:plain="btnStyle[1].plain"
						:round="btnStyle[1].round"
						:disabled="btnStyle[1].disabled"
						:icon="btnStyle[1].icon"
						@click="handleReset"
					>
						{{ btnStyle[1].text }}
					</el-button>
				</div>

			</el-col>

		</div>
	</el-row>
<!--	<el-row v-if="icon==='open'" style="flex-basis: 100%;">-->
<!--		<el-col :span="22" :offset="2">-->
<!--			<div style="margin: 6px auto;"-->
<!--			>-->
<!--				<el-button-->
<!--					:type="btnStyle[0].type"-->
<!--					:size="size"-->
<!--					:plain="btnStyle[0].plain"-->
<!--					:round="btnStyle[0].round"-->
<!--					:icon="btnStyle[0].icon"-->
<!--					:disabled="btnStyle[0].disabled"-->
<!--					@click="handleFilter"-->
<!--				>-->
<!--					{{ btnStyle[0].text }}-->
<!--				</el-button>-->
<!--				<el-button-->
<!--					:type="btnStyle[1].type"-->
<!--					:size="size"-->
<!--					:plain="btnStyle[1].plain"-->
<!--					:round="btnStyle[1].round"-->
<!--					:disabled="btnStyle[1].disabled"-->
<!--					:icon="btnStyle[1].icon"-->
<!--					@click="handleReset"-->
<!--				>-->
<!--					{{ btnStyle[1].text }}-->
<!--				</el-button>-->
<!--			</div>-->

<!--		</el-col>-->
<!--	</el-row>-->
</template>
<script>
// filterInfo: {
// 	data: {
// 		name: null,
// 			age: null,
// 			count: 0,
// 			sex: 1,
// 			date: null,
// 			dateTime: null,
// 			range: null
// 	},
// 	fieldList: [
// 		{ label: '姓名', type: 'input', value: 'name' },
// 		{ label: '年龄', type: 'input', value: 'age', disabled: true },
// 		{ label: '计数', type: 'inputNumber', value: 'count', min: 1, max: 10 },
// 		{ label: '性别', type: 'select', value: 'sex', list: [
// 				{ id: 1, name: '男' },
// 				{ id: 2, name: '女' },
// 				{ id: 3, name: '保密' },
// 			] },
// 		{ label: '日期', type: 'date', value: 'date' },
// 		{ label: '创建时间', type: 'date', value: 'dateTime', dateType: 'datetime', clearable: true  },
// 		{ label: '时间区间', type: 'date', value: 'range', dateType: 'daterange' }
// 	],
// 		/**搜索/重置区域是否隐藏 */
// 		// btnHidden: true,
// 		/**组件尺寸 */
// 		// size: 'small',
// 		/**默认展示搜索条件 */
// 		count: 4,
// 		/**label/item 宽度 */
// 		// width: {
// 		//   labelWidth: 110,
// 		//   itemWidth: 220
// 		// },
// 		/**配置项 */
// 		btnStyle: [
// 		{ icon: 'el-icon-search', text: '过滤',type: 'primary' },
// 		{ icon: 'el-icon-refresh', text: '重置' }
// 	]
// },
export default {
	name: 'scSearch',
	props: {
		/**查询展开数据 */
		popoverSize: {
			type: String,
			default: 'mini'
		},
		/**字段默认数据 */
		data: {
			type: Object,
			default: () => ({})
		},
		/**字段配置项 */
		fieldList: {
			type: Array,
			default: () => []
		},
		/**相关的列表 */
		listTypeInfo: {
			type: Object,
			default: () => ({})
		},
		/**按钮区域是否隐藏 */
		btnHidden: {
			type: Boolean,
			default: false
		},
		foldBtnHidden: {
			type: Boolean,
			default: false
		},
		/**组件尺寸 */
		size: {
			type: String,
			default: 'small'
		},
		/**默认搜索数 */
		count: {
			type: Number,
			default: 0
		},
		/**组件及label宽度 */
		width: {
			type: Object,
			default: () => ({
				labelWidth: 110,
				itemWidth: 220
			})
		},
		/**按钮配置 */
		btnStyle: {
			type: Array,
			default: () => [
				{icon: null, text: '搜索', disabled: false, type: 'primary', plain: false, round: false},
				{icon: null, text: '重置', disabled: false, type: null, plain: false, round: false}
			]
		}
	},
	data() {
		return {
			defaultData: JSON.parse(JSON.stringify(this.data)),
			icon: 'close'
		}
	},
	mounted() {
		/**
		 * 子组件无法直接修改父组件传递过来的值
		 * 于是将父组件传递的值首先赋值给 defaultData
		 * 在搜索条件中同样使用 defaultData
		 * 永远保持 props 传递的 data 纯洁度
		 */
		// this.defaultData = {...this.data}
	},
	methods: {
		/**
		 * @func 占位符显示
		 * @param {Object} row
		 * @desc 📝
		 */
		close() {
			this.icon = 'close'
		},
		showAll() {
			if (this.icon === 'close') {
				this.icon = 'open'
			} else {
				this.icon = 'close'
			}
			this.$emit('showStatus', this.icon)
		},
		getValue(value, item) {
			this.defaultData[item.value] = value;
		},
		/**
		 * @func 占位符显示
		 * @param {Object} row
		 * @desc 📝
		 */

		getPlaceholder(row) {
			let placeholder
			if (row.type === 'input') {
				placeholder = '请输入' + row.label
			} else if (row.type === 'select' || row.type === 'time' || row.type === 'date') {
				placeholder = '请选择' + row.label
			} else {
				placeholder = row.label
			}
			return placeholder
		},
		/**
		 * @func 事件处理
		 * @desc 📝
		 */
		handleEvent(event, val, change, type) {
			let obj = {
				value: change === 'change' ? event : event.target.value,
				label: val
			}
			this.$emit('handleEvent', obj, this.defaultData)
		},
		/**
		 * @func 搜索
		 * @desc 📝
		 */
		handleFilter() {
			this.$emit('handleFilter', this.defaultData)
		},
		/**
		 * @func 重置
		 * @desc 📝
		 */
		handleReset() {
			this.defaultData = {...this.data}
			// this.$refs.treeSelect.clearHandle();
			this.$emit('handleReset', this.defaultData)
		}
	}
}
</script>
<style lang="css" scoped>
.content {
	display: flex;
	flex-wrap: wrap !important;
	position: relative;
}

.content .item-style {
	margin: 5px auto;
	line-height: 1;
	display: flex;
}

.content .item-style .label-style {
	display: inline-block;
	justify-self: end;
	font-size: 13px;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	text-align: right;
	margin-right: 12px;
	color: #222222;
}

.btn-style {
	margin: 6px 0 6px auto;
}
</style>
