<template>
  <el-container>
    <el-container>
      <el-header>
        <div class="left-panel">
          <el-button type="primary" icon="el-icon-plus" @click="add"></el-button>
        </div>
        <div class="right-panel">
          <div class="right-panel-search">


               <el-select v-model="search.nj" placeholder="请选择年级" @change="getlesson" clearable>
              <el-option label="小学" value="小学"></el-option>
              <el-option label="初中" value="初中"></el-option>
              <el-option label="高中" value="高中"></el-option>
            </el-select>
            <el-select v-model="search.lesson" placeholder="请选择学科" clearable>
              <el-option v-for="item in lessonList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
          </div>
        </div>
      </el-header>
      <el-main class="nopadding">
        <scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
          remoteFilter>
          <el-table-column type="selection" width="50"></el-table-column>
           <el-table-column label="学校" prop="schoolname" width="150"></el-table-column>
            <el-table-column label="年级" prop="nj" width="70"></el-table-column>
            <el-table-column label="学科" prop="lessonname" width="70"></el-table-column>
          <el-table-column label="商品名称" prop="name" width="150"></el-table-column>
          <el-table-column label="价格" prop="price" width="100">
            <template #default="scope">
              ¥{{ scope.row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="购买时长(天)" prop="duration" width="120"></el-table-column>
          <el-table-column label="数量" prop="quantity" width="80"></el-table-column>
          <el-table-column label="状态" prop="status" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.status == 1" type="success">上架</el-tag>
              <el-tag v-if="scope.row.status == 0">下架</el-tag>
            </template>
          </el-table-column>

            <el-table-column label="是否计算工资" prop="ifjs" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.ifjs == 1" type="success">是</el-tag>
              <el-tag v-if="scope.row.ifjs == 0">否</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="添加时间" prop="create_time" width="150" sortable='create_date'></el-table-column>
          <el-table-column label="添加人" prop="uname" width="120" sortable='custom'></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="140">
            <template #default="scope">
              <el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
              </el-button>
              <el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
              </el-button>
              <el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
  </el-container>

  <save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
</template>

<script>
import saveDialog from './save'

export default {
  name: 'product',
  components: {
    saveDialog
  },
  data() {
    return {
      dialog: {
        save: false
      },
      showGrouploading: false,
      groupFilterText: '',
      group: [],
      apiObj: this.$API.product.page,
      selection: [],
      search: {
        nj:null
      },

      lessonList: [],
      defaultProps: {
        label: 'name'
      }
    }
  },
  watch: {
    groupFilterText(val) {
      this.$refs.group && this.$refs.group.filter(val);
    }
  },
  mounted() {
    // 如果需要加载初始数据，可以在这里调用
  },
  methods: {
    // 添加
    add() {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('add');
      })
    },
    // 编辑
    async table_edit(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('edit').setData(row)
      })
    },
    // 查看
    table_show(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('show').setData(row)
      })
    },
    // 删除
    async table_del(row, index) {
      var reqData = { id: row.id }
      var res = await this.$API.product.delete.post(reqData);
      if (res.code == 200) {
        // 这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功")
      } else {
        this.$alert(res.message, "提示", { type: 'error' })
      }
    },
    // 表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    // 搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    async getlesson(){

      
      let res = await this.$API.common.util.post('/buss/lesson/lessonbynj',{where:{type:this.search.nj,parent_id:0}  })

      
      this.lessonList = res;
    },
    // 本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    // 批量删除
    async batchDel() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      
      const ids = this.selection.map(item => item.id);
      
      this.$confirm('确定要删除选中的商品吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        const res = await this.$API.product.batchDel.post({ ids });
        if (res.code === 200) {
          this.$message.success("批量删除成功");
          this.$refs.table.refresh();
        } else {
          this.$alert(res.message, "提示", { type: 'error' });
        }
      }).catch(() => {});
    },
    // 导出数据
 
  }
}
</script>

<style>
.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: auto !important;
  padding-top: 10px;
  padding-bottom: 10px;
}

.left-panel {
  display: flex;
  align-items: center;
}

.right-panel {
  display: flex;
}

.right-panel-search {
  display: flex;
  align-items: center;
}

.right-panel-search .el-input {
  margin-right: 10px;
  width: 180px;
}
</style>
