const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {

    async removeAction() {
        const id = this.post('id');
    
        const model = this.model('bk');
        await model.where({'id': id}).update({'del_flag': 1});

        await this.model("bk_list").where({"bkid":id}).delete();
        await this.model("bk_user").where({"bkid":id}).delete();
    
        return this.json({'code': 200});
      }


      async tkpage3Action() {
        let respData = {};
   
        const userInfo = await this.session('userInfo');

        const page = this.get('page') ? this.get('page') : 1;
        const rows = 15;
        const where = this.get('where') ? JSON.parse(this.get('where')) : {};
        const model = this.model('tk_school');
        where['c.del_flag'] = 0;


        if(think.isEmpty(where.lessonid)){
          let  respData = {
                code: 200,
                count: 0,
                data:[],
                msg: ''
              };
              return this.json(respData);

        }

        if(!think.isEmpty(where.start)){
            let start=where.start
            delete where.start
            where["c.score"]=[">=",parseInt(start)];
        }
        
        if(!think.isEmpty(where.end)){

            let end=where.end
            delete where.end
            where["c.score"]=["<=",parseInt(end)];
        }


        if(!think.isEmpty(where.lessonid)){

            let lession_id=where.lessonid.split(",")
            delete where.lessonid
            where["c.lessonid"]=["in",lession_id];
        }


    
       
        if(this.get("state")==1||this.get("state")==0){
          where['c.state']=this.get("state")
        }

        let order='c.score asc';
        let orderstr=this.get("order");
        if(orderstr=="descending"){
          order=" c."+this.get("prop")+" desc";

        }

        if(orderstr=="ascending"){
          order=" c."+this.get("prop")+" asc";

        }

        where["d.num2"]=[">",0]


        let sql2=""

        if(!think.isEmpty(where.ifjj)){
          console.log(where)

          if(where.ifjj=="未讲解"){
             sql2="  c.id not in (select tkid from sys_bk_list )"
          } else if(where.ifjj=="已讲解"){
            sql2="  c.id in (select tkid from sys_bk_list )"
          }
            
        }
        delete where.ifjj


        where['d.schoolid']=userInfo.schoolid;
        const response = await model.alias("d").join("sys_tk c on c.id=d.tkid").page(page, rows).where(where).where(sql2).field("c.*,d.num2 as num2").order(order).countSelect();
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          msg: ''
        };
        return this.json(respData);
      }







      async tkpage2Action() {
        let respData = {};
   
        const userInfo = await this.session('userInfo');

        const page = this.get('page') ? this.get('page') : 1;
        const rows = 15;
        const where = this.get('where') ? JSON.parse(this.get('where')) : {};
        const model = this.model('jj');
        where['c.del_flag'] = 0;


        if(think.isEmpty(where.lessonid)){
          let  respData = {
                code: 200,
                count: 0,
                data:[],
                msg: ''
              };
              return this.json(respData);

        }

        if(!think.isEmpty(where.start)){
            let start=where.start
            delete where.start
            where["c.score"]=[">=",parseInt(start)];
        }
        
        if(!think.isEmpty(where.end)){

            let end=where.end
            delete where.end
            where["c.score"]=["<=",parseInt(end)];
        }


        if(!think.isEmpty(where.lessonid)){

            let lession_id=where.lessonid.split(",")
            delete where.lessonid
            where["c.lessonid"]=["in",lession_id];
        }


      if(!think.isEmpty(where)&&!think.isEmpty(where.lessonid)){
        //await this.updatelesson(where.lessonid);

      }
       
        if(this.get("state")==1||this.get("state")==0){
          where['c.state']=this.get("state")
        }

        where['d.schoolid']=userInfo.schoolid;

        let order='c.score asc';
        let orderstr=this.get("order");
        if(orderstr=="descending"){
          order=" c."+this.get("prop")+" desc";

        }

        if(orderstr=="ascending"){
          order=" c."+this.get("prop")+" asc";

        }


        let sql2=""

        if(!think.isEmpty(where.ifjj)){
          console.log(where)

          if(where.ifjj=="未讲解"){
             sql2="  c.id not in (select tkid from sys_bk_list )"
          } else if(where.ifjj=="已讲解"){
            sql2="  c.id in (select tkid from sys_bk_list )"
          }
            
        }
        delete where.ifjj

        const response = await model.alias("d").join("sys_tk c on c.id=d.tkid").page(page, rows).where(where).where(sql2).field("c.*,d.num as num2").order(order).countSelect();
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          msg: ''
        };
        return this.json(respData);
      }




      async infoAction(){

        let respData = {};
        const id = this.post('id') ? this.post('id') : null;
        const model = this.model('bk');
        respData=   await model.where({id: id}).find();
     
        return this.json(respData);
      }
    



    
      async tkpageAction() {
        let respData = {};
   
        const userInfo = await this.session('userInfo');

        const page = this.get('page') ? this.get('page') : 1;
        const rows = 15;
        const where = this.get('where') ? JSON.parse(this.get('where')) : {};
        const model = this.model('tk');
        where['c.del_flag'] = 0;


        if(think.isEmpty(where.lessonid)){
          let  respData = {
                code: 200,
                count: 0,
                data:[],
                msg: ''
              };
              return this.json(respData);

        }


        

        if(!think.isEmpty(where.start)){
            let start=where.start
            delete where.start
            where["c.score"]=[">=",parseInt(start)];
        }
        
        if(!think.isEmpty(where.end)){

            let end=where.end
            delete where.end
            where["c.score"]=["<=",parseInt(end)];
        }


        if(!think.isEmpty(where.lessonid)){

            let lession_id=where.lessonid.split(",")
            delete where.lessonid
            where["c.lessonid"]=["in",lession_id];
        }


      if(!think.isEmpty(where)&&!think.isEmpty(where.lessonid)){
        //await this.updatelesson(where.lessonid);

      }
       
        if(this.get("state")==1||this.get("state")==0){
          where['c.state']=this.get("state")
        }

        let order='c.score asc';
        let orderstr=this.get("order");
        if(orderstr=="descending"){
          order=" c."+this.get("prop")+" desc";

        }

        if(orderstr=="ascending"){
          order=" c."+this.get("prop")+" asc";

        }
        let sql2=""

        if(!think.isEmpty(where.ifjj)){
          console.log(where)

          if(where.ifjj=="未讲解"){
             sql2="  c.id not in (select tkid from sys_bk_list )"
          } else if(where.ifjj=="已讲解"){
            sql2="  c.id in (select tkid from sys_bk_list )"
          }
            
        }
        delete where.ifjj

        const response = await model.alias("c").join("left join (select * from sys_tk_school where schoolid="+userInfo.schoolid+") l on l.tkid=c.id").page(page, rows).where(where).where(sql2).field("c.*,l.num2").order(order).countSelect();
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          msg: ''
        };
        return this.json(respData);
      }



      async tmpsubmitAction(){
        const userInfo = await this.session('userInfo');
        const allParams = this.post();
        console.log(allParams);
        let model=this.model("bk_list")
        let arr=[];
        let tkids=this.post("ids");

        let para=JSON.parse(tkids);

        for(let obj of para.list){
          if(!think.isEmpty(obj.id)){
            let data={};
            console.log(obj.id)

            data.tkid=obj.id;



            data.bkid=this.post("id");
            data.schoolid=userInfo.schoolid;
            data.bktype=obj.bktype;
            arr.push(data);

          }
            

        }

        console.log( arr)

        await model.where({bkid:this.post("id")}).delete();
        await model.addMany(arr);
        
        return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1});
        
      }




      async gettoplessonAction(){
        const userInfo = await this.session('userInfo');
        let model = this.model("user_lesson");
        let res=await model.alias("c").join("sys_lesson l on l.id=c.lession_id").field("l.id,l.name").where({"user_id":userInfo.id}).select();
    
    
        return this.json(res);
    
    
    
    
      }

      async submitAction(){
        const userInfo = await this.session('userInfo');
        const allParams = this.post();
        console.log(allParams);
        let model=this.model("bk_list")
        let arr=[];
        let tkids=this.post("ids");
        let para=JSON.parse(tkids);

        for(let obj of para.list){
          if(!think.isEmpty(obj.id)){
            let data={};
            console.log(obj.id)

            data.tkid=obj.id;



            data.bkid=this.post("id");
            data.schoolid=userInfo.schoolid;
            data.bktype=obj.bktype;
            arr.push(data);

          }
            

        }

        await model.where({bkid:this.post("id")}).delete();
        await model.addMany(arr);
        await this.model("bk").where({"id":this.post("id")}).update({"state":2,"fbdate":think.datetime()});
        await this.model("bk_user").where({"bkid":this.post("id")}).delete();
        return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1});
        
      }


      async getlistAction(){
        const userInfo = await this.session('userInfo');
        let model=this.model("bk_list");
        let bkid=this.get("id");
        let where={"a.bkid":bkid};
        let res=await model.alias("a").join("left join sys_tk c on c.id=a.tkid")
        .join(" left join (select * from sys_tk_school where schoolid="+userInfo.schoolid+") d on d.tkid=a.tkid ")
        .where(where).field("c.*,d.num2 as num2,a.bktype,a.bkid as id2")
        .select();
        return this.json(res);


      }



    async saveAction() {
        const colums = ['title', 'reamrks', 'lessonname', 'skdate', 'remarks', 'password', 'role', 'user_type', 'birthday',"schoolid","id","lesson","toplesson"];
        const allParams = this.post();
        const data = {};
        for (var c of colums) {
          if (c in allParams) {
            data[c] = allParams[c];
          }
        }
    

        const model = this.model('bk');

      
        let lesson=data.lesson;
        console.log(lesson)

        let arr=lesson.split(",");
        let lessonname=[];
        for(var item of arr){

          let lesson=await this.model("lesson").where({id:item}).find();
          lessonname.push(lesson.name);

        }

        data.lessonname=lessonname.join(",");

        
        if (this.post('id')) { 
         
          await model.where({id: this.post('id')}).update(data);
    
          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
        } else {
            
            const userInfo = await this.session('userInfo');
            data.teacherid=userInfo.id;
            data.schoolid=userInfo.schoolid;
            data.state=1;
            data.create_date=think.datetime();

          await model.add(data);

          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
        }
      }
 

  
  async listAction() {
    let order = 'c.create_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');
    const where2 = this.get('where') ? JSON.parse(this.get('where')) : {};
    const keyword = this.get('name');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)&&!think.isEmpty(this.get('order'))) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('bk');
    const where = {'c.del_flag': 0};
    if (!think.isEmpty(keyword)) {
      where['c.title'] = ['like', '%' + keyword + '%'];
    }

    const userInfo = await this.session('userInfo');
   
    console.log(userInfo);

    if(userInfo.name!="系统管理员"){

        let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

        if(think.isEmpty(schoolres)){
            where["c.teacherid"]=userInfo.id
           
        }else{

            where["c.schoolid"]=["in",schoolres.map(item => item.id)];
        }

     
      }
   
    if (!think.isEmpty(this.get("title"))) {
      where['c.title'] = ['like', '%' + this.get("name") + '%'];
    }




    const res = await model
      .alias('c')
      .field('c.*,t.name as teacher')
       
      .join(["buss_school s on s.id=c.schoolid"])
      .join(["sys_user t on t.id=c.teacherid"])
      .page(page, rows)
      .where(where)
    .where(where2)
      .order(order)
      .countSelect();
    const data = {};

    for(let item of res.data){
        item.lessonname=item.lessonname.replace(/,/g, '<br>');
        item.skdate=item.skdate.replace(/ /g, '<br>');
    }




    
    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';

  
 
    return this.json(data);
  }

 
  async recordAction(){

    let respData = {};
    const id = this.post('id') ? this.post('id') : null;

 

    const model = this.model('tk');
    const userInfo = await this.session('userInfo');
    respData=   await model.where({id: id}).find();
    
    return this.json(respData);
  }
  async recordlistAction(){
    const page = this.get('page') ? this.get('page') : 1;
    const rows = 15;
    const userInfo = await this.session('userInfo');
    const model = this.model('tk_record');
    let tkid=this.get("tkid");
    let where={};
    where['c.tkid']=tkid;
    where['c.schoolid']=userInfo.schoolid;
    const res = await model.alias("c").join("buss_student u on u.id=c.userid").page(page,rows).where(where).field("c.*,u.name").order("c.create_date asc").countSelect();
   let respData = {
      code: 200,
      count: res.count,
      data: res.data,
      msg: ''
    };
    return this.json(respData);
  }

}