function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const BaseRest = require('../rest.js');

module.exports = class extends BaseRest {

        removeAction() {
                var _this = this;

                return _asyncToGenerator(function* () {
                        const id = _this.post('id');

                        const model = _this.model('bk');
                        yield model.where({ 'id': id }).update({ 'del_flag': 1 });

                        yield _this.model("bk_list").where({ "bkid": id }).delete();
                        yield _this.model("bk_user").where({ "bkid": id }).delete();

                        return _this.json({ 'code': 200 });
                })();
        }

        tkpage3Action() {
                var _this2 = this;

                return _asyncToGenerator(function* () {
                        let respData = {};

                        const userInfo = yield _this2.session('userInfo');

                        const page = _this2.get('page') ? _this2.get('page') : 1;
                        const rows = 15;
                        const where = _this2.get('where') ? JSON.parse(_this2.get('where')) : {};
                        const model = _this2.model('tk_school');
                        where['c.del_flag'] = 0;

                        if (think.isEmpty(where.lessonid)) {
                                let respData = {
                                        code: 200,
                                        count: 0,
                                        data: [],
                                        msg: ''
                                };
                                return _this2.json(respData);
                        }

                        if (!think.isEmpty(where.start)) {
                                let start = where.start;
                                delete where.start;
                                where["c.score"] = [">=", parseInt(start)];
                        }

                        if (!think.isEmpty(where.end)) {

                                let end = where.end;
                                delete where.end;
                                where["c.score"] = ["<=", parseInt(end)];
                        }

                        if (!think.isEmpty(where.lessonid)) {

                                let lession_id = where.lessonid.split(",");
                                delete where.lessonid;
                                where["c.lessonid"] = ["in", lession_id];
                        }

                        if (_this2.get("state") == 1 || _this2.get("state") == 0) {
                                where['c.state'] = _this2.get("state");
                        }

                        let order = 'c.score asc';
                        let orderstr = _this2.get("order");
                        if (orderstr == "descending") {
                                order = " c." + _this2.get("prop") + " desc";
                        }

                        if (orderstr == "ascending") {
                                order = " c." + _this2.get("prop") + " asc";
                        }

                        where["d.num2"] = [">", 0];

                        let sql2 = "";

                        if (!think.isEmpty(where.ifjj)) {
                                console.log(where);

                                if (where.ifjj == "未讲解") {
                                        sql2 = "  c.id not in (select tkid from sys_bk_list )";
                                } else if (where.ifjj == "已讲解") {
                                        sql2 = "  c.id in (select tkid from sys_bk_list )";
                                }
                        }
                        delete where.ifjj;

                        where['d.schoolid'] = userInfo.schoolid;
                        const response = yield model.alias("d").join("sys_tk c on c.id=d.tkid").page(page, rows).where(where).where(sql2).field("c.*,d.num2 as num2").order(order).countSelect();
                        respData = {
                                code: 200,
                                count: response.count,
                                data: response.data,
                                msg: ''
                        };
                        return _this2.json(respData);
                })();
        }

        tkpage2Action() {
                var _this3 = this;

                return _asyncToGenerator(function* () {
                        let respData = {};

                        const userInfo = yield _this3.session('userInfo');

                        const page = _this3.get('page') ? _this3.get('page') : 1;
                        const rows = 15;
                        const where = _this3.get('where') ? JSON.parse(_this3.get('where')) : {};
                        const model = _this3.model('jj');
                        where['c.del_flag'] = 0;

                        if (think.isEmpty(where.lessonid)) {
                                let respData = {
                                        code: 200,
                                        count: 0,
                                        data: [],
                                        msg: ''
                                };
                                return _this3.json(respData);
                        }

                        if (!think.isEmpty(where.start)) {
                                let start = where.start;
                                delete where.start;
                                where["c.score"] = [">=", parseInt(start)];
                        }

                        if (!think.isEmpty(where.end)) {

                                let end = where.end;
                                delete where.end;
                                where["c.score"] = ["<=", parseInt(end)];
                        }

                        if (!think.isEmpty(where.lessonid)) {

                                let lession_id = where.lessonid.split(",");
                                delete where.lessonid;
                                where["c.lessonid"] = ["in", lession_id];
                        }

                        if (!think.isEmpty(where) && !think.isEmpty(where.lessonid)) {
                                //await this.updatelesson(where.lessonid);

                        }

                        if (_this3.get("state") == 1 || _this3.get("state") == 0) {
                                where['c.state'] = _this3.get("state");
                        }

                        where['d.schoolid'] = userInfo.schoolid;

                        let order = 'c.score asc';
                        let orderstr = _this3.get("order");
                        if (orderstr == "descending") {
                                order = " c." + _this3.get("prop") + " desc";
                        }

                        if (orderstr == "ascending") {
                                order = " c." + _this3.get("prop") + " asc";
                        }

                        let sql2 = "";

                        if (!think.isEmpty(where.ifjj)) {
                                console.log(where);

                                if (where.ifjj == "未讲解") {
                                        sql2 = "  c.id not in (select tkid from sys_bk_list )";
                                } else if (where.ifjj == "已讲解") {
                                        sql2 = "  c.id in (select tkid from sys_bk_list )";
                                }
                        }
                        delete where.ifjj;

                        const response = yield model.alias("d").join("sys_tk c on c.id=d.tkid").page(page, rows).where(where).where(sql2).field("c.*,d.num as num2").order(order).countSelect();
                        respData = {
                                code: 200,
                                count: response.count,
                                data: response.data,
                                msg: ''
                        };
                        return _this3.json(respData);
                })();
        }

        infoAction() {
                var _this4 = this;

                return _asyncToGenerator(function* () {

                        let respData = {};
                        const id = _this4.post('id') ? _this4.post('id') : null;
                        const model = _this4.model('bk');
                        respData = yield model.where({ id: id }).find();

                        return _this4.json(respData);
                })();
        }

        tkpageAction() {
                var _this5 = this;

                return _asyncToGenerator(function* () {
                        let respData = {};

                        const userInfo = yield _this5.session('userInfo');

                        const page = _this5.get('page') ? _this5.get('page') : 1;
                        const rows = 15;
                        const where = _this5.get('where') ? JSON.parse(_this5.get('where')) : {};
                        const model = _this5.model('tk');
                        where['c.del_flag'] = 0;

                        if (think.isEmpty(where.lessonid)) {
                                let respData = {
                                        code: 200,
                                        count: 0,
                                        data: [],
                                        msg: ''
                                };
                                return _this5.json(respData);
                        }

                        if (!think.isEmpty(where.start)) {
                                let start = where.start;
                                delete where.start;
                                where["c.score"] = [">=", parseInt(start)];
                        }

                        if (!think.isEmpty(where.end)) {

                                let end = where.end;
                                delete where.end;
                                where["c.score"] = ["<=", parseInt(end)];
                        }

                        if (!think.isEmpty(where.lessonid)) {

                                let lession_id = where.lessonid.split(",");
                                delete where.lessonid;
                                where["c.lessonid"] = ["in", lession_id];
                        }

                        if (!think.isEmpty(where) && !think.isEmpty(where.lessonid)) {
                                //await this.updatelesson(where.lessonid);

                        }

                        if (_this5.get("state") == 1 || _this5.get("state") == 0) {
                                where['c.state'] = _this5.get("state");
                        }

                        let order = 'c.score asc';
                        let orderstr = _this5.get("order");
                        if (orderstr == "descending") {
                                order = " c." + _this5.get("prop") + " desc";
                        }

                        if (orderstr == "ascending") {
                                order = " c." + _this5.get("prop") + " asc";
                        }
                        let sql2 = "";

                        if (!think.isEmpty(where.ifjj)) {
                                console.log(where);

                                if (where.ifjj == "未讲解") {
                                        sql2 = "  c.id not in (select tkid from sys_bk_list )";
                                } else if (where.ifjj == "已讲解") {
                                        sql2 = "  c.id in (select tkid from sys_bk_list )";
                                }
                        }
                        delete where.ifjj;

                        const response = yield model.alias("c").join("left join (select * from sys_tk_school where schoolid=" + userInfo.schoolid + ") l on l.tkid=c.id").page(page, rows).where(where).where(sql2).field("c.*,l.num2").order(order).countSelect();
                        respData = {
                                code: 200,
                                count: response.count,
                                data: response.data,
                                msg: ''
                        };
                        return _this5.json(respData);
                })();
        }

        tmpsubmitAction() {
                var _this6 = this;

                return _asyncToGenerator(function* () {
                        const userInfo = yield _this6.session('userInfo');
                        const allParams = _this6.post();
                        console.log(allParams);
                        let model = _this6.model("bk_list");
                        let arr = [];
                        let tkids = _this6.post("ids");

                        let para = JSON.parse(tkids);

                        for (let obj of para.list) {
                                if (!think.isEmpty(obj.id)) {
                                        let data = {};
                                        console.log(obj.id);

                                        data.tkid = obj.id;

                                        data.bkid = _this6.post("id");
                                        data.schoolid = userInfo.schoolid;
                                        data.bktype = obj.bktype;
                                        arr.push(data);
                                }
                        }

                        console.log(arr);

                        yield model.where({ bkid: _this6.post("id") }).delete();
                        yield model.addMany(arr);

                        return _this6.json({ 'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1 });
                })();
        }

        gettoplessonAction() {
                var _this7 = this;

                return _asyncToGenerator(function* () {
                        const userInfo = yield _this7.session('userInfo');
                        let model = _this7.model("user_lesson");
                        let res = yield model.alias("c").join("sys_lesson l on l.id=c.lession_id").field("l.id,l.name").where({ "user_id": userInfo.id }).select();

                        return _this7.json(res);
                })();
        }

        submitAction() {
                var _this8 = this;

                return _asyncToGenerator(function* () {
                        const userInfo = yield _this8.session('userInfo');
                        const allParams = _this8.post();
                        console.log(allParams);
                        let model = _this8.model("bk_list");
                        let arr = [];
                        let tkids = _this8.post("ids");
                        let para = JSON.parse(tkids);

                        for (let obj of para.list) {
                                if (!think.isEmpty(obj.id)) {
                                        let data = {};
                                        console.log(obj.id);

                                        data.tkid = obj.id;

                                        data.bkid = _this8.post("id");
                                        data.schoolid = userInfo.schoolid;
                                        data.bktype = obj.bktype;
                                        arr.push(data);
                                }
                        }

                        yield model.where({ bkid: _this8.post("id") }).delete();
                        yield model.addMany(arr);
                        yield _this8.model("bk").where({ "id": _this8.post("id") }).update({ "state": 2, "fbdate": think.datetime() });
                        yield _this8.model("bk_user").where({ "bkid": _this8.post("id") }).delete();
                        return _this8.json({ 'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1 });
                })();
        }

        getlistAction() {
                var _this9 = this;

                return _asyncToGenerator(function* () {
                        const userInfo = yield _this9.session('userInfo');
                        let model = _this9.model("bk_list");
                        let bkid = _this9.get("id");
                        let where = { "a.bkid": bkid };
                        let res = yield model.alias("a").join("left join sys_tk c on c.id=a.tkid").join(" left join (select * from sys_tk_school where schoolid=" + userInfo.schoolid + ") d on d.tkid=a.tkid ").where(where).field("c.*,d.num2 as num2,a.bktype,a.bkid as id2").select();
                        return _this9.json(res);
                })();
        }

        saveAction() {
                var _this10 = this;

                return _asyncToGenerator(function* () {
                        const colums = ['title', 'reamrks', 'lessonname', 'skdate', 'remarks', 'password', 'role', 'user_type', 'birthday', "schoolid", "id", "lesson", "toplesson"];
                        const allParams = _this10.post();
                        const data = {};
                        for (var c of colums) {
                                if (c in allParams) {
                                        data[c] = allParams[c];
                                }
                        }

                        const model = _this10.model('bk');

                        let lesson = data.lesson;
                        console.log(lesson);

                        let arr = lesson.split(",");
                        let lessonname = [];
                        for (var item of arr) {

                                let lesson = yield _this10.model("lesson").where({ id: item }).find();
                                lessonname.push(lesson.name);
                        }

                        data.lessonname = lessonname.join(",");

                        if (_this10.post('id')) {

                                yield model.where({ id: _this10.post('id') }).update(data);

                                return _this10.json({ 'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1 });
                        } else {

                                const userInfo = yield _this10.session('userInfo');
                                data.teacherid = userInfo.id;
                                data.schoolid = userInfo.schoolid;
                                data.state = 1;
                                data.create_date = think.datetime();

                                yield model.add(data);

                                return _this10.json({ 'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1 });
                        }
                })();
        }

        listAction() {
                var _this11 = this;

                return _asyncToGenerator(function* () {
                        let order = 'c.create_date desc';
                        const page = _this11.get('page');
                        const rows = _this11.get('pageSize');
                        const where2 = _this11.get('where') ? JSON.parse(_this11.get('where')) : {};
                        const keyword = _this11.get('name');
                        const prop = _this11.get('prop');
                        if (!think.isEmpty(prop) && !think.isEmpty(_this11.get('order'))) {
                                order = prop + ' ' + _this11.get('order').replace('ending', '');
                        }

                        const model = _this11.model('bk');
                        const where = { 'c.del_flag': 0 };
                        if (!think.isEmpty(keyword)) {
                                where['c.title'] = ['like', '%' + keyword + '%'];
                        }

                        const userInfo = yield _this11.session('userInfo');

                        console.log(userInfo);

                        if (userInfo.name != "系统管理员") {

                                let schoolres = yield _this11.model("school").where({ "uid": userInfo.id }).select();

                                if (think.isEmpty(schoolres)) {
                                        where["c.teacherid"] = userInfo.id;
                                } else {

                                        where["c.schoolid"] = ["in", schoolres.map(function (item) {
                                                return item.id;
                                        })];
                                }
                        }

                        if (!think.isEmpty(_this11.get("title"))) {
                                where['c.title'] = ['like', '%' + _this11.get("name") + '%'];
                        }

                        const res = yield model.alias('c').field('c.*,t.name as teacher').join(["buss_school s on s.id=c.schoolid"]).join(["sys_user t on t.id=c.teacherid"]).page(page, rows).where(where).where(where2).order(order).countSelect();
                        const data = {};

                        for (let item of res.data) {
                                item.lessonname = item.lessonname.replace(/,/g, '<br>');
                                item.skdate = item.skdate.replace(/ /g, '<br>');
                        }

                        data.code = 200;
                        data.count = res.count;
                        data.data = res.data;
                        data.msg = '';

                        return _this11.json(data);
                })();
        }

        recordAction() {
                var _this12 = this;

                return _asyncToGenerator(function* () {

                        let respData = {};
                        const id = _this12.post('id') ? _this12.post('id') : null;

                        const model = _this12.model('tk');
                        const userInfo = yield _this12.session('userInfo');
                        respData = yield model.where({ id: id }).find();

                        return _this12.json(respData);
                })();
        }
        recordlistAction() {
                var _this13 = this;

                return _asyncToGenerator(function* () {
                        const page = _this13.get('page') ? _this13.get('page') : 1;
                        const rows = 15;
                        const userInfo = yield _this13.session('userInfo');
                        const model = _this13.model('tk_record');
                        let tkid = _this13.get("tkid");
                        let where = {};
                        where['c.tkid'] = tkid;
                        where['c.schoolid'] = userInfo.schoolid;
                        const res = yield model.alias("c").join("buss_student u on u.id=c.userid").page(page, rows).where(where).field("c.*,u.name").order("c.create_date asc").countSelect();
                        let respData = {
                                code: 200,
                                count: res.count,
                                data: res.data,
                                msg: ''
                        };
                        return _this13.json(respData);
                })();
        }

};
//# sourceMappingURL=bk.js.map