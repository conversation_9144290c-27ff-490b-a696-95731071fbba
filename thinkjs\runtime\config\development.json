{"port": 8361, "workers": 1, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "ignoreUrl": ["/api/sys/sysdict/dict", "/api/sys/table/infocolmun"], "ueditor": {"__esModule": true, "default": {"imageActionName": "uploadimage", "imageFieldName": "upfile", "imageMaxSize": 52428800, "imageAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"], "imageCompressEnable": true, "imageCompressBorder": 1600, "imageInsertAlign": "none", "imageUrlPrefix": "", "imagePathFormat": "storage/image/{yyyy}{mm}{dd}/{time}", "scrawlActionName": "uploadscrawl", "scrawlFieldName": "upfile", "scrawlPathFormat": "storage/image/{yyyy}{mm}{dd}/{time}", "scrawlMaxSize": 52428800, "scrawlUrlPrefix": "", "scrawlInsertAlign": "none", "snapscreenActionName": "uploadimage", "snapscreenPathFormat": "storage/image/{yyyy}{mm}{dd}/{time}", "snapscreenUrlPrefix": "", "snapscreenInsertAlign": "none", "catcherLocalDomain": ["127.0.0.1", "localhost", "img.baidu.com"], "catcherActionName": "catchimage", "catcherFieldName": "source", "catcherPathFormat": "storage/image/{yyyy}{mm}{dd}/{time}", "catcherUrlPrefix": "", "catcherMaxSize": 52428800, "catcherAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "videoActionName": "uploadvideo", "videoFieldName": "upfile", "videoPathFormat": "storage/video/{yyyy}{mm}{dd}/{time}", "videoUrlPrefix": "", "videoMaxSize": 209715200, "videoAllowFiles": [".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], "fileActionName": "uploadfile", "fileFieldName": "upfile", "filePathFormat": "storage/file/{yyyy}{mm}{dd}/{time}", "fileUrlPrefix": "", "fileMaxSize": 209715200, "fileAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"], "imageManagerActionName": "listimage", "imageManagerListPath": "storage/image/", "imageManagerListSize": 20, "imageManagerUrlPrefix": "", "imageManagerInsertAlign": "none", "imageManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "fileManagerActionName": "listfile", "fileManagerListPath": "storage/file/", "fileManagerUrlPrefix": "", "fileManagerListSize": 20, "fileManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"]}}, "domain": "https://jsjy.hanruisoft.com", "appid": "wx19db7a1f925ec299", "secret": "b1da3d4739676a46f234f6c8896d3155", "jsapi": {"debug": true, "apiList": ["updateAppMessageShareData", "updateTimelineShareData", "chooseImage", "previewImage", "scanQRCode"]}, "wechat": {"appid": "wx19db7a1f925ec299", "secret": "b1da3d4739676a46f234f6c8896d3155", "token": "jsjywechat", "encodingAESKey": "lvxBRL5LNCKlOl1EvYN3YgH5tepeRWVDMdVCAKOJnsi"}, "wxpay": {"appid": "wx19db7a1f925ec299", "mch_id": "1706925459", "key": "kH3uB8yO0jP9rL9uP8oH0jH4sI0vX6zB", "notify_url": "https://app.jisijy.com/api/wechat/notify/wxpay"}, "alicloud": {"accessKeyId": "LTAI5tLeZtNFX9CmwPksB1pq", "accessKeySecret": "******************************", "signName": "瀚瑞信息", "templateCode": {"login": "SMS_479815122"}}, "tencentcloud": {"secretId": "AKIDeEetzFNK5f3gC0fBXklofLrDSXSYbVEr", "secretKey": "b5PYfcRCpHZihYFqI5BcOLExjMF2d2Lv", "sdkAppId": "1400971436", "signName": "泰安极思信息", "templateId": {"register": "2384191", "login": "2384191", "resetPassword": "2384191"}}, "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "C:\\jsjy\\jsjy\\thinkjs\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "jsjy2", "prefix": "sys_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root2", "password": "123456", "dateStrings": true}, "sequel": {"logConnect": false, "logSql": true, "database": "jsjy", "user": "root", "password": "root", "options": {"host": "127.0.0.1", "port": 3308, "dialect": "mysql", "logging": false, "timezone": "+08:00", "dialectOptions": {"options": {"useUTC": false, "dateFirst": 1, "charset": "utf8mb4", "dateStrings": true, "typeCast": true}, "multipleStatements": true}, "define": {"createdAt": "create_date", "updatedAt": "update_date", "deletedAt": "del_flag", "omitNull": true, "paranoid": true, "freezeTableName": false}}}}, "session": {"type": "jwt", "jwt": {"cookie": {"name": "jsjy"}, "secret": "secret", "tokenType": "header", "tokenName": "authorization", "sign": {"expiresIn": 43200}, "verify": {}}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "C:\\jsjy\\jsjy\\thinkjs\\view", "sep": "_", "extname": ".html"}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "C:\\jsjy\\jsjy\\thinkjs\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "C:\\jsjy\\jsjy\\thinkjs\\logs\\app.log"}}, "websocket": {"type": "socketio", "socketio": {"allowOrigin": null, "path": "/socket.io", "adapter": null, "messages": {"open": "/websocket/im/open", "close": "/websocket/im/close", "addUser": "/websocket/im/addUser"}}}}