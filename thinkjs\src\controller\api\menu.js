const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {
  async removeAction() {
    const id = this.post('id');

    const model = this.model('menu');
    await model.where({'id': id}).update({'del_flag': 1});

    return this.json({'state': 1});
  }

  async saveAction() {
    const model = this.model('menu');
    let data = this.post();
    data.meta.hidden = data.meta.hidden ? 1 : 0;
    data.meta.hiddenBreadcrumb = data.meta.hiddenBreadcrumb ? 1 : 0;
    Object.assign(data, data.meta);
    let flag = false;

    if (think.isEmpty(data.id)) {
      const res = await model.where({'name': data.name, 'del_flag': 0}).find();
      if (think.isEmpty(res)) {
        flag = true;
      }
    } else {
      const one = await model.where({'id': data.id}).find();
      const res = await model.where({'name': data.name, 'del_flag': 0}).find();
      if (data.name == one.name || think.isEmpty(res)) {
        flag = true;
      }
    }

    if (flag) {
      if (think.isEmpty(data.parent)) {
        data.parent_id = 0;
      } else {
        if (typeof data.parent === 'string') {
          data.parent_id = data.parent;
        } else {
          data.parent_id = data.parent.pop();
        }
      }
      if (think.isEmpty(data.id)) {
        data.id = think.uuid();
        await model.add(data);
      } else {
        await model.where({'id': data.id}).update(data);
      }
      return this.json({state: 1});
    } else {
      return this.json({state: 0});
    }
  }

  async treeAction() {
    const model = this.model('menu');
    const where = {del_flag: 0};

    const data = await model.where(where).order(['sort asc']).select();

    var tree = [];
    for (var t of data) {
      if (t.parent_id == '0') {
        const obj = {};
        obj.id = t.id;
        obj.name = t.name;
        obj.sort = t.sort;
        obj.title = t.title;
        obj.path = t.path;
        obj.redirect = t.redirect;

        obj.component = t.component;
        const meta = {};
        meta.title = t.title;
        meta.icon = t.icon;
        meta.type = t.type;
        meta.hidden = t.hidden === 1;
        meta.affix = t.affix;
        meta.hiddenBreadcrumb = t.hiddenBreadcrumb === 1;

        meta.active = t.active;
        obj.meta = meta;

        await this.getchild(obj, data);
        tree.push(obj);
      }
    }
    this.json(tree);
  }

  async getchild(tree, menus) {
    tree.hasChildren = false;
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id) {
        const obj = {};
        obj.id = t.id;
        obj.name = t.name;
        obj.title = t.title;
        obj.sort = t.sort;
        obj.path = t.path;
        obj.redirect = t.redirect;

        obj.component = t.component;
        const meta = {};
        meta.title = t.title;
        meta.icon = t.icon;
        meta.type = t.type;
        meta.hidden = t.hidden === 1;
        meta.affix = t.affix;
        meta.affix = t.affix;
        meta.hiddenBreadcrumb = t.hiddenBreadcrumb === 1;
        meta.active = t.active;

        obj.meta = meta;
        await this.getchild(obj, menus);
        childs.push(obj);
      }
    }
    tree.children = childs;
  }
};
