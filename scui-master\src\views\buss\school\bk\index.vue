<template>
	<el-container>
		
		<el-container>
			<el-header style="    height: auto !important;">
				<div class="left-panel">

<sc-search ref="searchType"
						   :data="filterInfo.data"
						   :field-list="filterInfo.fieldList"
						    
						   @handleReset="handleReset"
						   @handleFilter="handleFilter"
				/>



				


			 
				</div>
				<div class="right-panel">
					<div class="right-panel-search">
						<el-button type="primary" icon="el-icon-plus" @click="add"></el-button>
					</div>
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					<el-table-column label="标题" prop="title" width="120"  
					                 column-key="filterUserName"></el-table-column>
					<el-table-column label="章节名称" prop="lessonname" width="350"  >
							 <template #default="scope">
							<div v-html="scope.row.lessonname"></div>
						</template>
					</el-table-column>
					<el-table-column label="开课时间" prop="skdate" width="150" sortable='custom'>

	 <template #default="scope">
							<div v-html="scope.row.skdate"></div>
						</template>

					</el-table-column>
					<el-table-column label="教师" prop="teacher" width="100"></el-table-column>
				 
				
                    <el-table-column label="状态" prop="state" width="100">
                                <template #default="scope">
                                <el-tag type="info" v-if="scope.row.state == 1">未发布</el-tag>
                                <el-tag type="success" v-if="scope.row.state == 2">已发布</el-tag>
								<el-tag type="success" v-if="scope.row.state == 3">已上课</el-tag>
                                </template>
                            </el-table-column>

					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_preview(scope.row, scope.$index)">预览
							</el-button>
							<el-button type="text" size="small" v-if="scope.row.state == 1" @click="table_edit(scope.row, scope.$index)">编辑
							</el-button>
                            <el-button type="text" size="small" v-if="scope.row.state == 1||scope.row.state == 2" @click="table_bk(scope.row, scope.$index)">设置备课内容
                                                        </el-button>
						
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button type="text" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
    	<bk-dialog v-if="dialog.bk" ref="bkDialog" @success="handleSuccess" @closed="dialog.bk=false"></bk-dialog>

        	<preview-dialog v-if="dialog.preview" ref="previewDialog" @success="handleSuccess" @closed="dialog.preview=false"></preview-dialog>

</template>

<script>
import saveDialog from './save'
import bkDialog from './bk'
import previewDialog from './preview'

export default {
	name: 'user',
	components: {
		saveDialog,
        bkDialog,
        previewDialog
	},
	data() {
		return {

			dialog: {
				save: false,
                bk:false,
                preview:false
			},
            menuList:[],
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.bk.list,
			selection: [],
			search: {
				name: null
			},
			params:{},
			defaultProps: {

				label: 'name'
			},
               filterInfo: {
				data: {
					'c.create_by': null,
					 
					'c.create_date': null,

				},
				fieldList: [
				   
					{
						label: '时间', type: 'date', value: 'c.create_date', dateType: 'daterange'
						, valueFormat: 'YYYY-MM-DD'
					},
					// 

				],
				//组件尺寸
				size: 'mini',
				//配置项
				btnStyle: [
					{icon: 'el-icon-search', text: '过滤', type: 'primary'},
					{icon: 'el-icon-refresh', text: '重置'}
				]
			},

		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup();
		this.$refs.searchType.showAll();
	},
	methods: {

		   handleReset(data) {
			this.params.where = {};
        
			this.$refs.table.upData(this.params);
		},
		//焦点失去事件
		handleFilter(data) {
			let params = {where: {}};
			this.filterInfo.fieldList.map(r => {
				 		if (!this.$TOOL.isEmpty(data[r.value])) {
							console.log(r);
				if (r.type == "date") {
							if (r.dateType.indexOf('range') > -1) {
								params.where[r.value] = ['between', data['c.create_date'][0] + "," +
								data['c.create_date'][1]]
							} else {
								params.where[r.value] = data[r.value];
							}

						} else if (r.type == "input") {
							params.where[r.value] = ['like', '%' + data[r.value] + '%'];
						} else {
							params.where[r.value] = data[r.value];
						}
					}
				}
			)

			console.log("123123");
			this.$refs.table.upData(params);
		},

		//添加
	 async	add() {
			this.dialog.save = true
			//	let row={};
			
			//	row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add');
			})
		},
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},


        	table_preview(row) {
			this.dialog.preview = true
			this.$nextTick(() => {
				this.$refs.previewDialog.open('show').setData(row)
			})
		},

        table_bk(row) {
			this.dialog.bk = true
			this.$nextTick(() => {
				this.$refs.bkDialog.open('show').setData(row)
			})
		},

		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.bk.del.post(reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			 var res = await this.$API.lesson.list.get();
              this.menuList = res;
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {

			this.$refs.table.upData(this.search);

		},
		//本地更新数据
		handleSuccess() {

			this.$refs.table.refresh();

		}
 

	}
}
</script>

<style>
</style>
