import config from "@/config";
import http from "@/utils/request";

const im = {

	getbuss: {
		url: `${config.API_URL}/sys/im/getbuss`,
		name: "getbuss",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},


    getlist: {
		url: `${config.API_URL}/sys/im/getlist`,
		name: "getlist",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},


	getoffmsg: {
		url: `${config.API_URL}/sys/im/getoffmsg`,
		name: "getoffmsg",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
	updatemsg: {
		url: `${config.API_URL}/sys/im/updatemsg`,
		name: "updatemsg",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	

}
export default im;