const got = require('got');
module.exports = class extends think.Controller {
  async loginAction() {
    const {user, password} = this.get();

    let model = this.model('user');

    const res = await model.where({'del_flag': 0, 'login_name': user, 'password': password}).find();
    if (!think.isEmpty(res)) {

      const user = Object.assign({}, res, {'userName': res['name']});


      let childoffice = await model.query(" SELECT getLeafNodeList(" + res['office_id'] + ") AS office_ids");
      user['childoffice'] = childoffice[0].office_ids;
      let office = this.model("office")
      let masterofficeList = await office.where({"masterid": user.id}).select();
      let masteroffices = ''
      masterofficeList.map(r => {
        masteroffices += r.id + ","
      })
      user.masteroffices = masteroffices;

      delete user.skinfo;
      delete  user.content;
      const token = await this.session('userInfo', user);
      user['token'] = token;
      user['state'] = 1;

      model = this.model('user_role');
      const mmodel = this.model('menu');
      const roles = await model.where({'user_id': user.id}).select();
      if (think.isEmpty(roles)) {
        user['menuList'] = [];
      } else {
        let tmpsql = '';
        let flag = 1;
        for (var one of roles) {
          if (flag < roles.length) {
            tmpsql = tmpsql + "'" + one.role_id + "',";
          } else {
            tmpsql = tmpsql + "'" + one.role_id + "'";
          }
          flag++;
        }

        const tmparr = [];
        const sql = "select * from sys_menu where id in (select  DISTINCT m.id from sys_menu m join sys_role_menu rm on rm.menu_id=m.id where m.del_flag='0' and rm.role_id  in (" + tmpsql + ')) order by sort asc';
        const res = await model.query(sql);

        for (var one of res) {
          if (one.parent_id != '0') {
            let ifhava = false;
            for (var t of res) {
              if (t.id == one.parent_id) {
                ifhava = true;
              }
            }
            if (!ifhava) {
              const m = await mmodel.where({'id': one.parent_id}).find();

              res.push(m);
            }
          }
        }

        var tree = [];
        var rolePermissions = [];
        for (var t of res) {
          if (t.type == 'button') {
            rolePermissions.push(t.name);
          } else {
            if (t.parent_id == '0') {
              const obj = {};
              obj.id = t.id;
              obj.name = t.name;
              obj.sort = t.sort;
              obj.title = t.title;
              obj.path = t.path;
              obj.redirect = t.redirect;
              obj.component = t.component;
              const meta = {};
              meta.title = t.title;
              meta.icon = t.icon;
              meta.type = t.type;
              meta.hidden = t.hidden === 1;
              meta.affix = t.affix;
              meta.hiddenBreadcrumb = t.hiddenBreadcrumb === 1;
              meta.active = t.active;
              obj.meta = meta;
              await this.getchild(obj, res);
              tree.push(obj);
            }
          }
        }
      }

      user['menuList'] = tree;

      let rsql = "select r.name FROM sys_user_role u join sys_role r on r.id=u.role_id where user_id=\'" + user.id + "\'";
      let rres = await model.query(rsql);
      let role = "";
      for (var o of rres) {
        role = role + o.name + ",";

      }


      user['role'] = role;//用户角色

      user['rolePermissions'] = rolePermissions;//角色权限
      return this.json({'data': user});
    } else {
      return this.json({'data': {state: 0}});
    }
  }

  async menuAction() {
    const user = await this.session('userInfo');
    const model = this.model('user_role');
    const mmodel = this.model('menu');
    const roles = await model.where({'user_id': user.id}).select();
    if (think.isEmpty(roles)) {
      this.json([]);
    } else {
      let tmpsql = '';
      let flag = 1;
      for (var one of roles) {
        if (flag < roles.length) {
          tmpsql = tmpsql + "'" + one.role_id + "',";
        } else {
          tmpsql = tmpsql + "'" + one.role_id + "'";
        }
        flag++;
      }

      const tmparr = [];
      const sql = "select * from sys_menu where id in (select  DISTINCT m.id from sys_menu m join sys_role_menu rm on rm.menu_id=m.id where m.del_flag='0' and rm.role_id  in (" + tmpsql + ')) order by sort asc';
      const res = await model.query(sql);

      for (var one of res) {
        if (one.parent_id != '0') {

          let ifhava = false;
          for (var t of res) {
            if (t.id == one.parent_id) {
              ifhava = true;
            }
          }
          if (!ifhava) {
            const m = await mmodel.where({'id': one.parent_id}).find();

            res.push(m);
          }
        }
      }

      var tree = [];
      for (var t of res) {
        if (t.parent_id == '0' && t.type !== 'button') {
          const obj = {};
          obj.id = t.id;
          obj.name = t.name;
          obj.sort = t.sort;
          obj.title = t.title;
          obj.path = t.path;
          obj.redirect = t.redirect;
          obj.component = t.component;
          const meta = {};
          meta.title = t.title;
          meta.icon = t.icon;
          meta.type = t.type;
          meta.hidden = t.hidden === 1;
          meta.affix = t.affix;
          meta.hiddenBreadcrumb = t.hiddenBreadcrumb === 1;
          meta.active = t.active;

          obj.meta = meta;

          await this.getchild(obj, res);
          tree.push(obj);
        }
      }
      this.json(tree);
    }
  }


  //ksdkfksdfk
  async getchild(tree, menus) {
    tree.hasChildren = false;
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id && t.type !== 'button') {
        tree.hasChildren = true;
        const obj = {};
        obj.id = t.id;
        obj.name = t.name;
        obj.title = t.title;
        obj.sort = t.sort;
        obj.path = t.path;
        obj.redirect = t.redirect;
        obj.component = t.component;
        const meta = {};
        meta.title = t.title;
        meta.icon = t.icon;
        meta.type = t.type;
        meta.hidden = t.hidden === 1;
        meta.affix = t.affix;
        meta.hiddenBreadcrumb = t.hiddenBreadcrumb === 1;
        meta.active = t.active;

        obj.meta = meta;
        await this.getchild(obj, menus);
        childs.push(obj);
      }
    }
    if (childs.length > 0) {
      tree.children = childs;
    }

  }


  async homeAction() {


    this.display();
  }


  async getopenidAction() {

    let respData = {};
    let userInfo = await this.session('userInfo');
    if (!think.isEmpty(userInfo)) {
      let model = this.model('user');
      const res = await model.where({'del_flag': 0, 'id': userInfo.id}).find();

      if (think.isEmpty(res.openid)) {
        respData = {
          code: 200,
          openid: false
        };
      } else {
        respData = {
          code: 200,
          openid: true,
          data: {
            openid: res.openid
          }
        };
      }
    } else {
      respData = {
        code: 200,
        openid: false
      };
    }
    return this.json(respData);
  }

  async getchildofficeAction() {
    let office_id = this.get("office") ? this.get("office") : null;
    let model = this.model("office");
    if (think.isEmpty(office_id)) {
      return this.success("200", null);
    } else {
      let officeids = await model.query(" SELECT getLeafNodeList(" + parseInt(office_id) + ") AS office_ids");
      if (officeids.length > 0) {
        return this.success("200", officeids[0].office_ids);
      } else {
        return this.success("200", null);
      }
    }
  }


  async getlistAction() {
    let data = {};
    data.code = 0;
    data.msg = "";
    let cuser = {};

    let user = this.get("id");
    let model = this.model('user');
    let res = await model.where({'id': user}).find();
    cuser.username = res.name;
    cuser.id = res.id;
    cuser.status = "online";
    cuser.sign = res.sign;
    cuser.avatar = res.avatar;
    data.data = {"mine": cuser};

    let ofmodel = this.model("office");
    let offices = await ofmodel.where({"del_flag": 0}).select();
    let firends = [];
    let obj = {}
    obj.groupname = "泰安市信访局";
    obj.id = "xfj";

    let users = await model.where({'del_flag': 0, id: ["!=", res.id]}).select();
    var userlist = [];
    let online = 0;
    for (var u of users) {
      let userobj = {};
      userobj.username = u.name;
      userobj.id = u.id;
      userobj.avatar = u.avatar;
      userobj.sign = u.sign;
      userobj.status = u.status;
      userlist.push(userobj);
      if (u.status == 'online') {
        online++;
      }
    }

    obj.online = online;
    obj.list = userlist;
    firends.push(obj);
    data.data.friend = firends;

    this.json(data);
  }

  async getdinguserAction() {
    let code = this.post("code");

    const aa = await got('https://oapi.dingtalk.com/gettoken', {
      searchParams: {
        appkey: 'dingtwesb5m47gcdzeyo',
        appsecret: 'vQVzoGxvd9EJCPcb9NsdZemSj2r8fgUIhAbToWuzdX0KDhsnMDwPvfaw0DlD7cj8',
      }
    }).json().then(async(response) => {
      if (response.errcode == 0) {
        const userInfo = await got('https://httpbin.org/anything', {
          method: 'post',
          searchParams: {
            appkey: 'dingtwesb5m47gcdzeyo',
            appsecret: 'vQVzoGxvd9EJCPcb9NsdZemSj2r8fgUIhAbToWuzdX0KDhsnMDwPvfaw0DlD7cj8',
          },
          json: {code: code}
        }).json();
        return userInfo;
      }
    });
    // (async () => {
    //   try {
    //     const {body} = await got.get('https://oapi.dingtalk.com/gettoken', {
    //       searchParams: {
    //         appkey: 'dingtwesb5m47gcdzeyo',
    //         appsecret: 'vQVzoGxvd9EJCPcb9NsdZemSj2r8fgUIhAbToWuzdX0KDhsnMDwPvfaw0DlD7cj8',
    //       },
    //       timeout: 150000,
    //       retry: 2,
    //       responseType: 'json',
    //       throwHttpErrors: false
    //     });
    //     return {
    //       'code': 200,
    //       'data': body
    //     };
    //   } catch (error) {
    //     return {
    //       'code': 500,
    //       'errorMessage': error.message
    //     };
    //   }
    // })().then(async (getApiToken) => {
    //   if (getApiToken.code == 200) {
    //     if (getApiToken.data.errcode == 0) {
    //       let getUserInfo = await (async () => {
    //         try {
    //           const {body} = await got.post('POST https://oapi.dingtalk.com/topapi/v2/user/getuserinfo', {
    //             searchParams: {
    //               access_token: 'dingtwesb5m47gcdzeyo',
    //             },
    //             body:{
    //
    //             },
    //             timeout: 150000,
    //             retry: 2,
    //             responseType: 'json',
    //             throwHttpErrors: false
    //           });
    //           return {
    //             'code': 200,
    //             'data': body
    //           };
    //         } catch (error) {
    //           return {
    //             'code': 500,
    //             'errorMessage': error.message
    //           };
    //         }
    //       })();
    //       return getUserInfo;
    //     }
    //   }
    // });

    return this.success("200", null);
  }

  async buildcode(code,id){
      console.log(id);
      console.log(code);
        let model=this.model("lesson");
        let res=await model.where({id:id}).find();
       
        if(res.parent_id==0){
         code=res.code+"_"+code;
          return code
        }else{
           code=res.code+"_"+code;
          code=    await  this.buildcode(code,res.parent_id);
            return code
        }

        
  }

  async processcodeAction(){
    let model=this.model("lesson");
     let list=await model.where({"del_flag":0}).select();
     let code="";

     for(var i of list){
      if(i.id!=0){


        let code="";

    let str1=  await this.buildcode(code,i.id);
         
        console.log(str1)

        await model.where({id:i.id}).update({code2:str1});
      }



     }


     this.json({});

  }
};
