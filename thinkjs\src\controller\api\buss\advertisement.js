const BaseRest = require('../rest.js');

module.exports = class extends BaseRest {
  /**
   * 获取广告列表
   */
  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = {};

    // 处理名称搜索
    let name = this.get('name') ? this.get('name') : '';
    if(name) {
      where['a.name'] = ['like', `%${name}%`];
    }

    // 处理状态搜索
    let status = this.get('status');
    if(status !== undefined && status !== null && status !== '') {
      where['a.status'] = status;
    }

    const advertisementModel = this.model('advertisement');
    where['a.del_flag'] = 0;
    
    const response = await advertisementModel
      .alias('a')
      .field('a.*, u.name AS uname')
      .join(["left join sys_user u ON a.`create_user`=u.`id`"])
      .page(page, rows)
      .where(where)
      .order('a.create_time desc')
      .countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }

  /**
   * 保存广告（添加/更新）
   */
  async saveAction() {
    let respData = {};
    const model = this.model('advertisement');
    const id = this.post('id') ? this.post('id') : null;
    const userInfo = await this.session('userInfo');
    let data = this.post();
    console.log(data);
    data.create_user = userInfo.id;

    // 处理链接URL逻辑
    if (!data.clickable) {
      data.link_url = ''; // 
      // 如果不允许点击，清空链接地址
    }
    if(data.clickable){
        data.clickable=1;
    }else{
        data.clickable=0;
    }


    if (think.isEmpty(id)) {
      // 添加新广告
      await model.add(await this.addDataNoId(data));
      respData = {
        code: 200,
        state: 1,
        data: {},
        message: '添加成功'
      };
    } else {
      // 更新广告
      await model.where({id: id}).update(await this.updateData(data));
      respData = {
        code: 200,
        state: 1,
        data: {},
        message: '更新成功'
      };
    }
    return this.json(respData);
  }

  /**
   * 删除广告
   */
  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('advertisement');
      await model.where({id: id}).update({del_flag: 1});
      respData = {
        code: 200,
        data: {},
        message: '删除成功'
      };
    }
    return this.json(respData);
  }

  /**
   * 批量删除广告
   */
  async batchDelAction() {
    let respData = {};
    const ids = this.post('ids');
    
    if (think.isEmpty(ids) || !Array.isArray(ids)) {
      return this.json({
        code: 400,
        data: {},
        message: '参数错误'
      });
    }
    
    const model = this.model('advertisement');
    await model.where({id: ['IN', ids]}).update({del_flag: 1});
    
    respData = {
      code: 200,
      data: {},
      message: '批量删除成功'
    };
    return this.json(respData);
  }

  /**
   * 修改广告状态
   */
  async changeStatusAction() {
    let respData = {};
    const id = this.post('id');
    const status = this.post('status');
    
    if (think.isEmpty(id) || (status !== 0 && status !== 1)) {
      return this.json({
        code: 400,
        data: {},
        message: '参数错误'
      });
    }
    
    const model = this.model('advertisement');
    await model.where({id: id}).update({status: status});
    
    respData = {
      code: 200,
      data: {},
      message: '状态修改成功'
    };
    return this.json(respData);
  }

  /**
   * 获取所有启用的广告（APP端调用）
   */
  async listAction() {
    const model = this.model('advertisement');
    
    const list = await model
      .field('id, name, content, image, clickable, link_url')
      .where({
        status: 1,
        del_flag: 0
      })
      .order('create_time desc')
      .select();
      
    return this.json({
      code: 200,
      data: list,
      message: ''
    });
  }
};