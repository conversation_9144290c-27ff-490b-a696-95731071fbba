const path = require('path');
const isDev = think.env === 'development';
const wechat = require('think-wechat')
const addon = require('./addon')
const cors = require('kcors');
module.exports = [

  {
    handle: wechat,
    match: '/wechat/wechat',
    options: {
      token: "winway",
      appid: "wxfd97638ea74090ab",
      encodingAESKey: "68irD9JbiXV7OrxvjyRp7xz0PLQWiKfYY1ps1fsGztH",
      checkSignature: false 
    }
  },
  {
    handle: addon,
    match: '/abc',
    options: {}
  },
  {
    handle: 'payload',
    options: {}
  },
  
  {
    handle: cors,
    options: {
      origin: '*',
      credentials: true,
      allowMethods: '*'
    }
  },
  {
    handle: 'meta',
    options: {
      logRequest: isDev,
      sendResponseTime: isDev
    }
  },
  {
    handle: 'resource',
    enable: isDev,
   // enable: false, // 始终开启，默认为 `enable: isDev` 表示只在开发环境下开启
    options: {
      root: path.join(think.ROOT_PATH, 'www'),
      publicPath: /^\/(static|favicon\.ico)/,
      // publicPath: '/',
      index: 'index.html',
      hidden: false,
      format: true,
      gzip: false,
      maxage: 0,
      notFoundNext: false
    }
  },
  {
    handle: 'trace',
    enable: !think.isCli,
    options: {
      debug: isDev,
      contentType(ctx) {
        // All request url starts of /api or request header contains `X-Requested-With: XMLHttpRequest` will output json error
        const APIRequest = /^\/api/.test(ctx.request.path);
        const AJAXRequest = ctx.is('X-Requested-With', 'XMLHttpRequest');
        return APIRequest || AJAXRequest ? 'json' : 'html';
      }
    }
  },
  {
    handle: 'payload',
    options: {
      keepExtensions: true,
      limit: '5mb'
    }
  },
  {
    handle: 'router',
    options: {}
  },
  // 自定义日志写入数据库
  {
    handle: 'logger',
    enable: true
  },
  'logic',
  'controller'
];
