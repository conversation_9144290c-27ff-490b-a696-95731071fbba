import config from "@/config";
import http from "@/utils/request";

const bk = {
	list: {
		url: `${config.API_URL}/buss/bk/list`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
 
    tkpage: {
		url: `${config.API_URL}/buss/bk/tkpage`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},


    getlist:{
		url: `${config.API_URL}/buss/bk/getlist`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},


	
    gettoplesson:{
		url: `${config.API_URL}/buss/bk/gettoplesson`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	recordlist: {
		url: `${config.API_URL}/buss/bk/recordlist`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	record: {
		url: `${config.API_URL}/buss/bk/record`,
		name: "info",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},

    

    tkpage2: {
		url: `${config.API_URL}/buss/bk/tkpage2`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

    tkpage3: {
		url: `${config.API_URL}/buss/bk/tkpage3`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	save: {
		url: `${config.API_URL}/buss/bk/save`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},


	tmpsubmit: {
		url: `${config.API_URL}/buss/bk/tmpsubmit`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
    
    submit: {
		url: `${config.API_URL}/buss/bk/submit`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
 

    

    info: {
		url: `${config.API_URL}/buss/bk/info`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	del: {
		url: `${config.API_URL}/buss/bk/remove`,
		name: "删除用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

}
export default bk;
