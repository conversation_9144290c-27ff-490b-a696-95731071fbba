import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/case/caselist/page`,
		name: "场所数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/case/caselist/add`,
		name: "场所数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/case/caselist/save`,
		name: "场所数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},

	base: {
		url: `${config.API_URL}/case/caselist/base`,
		name: "基础数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	caselist: {
		url: `${config.API_URL}/case/caselist/caselist`,
		name: "场所数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	list: {
		url: `${config.API_URL}/case/caselist/list`,
		name: "场所数据列表",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/case/caselist/delete`,
		name: "删除场所数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
