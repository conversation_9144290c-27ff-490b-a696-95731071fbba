.name_right {
    margin-left: 200px;
    margin-top: 5px;
}

.name_left {
    margin-left: 10px;
    margin-top: 5px;
}

.content_right {
    background: #8698e9;
    color: white;
    border-radius: 10px 0px 10px 10px;
    padding: 8px 12px;
    word-wrap: break-word;
    width: 70%;
    margin-left: 87px;
    font-size: 14px;
    text-align: right;
    margin-bottom: 20px;
}

.content_left {
    background: #8698e9;
    color: white;
    border-radius: 0px 10px 10px 10px;
    padding: 8px 12px;
    word-wrap: break-word;
    width: 70%;
    margin-left: 18px;
    font-size: 10px;
    text-align: left;
    margin-bottom: 20px;
}

.url_right {
    margin-left: 10px;

}

.url-left {
    margin-left: 15px;

}

::-webkit-scrollbar {
    height: 8px !important;
    width: 5px !important;
}

::-webkit-scrollbar-thumb {
    border-radius: 0;
    border-style: dashed;
    background-color: #c5c8cb;
    border-color: #e2242400;
    border-width: 0px;
    background-clip: padding-box;
}



::-webkit-scrollbar-track { /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    /*border-radius: 10px;*/
    background: white;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(157, 165, 183, 0.7)
}


.word-break {
    word-break: break-all;
}


.infinite-list-wrapper p {
    text-align: center;
}


.show-input-content {
    height: 100%;
    width: 100%;
    border: 1px solid black;

}

.emotion {
    width: 22px !important;
    height: 22px !important;
    margin-right: 4px;
    line-height: 40px;
    top: 5px;

}


.icon2 {
    width: 2em;
    height: 2em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.talk {
    border: 1px solid #DCE1E6;
    width: 400px;
    height: 450px;
    margin: 0 auto;
}

.talk-header {
    width: 100%;
    height: 50px;
    z-index: 1;
    border-bottom: 1px solid #DCE1E6;
    background: #f6f6f6 !important;
}

.talk-header-icon {
    
}

.talk-content {
    overflow-y: auto;
    height: 350px;
}

.talk-message {
    border-top: 1px solid #DCE1E6;
    border-bottom: 1px solid #DCE1E6;
    border-right: 1px solid #DCE1E6;
    height: 71px;
    display: flex;
}

.talk-message-face {
    width: 40px;
    padding-right: 4px;
    padding-top: 20px;
}
.talk-message-content {
    padding-top: 10px;
    width: 280px;
}

.talk-message-send {
   
    width: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.el-tabs__item {
padding-left: 10px !important;

}