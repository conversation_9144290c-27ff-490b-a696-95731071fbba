import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/site/info/page`,
		name: "场所数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/site/info/add`,
		name: "场所数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/site/info/save`,
		name: "场所数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/site/info/info`,
		name: "场所数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	list: {
		url: `${config.API_URL}/site/info/list`,
		name: "场所数据列表",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/site/info/delete`,
		name: "删除场所数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
