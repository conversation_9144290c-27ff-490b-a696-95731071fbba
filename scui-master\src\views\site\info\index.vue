<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/9/28 08:58:13
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-container>
		<el-header height="auto">
			<div class="left-panel" style="width: 100%;">
				<el-button type="primary" icon="el-icon-plus" @click="add" size="small"></el-button>
				<sc-file-import-data :apiObj="$API.common.importFile" :templateUrl="templateUrl"
				                @success="success">
					<template #default="{open}">
						<el-button type="primary" icon="sc-icon-upload" size="small" @click="open">导入</el-button>
					</template>
				</sc-file-import-data>
				<export-table-excel refName="table"></export-table-excel>
				<el-divider direction="vertical"></el-divider>
				<sc-search ref="searchType"
						   :data="filterInfo.data"
						   :field-list="filterInfo.fieldList"
						   @handleReset="handleReset"
						   @handleFilter="handleFilter"
				/>
			</div>
			<div class="right-panel">

			</div>
		</el-header>
		<el-main class="nopadding">
			<scTable ref="table" :apiObj="apiObj" :params="params"
					 row-key="id" tableLayout="auto" :column="column" :header-cell-style="{'text-align':'center'}"
			>
				<template v-slot:operation>
					<el-table-column label="操作" width="170">
						<template #default="scope">
							<el-button-group>
								<el-button type="primary" text size="small"
										   @click="table_show(scope.row, scope.$index)">查看
								</el-button>
								<el-button type="primary" text size="small"
										   @click="table_edit(scope.row, scope.$index)">编辑
								</el-button>
								<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
									<template #reference>
										<el-button type="primary" text size="small">删除</el-button>
									</template>
								</el-popconfirm>
							</el-button-group>
						</template>
					</el-table-column>
				</template>
			</scTable>
		</el-main>
	</el-container>

	<sc-dialog :title="titleMap[saveMode]" v-model="saveDialogVisible" :width="600" destroy-on-close>
		<save-dialog ref="saveDialog" :mode="saveMode"></save-dialog>
		<template #footer>
			<el-button @click="saveDialogVisible=false">取 消</el-button>
			<el-button v-if="saveMode!='show'" type="primary" @click="saveForm()" :loading="isSaveing">保 存</el-button>
		</template>
	</sc-dialog>
</template>

<script>
import saveDialog from './save'
import scFileImportData from '@/components/scFileImport/data'
import scFileExport from '@/components/scFileExport'

export default {
	name: 'siteInfo',
	components: {
		saveDialog,
		scFileImportData,
		scFileExport
	},
	data() {
		return {
			apiObj: this.$API.siteInfo.page,
			selection: [],
			saveDialogVisible: false,
			saveMode: 'add',
			titleMap: {
				add: "新增",
				edit: "编辑",
				show: "查看"
			},
			isSaveing: false,
			filterInfo: {
				data: {
					'p.site_star': null,
					'p.site_name': null,

				},
				fieldList: [
					{
						label: '站点名称', type: 'input', value: 'p.site_name'
					},
					{
						label: '星级', type: 'scSelect',
						value: 'p.site_star', method: "post", dic: "siteStar", selectConfig: {
							label: 'name',
							value: 'key'
						}
					},

				],
				//组件尺寸
				size: 'mini',
				//配置项
				btnStyle: [
					{icon: 'el-icon-search', text: '过滤', type: 'primary'},
					{icon: 'el-icon-refresh', text: '重置'}
				]
			},
			column: this.$TOOL.tableData.site_info.column,
			templateUrl: "/static/template/siteInfo.xlsx",
			params: {
				where: {}
			},
		}
	},
	mounted() {
	},
	methods: {
		//重置
		handleReset(data) {
			this.params.where = {};
			this.$refs.table.upData({where: {}});
		},
		//焦点失去事件
		handleFilter(data) {
			let params = {where: {}};
			this.filterInfo.fieldList.map(r => {
					if (!this.$TOOL.isEmpty(data[r.value])) {
						if (r.type == "date") {
							if (r.dateType.indexOf('range') > -1) {
								params.where[r.value] = ['between', data[r.value][0] + "," + data[r.value][1]]
							} else {
								params.where[r.value] = data[r.value];
							}
						} else if (r.type == "input") {
							params.where[r.value] = ['like', '%' + data[r.value] + '%'];
						} else {
							params.where[r.value] = data[r.value];
						}
					}
				}
			)
			this.$refs.table.upData(params);
		},
		//添加
		add() {
			this.saveMode = 'add';
			this.saveDialogVisible = true;
		},
		//编辑
		table_edit(row) {
			this.saveMode = 'edit';
			this.saveDialogVisible = true;
			this.$nextTick(() => {
				//这里可以再次根据ID查询详情接口
				this.$refs.saveDialog.setData(row)
			})
		},
		//查看
		table_show(row) {
			this.saveMode = 'show';
			this.saveDialogVisible = true;
			this.$nextTick(() => {
				//这里可以再次根据ID查询详情接口
				this.$refs.saveDialog.setData(row)
			})
		},
		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.siteInfo.delete.post(reqData);
			if (res.code == 200) {
				this.$refs.table.refresh();
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},
		//提交
		saveForm() {
			this.$refs.saveDialog.submit(async (formData) => {
				this.isSaveing = true;
				var res = await this.$API.siteInfo.save.post(formData);
				this.isSaveing = false;

				if (res.code == 200) {
					//这里选择刷新整个表格 OR 插入/编辑现有表格数据
					this.saveDialogVisible = false;
					this.$refs.table.refresh();
					this.$message.success("操作成功")
				} else {
					this.$alert(res.message, "提示", {type: 'error'})
				}
			})
		},
		success(formatData, close) {
			console.log("dd")
			console.log(formatData)
			close()
			if (formatData.length > 0) {
				// this.$alert("导入返回成功后，可后续操作，比如刷新表格等。执行回调函数close()可关闭上传窗口。", "导入成功", {
				// 	type: "success",
				// 	showClose: false,
				// 	center: true
				// })

			} else {
				//返回失败后的自定义操作，这里演示显示错误的条目
				this.importErrDialogVisible = true
				this.importErrData = "上传失败"
			}

		}
	}
}
</script>

<style>
</style>
