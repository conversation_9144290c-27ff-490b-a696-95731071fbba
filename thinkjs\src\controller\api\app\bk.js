
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {

    async tabsAction(){
       let model=this.model("lesson");
       const user = await this.session('userInfo');

       let res=await model.where({"type":user.level,"del_flag":0,"parent_id":0}).field("id,name").select();



       return this.json(res);




    }


    async teacherAction(){
        let model=this.model("user");
        let id=this.post("id");
        console.log(id);
        let res=await model.where({"id":id,"del_flag":0}).field("*").find();
        delete res.password;

        let domain=this.config("domain");
        if(!think.isEmpty(res.skinfo)){

            res.skinfo=res.skinfo.replace(/src="\/static\/upload/g, `src="${domain}/static/upload`);
        }
    
    
        return this.json(res);
    }
    

 async jisuanAction(){
    const user = await this.session('userInfo');
    let model=this.model("bk_countflag");
    let datetime=think.datetime();
   let data=await model.where({userid:user.id}).find();
   if(!think.isEmpty(data)){
    datetime=data.lasttime;
   }

   datetime=think.datetime(datetime,"YYYY-MM-DD");


    let model2=this.model("bk");
    let res=await model2.where({schoolid:user.school,del_flag:0,state:2,fbdate:{">=":datetime}}).select();


    let model3=this.model("bk_user");
    for(let item of res){
        let tmp=await model3.where({bkid:item.id,uid:user.id}).find();
        if(think.isEmpty(tmp)){
            //计算错题
            let tklist=await this.model("bk_list").where({bkid:item.id}).field("tkid").select();
            tklist=tklist.map(item=>item.tkid);
            

            let errorlist =await this.model("tk_record").where({userid:user.id,flag:0,tkid:["in",tklist]})
            .where("create_date >= '" + think.datetime(new Date(), 'YYYY-MM-01') + "'").field("tkid").select();

//计算讲解

        let jjlist=await this.model("jj_record").where({uid:user.id,tkid:["in",tklist]}).field("tkid").select();
//计算分数段


        let score=await this.model("lesson_user").where({userid:user.id,lessonid:["in",item.lesson.split(",")]}).field("score").find();
      

        if(think.isEmpty(score)){
            score=50;
        }else{
            score=score.score;
        }


        let max=score+10
        let min=score-10

        let scorelist=await this.model("tk").where({lessonid:["in",item.lesson.split(",")],score:["between",[min,max]],"id":["in",tklist]}).field("id").select();


        let para={};
        para.bkid=item.id;
        if(errorlist.length>0){
            para.iferror=1;
        }else{
            para.iferror=0;
        }
        if(jjlist.length>0){
            para.ifjj=1;
        }else{
            para.ifjj=0;
        }
        if(scorelist.length>0){
            para.ifscore=1;
        }else{
            para.ifscore=0;
        }

        para.state=0;
        para.uid=user.id;
        para.create_date=think.datetime();
        para.errorid=errorlist.map(item=>item.tkid).join(",");
        para.jjid=jjlist.map(item=>item.tkid).join(",");
        para.scoreid=scorelist.map(item=>item.id).join(",");
        await model3.add(para);

            item.state=1;
        }
    }
    



}

async listAction(){
    let model=this.model("bk_user")
    let lessonid=this.post("lessonid")
    const user = await this.session('userInfo');
    console.log(user);
    
    console.log(this.post())
    
    let where={};
 
    where['c.schoolid']=user.school;
    where['c.del_flag']='0';
    where['c.state']=["!=",1];
    where['toplesson']=lessonid;
    const page = this.post('page') ? this.post('page') : 1;
    const rows = this.post('pageSize') ? this.post('pageSize') : 20;
    let iferror=this.post("iferror")
    
    let state=this.post("state");
    if(!think.isEmpty(state)){
        if(state==1){
            
            where["c.state"]=1
        }else if(state==2){
            
             where["c.state"]=["in","2,3"]
        }
        
        
    }
    
    if(!think.isEmpty(iferror)&&iferror==1){
        
        where["bu.iferror"]=1
    }
      let ifjj=this.post("ifjj")
    
    if(!think.isEmpty(ifjj)&&ifjj==1){
        
        where["bu.ifjj"]=1
    }
    
    
    

 
    let res=await model.alias("bu").where(where).page(page, rows)
    .join("left join sys_bk as c on c.id=bu.bkid")
    .join("left join sys_user t on t.id=c.teacherid").field("bu.iferror,bu.ifjj,bu.ifscore,c.*,t.name,t.avatar,DATE_FORMAT(c.create_date, '%m-%d %H:%i') as d")
    .order("c.create_date desc")
    .countSelect();
    let data={}



    for(let item of res.data){
            item.lessonname= item.lessonname.replace(/,/g, '<br>');
            
        
    }



    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';




return this.json(data);
 
 


}


    async infoAction(){
        let model=this.model("bk_list")
        let id=this.post("id")
        let res=await model.alias("c").join("sys_tk t on t.id=c.tkid").where({bkid:id}).field("c.*,t.jx_p,t.tm_p").select();


        return this.json(res);


    }
    async listtmAction(){
     let    res=await this.model("bk_list").alias("c").join("sys_tk t on t.id=c.tkid").where({"bkid":this.post("id")}).field("c.*,t.no,t.type").select();
        let id=this.post("id");
        let  user=await this.session("userInfo");
        let res2=await this.model("bk_user").where({bkid:id,uid:user.id}).find();
        
        if(!think.isEmpty(res2)){
            let errorlist=res2.errorid.split(",");
            let jjlist=res2.jjid.split(",");
            let scorelist=res2.scoreid.split(",");

            res.forEach(item=>{
                if(errorlist.includes(item.id)){
                    item.error=1;
                }
                if(jjlist.includes(item.id)){
                    item.jj=1;
                }
                if(scorelist.includes(item.id)){
                    item.score=1;
                }
            })
        }





 
      
        return this.json({data:res});
    }

    async countflagAction(){
        const user = await this.session('userInfo');
        let model=this.model("bk_countflag");
        let data={};
        data.userid=user.id;
        data.lasttime=think.datetime();
        await model.where({userid:user.id}).delete();
        await model.add(data);
        return this.json({code:200,msg:"",data:{}});
    }

    async countAction(){

        const user = await this.session('userInfo');
        let model=this.model("bk_countflag");
        let datetime=think.datetime();
       let data=await model.where({userid:user.id}).find();
       if(!think.isEmpty(data)){
        datetime=data.lasttime;
       } 
       let model2=this.model("bk");
       let res=await model2.where({schoolid:user.school,del_flag:0,state:2,fbdate:{">=":datetime}}).count();
       return this.json({code:200,msg:"",data:{count:res}});




    }

}