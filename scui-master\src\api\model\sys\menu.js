import config from "@/config";
import http from "@/utils/request";

const menu = {
	list: {
		url: `${config.API_URL}/menu/tree`,
		name: "菜单管理",
		get: async function () {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url);

		}
	}
	, save: {
		url: `${config.API_URL}/menu/save`,
		name: "保存菜单",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}
	, remove: {
		url: `${config.API_URL}/menu/remove`,
		name: "删除菜单",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}
}

export default menu;
