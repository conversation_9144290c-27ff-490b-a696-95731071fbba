import config from "@/config";
import http from "@/utils/request";

const costtype = {
	list: {
		url: `${config.API_URL}/oa/notify/list`,
		name: "通知公告列表",
		get: async function () {
			return await http.get(this.url, this.name);
		}
	},
	mylist: {
		url: `${config.API_URL}/oa/notify/mylist`,
		name: "我的通知公告列表",
		get: async function () {
			return await http.get(this.url, this.name);
		}
	},
	infolist: {
		url: `${config.API_URL}/oa/notify/infolist`,
		name: "通知公告阅知列表",
		get: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	viewlist: {
		url: `${config.API_URL}/oa/notify/viewlist`,
		name: "通知公告阅知列表",
		get: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/oa/notify/save`,
		name: "通知公告保存",
		post: async function (data) {
			//{where：{id：1}}
			return await http.post(this.url, this.name, data);
		}
	},
	add: {
		url: `${config.API_URL}/oa/notify/add`,
		name: "通知公告新增",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/oa/notify/delete`,
		name: "通知公告删除",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	done: {
		url: `${config.API_URL}/oa/notify/done`,
		name: "确认",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	}
};

export default costtype;
