
require('./common.js')
think.beforeStartServer(async () => {
    const config = await think.model('config').where({id:1}).find();
    think.config('system', JSON.parse(config.data)); //从数据库中将配置读取出来，然后设置


    const config2 = await think.model('config').where({id:2}).find();
    think.config('sms', JSON.parse(config2.data)); //从数据库中将配置读取出来，然后设置


    const config3 = await think.model('config').where({id:3}).find();
    think.config('ti', JSON.parse(config3.data)); //从数据库中将配置读取出来，然后设置


    let res=await think.model('school').where({gzhconfig:["!=",null]}).select();
    for(let i=0;i<res.length;i++){
        let gzhconfig=JSON.parse(res[i].gzhconfig);
        if(gzhconfig.appid){
            think.config(res[i].id+'_wechat', gzhconfig); //从数据库中将配置读取出来，然后设置
        }
    }

    let res2=await think.model('school').where({wxpayconfig:["!=",null]}).select();
    for(let i=0;i<res2.length;i++){
        let wxpayconfig=JSON.parse(res2[i].wxpayconfig);
        if(wxpayconfig.appid){
            think.config(res2[i].id+'_wxpay', wxpayconfig); //从数据库中将配置读取出来，然后设置
        }
    }
    think.cache('wechat_access_token',"")

  })