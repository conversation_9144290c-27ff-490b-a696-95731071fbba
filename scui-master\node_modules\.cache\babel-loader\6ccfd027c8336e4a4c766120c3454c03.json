{"ast": null, "code": "import scCropper from '@/components/scCropper';\nexport default {\n  emits: [\"success\", \"closed\"],\n  components: {\n    sc<PERSON>ropper\n  },\n\n  data() {\n    return {\n      array: [1],\n      height: \"\",\n      rowsytle: {\n        height: \"\"\n      },\n      mode: \"add\",\n      titleMap: {\n        add: \"上传\",\n        edit: \"编辑\"\n      },\n      url: \"/static/upload/26b6ea56-7049-425a-a659-64defef541bf/t4.png\",\n      btnloading: false,\n      visible: false,\n      isSaveing: false,\n      menuList: [],\n      fileViewList: [],\n      uploadApi: this.$API.common.upload,\n      ifprocess: false,\n      dxlist: [{\n        value: \"A\"\n      }, {\n        value: \"B\"\n      }, {\n        value: \"C\"\n      }, {\n        value: \"D\"\n      }],\n      duoxuanlist: [{\n        value: \"A\"\n      }, {\n        value: \"B\"\n      }, {\n        value: \"C\"\n      }, {\n        value: \"D\"\n      }],\n      cropperVisible: false,\n      cropperImageSrc: '',\n      uploadLoading: false,\n      form: {\n        id: \"\",\n        tm_p: \"\",\n        jx_p: \"\",\n        score: \"\",\n        tm_p_new: \"\",\n        ans: \"\",\n        ans2: [],\n        value: [],\n        lessonid: 0\n      },\n      rules: {\n        name: [{\n          required: true,\n          message: \"请输入\",\n          trigger: \"blur\"\n        }],\n        parent: [{\n          required: true,\n          trigger: \"blur\",\n          message: \"请选择\"\n        }]\n      },\n      dict: [],\n      dicProps: {\n        value: \"id\",\n        label: \"name\",\n        checkStrictly: true,\n        emitPath: false\n      },\n      props: {\n        menu: {\n          type: Object,\n          default: () => {}\n        }\n      },\n      selectConfig: {\n        score: {\n          label: \"name\",\n          value: \"name\"\n        }\n      },\n      menuProps: {\n        value: \"id\",\n        emitPath: false,\n        label: \"title\",\n        checkStrictly: true\n      }\n    };\n  },\n\n  mounted() {\n    this.getDic();\n    this.getOffice();\n  },\n\n  created() {\n    this.height = document.documentElement.clientHeight - 180;\n    this.rowsytle.height = this.height + \"px\";\n  },\n\n  methods: {\n    async savedx() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans\n        });\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    async saveduoxuan() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\")\n        });\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    async savedx2() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans\n        });\n        await this.next();\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    async saveduoxuan2() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\")\n        });\n        await this.next();\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    async savetk() {\n      await this.$API.tk.savetk.post({\n        id: this.form.id,\n        ans: \"\"\n      });\n      await this.next();\n      this.$message.success(\"保存成功\");\n    },\n\n    async savetk2() {\n      if (!this.$TOOL.isEmpty(this.form.value)) {\n        await this.$API.tk.savetk.post({\n          id: this.form.id,\n          ans: JSON.stringify(this.form.value)\n        });\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请填写正确答案\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    close() {\n      this.$emit(\"success\", this.form, this.mode);\n      this.visible = false;\n    },\n\n    async savejd() {\n      await this.$API.tk.savejd.post({\n        id: this.form.id\n      });\n      console.log(\"====\", this.form.value);\n      this.$message.success(\"保存成功\");\n    },\n\n    async savejd2() {\n      await this.$API.tk.savejd.post({\n        id: this.form.id\n      });\n      await this.next();\n      this.$message.success(\"保存成功\");\n    },\n\n    async getnext() {\n      let next = await this.$API.tk.getnext2.get({\n        lessonid: this.form.lessonid,\n        num: this.form.num\n      });\n\n      if (next.length == 0) {\n        this.$alert(\"本分类无待处理内容\", \"提示\", {\n          type: \"error\"\n        });\n      } else {\n        this.form.ans = \"\";\n        this.array = [1];\n        this.form.value = [];\n        this.form.id = next[0].id;\n        this.form.tm_p = next[0].tm_p;\n        this.form.jx_p = next[0].jx_p;\n        this.form.num = next[0].num;\n      }\n    },\n\n    async before() {\n      let next = await this.$API.tk.before.get({\n        lessonid: this.form.lessonid,\n        num: this.form.num\n      });\n\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", {\n          type: \"error\"\n        });\n      } else {\n        this.setData(next);\n      }\n    },\n\n    async next() {\n      let next = await this.$API.tk.next.get({\n        lessonid: this.form.lessonid,\n        num: this.form.num\n      });\n\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", {\n          type: \"error\"\n        });\n      } else {\n        this.setData(next);\n      }\n    },\n\n    dxadd() {\n      console.log(this.form.ans); // 生成新元素的value值\n\n      const newValue = String.fromCharCode(65 + this.dxlist.length); // 添加新元素\n\n      this.dxlist.push({\n        value: newValue\n      });\n    },\n\n    async process() {\n      this.btnloading = true;\n      let res = await this.$API.tk.process.post({\n        file: this.form.zip\n      });\n      this.form.buss_id = res.data.buss_id;\n      this.$message.success(\"解析完成\");\n      this.ifprocess = true;\n      this.btnloading = false;\n    },\n\n    fileSuccess(response) {\n      const suffix = response.data.file_name.substr(response.data.file_name.lastIndexOf(\".\") + 1); // 文件后缀\n\n      this.fileViewList.push({\n        suffix: suffix,\n        name: response.data.file_name,\n        url: response.data.src,\n        new_name: response.data.new_name,\n        id: response.data.new_name\n      });\n      this.$message.success(`文件上传成功`);\n      this.ifprocess = false;\n      return false;\n    },\n\n    beforeRemove(file) {\n      this.fileViewList.map((r, index) => {\n        if (r.name == file.name) {\n          this.form.files = this.form.files.replace(\"/static/upload/\" + file.name + \",\", \"\");\n          this.fileViewList.splice(index, 1);\n        }\n      });\n    },\n\n    async getOffice() {\n      var res = await this.$API.lesson.list.get();\n      this.menuList = res;\n    },\n\n    addtk() {\n      this.array.push(1); //通过添加array的值，增加input的个数\n    },\n\n    del(index) {\n      this.form.value.splice(index, 1); //先删除form中value对应索引的值\n\n      this.array.splice(index, 1); //然后删除array对应索引的值，实现点击删除按钮，减少input框效果\n    },\n\n    //显示\n    open(mode = \"add\") {\n      this.mode = mode;\n      this.visible = true;\n      return this;\n    },\n\n    //获取字典列表\n    async getDic() {\n      var res = await this.$API.dict.list.get();\n      this.dict = res.data;\n    },\n\n    //表单提交方法\n    submit() {\n      if (this.ifprocess) {\n        this.$refs.dialogForm.validate(async valid => {\n          if (valid) {\n            this.isSaveing = true;\n            let res = await this.$API.tk.saveimp.post(this.form);\n            this.isSaveing = false;\n\n            if (res.state == 1) {\n              this.$emit(\"success\", this.form, this.mode);\n              this.visible = false;\n              this.$message.success(\"操作成功\");\n            } else {\n              this.$alert(res.msg, \"提示\", {\n                type: \"error\"\n              });\n            }\n          }\n        });\n      } else {\n        this.$alert(\"请先解析压缩包内容！\", \"提示\", {\n          type: \"error\"\n        });\n      }\n    },\n\n    //表单注入数据\n    setData(data, mode) {\n      //可以和上面一样单个注入，也可以像下面一样直接合并进去\n      this.titleMap.edit = \"题号：\" + data.no + \"类型：\" + data.type;\n      this.form.ans = \"\";\n      this.form.ans2 = [];\n      this.form.value = [];\n      this.array = [1];\n      Object.assign(this.form, data);\n\n      if (data.type == \"单选\") {\n        console.log(data);\n        this.form.ans = data.ans;\n      }\n\n      if (data.type == \"多选\") {\n        this.form.ans2 = data.ans.split(\",\");\n      }\n    },\n\n    // 打开裁剪器\n    openCropper() {\n      if (this.form.tm_p) {\n        this.cropperImageSrc = this.form.tm_p;\n        this.cropperVisible = true;\n      } else {\n        this.$message.warning('没有题干图片可以裁剪');\n      }\n    },\n\n    // 保存裁剪后的图片\n    async saveCroppedImage() {\n      if (!this.$refs.cropper) {\n        this.$message.error('裁剪器未初始化');\n        return;\n      }\n\n      this.uploadLoading = true;\n\n      try {\n        // 获取裁剪后的文件\n        this.$refs.cropper.getCropFile(async file => {\n          try {\n            // 创建FormData上传\n            const formData = new FormData();\n            formData.append('file', file); // 上传到服务器\n\n            const response = await this.$API.common.upload.post(formData);\n            console.log(response);\n\n            if (response && response.data && response.data.src) {\n              // 更新题干图片路径\n              this.form.tm_p_new = response.data.src;\n              await this.$API.common.util.post('/sys/tk/updatetmnew', {\n                id: this.form.id,\n                tm_p_new: this.form.tm_p_new\n              });\n              this.cropperVisible = false;\n            } else {\n              this.$message.error('上传失败，请重试');\n            }\n          } catch (error) {\n            console.error('上传失败:', error);\n            this.$message.error('上传失败: ' + (error.message || '未知错误'));\n          } finally {\n            this.uploadLoading = false;\n          }\n        }, 'cropped_image.jpg', 'image/jpeg');\n      } catch (error) {\n        console.error('裁剪失败:', error);\n        this.$message.error('裁剪失败: ' + (error.message || '未知错误'));\n        this.uploadLoading = false;\n      }\n    }\n\n  }\n};", "map": {"version": 3, "mappings": "AAgMA,OAAOA,SAAP,MAAsB,wBAAtB;AAEA,eAAe;EACbC,KAAK,EAAE,CAAC,SAAD,EAAY,QAAZ,CADM;EAEbC,UAAU,EAAE;IACVF;EADU,CAFC;;EAKbG,IAAI,GAAG;IACL,OAAO;MACLC,KAAK,EAAE,CAAC,CAAD,CADF;MAELC,MAAM,EAAE,EAFH;MAGLC,QAAQ,EAAE;QACRD,MAAM,EAAE;MADA,CAHL;MAMLE,IAAI,EAAE,KAND;MAOLC,QAAQ,EAAE;QACRC,GAAG,EAAE,IADG;QAERC,IAAI,EAAE;MAFE,CAPL;MAWLC,GAAG,EAAE,4DAXA;MAYLC,UAAU,EAAE,KAZP;MAaLC,OAAO,EAAE,KAbJ;MAcLC,SAAS,EAAE,KAdN;MAeLC,QAAQ,EAAE,EAfL;MAgBLC,YAAY,EAAE,EAhBT;MAiBLC,SAAS,EAAE,KAAKC,IAAL,CAAUC,MAAV,CAAiBC,MAjBvB;MAmBLC,SAAS,EAAE,KAnBN;MAoBLC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAT,CAAD,EAAiB;QAAEA,KAAK,EAAE;MAAT,CAAjB,EAAiC;QAAEA,KAAK,EAAE;MAAT,CAAjC,EAAiD;QAAEA,KAAK,EAAE;MAAT,CAAjD,CApBH;MAsBLC,WAAW,EAAE,CACX;QAAED,KAAK,EAAE;MAAT,CADW,EAEX;QAAEA,KAAK,EAAE;MAAT,CAFW,EAGX;QAAEA,KAAK,EAAE;MAAT,CAHW,EAIX;QAAEA,KAAK,EAAE;MAAT,CAJW,CAtBR;MA6BLE,cAAc,EAAE,KA7BX;MA8BLC,eAAe,EAAE,EA9BZ;MA+BLC,aAAa,EAAE,KA/BV;MAgCLC,IAAI,EAAE;QACJC,EAAE,EAAE,EADA;QAEJC,IAAI,EAAE,EAFF;QAGJC,IAAI,EAAE,EAHF;QAIJC,KAAK,EAAE,EAJH;QAKJC,QAAQ,EAAE,EALN;QAMJC,GAAG,EAAE,EAND;QAOJC,IAAI,EAAE,EAPF;QAQJZ,KAAK,EAAE,EARH;QASJa,QAAQ,EAAE;MATN,CAhCD;MA2CLC,KAAK,EAAE;QACLC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAZ;UAAkBC,OAAO,EAAE,KAA3B;UAAkCC,OAAO,EAAE;QAA3C,CAAD,CADD;QAGLC,MAAM,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAZ;UAAkBE,OAAO,EAAE,MAA3B;UAAmCD,OAAO,EAAE;QAA5C,CAAD;MAHH,CA3CF;MAgDLG,IAAI,EAAE,EAhDD;MAiDLC,QAAQ,EAAE;QACRrB,KAAK,EAAE,IADC;QAERsB,KAAK,EAAE,MAFC;QAGRC,aAAa,EAAE,IAHP;QAIRC,QAAQ,EAAE;MAJF,CAjDL;MAuDLC,KAAK,EAAE;QACLC,IAAI,EAAE;UACJC,IAAI,EAAEC,MADF;UAEJC,OAAO,EAAE,MAAM,CAAE;QAFb;MADD,CAvDF;MA6DLC,YAAY,EAAE;QACZrB,KAAK,EAAE;UACLa,KAAK,EAAE,MADF;UAELtB,KAAK,EAAE;QAFF;MADK,CA7DT;MAmEL+B,SAAS,EAAE;QACT/B,KAAK,EAAE,IADE;QAETwB,QAAQ,EAAE,KAFD;QAGTF,KAAK,EAAE,OAHE;QAITC,aAAa,EAAE;MAJN;IAnEN,CAAP;EA0ED,CAhFY;;EAiFbS,OAAO,GAAG;IACR,KAAKC,MAAL;IACA,KAAKC,SAAL;EACD,CApFY;;EAqFbC,OAAO,GAAG;IACR,KAAKrD,MAAL,GAAcsD,QAAQ,CAACC,eAAT,CAAyBC,YAAzB,GAAwC,GAAtD;IAEA,KAAKvD,QAAL,CAAcD,MAAd,GAAuB,KAAKA,MAAL,GAAc,IAArC;EACD,CAzFY;;EA0FbyD,OAAO,EAAE;IACP,MAAMC,MAAN,GAAe;MACb,IAAI,CAAC,KAAKC,KAAL,CAAWC,OAAX,CAAmB,KAAKrC,IAAL,CAAUM,GAA7B,CAAL,EAAwC;QACtC,MAAM,KAAKhB,IAAL,CAAUgD,EAAV,CAAaH,MAAb,CAAoBI,IAApB,CAAyB;UAC7BtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EADe;UAE7BK,GAAG,EAAE,KAAKN,IAAL,CAAUM;QAFc,CAAzB,CAAN;QAIA,KAAKkC,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACF,CANA,MAMO;QACL,KAAKC,MAAL,CAAY,SAAZ,EAAuB,IAAvB,EAA6B;UAAEpB,IAAI,EAAE;QAAR,CAA7B;MACF;IACD,CAXM;;IAaP,MAAMqB,WAAN,GAAoB;MAClB,IAAI,CAAC,KAAKP,KAAL,CAAWC,OAAX,CAAmB,KAAKrC,IAAL,CAAUO,IAA7B,CAAL,EAAyC;QACvC,MAAM,KAAKjB,IAAL,CAAUgD,EAAV,CAAaK,WAAb,CAAyBJ,IAAzB,CAA8B;UAClCtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EADoB;UAElCK,GAAG,EAAE,KAAKN,IAAL,CAAUO,IAAV,CAAeqC,IAAf,CAAoB,GAApB;QAF6B,CAA9B,CAAN;QAIA,KAAK5C,IAAL,CAAUO,IAAV,GAAiB,EAAjB;QACA,KAAKiC,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACF,CAPA,MAOO;QACL,KAAKC,MAAL,CAAY,SAAZ,EAAuB,IAAvB,EAA6B;UAAEpB,IAAI,EAAE;QAAR,CAA7B;MACF;IACD,CAxBM;;IA0BP,MAAMuB,OAAN,GAAgB;MACd,IAAI,CAAC,KAAKT,KAAL,CAAWC,OAAX,CAAmB,KAAKrC,IAAL,CAAUM,GAA7B,CAAL,EAAwC;QACtC,MAAM,KAAKhB,IAAL,CAAUgD,EAAV,CAAaH,MAAb,CAAoBI,IAApB,CAAyB;UAC7BtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EADe;UAE7BK,GAAG,EAAE,KAAKN,IAAL,CAAUM;QAFc,CAAzB,CAAN;QAIA,MAAM,KAAKwC,IAAL,EAAN;QACA,KAAKN,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACF,CAPA,MAOO;QACL,KAAKC,MAAL,CAAY,SAAZ,EAAuB,IAAvB,EAA6B;UAAEpB,IAAI,EAAE;QAAR,CAA7B;MACF;IACD,CArCM;;IAuCP,MAAMyB,YAAN,GAAqB;MACnB,IAAI,CAAC,KAAKX,KAAL,CAAWC,OAAX,CAAmB,KAAKrC,IAAL,CAAUO,IAA7B,CAAL,EAAyC;QACvC,MAAM,KAAKjB,IAAL,CAAUgD,EAAV,CAAaK,WAAb,CAAyBJ,IAAzB,CAA8B;UAClCtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EADoB;UAElCK,GAAG,EAAE,KAAKN,IAAL,CAAUO,IAAV,CAAeqC,IAAf,CAAoB,GAApB;QAF6B,CAA9B,CAAN;QAIA,MAAO,KAAKE,IAAL,EAAP;QACA,KAAK9C,IAAL,CAAUO,IAAV,GAAiB,EAAjB;QACA,KAAKiC,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACF,CARA,MAQO;QACL,KAAKC,MAAL,CAAY,SAAZ,EAAuB,IAAvB,EAA6B;UAAEpB,IAAI,EAAE;QAAR,CAA7B;MACF;IACD,CAnDM;;IAqDP,MAAM0B,MAAN,GAAe;MACb,MAAM,KAAK1D,IAAL,CAAUgD,EAAV,CAAaU,MAAb,CAAoBT,IAApB,CAAyB;QAAEtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EAAhB;QAAoBK,GAAG,EAAE;MAAzB,CAAzB,CAAN;MACA,MAAO,KAAKwC,IAAL,EAAP;MACA,KAAKN,QAAL,CAAcC,OAAd,CAAsB,MAAtB;IACD,CAzDM;;IA0DP,MAAMQ,OAAN,GAAgB;MACd,IAAI,CAAC,KAAKb,KAAL,CAAWC,OAAX,CAAmB,KAAKrC,IAAL,CAAUL,KAA7B,CAAL,EAA0C;QACxC,MAAM,KAAKL,IAAL,CAAUgD,EAAV,CAAaU,MAAb,CAAoBT,IAApB,CAAyB;UAC7BtC,EAAE,EAAE,KAAKD,IAAL,CAAUC,EADe;UAE7BK,GAAG,EAAE4C,IAAI,CAACC,SAAL,CAAe,KAAKnD,IAAL,CAAUL,KAAzB;QAFwB,CAAzB,CAAN;QAKA,KAAK6C,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACF,CAPA,MAOO;QACL,KAAKC,MAAL,CAAY,SAAZ,EAAuB,IAAvB,EAA6B;UAAEpB,IAAI,EAAE;QAAR,CAA7B;MACF;IACD,CArEM;;IAuEP8B,KAAK,GAAG;MACN,KAAKC,KAAL,CAAW,SAAX,EAAsB,KAAKrD,IAA3B,EAAiC,KAAKrB,IAAtC;MACA,KAAKM,OAAL,GAAe,KAAf;IACD,CA1EM;;IA4EP,MAAMqE,MAAN,GAAe;MACb,MAAM,KAAKhE,IAAL,CAAUgD,EAAV,CAAagB,MAAb,CAAoBf,IAApB,CAAyB;QAAEtC,EAAE,EAAE,KAAKD,IAAL,CAAUC;MAAhB,CAAzB,CAAN;MACAsD,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoB,KAAKxD,IAAL,CAAUL,KAA9B;MACA,KAAK6C,QAAL,CAAcC,OAAd,CAAsB,MAAtB;IACD,CAhFM;;IAiFP,MAAMgB,OAAN,GAAgB;MACd,MAAM,KAAKnE,IAAL,CAAUgD,EAAV,CAAagB,MAAb,CAAoBf,IAApB,CAAyB;QAAEtC,EAAE,EAAE,KAAKD,IAAL,CAAUC;MAAhB,CAAzB,CAAN;MACA,MAAO,KAAK6C,IAAL,EAAP;MACA,KAAKN,QAAL,CAAcC,OAAd,CAAsB,MAAtB;IACD,CArFM;;IAuFP,MAAMiB,OAAN,GAAgB;MACd,IAAIZ,IAAG,GAAI,MAAM,KAAKxD,IAAL,CAAUgD,EAAV,CAAaqB,QAAb,CAAsBC,GAAtB,CAA0B;QACzCpD,QAAQ,EAAE,KAAKR,IAAL,CAAUQ,QADqB;QAEzCqD,GAAG,EAAC,KAAK7D,IAAL,CAAU6D;MAF2B,CAA1B,CAAjB;;MAIA,IAAIf,IAAI,CAACgB,MAAL,IAAe,CAAnB,EAAsB;QACpB,KAAKpB,MAAL,CAAY,WAAZ,EAAyB,IAAzB,EAA+B;UAAEpB,IAAI,EAAE;QAAR,CAA/B;MACF,CAFA,MAEO;QACL,KAAKtB,IAAL,CAAUM,GAAV,GAAgB,EAAhB;QACA,KAAK9B,KAAL,GAAa,CAAC,CAAD,CAAb;QACA,KAAKwB,IAAL,CAAUL,KAAV,GAAkB,EAAlB;QACA,KAAKK,IAAL,CAAUC,EAAV,GAAe6C,IAAI,CAAC,CAAD,CAAJ,CAAQ7C,EAAvB;QACA,KAAKD,IAAL,CAAUE,IAAV,GAAiB4C,IAAI,CAAC,CAAD,CAAJ,CAAQ5C,IAAzB;QACA,KAAKF,IAAL,CAAUG,IAAV,GAAiB2C,IAAI,CAAC,CAAD,CAAJ,CAAQ3C,IAAzB;QACA,KAAKH,IAAL,CAAU6D,GAAV,GAAcf,IAAI,CAAC,CAAD,CAAJ,CAAQe,GAAtB;MACF;IACD,CAvGM;;IAyGX,MAAME,MAAN,GAAc;MAGL,IAAIjB,IAAG,GAAI,MAAM,KAAKxD,IAAL,CAAUgD,EAAV,CAAayB,MAAb,CAAoBH,GAApB,CAAwB;QAC1CpD,QAAQ,EAAE,KAAKR,IAAL,CAAUQ,QADsB;QAE1CqD,GAAG,EAAC,KAAK7D,IAAL,CAAU6D;MAF4B,CAAxB,CAAjB;;MAIH,IAAIf,IAAI,CAACgB,MAAL,IAAe,CAAnB,EAAsB;QACpB,KAAKpB,MAAL,CAAY,OAAZ,EAAqB,IAArB,EAA2B;UAAEpB,IAAI,EAAE;QAAR,CAA3B;MACF,CAFA,MAEO;QACN,KAAK0C,OAAL,CAAalB,IAAb;MACD;IACD,CArHM;;IAuHP,MAAMA,IAAN,GAAY;MAGP,IAAIA,IAAG,GAAI,MAAM,KAAKxD,IAAL,CAAUgD,EAAV,CAAaQ,IAAb,CAAkBc,GAAlB,CAAsB;QACxCpD,QAAQ,EAAE,KAAKR,IAAL,CAAUQ,QADoB;QAExCqD,GAAG,EAAC,KAAK7D,IAAL,CAAU6D;MAF0B,CAAtB,CAAjB;;MAIH,IAAIf,IAAI,CAACgB,MAAL,IAAe,CAAnB,EAAsB;QACpB,KAAKpB,MAAL,CAAY,OAAZ,EAAqB,IAArB,EAA2B;UAAEpB,IAAI,EAAE;QAAR,CAA3B;MACF,CAFA,MAEO;QACN,KAAK0C,OAAL,CAAalB,IAAb;MACD;IACD,CAnIM;;IAqIPmB,KAAK,GAAG;MACNV,OAAO,CAACC,GAAR,CAAY,KAAKxD,IAAL,CAAUM,GAAtB,EADM,CAEN;;MACA,MAAM4D,QAAO,GAAIC,MAAM,CAACC,YAAP,CAAoB,KAAK,KAAK1E,MAAL,CAAYoE,MAArC,CAAjB,CAHM,CAKN;;MACA,KAAKpE,MAAL,CAAY2E,IAAZ,CAAiB;QAAE1E,KAAK,EAAEuE;MAAT,CAAjB;IACD,CA5IM;;IA6IP,MAAMI,OAAN,GAAgB;MACd,KAAKtF,UAAL,GAAkB,IAAlB;MACA,IAAIuF,GAAE,GAAI,MAAM,KAAKjF,IAAL,CAAUgD,EAAV,CAAagC,OAAb,CAAqB/B,IAArB,CAA0B;QAAEiC,IAAI,EAAE,KAAKxE,IAAL,CAAUyE;MAAlB,CAA1B,CAAhB;MACA,KAAKzE,IAAL,CAAU0E,OAAV,GAAoBH,GAAG,CAAChG,IAAJ,CAASmG,OAA7B;MACA,KAAKlC,QAAL,CAAcC,OAAd,CAAsB,MAAtB;MACA,KAAKhD,SAAL,GAAiB,IAAjB;MACA,KAAKT,UAAL,GAAkB,KAAlB;IACD,CApJM;;IAqJP2F,WAAW,CAACC,QAAD,EAAW;MACpB,MAAMC,MAAK,GAAID,QAAQ,CAACrG,IAAT,CAAcuG,SAAd,CAAwBC,MAAxB,CACbH,QAAQ,CAACrG,IAAT,CAAcuG,SAAd,CAAwBE,WAAxB,CAAoC,GAApC,IAA2C,CAD9B,CAAf,CADoB,CAGjB;;MACH,KAAK5F,YAAL,CAAkBiF,IAAlB,CAAuB;QACrBQ,MAAM,EAAEA,MADa;QAErBnE,IAAI,EAAEkE,QAAQ,CAACrG,IAAT,CAAcuG,SAFC;QAGrB/F,GAAG,EAAE6F,QAAQ,CAACrG,IAAT,CAAc0G,GAHE;QAIrBC,QAAQ,EAAEN,QAAQ,CAACrG,IAAT,CAAc2G,QAJH;QAKrBjF,EAAE,EAAE2E,QAAQ,CAACrG,IAAT,CAAc2G;MALG,CAAvB;MAOA,KAAK1C,QAAL,CAAcC,OAAd,CAAuB,QAAvB;MACA,KAAKhD,SAAL,GAAiB,KAAjB;MACA,OAAO,KAAP;IACD,CAnKM;;IAoKP0F,YAAY,CAACX,IAAD,EAAO;MACjB,KAAKpF,YAAL,CAAkBgG,GAAlB,CAAsB,CAACC,CAAD,EAAIC,KAAJ,KAAc;QAClC,IAAID,CAAC,CAAC3E,IAAF,IAAU8D,IAAI,CAAC9D,IAAnB,EAAyB;UACvB,KAAKV,IAAL,CAAUuF,KAAV,GAAkB,KAAKvF,IAAL,CAAUuF,KAAV,CAAgBC,OAAhB,CAChB,oBAAoBhB,IAAI,CAAC9D,IAAzB,GAAgC,GADhB,EAEhB,EAFgB,CAAlB;UAIA,KAAKtB,YAAL,CAAkBqG,MAAlB,CAAyBH,KAAzB,EAAgC,CAAhC;QACF;MACD,CARD;IASD,CA9KM;;IA+KP,MAAMzD,SAAN,GAAkB;MAChB,IAAI0C,GAAE,GAAI,MAAM,KAAKjF,IAAL,CAAUoG,MAAV,CAAiBC,IAAjB,CAAsB/B,GAAtB,EAAhB;MACA,KAAKzE,QAAL,GAAgBoF,GAAhB;IACD,CAlLM;;IAoLPqB,KAAK,GAAG;MACN,KAAKpH,KAAL,CAAW6F,IAAX,CAAgB,CAAhB,EADM,CACc;IACrB,CAtLM;;IAuLPwB,GAAG,CAACP,KAAD,EAAQ;MACT,KAAKtF,IAAL,CAAUL,KAAV,CAAgB8F,MAAhB,CAAuBH,KAAvB,EAA8B,CAA9B,EADS,CACyB;;MAClC,KAAK9G,KAAL,CAAWiH,MAAX,CAAkBH,KAAlB,EAAyB,CAAzB,EAFS,CAEoB;IAC9B,CA1LM;;IA4LP;IACAQ,IAAI,CAACnH,IAAG,GAAI,KAAR,EAAe;MACjB,KAAKA,IAAL,GAAYA,IAAZ;MACA,KAAKM,OAAL,GAAe,IAAf;MACA,OAAO,IAAP;IACD,CAjMM;;IAkMP;IACA,MAAM2C,MAAN,GAAe;MACb,IAAI2C,GAAE,GAAI,MAAM,KAAKjF,IAAL,CAAUyB,IAAV,CAAe4E,IAAf,CAAoB/B,GAApB,EAAhB;MACA,KAAK7C,IAAL,GAAYwD,GAAG,CAAChG,IAAhB;IACD,CAtMM;;IAuMP;IACAwH,MAAM,GAAG;MACP,IAAI,KAAKtG,SAAT,EAAoB;QAClB,KAAKuG,KAAL,CAAWC,UAAX,CAAsBC,QAAtB,CAA+B,MAAOC,KAAP,IAAiB;UAC9C,IAAIA,KAAJ,EAAW;YACT,KAAKjH,SAAL,GAAiB,IAAjB;YAEA,IAAIqF,GAAE,GAAI,MAAM,KAAKjF,IAAL,CAAUgD,EAAV,CAAa8D,OAAb,CAAqB7D,IAArB,CAA0B,KAAKvC,IAA/B,CAAhB;YACA,KAAKd,SAAL,GAAiB,KAAjB;;YACA,IAAIqF,GAAG,CAAC8B,KAAJ,IAAa,CAAjB,EAAoB;cAClB,KAAKhD,KAAL,CAAW,SAAX,EAAsB,KAAKrD,IAA3B,EAAiC,KAAKrB,IAAtC;cACA,KAAKM,OAAL,GAAe,KAAf;cACA,KAAKuD,QAAL,CAAcC,OAAd,CAAsB,MAAtB;YACF,CAJA,MAIO;cACL,KAAKC,MAAL,CAAY6B,GAAG,CAAC+B,GAAhB,EAAqB,IAArB,EAA2B;gBAAEhF,IAAI,EAAE;cAAR,CAA3B;YACF;UACF;QACD,CAdD;MAeF,CAhBA,MAgBO;QACL,KAAKoB,MAAL,CAAY,YAAZ,EAA0B,IAA1B,EAAgC;UAAEpB,IAAI,EAAE;QAAR,CAAhC;MACF;IACD,CA5NM;;IA6NP;IACA0C,OAAO,CAACzF,IAAD,EAAOI,IAAP,EAAa;MAClB;MACA,KAAKC,QAAL,CAAcE,IAAd,GAAqB,QAAQP,IAAI,CAACgI,EAAb,GAAgB,KAAhB,GAAsBhI,IAAI,CAAC+C,IAAhD;MACA,KAAKtB,IAAL,CAAUM,GAAV,GAAgB,EAAhB;MACA,KAAKN,IAAL,CAAUO,IAAV,GAAiB,EAAjB;MACA,KAAKP,IAAL,CAAUL,KAAV,GAAkB,EAAlB;MACA,KAAKnB,KAAL,GAAa,CAAC,CAAD,CAAb;MACA+C,MAAM,CAACiF,MAAP,CAAc,KAAKxG,IAAnB,EAAyBzB,IAAzB;;MAIA,IAAIA,IAAI,CAAC+C,IAAL,IAAa,IAAjB,EAAuB;QACrBiC,OAAO,CAACC,GAAR,CAAYjF,IAAZ;QACA,KAAKyB,IAAL,CAAUM,GAAV,GAAgB/B,IAAI,CAAC+B,GAArB;MACF;;MACA,IAAI/B,IAAI,CAAC+C,IAAL,IAAa,IAAjB,EAAuB;QACrB,KAAKtB,IAAL,CAAUO,IAAV,GAAiBhC,IAAI,CAAC+B,GAAL,CAASmG,KAAT,CAAe,GAAf,CAAjB;MACF;IACD,CAhPM;;IAkPP;IACAC,WAAW,GAAG;MACZ,IAAI,KAAK1G,IAAL,CAAUE,IAAd,EAAoB;QAClB,KAAKJ,eAAL,GAAuB,KAAKE,IAAL,CAAUE,IAAjC;QACA,KAAKL,cAAL,GAAsB,IAAtB;MACF,CAHA,MAGO;QACL,KAAK2C,QAAL,CAAcmE,OAAd,CAAsB,YAAtB;MACF;IACD,CA1PM;;IA4PP;IACA,MAAMC,gBAAN,GAAyB;MACvB,IAAI,CAAC,KAAKZ,KAAL,CAAWa,OAAhB,EAAyB;QACvB,KAAKrE,QAAL,CAAcsE,KAAd,CAAoB,SAApB;QACA;MACF;;MAEA,KAAK/G,aAAL,GAAqB,IAArB;;MACA,IAAI;QACF;QACA,KAAKiG,KAAL,CAAWa,OAAX,CAAmBE,WAAnB,CAA+B,MAAOvC,IAAP,IAAgB;UAC7C,IAAI;YACF;YACA,MAAMwC,QAAO,GAAI,IAAIC,QAAJ,EAAjB;YACAD,QAAQ,CAACE,MAAT,CAAgB,MAAhB,EAAwB1C,IAAxB,EAHE,CAKF;;YACA,MAAMI,QAAO,GAAI,MAAM,KAAKtF,IAAL,CAAUC,MAAV,CAAiBC,MAAjB,CAAwB+C,IAAxB,CAA6ByE,QAA7B,CAAvB;YAEAzD,OAAO,CAACC,GAAR,CAAYoB,QAAZ;;YAEA,IAAIA,QAAO,IAAKA,QAAQ,CAACrG,IAArB,IAA6BqG,QAAQ,CAACrG,IAAT,CAAc0G,GAA/C,EAAoD;cAClD;cACA,KAAKjF,IAAL,CAAUK,QAAV,GAAqBuE,QAAQ,CAACrG,IAAT,CAAc0G,GAAnC;cAGZ,MAAM,KAAK3F,IAAL,CAAUC,MAAV,CAAiB4H,IAAjB,CAAsB5E,IAAtB,CAA2B,qBAA3B,EAAiD;gBAACtC,EAAE,EAAC,KAAKD,IAAL,CAAUC,EAAd;gBAAiBI,QAAQ,EAAC,KAAKL,IAAL,CAAUK;cAApC,CAAjD,CAAN;cAEY,KAAKR,cAAL,GAAsB,KAAtB;YACF,CARA,MAQO;cACL,KAAK2C,QAAL,CAAcsE,KAAd,CAAoB,UAApB;YACF;UACF,CArBA,CAqBE,OAAOA,KAAP,EAAc;YACdvD,OAAO,CAACuD,KAAR,CAAc,OAAd,EAAuBA,KAAvB;YACA,KAAKtE,QAAL,CAAcsE,KAAd,CAAoB,YAAYA,KAAK,CAAClG,OAAN,IAAiB,MAA7B,CAApB;UACF,CAxBA,SAwBU;YACR,KAAKb,aAAL,GAAqB,KAArB;UACF;QACD,CA5BD,EA4BG,mBA5BH,EA4BwB,YA5BxB;MA6BF,CA/BA,CA+BE,OAAO+G,KAAP,EAAc;QACdvD,OAAO,CAACuD,KAAR,CAAc,OAAd,EAAuBA,KAAvB;QACA,KAAKtE,QAAL,CAAcsE,KAAd,CAAoB,YAAYA,KAAK,CAAClG,OAAN,IAAiB,MAA7B,CAApB;QACA,KAAKb,aAAL,GAAqB,KAArB;MACF;IACD;;EAxSM;AA1FI,CAAf", "names": ["scCropper", "emits", "components", "data", "array", "height", "<PERSON><PERSON><PERSON>", "mode", "titleMap", "add", "edit", "url", "btnloading", "visible", "isSaveing", "menuList", "fileViewList", "uploadApi", "$API", "common", "upload", "ifprocess", "dxlist", "value", "duoxuanlist", "cropperVisible", "cropperImageSrc", "uploadLoading", "form", "id", "tm_p", "jx_p", "score", "tm_p_new", "ans", "ans2", "lessonid", "rules", "name", "required", "message", "trigger", "parent", "dict", "dicProps", "label", "checkStrictly", "emitPath", "props", "menu", "type", "Object", "default", "selectConfig", "menuProps", "mounted", "getDic", "getOffice", "created", "document", "documentElement", "clientHeight", "methods", "savedx", "$TOOL", "isEmpty", "tk", "post", "$message", "success", "$alert", "saveduoxuan", "join", "savedx2", "next", "saveduoxuan2", "savetk", "savetk2", "JSON", "stringify", "close", "$emit", "<PERSON><PERSON><PERSON>", "console", "log", "savejd2", "getnext", "getnext2", "get", "num", "length", "before", "setData", "dxadd", "newValue", "String", "fromCharCode", "push", "process", "res", "file", "zip", "buss_id", "fileSuccess", "response", "suffix", "file_name", "substr", "lastIndexOf", "src", "new_name", "beforeRemove", "map", "r", "index", "files", "replace", "splice", "lesson", "list", "addtk", "del", "open", "submit", "$refs", "dialogForm", "validate", "valid", "saveimp", "state", "msg", "no", "assign", "split", "openCropper", "warning", "saveCroppedImage", "cropper", "error", "getCropFile", "formData", "FormData", "append", "util"], "sourceRoot": "", "sources": ["C:\\jsjy\\jsjy\\scui-master\\src\\views\\buss\\tiku\\save.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"titleMap[mode]\"\n    v-model=\"visible\"\n    fullscreen=\"true\"\n    destroy-on-close\n    @closed=\"$emit('closed')\"\n  >\n    <el-form\n      :model=\"form\"\n      :height=\"height\"\n      :rules=\"rules\"\n      ref=\"dialogForm\"\n      label-width=\"80px\"\n      label-position=\"left\"\n    >\n      <el-row :style=\"rowsytle\">\n        <el-col :span=\"8\">\n          <el-card :style=\"rowsytle\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>题干</span>\n              </div>\n            </template>\n            <div style=\"height: 500px; overflow-y: auto; overflow-x: hidden; border: 1px solid #ddd; padding: 10px; box-sizing: border-box;\">\n              <div style=\"margin-bottom: 20px;\" v-if=\"form.tm_p\">\n                <div style=\"margin-bottom: 5px; font-weight: bold; color: #666;\">原图:</div>\n                <img\n                  :src=\"form.tm_p\"\n                  style=\"width: 100%; height: auto; cursor: pointer; display: block; max-width: 100%;\"\n                  @click=\"openCropper\"\n                />\n              </div>\n\n              <div v-if=\"form.tm_p_new\">\n                <div style=\"margin-bottom: 5px; font-weight: bold; color: #666;\">裁剪后:</div>\n                <img\n                  :src=\"form.tm_p_new\"\n                  style=\"width: 100%; height: auto; cursor: pointer; display: block; max-width: 100%;\"\n                />\n              </div>\n\n              <!-- 强制内容高度，确保滚动条显示 -->\n              <div style=\"height: 800px;\"></div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>解析</span>\n              </div>\n            </template>\n            <el-image\n              style=\"height: 100%\"\n              :src=\"form.jx_p\"\n              :zoom-rate=\"1.2\"\n              :max-scale=\"7\"\n              :min-scale=\"0.2\"\n              fit=\"cover\"\n            />\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>答案</span>\n              </div>\n            </template>\n            <el-row style=\"height: 33%\">\n              <el-col>\n\n                 <span style=\"font-size:18px\">单选题</span>\n\n                <el-form-item label=\"答案\">\n                    <el-radio-group v-model=\"form.ans\">\n                      <el-radio\n                        v-for=\"item in dxlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"dxadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"savedx2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n\n                  <el-divider />\n\n\n                 <span style=\"font-size:18px\">多选题</span>\n\n                \n                  <el-form-item label=\"答案\">\n                    <el-checkbox-group v-model=\"form.ans2\">\n                      <el-checkbox\n                        v-for=\"item in duoxuanlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-checkbox>\n                    </el-checkbox-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"duoxuanadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"saveduoxuan2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n  <el-divider />\n             <span style=\"font-size:18px\">填空题</span>\n                <el-form-item label=\"\">\n                  <el-button icon=\"el-icon-check\" @click=\"savetk\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n  <el-divider />\n             <span style=\"font-size:18px\">解答题</span>\n\n        \n                <el-form-item label=\"\">\n                 \n                  <el-button icon=\"el-icon-check\" @click=\"savejd2\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n         \n\n        \n          </el-card>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <div  style=\"    position: absolute;\n    right: 60px;\n    top: 42px;\">\n          <el-button icon=\"el-icon-back\" @click=\"before\"\n                      >上一题</el-button\n                    >\n                      <el-button icon=\"el-icon-right\" @click=\"next\"\n                      >下一题</el-button\n                    >\n    </div>\n    <template #footer>\n      <el-button @click=\"close()\">取 消</el-button>\n    </template>\n\n    <!-- 裁剪对话框 -->\n    <el-dialog \n      title=\"题干图片裁剪\" \n      v-model=\"cropperVisible\" \n      width=\"600px\" \n      destroy-on-close\n      append-to-body\n    >\n      <sc-cropper \n        :src=\"cropperImageSrc\" \n        :compress=\"0.8\" \n        :aspectRatio=\"NaN\"\n        ref=\"cropper\"\n        v-if=\"cropperVisible\"\n      />\n      <template #footer>\n        <el-button @click=\"cropperVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveCroppedImage\" :loading=\"uploadLoading\">\n          保存裁剪\n        </el-button>\n      </template>\n    </el-dialog>\n  </el-dialog>\n</template>\n\n\n\n\n<script>\nimport scCropper from '@/components/scCropper'\n\nexport default {\n  emits: [\"success\", \"closed\"],\n  components: {\n    scCropper\n  },\n  data() {\n    return {\n      array: [1],\n      height: \"\",\n      rowsytle: {\n        height: \"\",\n      },\n      mode: \"add\",\n      titleMap: {\n        add: \"上传\",\n        edit: \"编辑\",\n      },\n      url: \"/static/upload/26b6ea56-7049-425a-a659-64defef541bf/t4.png\",\n      btnloading: false,\n      visible: false,\n      isSaveing: false,\n      menuList: [],\n      fileViewList: [],\n      uploadApi: this.$API.common.upload,\n\n      ifprocess: false,\n      dxlist: [{ value: \"A\" }, { value: \"B\" }, { value: \"C\" }, { value: \"D\" }],\n\n      duoxuanlist: [\n        { value: \"A\" },\n        { value: \"B\" },\n        { value: \"C\" },\n        { value: \"D\" },\n      ],\n\n      cropperVisible: false,\n      cropperImageSrc: '',\n      uploadLoading: false,\n      form: {\n        id: \"\",\n        tm_p: \"\",\n        jx_p: \"\",\n        score: \"\",\n        tm_p_new: \"\",\n        ans: \"\",\n        ans2: [],\n        value: [],\n        lessonid: 0,\n      },\n      rules: {\n        name: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n\n        parent: [{ required: true, trigger: \"blur\", message: \"请选择\" }],\n      },\n      dict: [],\n      dicProps: {\n        value: \"id\",\n        label: \"name\",\n        checkStrictly: true,\n        emitPath: false,\n      },\n      props: {\n        menu: {\n          type: Object,\n          default: () => {},\n        },\n      },\n      selectConfig: {\n        score: {\n          label: \"name\",\n          value: \"name\",\n        },\n      },\n      menuProps: {\n        value: \"id\",\n        emitPath: false,\n        label: \"title\",\n        checkStrictly: true,\n      },\n    };\n  },\n  mounted() {\n    this.getDic();\n    this.getOffice();\n  },\n  created() {\n    this.height = document.documentElement.clientHeight - 180;\n\n    this.rowsytle.height = this.height + \"px\";\n  },\n  methods: {\n    async savedx() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savedx2() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        await this.next();\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan2() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        await  this.next();\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savetk() {\n      await this.$API.tk.savetk.post({ id: this.form.id, ans: \"\" });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n    async savetk2() {\n      if (!this.$TOOL.isEmpty(this.form.value)) {\n        await this.$API.tk.savetk.post({\n          id: this.form.id,\n          ans: JSON.stringify(this.form.value),\n        });\n\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请填写正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    close() {\n      this.$emit(\"success\", this.form, this.mode);\n      this.visible = false;\n    },\n\n    async savejd() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      console.log(\"====\", this.form.value);\n      this.$message.success(\"保存成功\");\n    },\n    async savejd2() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n\n    async getnext() {\n      let next = await this.$API.tk.getnext2.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num\n      });\n      if (next.length == 0) {\n        this.$alert(\"本分类无待处理内容\", \"提示\", { type: \"error\" });\n      } else {\n        this.form.ans = \"\";\n        this.array = [1];\n        this.form.value = [];\n        this.form.id = next[0].id;\n        this.form.tm_p = next[0].tm_p;\n        this.form.jx_p = next[0].jx_p;\n        this.form.num=next[0].num\n      }\n    },\n\nasync before(){\n\n\n         let next = await this.$API.tk.before.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    async next(){\n\n\n         let next = await this.$API.tk.next.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    dxadd() {\n      console.log(this.form.ans);\n      // 生成新元素的value值\n      const newValue = String.fromCharCode(65 + this.dxlist.length);\n\n      // 添加新元素\n      this.dxlist.push({ value: newValue });\n    },\n    async process() {\n      this.btnloading = true;\n      let res = await this.$API.tk.process.post({ file: this.form.zip });\n      this.form.buss_id = res.data.buss_id;\n      this.$message.success(\"解析完成\");\n      this.ifprocess = true;\n      this.btnloading = false;\n    },\n    fileSuccess(response) {\n      const suffix = response.data.file_name.substr(\n        response.data.file_name.lastIndexOf(\".\") + 1\n      ); // 文件后缀\n      this.fileViewList.push({\n        suffix: suffix,\n        name: response.data.file_name,\n        url: response.data.src,\n        new_name: response.data.new_name,\n        id: response.data.new_name,\n      });\n      this.$message.success(`文件上传成功`);\n      this.ifprocess = false;\n      return false;\n    },\n    beforeRemove(file) {\n      this.fileViewList.map((r, index) => {\n        if (r.name == file.name) {\n          this.form.files = this.form.files.replace(\n            \"/static/upload/\" + file.name + \",\",\n            \"\"\n          );\n          this.fileViewList.splice(index, 1);\n        }\n      });\n    },\n    async getOffice() {\n      var res = await this.$API.lesson.list.get();\n      this.menuList = res;\n    },\n\n    addtk() {\n      this.array.push(1); //通过添加array的值，增加input的个数\n    },\n    del(index) {\n      this.form.value.splice(index, 1); //先删除form中value对应索引的值\n      this.array.splice(index, 1); //然后删除array对应索引的值，实现点击删除按钮，减少input框效果\n    },\n\n    //显示\n    open(mode = \"add\") {\n      this.mode = mode;\n      this.visible = true;\n      return this;\n    },\n    //获取字典列表\n    async getDic() {\n      var res = await this.$API.dict.list.get();\n      this.dict = res.data;\n    },\n    //表单提交方法\n    submit() {\n      if (this.ifprocess) {\n        this.$refs.dialogForm.validate(async (valid) => {\n          if (valid) {\n            this.isSaveing = true;\n\n            let res = await this.$API.tk.saveimp.post(this.form);\n            this.isSaveing = false;\n            if (res.state == 1) {\n              this.$emit(\"success\", this.form, this.mode);\n              this.visible = false;\n              this.$message.success(\"操作成功\");\n            } else {\n              this.$alert(res.msg, \"提示\", { type: \"error\" });\n            }\n          }\n        });\n      } else {\n        this.$alert(\"请先解析压缩包内容！\", \"提示\", { type: \"error\" });\n      }\n    },\n    //表单注入数据\n    setData(data, mode) {\n      //可以和上面一样单个注入，也可以像下面一样直接合并进去\n      this.titleMap.edit = \"题号：\" + data.no+\"类型：\"+data.type;\n      this.form.ans = \"\";\n      this.form.ans2 = [];\n      this.form.value = [];\n      this.array = [1];\n      Object.assign(this.form, data);\n\n   \n\n      if (data.type == \"单选\") {\n        console.log(data);\n        this.form.ans = data.ans;\n      }\n      if (data.type == \"多选\") {\n        this.form.ans2 = data.ans.split(\",\");\n      }\n    },\n\n    // 打开裁剪器\n    openCropper() {\n      if (this.form.tm_p) {\n        this.cropperImageSrc = this.form.tm_p;\n        this.cropperVisible = true;\n      } else {\n        this.$message.warning('没有题干图片可以裁剪');\n      }\n    },\n\n    // 保存裁剪后的图片\n    async saveCroppedImage() {\n      if (!this.$refs.cropper) {\n        this.$message.error('裁剪器未初始化');\n        return;\n      }\n\n      this.uploadLoading = true;\n      try {\n        // 获取裁剪后的文件\n        this.$refs.cropper.getCropFile(async (file) => {\n          try {\n            // 创建FormData上传\n            const formData = new FormData();\n            formData.append('file', file);\n            \n            // 上传到服务器\n            const response = await this.$API.common.upload.post(formData);\n\n            console.log(response);\n            \n            if (response && response.data && response.data.src) {\n              // 更新题干图片路径\n              this.form.tm_p_new = response.data.src;\n              \n\n  await this.$API.common.util.post('/sys/tk/updatetmnew',{id:this.form.id,tm_p_new:this.form.tm_p_new});\n\n              this.cropperVisible = false;\n            } else {\n              this.$message.error('上传失败，请重试');\n            }\n          } catch (error) {\n            console.error('上传失败:', error);\n            this.$message.error('上传失败: ' + (error.message || '未知错误'));\n          } finally {\n            this.uploadLoading = false;\n          }\n        }, 'cropped_image.jpg', 'image/jpeg');\n      } catch (error) {\n        console.error('裁剪失败:', error);\n        this.$message.error('裁剪失败: ' + (error.message || '未知错误'));\n        this.uploadLoading = false;\n      }\n    },\n  },\n};\n</script>\n\n<style></style>\n"]}, "metadata": {}, "sourceType": "module"}