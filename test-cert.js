const fs = require('fs');
const https = require('https');

// 证书路径和密码
const certPath = 'C:/jsjy/jsjy/thinkjs/www/static/upload/4200360b-2ce0-4ac4-b4ea-0a983d221505.p12';
const passphrase = '1706925459'; // 商户号

try {
  console.log('尝试读取证书文件...');
  const certBuffer = fs.readFileSync(certPath);
  console.log('证书文件大小:', certBuffer.length, '字节');

  console.log('\n尝试创建 HTTPS 选项...');
  const options = {
    pfx: certBuffer,
    passphrase: passphrase
  };

  try {
    // 尝试创建 HTTPS Agent
    console.log('尝试创建 HTTPS Agent...');
    const agent = new https.Agent(options);
    console.log('成功创建 HTTPS Agent!');
  } catch (agentError) {
    console.error('创建 HTTPS Agent 失败:', agentError);
  }

} catch (error) {
  console.error('证书处理失败:', error);
} 