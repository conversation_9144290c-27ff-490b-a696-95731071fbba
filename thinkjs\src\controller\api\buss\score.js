const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {

  async countAction() {
    let respData = {};
    const model = this.model("tk_record");
    const userInfo = await this.session('userInfo');
    respData=await model.where("(ifcheck=0 and iflock=0 and schoolid="+userInfo.schoolid+" and pic is not null) or (ifcheck=0 and iflock=1 and teacherid='"+userInfo.id+"' and schoolid="+userInfo.schoolid+" and pic is not null)").count();
    console.log(respData);
    return this.json(respData);
  }
  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const model = this.model("tk_record");
    const userInfo = await this.session('userInfo');
 
    //let dataScopeWhere = await this.dataScope('p');

    const response = await model
      .alias('p')
     
      .join('buss_student s ON p.`userid`=s.`id`')
      .join("left join sys_lesson l on l.id=p.lessonid")
      .field("p.*,s.name,l.name as lessonname")
      
      .page(page, rows).where(where).where("(ifcheck=0 and iflock=0 and schoolid="+userInfo.schoolid+" and pic is not null) or (ifcheck=0 and iflock=1 and teacherid='"+userInfo.id+"'   and schoolid="+userInfo.schoolid+" and pic is not null)")
      .order('p.create_date desc').countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }

  async infoAction(){

    let respData = {};
    let id = this.post('id') ? this.post('id') : null;
    const model = this.model('tk_record');

    const userInfo = await this.session('userInfo');


    if(id==null){
      let   respData=await model.where("(ifcheck=0 and iflock=0 and schoolid="+userInfo.schoolid+" and pic is not null) or (ifcheck=0 and iflock=1 and teacherid='"+userInfo.id+"' and schoolid="+userInfo.schoolid+" and pic is not null)").order("create_date asc").limit(1).select();
      if(respData.length>0){
        id=respData[0].id;
      }else{
        
       let   respData={
            code:400,
            message:"没有更多题目"
          }
        
          return this.json(respData);
      }
      
    }

    
    respData=   await model.where({id: id,schoolid:userInfo.schoolid}).find();

    let tkmodel=this.model("tk");
     
    let tk=await tkmodel.where({id:respData.tkid}).find();

    let model3=this.model("lesson");
    let lesson=await model3.where({id:tk.lessonid}).find();

    let lessonname="";
    for(let lessonid of lesson.parent_ids.split(",")){
      if(lessonid!="0"){
        let lesson=await model3.where({id:lessonid}).find();
      lessonname+=lesson.name+"/";
      }
     
    }
    respData.lessonname=lessonname;


    if(!think.isEmpty(respData.pic)){
       respData.images=respData.pic.split(",");

      
    }
    


    respData.tk=tk;
    respData.lesson=lesson;

    

    await this.model("tk_record").where({id: id,schoolid:userInfo.schoolid}).update({iflock:1,teacherid:userInfo.id});


    
    return this.json(respData);
  }
  async getnexttkAction(){
    let respData = {};
    const userInfo = await this.session('userInfo');
    let model=this.model("tk_record");
    respData=await model.where("(ifcheck=0 and iflock=0 and schoolid="+userInfo.schoolid+" and pic is not null) or (ifcheck=0 and iflock=1 and teacherid='"+userInfo.id+"' and schoolid="+userInfo.schoolid+" and pic is not null)").order("create_date asc").limit(1).select();

    if(respData.length>0){
      respData=respData[0];

      let tkmodel=this.model("tk");
     
      let tk=await tkmodel.where({id:respData.tkid}).find();
  
      let model3=this.model("lesson");
      let lesson=await model3.where({id:tk.lessonid}).find();
  
      let lessonname="";
      for(let lessonid of lesson.parent_ids.split(",")){
        if(lessonid!="0"){
          let lesson=await model3.where({id:lessonid}).find();
        lessonname+=lesson.name+"/";
        }
       
      }
      respData.lessonname=lessonname;
  
  
      if(!think.isEmpty(respData.pic)){
         respData.images=respData.pic.split(",");
  
        
      }
      
  
  
      respData.tk=tk;
      respData.lesson=lesson;



    } else {
      respData={
        code:400,
        message:"没有更多题目"
      }
    }

    return this.json(respData);
  }


  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model("tk_record");
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }


  async updateflagAction(){
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    const flag = this.post('flag') ? this.post('flag') : null;
    let teacherid=await this.session('userInfo').id;
    let tkres=await this.model("tk_record").where({id: id}).find();
    let flagres=this.post('flag');

    if(tkres.ifcheck==1){
      respData={
        code:400,
        data:{},
        message:"已批阅"
      }
      return this.json(respData);
    }
    if(tkres.flag!=this.post('flag')){
      let model=this.model("lesson_user");

      
      if(tkres.flag==1&&flagres=="0"){
        await model.where({lessonid:tkres.lessonid,userid:tkres.userid}).decrement("score", 3);

      }
      if(tkres.flag==0&&flagres=="1"){
        await model.where({lessonid:tkres.lessonid,userid:tkres.userid}).increment("score", 3);

      }
    }
    await this.model("tk_record").where({id: id,ifcheck:0}).update({teacherflag:flagres,teacherid:teacherid,ifcheck:1,ifsee:0,pg_date:think.datetime()});
    respData = {
      code: 200,
      data: {},
      message: '成功'
    };
    return this.json(respData);
  }


  
  
  async recordsAction() {
    let order = 'c.pg_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');
    const where2 = this.get('where') ? JSON.parse(this.get('where')) : {};
    const keyword = this.get('name');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)&&!think.isEmpty(this.get('order'))) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('tk_record');
    let where = { };
    if (!think.isEmpty(keyword)) {
      where['c.title'] = ['like', '%' + keyword + '%'];
    }

    const userInfo = await this.session('userInfo');
   
    console.log(userInfo);

    if(userInfo.name!="系统管理员"){

        let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

        if(think.isEmpty(schoolres)){
            where["c.teacherid"]=userInfo.id
           
        }else{

            where["c.schoolid"]=["in",schoolres.map(item => item.id)];
        }

     
      }
   
    if (!think.isEmpty(this.get("title"))) {
      where['c.title'] = ['like', '%' + this.get("name") + '%'];
    }

    where["c.ifcheck"]=1;
    if(think.isEmpty(this.get("pg_date"))){
      where["c.pg_date"]=["between",getMonthDateRange().startDate+","+getMonthDateRange().endDate]
  }


    const res = await model
      .alias('c')
      .field('c.*,t.name as teacher,st.name as studentname,st.phone')
       .join(["buss_student st on st.id=c.userid"])
      .join(["buss_school s on s.id=c.schoolid"])
      .join(["sys_user t on t.id=c.teacherid"])
      .page(page, rows)
      .where(where)
    .where(where2)
      .order(order)
      .countSelect();
    const data = {};

 
 //过滤手机号中间部分用* 代替 前面保留3位 后面保留4位
  for(let item of res.data){
    item.phone = item.phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
  }

    
    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';

  
 
    return this.json(data);
  }




  async tongjiAction(){



    let order = 'c.pg_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');
    const where2 = this.get('where') ? JSON.parse(this.get('where')) : {};
    const keyword = this.get('name');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)&&!think.isEmpty(this.get('order'))) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('tk_record');
    let where = { };
    if (!think.isEmpty(keyword)) {
      where['c.title'] = ['like', '%' + keyword + '%'];
    }

    const userInfo = await this.session('userInfo');
   
    console.log(userInfo);

    if(userInfo.name!="系统管理员"){

        let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

        if(think.isEmpty(schoolres)){
            where["c.teacherid"]=userInfo.id
           
        }else{

            where["c.schoolid"]=["in",schoolres.map(item => item.id)];
        }

     
      }
   
    if (!think.isEmpty(this.get("title"))) {
      where['c.title'] = ['like', '%' + this.get("name") + '%'];
    }

      if(think.isEmpty(this.get("pg_date"))){
        where["c.pg_date"]=["between",getMonthDateRange().startDate+","+getMonthDateRange().endDate]
    }

    where["c.ifcheck"]=1;



    const res = await model
    .alias('c')
    .field('COUNT(*) as num, DATE(c.pg_date) as pg_date, t.name as teacher')
    .join(["buss_student st on st.id=c.userid"])
    .join(["buss_school s on s.id=c.schoolid"])
    .join(["sys_user t on t.id=c.teacherid"])
    .where(where)
    .where(where2)
    .group('DATE(c.pg_date), t.name')
    .order('DATE(c.pg_date) desc')
    .select();

  const data = {
    code: 200,
    count: res.length,
    data: res,
    msg: ''
  };

  return this.json(data);


  }




};

const getMonthDateRange = () => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    return {
        startDate: formatDate(startOfMonth),
        endDate: formatDate(endOfMonth)
    };
};

 