// src/service/sms.js
const tencentcloud = require('tencentcloud-sdk-nodejs');

module.exports = class extends think.Service {
  constructor() {
    super();
    // 初始化腾讯云短信客户端
    const { secretId, secretKey } = think.config('tencentcloud');
    
    // 导入 SMS 模块的 client
    const SmsClient = tencentcloud.sms.v20210111.Client;
    
    // 实例化 SMS 的 client 对象
    this.client = new SmsClient({
      credential: {
        secretId,
        secretKey,
      },
      region: 'ap-guangzhou', // 地域信息，可以根据实际情况修改
      profile: {
        signMethod: 'HmacSHA256', // 签名方法
        httpProfile: {
          reqMethod: 'POST', // 请求方法
          reqTimeout: 30, // 请求超时时间，单位为秒
        },
      },
    });
  }

  /**
   * 发送短信验证码
   * @param {string} phone 手机号
   * @param {string} code 验证码
   * @param {string} type 短信类型 (register, login, resetPassword)
   * @return {Promise} 发送结果
   */
  async sendVerificationCode(phone, code, type = 'register') {
    const { sdkAppId, signName, templateId } = think.config('tencentcloud');
    
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return { success: false, message: '手机号格式不正确' };
    }
    
    // 验证码格式验证
    if (!/^\d{6}$/.test(code)) {
      return { success: false, message: '验证码格式不正确' };
    }

    try {
      // 构造请求参数
      const params = {
        SmsSdkAppId: sdkAppId,
        SignName: signName,
        PhoneNumberSet: [`+86${phone}`], // 需要加上国家码
        TemplateId: templateId[type],
        TemplateParamSet: [code], // 验证码参数
      };

      // 发送短信
      const result = await this.client.SendSms(params);
      
      // 记录发送日志
      await this.recordSmsLog({
        phone,
        code,
        type,
        result: JSON.stringify(result)
      });
      
      // 判断发送结果
      if (result.SendStatusSet[0].Code === 'Ok') {
        return { success: true, message: '短信发送成功' };
      } else {
        return { 
          success: false, 
          message: `短信发送失败: ${result.SendStatusSet[0].Message}` 
        };
      }
    } catch (error) {
      think.logger.error('短信发送异常:', error);
      return { success: false, message: '短信发送异常' };
    }
  }
  
  /**
   * 记录短信发送日志
   * @param {Object} data 日志数据
   */
  async recordSmsLog(data) {
    try {
      // 将短信发送记录保存到数据库
      await think.model('sms').add({
        phone: data.phone,
        code: data.code,
        type: data.type,
        result: data.result,
        ip: this.ctx ? this.ctx.ip : '',
        create_time: think.datetime()
      });
    } catch (error) {
      think.logger.error('记录短信日志失败:', error);
    }
  }
  
  /**
   * 验证短信验证码
   * @param {string} phone 手机号
   * @param {string} code 验证码
   * @param {string} type 短信类型
   * @return {Promise<boolean>} 验证结果
   */
  async verifyCode(phone, code, type = 'register') {
    // 从数据库中查询最近一条验证码记录
    const record = await think.model('sms').where({
      phone,
      code,
      type
    }).order('id DESC').find();
    
    if (think.isEmpty(record)) {
      return false;
    }
    
    // 验证码有效期检查（通常为5分钟）
    const createTime = new Date(record.create_time);
    const now = new Date();
    const diffMinutes = (now - createTime) / (1000 * 60);
    
    if (diffMinutes > 50) {
      return false; // 验证码已过期
    }
    
    return true;
  }
};