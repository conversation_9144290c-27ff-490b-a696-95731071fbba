module.exports = class extends think.Controller {
    async saveAction() {
        // 获取POST请求参数
        const data = this.post();
        console.log(data);

        // 参数验证
        if (think.isEmpty(data.content)) {
            return this.json({
                success: false,
                message: '反馈内容不能为空'
            });
        }

        
      
        const user = await this.session('userInfo');
        console.log(user);
        try {
            // 构建保存数据
            const params = {
                content:data.content,
                tino:data.tino,
                type: data.type || '',
                pic: data.images.join(','),
                create_date: think.datetime(),
                userid: user.id || 0
            };
            
            // 保存到 jianyi 表
             await this.model('jianyi').add(params);
            return this.json({
                success: true
            });
        } catch (err) {
            console.log(err);
            return this.json({
                success: false,
                message: '保存失败'
            });
        }
    }
}