<template>
  <el-container>
    <el-container>
      <el-header>
        <div class="left-panel">
          <el-button type="primary" icon="el-icon-plus" @click="add">新增广告</el-button>
        </div>
        <div class="right-panel">
          <div class="right-panel-search">
            <el-input v-model="search.name" placeholder="广告名称" clearable></el-input>
            <el-select v-model="search.status" placeholder="状态" clearable>
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
          </div>
        </div>
      </el-header>
      <el-main class="nopadding">
        <scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
          remoteFilter>
          <el-table-column type="selection" width="50"></el-table-column>
          
          <el-table-column label="广告名称" prop="name" width="150"></el-table-column>
          <el-table-column label="广告图片" prop="image" width="120">
            <template #default="scope">
              <el-image 
                style="width: 80px; height: 50px" 
                :src="scope.row.image" 
                fit="cover"
                :preview-src-list="[scope.row.image]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column label="广告内容" prop="content" width="200">
            <template #default="scope">
              <div class="ad-content-preview">{{ scope.row.content }}</div>
            </template>
          </el-table-column>
          <el-table-column label="允许点击" prop="clickable" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.clickable" type="success">是</el-tag>
              <el-tag v-else type="info">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="外链地址" prop="link_url" width="200">
            <template #default="scope">
              <div class="link-url-preview">{{ scope.row.link_url || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.status == 1" type="success">启用</el-tag>
              <el-tag v-if="scope.row.status == 0" type="info">禁用</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="添加时间" prop="create_time" width="150" sortable='create_date'></el-table-column>
          <el-table-column label="添加人" prop="uname" width="120" sortable='custom'></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="180">
            <template #default="scope">
              <el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看</el-button>
              <el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑</el-button>
              <el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
  </el-container>

  <save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
</template>

<script>
import saveDialog from './save'

export default {
  name: 'advertisement',
  components: {
    saveDialog
  },
  data() {
    return {
      dialog: {
        save: false
      },
      apiObj: this.$API.advertisement.page,
      selection: [],
      search: {
        name: null,
        status: null
      }
    }
  },
  mounted() {
    // 如果需要加载初始数据，可以在这里调用
  },
  methods: {
    // 添加
    add() {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('add');
      })
    },
    // 编辑
    async table_edit(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('edit').setData(row)
      })
    },
    // 查看
    table_show(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('show').setData(row)
      })
    },
    // 删除
    async table_del(row, index) {
      var reqData = { id: row.id }
      var res = await this.$API.advertisement.delete.post(reqData);
      if (res.code == 200) {
        // 这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功")
      } else {
        this.$alert(res.message, "提示", { type: 'error' })
      }
    },
    // 表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    // 搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    // 本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    // 批量删除
    async batchDel() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      
      const ids = this.selection.map(item => item.id);
      
      this.$confirm('确定要删除选中的广告吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        const res = await this.$API.advertisement.batchDel.post({ ids });
        if (res.code === 200) {
          this.$message.success("批量删除成功");
          this.$refs.table.refresh();
        } else {
          this.$alert(res.message, "提示", { type: 'error' });
        }
      }).catch(() => {});
    }
  }
}
</script>

<style>
.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: auto !important;
  padding-top: 10px;
  padding-bottom: 10px;
}

.left-panel {
  display: flex;
  align-items: center;
}

.right-panel {
  display: flex;
}

.right-panel-search {
  display: flex;
  align-items: center;
}

.right-panel-search .el-input, 
.right-panel-search .el-select {
  margin-right: 10px;
  width: 180px;
}

.ad-content-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.link-url-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
</style>