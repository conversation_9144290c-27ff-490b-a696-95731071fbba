{"version": 3, "sources": ["..\\..\\..\\..\\src\\controller\\api\\app\\ti.js"], "names": ["BaseRest", "require", "module", "exports", "jjAction", "tkid", "post", "model", "user", "session", "res", "where", "find", "process<PERSON><PERSON><PERSON>j<PERSON>", "lessonid", "school", "model3", "res3", "think", "datetime", "Date", "isEmpty", "data", "schoolid", "create_date", "lesson", "num", "add", "id", "increment", "data2", "uid", "json", "lessionid", "num3", "updateparentnum3", "parent_id", "lessons", "field", "select", "arr", "item", "push", "sql", "join", "childres", "query", "model2", "update", "gettiAction", "tilist", "para", "userid", "state", "score", "gettest<PERSON>", "zsti", "tisetup", "tisetup3", "testscore", "tisetup2", "processinsql", "a", "getnodo", "type", "neednum", "config", "score1", "parseInt", "score2", "console", "log", "length", "tmpnum", "sql2", "tilist2", "concat", "sql3", "tilist3", "score3", "tilist4", "lesson_user", "modeljx", "list", "op", "title", "checked", "jxlist", "jxcontent", "jxitem", "file", "tmcontent", "tm_p", "cache", "lmodel", "order", "limit", "res2", "resetlessonscoreAction", "lessonmodel", "updatelessonscoreAction", "dotesttiAction", "ticonfig", "flag", "usetime", "tkmodel", "tires", "lesson_userres", "processdotinum", "num1", "num2", "updateparent", "dotiAction", "dayrecord", "dayrecordres", "pic", "jsscore", "nexttmp", "netxlesson", "id2", "ifgl", "lmp", "lasttime", "curTime", "getSeconds", "time1", "yxq", "setSeconds", "mode2", "para2", "updatetiscore", "para3", "adds", "tiid", "count", "socre", "curAction", "curtino"], "mappings": ";;AACA,MAAMA,WAAWC,QAAQ,WAAR,CAAjB;AACAC,OAAOC,OAAP,GAAiB,cAAcH,QAAd,CAAuB;;AAI9BI,YAAN,GAAgB;AAAA;;AAAA;AACZ,gBAAIC,OAAK,MAAKC,IAAL,CAAU,MAAV,CAAT;AACA,gBAAIC,QAAM,MAAKA,KAAL,CAAW,IAAX,CAAV;AACA,kBAAMC,OAAO,MAAM,MAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIC,MAAK,MAAMH,MAAMI,KAAN,CAAY,EAAC,MAAKN,IAAN,EAAZ,EAAyBO,IAAzB,EAAf;;AAEA,kBAAM,MAAKC,gBAAL,CAAsBH,IAAII,QAA1B,EAAmCN,KAAKO,MAAxC,EAA+CV,IAA/C,CAAN;;AAGA,gBAAIW,SAAO,MAAKT,KAAL,CAAW,IAAX,CAAX;;AAMA,gBAAIU,OAAK,MAAMD,OAAOL,KAAP,CAAa,EAAC,QAAON,IAAR,EAAa,YAAWG,KAAKO,MAA7B,EAAoC,eAAcG,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAlD,EAAb,EAA0GR,IAA1G,EAAf;;AAEA,gBAAGM,MAAMG,OAAN,CAAcJ,IAAd,CAAH,EAAuB;;AAEnB,oBAAIK,OAAK,EAAT;AACAA,qBAAKjB,IAAL,GAAUA,IAAV;AACAiB,qBAAKC,QAAL,GAAcf,KAAKO,MAAnB;AACAO,qBAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;AACAE,qBAAKG,MAAL,GAAYf,IAAII,QAAhB;AACIQ,qBAAKI,GAAL,GAAS,CAAT;;AAGJ,sBAAMV,OAAOW,GAAP,CAAWL,IAAX,CAAN;AAEH,aAZD,MAYK;AACD,sBAAMN,OAAOL,KAAP,CAAa,EAACiB,IAAGX,KAAKW,EAAT,EAAb,EAA2BC,SAA3B,CAAqC,KAArC,EAA4C,CAA5C,CAAN;AAEH;;AAGD,gBAAIC,QAAM,EAAV;AACAA,kBAAMzB,IAAN,GAAWA,IAAX;AACAyB,kBAAMP,QAAN,GAAef,KAAKO,MAApB;AACAe,kBAAMN,WAAN,GAAkBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAlB;AACAU,kBAAML,MAAN,GAAaf,IAAII,QAAjB;AACAgB,kBAAMC,GAAN,GAAUvB,KAAKoB,EAAf;AACA,kBAAM,MAAKrB,KAAL,CAAW,WAAX,EAAwBoB,GAAxB,CAA4BG,KAA5B,CAAN;;AAEA,mBAAO,MAAKE,IAAL,CAAU,EAAC,SAAQ,CAAT,EAAV,CAAP;AA3CY;AA8Cf;;AAQD;AACMnB,oBAAN,CAAuBoB,SAAvB,EAAiCV,QAAjC,EAA0ClB,IAA1C,EAA+C;AAAA;;AAAA;AAC3C,gBAAIE,QAAM,OAAKA,KAAL,CAAW,eAAX,CAAV;;AAMA,gBAAIG,MAAI,MAAMH,MAAMI,KAAN,CAAY,EAACG,UAASmB,SAAV,EAAoBV,UAASA,QAA7B,EAAsC,eAAcL,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAApD,EAAZ,EAA2GR,IAA3G,EAAd;AACA,gBAAGM,MAAMG,OAAN,CAAcX,GAAd,CAAH,EAAsB;;AAElB,oBAAIY,OAAK,EAAT;AACAA,qBAAKR,QAAL,GAAcmB,SAAd;AACAX,qBAAKC,QAAL,GAAcA,QAAd;AACAD,qBAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;;AAEIE,qBAAKY,IAAL,GAAU,CAAV;;AAGJ,sBAAM3B,MAAMoB,GAAN,CAAUL,IAAV,CAAN;AAEH,aAZD,MAYK;;AAEG,sBAAMf,MAAMI,KAAN,CAAY,EAACiB,IAAGlB,IAAIkB,EAAR,EAAZ,EAAyBC,SAAzB,CAAmC,MAAnC,EAA2C,CAA3C,CAAN;AAEP;;AAGD,kBAAM,OAAKM,gBAAL,CAAsBZ,QAAtB,EAA+BU,SAA/B,CAAN;AA3B2C;AA8B5C;AACD;AACOE,oBAAN,CAAuBZ,QAAvB,EAAgCU,SAAhC,EAA0C;AAAA;;AAAA;AACvC,gBAAI1B,QAAM,OAAKA,KAAL,CAAW,QAAX,CAAV;AACA,gBAAIkB,SAAO,MAAMlB,MAAMI,KAAN,CAAY,EAACiB,IAAGK,SAAJ,EAAZ,EAA4BrB,IAA5B,EAAjB;;AAEA,gBAAGa,OAAOW,SAAP,IAAkB,CAArB,EAAuB;AACrB,oBAAIC,UAAQ,MAAM9B,MAAMI,KAAN,CAAY,EAACyB,WAAUX,OAAOW,SAAlB,EAAZ,EAA0CE,KAA1C,CAAgD,IAAhD,EAAsDC,MAAtD,EAAlB;;AAGA,oBAAG,CAACrB,MAAMG,OAAN,CAAcgB,OAAd,CAAJ,EAA2B;AACvB,wBAAIG,MAAI,EAAR;AACA,yBAAI,IAAIC,IAAR,IAAgBJ,OAAhB,EAAwB;AACpBG,4BAAIE,IAAJ,CAASD,KAAKb,EAAd;AACH;AACD,wBAAIe,MAAI,oFAAkFzB,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAlF,GAA2H,kBAA3H,GAA8IG,QAA9I,GAAuJ,oBAAvJ,GAA4KiB,IAAII,IAAJ,CAAS,GAAT,CAA5K,GAA0L,GAAlM;AACA,wBAAIC,WAAS,MAAMtC,MAAMuC,KAAN,CAAYH,GAAZ,CAAnB;;AAEA,wBAAII,SAAO,OAAKxC,KAAL,CAAW,eAAX,CAAX;;AAEA,wBAAIG,MAAI,MAAMqC,OAAOpC,KAAP,CAAa,EAACY,UAASA,QAAV,EAAmBT,UAASW,OAAOW,SAAnC,EAA6CZ,aAAYN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAzD,EAAb,EAAiHR,IAAjH,EAAd;AACA,wBAAGM,MAAMG,OAAN,CAAcX,GAAd,CAAH,EAAsB;;AAElB,4BAAIY,OAAK,EAAT;AACAA,6BAAKC,QAAL,GAAcA,QAAd;AACAD,6BAAKR,QAAL,GAAcW,OAAOW,SAArB;AACAd,6BAAKY,IAAL,GAAUW,SAAS,CAAT,EAAY,MAAZ,CAAV;;AAEAvB,6BAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;;AAEA,8BAAM2B,OAAOpB,GAAP,CAAWL,IAAX,CAAN;AACH,qBAVD,MAUK;;AAED,8BAAMyB,OAAOpC,KAAP,CAAa,EAACiB,IAAGlB,IAAIkB,EAAR,EAAb,EAA0BoB,MAA1B,CAAiC,EAACd,MAAKW,SAAS,CAAT,EAAY,MAAZ,CAAN,EAAjC,CAAN;AAIH;AAEJ;;AAGH,sBAAO,OAAKV,gBAAL,CAAsBZ,QAAtB,EAA+BE,OAAOW,SAAtC,CAAP;AAKC;AA7CsC;AA+CzC;;AAcJ;AACMa,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI1C,QAAQ,OAAKA,KAAL,CAAW,aAAX,CAAZ;AACA,gBAAIO,WAAW,OAAKR,IAAL,CAAU,UAAV,CAAf;AACA,kBAAME,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA;AACA,gBAAIyC,SAAS,EAAb;;AAIA,gBAAIxC,MAAM,MAAMH,MAAMI,KAAN,CAAY,EAAE,YAAYG,QAAd,EAAwB,UAAUN,KAAKoB,EAAvC,EAAZ,EAAyDhB,IAAzD,EAAhB;;AAEA,gBAAIM,MAAMG,OAAN,CAAcX,GAAd,CAAJ,EAAwB;AACpB,oBAAIyC,OAAO,EAAX;AACAA,qBAAKC,MAAL,GAAc5C,KAAKoB,EAAnB;AACAuB,qBAAKrC,QAAL,GAAgBA,QAAhB;AACAqC,qBAAKE,KAAL,GAAa,CAAb;AACAF,qBAAKG,KAAL,GAAa,EAAb;AACA,sBAAM/C,MAAMoB,GAAN,CAAUwB,IAAV,CAAN;AACAD,uBAAOG,KAAP,GAAe,CAAf;AACAH,uBAAOI,KAAP,GAAa,EAAb;AACAJ,uBAAO5B,IAAP,GAAc,MAAM,OAAKiC,SAAL,CAAezC,QAAf,CAApB;AAEH,aAXD,MAWO;AACH,oBAAIJ,IAAI2C,KAAJ,IAAa,CAAjB,EAAoB;AAChBH,2BAAOG,KAAP,GAAe,CAAf;AACAH,2BAAO5B,IAAP,GAAc,MAAM,OAAKkC,IAAL,CAAU1C,QAAV,EAAoBJ,IAAI4C,KAAxB,EAA8B5C,GAA9B,CAApB;AACAwC,2BAAOI,KAAP,GAAa5C,IAAI4C,KAAjB;AACAJ,2BAAOO,OAAP,GAAe/C,IAAIgD,QAAnB;AAEH,iBAND,MAMO;;AAIHR,2BAAO5B,IAAP,GAAc,MAAM,OAAKiC,SAAL,CAAezC,QAAf,CAApB;AACAoC,2BAAOG,KAAP,GAAe,CAAf;AACAH,2BAAOI,KAAP,GAAa5C,IAAIiD,SAAjB;AACAT,2BAAOO,OAAP,GAAe/C,IAAIkD,QAAnB;AAEH;AAEJ;;AASD,mBAAO,OAAK5B,IAAL,CAAUkB,MAAV,CAAP;AAjDgB;AAmDnB;;AAGAW,iBAAaX,MAAb,EAAoB;AACrB,YAAIP,MAAI,mBAAR;;AAEA,YAAIH,MAAI,EAAR;AACA,aAAI,IAAIsB,CAAR,IAAaZ,MAAb,EAAoB;AAChBV,gBAAIE,IAAJ,CAASoB,EAAElC,EAAX;AAEH;AACDe,cAAIA,MAAI,GAAJ,GAAU,IAAGH,IAAII,IAAJ,CAAS,MAAT,CAAiB,GAAlC;AACAD,cAAIA,MAAI,IAAR;AACA,eAAOA,GAAP;AAGA;AACMoB,WAAN,CAAcjD,QAAd,EAAwBsC,MAAxB,EAAgCE,KAAhC,EAAuCU,IAAvC,EAA6CC,OAA7C,EAAsD;AAAA;;AAAA;;AAGlD,gBAAIC,SAAO,OAAKA,MAAL,CAAY,IAAZ,CAAX;AACA,gBAAIC,SAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA/B;AACA,gBAAIE,SAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA/B;;AAIA,gBAAIxB,MAAM,sEAAsE0B,MAAtE,GAA+E,eAA/E,GAAiGF,MAAjG,GAA0G,OAA1G,GAAoHH,IAApH,GAA2H,gBAA3H,GAA8IlD,QAA9I,GAAyJ,4DAAzJ,GAAwNsC,MAAxN,GAAiO,gBAAjO,GAAoPtC,QAApP,GAA+P,+BAA/P,GAAiSmD,OAA3S;;AAGA,gBAAI1D,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAZ;AACA,gBAAI2C,SAAS,MAAM3C,MAAMuC,KAAN,CAAYH,GAAZ,CAAnB;AACA2B,oBAAQC,GAAR,CAAY,iBAAZ,EAA+B5B,GAA/B;AACA;AACA,gBAAIO,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA,oBAAIE,OAAO,wEAAwEL,MAAxE,GAAiF,eAAjF,GAAmGF,MAAnG,GAA4G,OAA5G,GAAsHH,IAAtH,GAA6H,gBAA7H,GAAgJlD,QAAhJ,GAA0J,OAAK+C,YAAL,CAAkBX,MAAlB,CAA1J,GAAoL,yDAApL,GAAgPE,MAAhP,GAAyP,gBAAzP,GAA4QtC,QAA5Q,GAAuR,sDAAvR,GAAgV2D,MAA3V;;AAEA,oBAAIE,UAAU,MAAMpE,MAAMuC,KAAN,CAAY4B,IAAZ,CAApB;;AAEAJ,wBAAQC,GAAR,CAAY,UAAZ,EAAwBG,IAAxB;AACAxB,yBAASA,OAAO0B,MAAP,CAAcD,OAAd,CAAT;AACH;;AAED;AACA,gBAAIzB,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA,oBAAIK,OAAO,wEAAwER,MAAxE,GAAiF,eAAjF,GAAmGF,MAAnG,GAA4G,OAA5G,GAAsHH,IAAtH,GAA6H,gBAA7H,GAAgJlD,QAAhJ,GAA0J,OAAK+C,YAAL,CAAkBX,MAAlB,CAA1J,GAAqL,yDAArL,GAAiPE,MAAjP,GAA0P,gBAA1P,GAA6QtC,QAA7Q,GAAwR,sDAAxR,GAAiV2D,MAA5V;;AAEA,oBAAIK,UAAU,MAAMvE,MAAMuC,KAAN,CAAY+B,IAAZ,CAApB;;AAEAP,wBAAQC,GAAR,CAAY,UAAZ,EAAwBM,IAAxB;AACA3B,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAGD;;AAEAX,qBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;AACAA,qBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;;AAGA;AACA,gBAAInB,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,4DAApL,GAAmPE,MAAnP,GAA4P,gBAA5P,GAA+QtC,QAA/Q,GAA0R,+BAA1R,GAA4T2D,MAAlU;AACF,oBAAKK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAArB;;AAEEO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAKD;;AAEA,gBAAI5B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;AACzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,yDAApL,GAAgPE,MAAhP,GAAyP,gBAAzP,GAA4QtC,QAA5Q,GAAuR,sDAAvR,GAAgV2D,MAAtV,CAA6V;AAC7V,oBAAMK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAAtB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAED,gBAAI5B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;AACzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,yDAApL,GAAgPE,MAAhP,GAAyP,gBAAzP,GAA4QtC,QAA5Q,GAAuR,qDAAvR,GAA+U2D,MAArV,CAA4V;AAC5V,oBAAMK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAAtB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAID;;AAEAX,qBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;AACAV,qBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;;AAGA;AACA,gBAAI7B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,4DAApL,GAAmPE,MAAnP,GAA4P,gBAA5P,GAA+QtC,QAA/Q,GAA0R,+BAA1R,GAA4T2D,MAAlU;AACA,oBAAMK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAAtB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAKD;;AAEA,gBAAI5B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;AACzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,yDAApL,GAAgPE,MAAhP,GAAyP,gBAAzP,GAA4QtC,QAA5Q,GAAuR,sDAAvR,GAAgV2D,MAAtV,CAA6V;AAC7V,oBAAMK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAAtB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAED,gBAAI5B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;AACzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACA7B,sBAAM,uEAAuE0B,MAAvE,GAAgF,eAAhF,GAAkGF,MAAlG,GAA2G,OAA3G,GAAqHH,IAArH,GAA4H,gBAA5H,GAA+IlD,QAA/I,GAAyJ,OAAK+C,YAAL,CAAkBX,MAAlB,CAAzJ,GAAoL,yDAApL,GAAgPE,MAAhP,GAAyP,gBAAzP,GAA4QtC,QAA5Q,GAAuR,qDAAvR,GAA+U2D,MAArV,CAA4V;AAC5V,oBAAOK,UAAU,MAAMvE,MAAMuC,KAAN,CAAYH,GAAZ,CAAvB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcE,OAAd,CAAT;AACH;;AAGT;;;AAGQX,qBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA3B;AACAE,qBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA3B;;AAEC,gBAAIjB,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACD7B,sBAAM,wEAAwE0B,MAAxE,GAAiF,eAAjF,GAAmGF,MAAnG,GAA4G,OAA5G,GAAsHH,IAAtH,GAA6H,gBAA7H,GAAgJlD,QAAhJ,GAA2J,qEAA3J,GAAmOsC,MAAnO,GAA4O,gBAA5O,GAA+PtC,QAA/P,GAA0Q,+BAA1Q,GAA4S2D,MAAlT;AACA,oBAAOO,UAAU,MAAMzE,MAAMuC,KAAN,CAAYH,GAAZ,CAAvB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcI,OAAd,CAAT;AAEF;;AAGFb,qBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;AACAA,qBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;;AAEC,gBAAInB,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACD7B,sBAAM,wEAAwE0B,MAAxE,GAAiF,eAAjF,GAAmGF,MAAnG,GAA4G,OAA5G,GAAsHH,IAAtH,GAA6H,gBAA7H,GAAgJlD,QAAhJ,GAA2J,qEAA3J,GAAmOsC,MAAnO,GAA4O,gBAA5O,GAA+PtC,QAA/P,GAA0Q,+BAA1Q,GAA4S2D,MAAlT;AACA,oBAAOO,UAAU,MAAMzE,MAAMuC,KAAN,CAAYH,GAAZ,CAAvB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcI,OAAd,CAAT;AAEF;;AAEDb,qBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;AACDV,qBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;;AAEC,gBAAI7B,OAAOsB,MAAP,GAAgBP,OAApB,EAA6B;;AAEzB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBf,OAAOsB,MAAxC;AACD7B,sBAAM,yEAAyE0B,MAAzE,GAAkF,eAAlF,GAAoGF,MAApG,GAA6G,OAA7G,GAAuHH,IAAvH,GAA8H,gBAA9H,GAAiJlD,QAAjJ,GAA4J,qEAA5J,GAAoOsC,MAApO,GAA6O,gBAA7O,GAAgQtC,QAAhQ,GAA2Q,+BAA3Q,GAA6S2D,MAAnT;AACA,oBAAOO,UAAU,MAAMzE,MAAMuC,KAAN,CAAYH,GAAZ,CAAvB;;AAEAO,yBAASA,OAAO0B,MAAP,CAAcI,OAAd,CAAT;AAEF;;AAKF,mBAAO9B,MAAP;AAnKkD;AAuKrD;;AAEKM,QAAN,CAAW1C,QAAX,EAAqBwC,KAArB,EAA2B2B,WAA3B,EAAwC;AAAA;;AAAA;AACpC,gBAAI1E,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAZ;;AAGA,gBAAI2E,UAAQ,OAAK3E,KAAL,CAAW,OAAX,CAAZ;;AAGA,gBAAIwC,SAAO,OAAKxC,KAAL,CAAW,aAAX,CAAX;;AAEA,kBAAMC,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIyD,SAAS,OAAKA,MAAL,CAAY,IAAZ,CAAb;AACA,gBAAIiB,OAAK,EAAT;AACA,gBAAGF,YAAYxB,OAAZ,IAAqB,CAAxB,EAA0B;;AAErB0B,uBAAO,MAAM,OAAKpB,OAAL,CAAajD,QAAb,EAAuBN,KAAKoB,EAA5B,EAAgC0B,KAAhC,EAAuC,2BAAvC,EAAoE,CAApE,CAAb;AACJ;;AAED,gBAAG2B,YAAYxB,OAAZ,GAAoB,CAApB,IAAuBwB,YAAYxB,OAAZ,GAAoB,CAA9C,EAAgD;;AAE3C0B,uBAAO,MAAM,OAAKpB,OAAL,CAAajD,QAAb,EAAuBN,KAAKoB,EAA5B,EAAgC0B,KAAhC,EAAuC,aAAvC,EAAsD,CAAtD,CAAb;AACJ;;AAEF;;AAEA,gBAAG2B,YAAYxB,OAAZ,IAAqB,CAAxB,EAA0B;;AAErB0B,uBAAQ,MAAM,OAAKpB,OAAL,CAAajD,QAAb,EAAuBN,KAAKoB,EAA5B,EAAgC0B,KAAhC,EAAuC,aAAvC,EAAqD,CAArD,CAAd;AACH;;AASD,iBAAK,IAAIb,IAAT,IAAiB0C,IAAjB,EAAuB;;AAGnB1C,qBAAK2C,EAAL,GAAU,CACN;AACIC,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBADM,EAKN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBALM,EASN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBATM,EAaN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBAbM,CAAV;AAwBH;;AAED,gBAAI5E,MAAIyE,IAAR;;AAEA,iBAAI,IAAI1C,IAAR,IAAgB/B,GAAhB,EAAoB;;AAEf,oBAAI6E,SAAO,MAAML,QAAQvE,KAAR,CAAc,EAAC,QAAO8B,KAAKb,EAAb,EAAgB,YAAW,CAA3B,EAAd,EAA6CW,MAA7C,EAAjB;AACD,oBAAG,CAACrB,MAAMG,OAAN,CAAckE,MAAd,CAAJ,EAA0B;AACtB,wBAAIC,YAAU,EAAd;AACA,yBAAI,IAAIC,MAAR,IAAkBF,MAAlB,EAAyB;AACrB,4BAAGE,OAAOzB,IAAP,IAAa,IAAhB,EAAqB;;AAEjBwB,wCAAUA,YAAU,oBAAV,GAA+BC,OAAO7D,EAAtC,GAAyC,SAAzC,GAAmD,OAAKsC,MAAL,CAAY,QAAZ,CAAnD,GAAyEuB,OAAOC,IAAhF,GAAqF,yBAA/F;AACH;AACD,4BAAGD,OAAOzB,IAAP,IAAa,IAAhB,EAAqB;;AAEjBwB,wCAAUA,YAAU,uBAAV,GAAkCC,OAAO7D,EAAzC,GAA4C,SAA5C,GAAsD,OAAKsC,MAAL,CAAY,QAAZ,CAAtD,GAA4EuB,OAAOC,IAAnF,GAAwF,yBAAlG;AACH;AACJ;;AAEDjD,yBAAK+C,SAAL,GAAeA,SAAf;AACAlB,4BAAQC,GAAR,CAAY9B,KAAK+C,SAAjB;AACH;;AAEA/C,qBAAKkD,SAAL,GAAe,qBAAmB,OAAKzB,MAAL,CAAY,QAAZ,CAAnB,GAAyCzB,KAAKmD,IAA9C,GAAmD,yBAAlE;AAEJ;;AAMDtB,oBAAQC,GAAR,CAAY7D,GAAZ;AACA,kBAAM,OAAKmF,KAAL,CAAW,MAAX,EAAmBnF,GAAnB,CAAN;AACA,mBAAOA,GAAP;AAjGoC;AAkGvC;;AAGK6C,aAAN,CAAgBzC,QAAhB,EAA0B;AAAA;;AAAA;AACtB,gBAAIP,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAZ;AACA,gBAAO2E,UAAQ,OAAK3E,KAAL,CAAW,OAAX,CAAf;;AAEA,gBAAIuF,SAAO,OAAKvF,KAAL,CAAW,aAAX,CAAX;AACA,kBAAMC,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAIgB,SAAO,MAAMqE,OAAOnF,KAAP,CAAa,EAAC,YAAWG,QAAZ,EAAqB,UAASN,KAAKoB,EAAnC,EAAb,EAAqDhB,IAArD,EAAjB;AACA,gBAAI0C,QAAM7B,OAAOkC,SAAjB;;AAEA,gBAAGlC,OAAOmC,QAAP,GAAgB,EAAnB,EAAsB;;AAIlB,uBAAO,EAAP;AACH;AACD,gBAAIM,SAAO,OAAKA,MAAL,CAAY,IAAZ,CAAX;AACA,gBAAIC,SAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA/B;AACA,gBAAIE,SAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOC,MAAhB,CAA/B;;AAEA,gBAAIF,UAAQ,CAAZ;;AAEA,gBAAIvD,MAAM,MAAMH,MAAMI,KAAN,CAAY,EAAE,SAAQ,CAAV,EAAY,YAAW,CAAvB,EAAyB,SAAS,CAAC,SAAD,EAAYwD,MAAZ,EAAoBE,MAApB,CAAlC,EAA+D,YAAYvD,QAA3E,EAAqF,QAAQ,CAAC,IAAD,EAAO,CAAC,IAAD,EAAO,IAAP,CAAP,CAA7F,EAAZ,EAAiIiF,KAAjI,CAAuI,QAAvI,EAAiJC,KAAjJ,CAAuJ,CAAvJ,EAA0JzD,MAA1J,EAAhB;AACA,gBAAI7B,IAAI8D,MAAJ,GAAaP,OAAjB,EAA0B;AACtB,oBAAIQ,SAASL,SAASH,OAAT,IAAoBvD,IAAI8D,MAArC;;AAECL,yBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;AACAA,yBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOG,MAAhB,CAA3B;;AAGD,oBAAI4B,OAAO,MAAM1F,MAAMI,KAAN,CAAY,EAAE,SAAQ,CAAV,EAAY,YAAW,CAAvB,EAAyB,SAAS,CAAC,SAAD,EAAYwD,MAAZ,EAAoBE,MAApB,CAAlC,EAA+D,YAAYvD,QAA3E,EAAqF,QAAQ,CAAC,IAAD,EAAO,CAAC,IAAD,EAAO,IAAP,CAAP,CAA7F,EAAZ,EAAiIiF,KAAjI,CAAuI,QAAvI,EAAiJC,KAAjJ,CAAuJvB,MAAvJ,EAA+JlC,MAA/J,EAAjB;;AAGA7B,sBAAIA,IAAIkE,MAAJ,CAAWqB,IAAX,CAAJ;AACH;AACD,gBAAIvF,IAAI8D,MAAJ,GAAaP,OAAjB,EAA0B;;AAEtB,oBAAMQ,SAASL,SAASH,OAAT,IAAoBvD,IAAI8D,MAAvC;;AAEAL,yBAASC,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;AACAV,yBAASD,SAASd,KAAT,IAAkBc,SAASF,OAAOa,MAAhB,CAA3B;;AAGA,oBAAMkB,OAAO,MAAM1F,MAAMI,KAAN,CAAY,EAAE,SAAQ,CAAV,EAAY,YAAW,CAAvB,EAAyB,SAAS,CAAC,SAAD,EAAYwD,MAAZ,EAAoBE,MAApB,CAAlC,EAA+D,YAAYvD,QAA3E,EAAqF,QAAQ,CAAC,IAAD,EAAO,CAAC,IAAD,EAAO,IAAP,CAAP,CAA7F,EAAZ,EAAiIiF,KAAjI,CAAuI,QAAvI,EAAiJC,KAAjJ,CAAuJvB,MAAvJ,EAA+JlC,MAA/J,EAAnB;;AAGD7B,sBAAIA,IAAIkE,MAAJ,CAAWqB,IAAX,CAAJ;AAEF;;AAKD,iBAAK,IAAIxD,IAAT,IAAiB/B,GAAjB,EAAsB;;AAGlB+B,qBAAK2C,EAAL,GAAU,CACN;AACIC,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBADM,EAKN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBALM,EASN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBATM,EAaN;AACID,2BAAO,GADX;AAEIC,6BAAS;AAFb,iBAbM,CAAV;;AAsBK,oBAAIC,SAAO,MAAML,QAAQvE,KAAR,CAAc,EAAC,QAAO8B,KAAKb,EAAb,EAAgB,YAAW,CAA3B,EAAd,EAA6CW,MAA7C,EAAjB;AACD,oBAAG,CAACrB,MAAMG,OAAN,CAAckE,MAAd,CAAJ,EAA0B;;AAEtB,wBAAIC,YAAU,EAAd;AACA,yBAAI,IAAIC,MAAR,IAAkBF,MAAlB,EAAyB;AACrB,4BAAGE,OAAOzB,IAAP,IAAa,IAAhB,EAAqB;;AAEjBwB,wCAAUA,YAAU,oBAAV,GAA+BC,OAAO7D,EAAtC,GAAyC,SAAzC,GAAmD,OAAKsC,MAAL,CAAY,QAAZ,CAAnD,GAAyEuB,OAAOC,IAAhF,GAAqF,yBAA/F;AACH;AACD,4BAAGD,OAAOzB,IAAP,IAAa,IAAhB,EAAqB;;AAEjBwB,wCAAUA,YAAU,uBAAV,GAAkCC,OAAO7D,EAAzC,GAA4C,SAA5C,GAAsD,OAAKsC,MAAL,CAAY,QAAZ,CAAtD,GAA4EuB,OAAOC,IAAnF,GAAwF,yBAAlG;AACH;AACJ;;AAEDjD,yBAAK+C,SAAL,GAAeA,SAAf;AACAlB,4BAAQC,GAAR,CAAY9B,KAAK+C,SAAjB;AACH;AACF/C,qBAAKkD,SAAL,GAAe,qBAAmB,OAAKzB,MAAL,CAAY,QAAZ,CAAnB,GAAyCzB,KAAKmD,IAA9C,GAAmD,yBAAlE;AAEN;;AAEA,kBAAM,OAAKC,KAAL,CAAW,QAAX,EAAqBnF,GAArB,CAAN;;AAED,mBAAOA,GAAP;AArGsB;AAsGzB;;AAGKwF,0BAAN,GAA+B;AAAA;;AAAA;;AAE3B,gBAAI3F,QAAQ,OAAKA,KAAL,CAAW,aAAX,CAAZ;AACA,gBAAIO,WAAW,OAAKR,IAAL,CAAU,UAAV,CAAf;AACA,kBAAME,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;;AAGA,gBAAI0F,cAAc,OAAK5F,KAAL,CAAW,QAAX,CAAlB;AACA,gBAAIkB,SAAS,MAAM0E,YAAYxF,KAAZ,CAAkB,EAAEiB,IAAId,QAAN,EAAlB,EAAoCF,IAApC,EAAnB;;AAEA,gBAAIF,MAAM,MAAMH,MAAMI,KAAN,CAAY,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAYd,QAAjC,EAAZ,EAAyDF,IAAzD,EAAhB;;AAGA,kBAAML,MAAMI,KAAN,CAAY,EAAE,MAAMD,IAAIkB,EAAZ,EAAZ,EAA8BoB,MAA9B,CAAqC,EAAE,aAAa,EAAf,EAAmB,SAAS,CAA5B,EAA8B,YAAW,CAAzC,EAArC,CAAN;;AAIA;AACA;;AAEA;;AAEA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;;AAUA,mBAAKhB,IAAL,CAAU,EAAE,SAAS,CAAX,EAAV;AA7C2B;AA8C9B;;AAGKoE,2BAAN,GAAgC;AAAA;;AAAA;;AAE5B,gBAAI7F,QAAQ,OAAKA,KAAL,CAAW,aAAX,CAAZ;AACA,gBAAIO,WAAW,OAAKR,IAAL,CAAU,UAAV,CAAf;AACA,kBAAME,OAAO,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAnB;AACA,gBAAI6C,QAAQ,OAAKhD,IAAL,CAAU,OAAV,CAAZ;;AAEA,gBAAI6F,cAAc,OAAK5F,KAAL,CAAW,QAAX,CAAlB;AACA,gBAAIkB,SAAS,MAAM0E,YAAYxF,KAAZ,CAAkB,EAAEiB,IAAId,QAAN,EAAlB,EAAoCF,IAApC,EAAnB;;AAEA,gBAAIF,MAAM,MAAMH,MAAMI,KAAN,CAAY,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAYd,QAAjC,EAAZ,EAAyDF,IAAzD,EAAhB;;AAGA,kBAAML,MAAMI,KAAN,CAAY,EAAE,MAAMD,IAAIkB,EAAZ,EAAZ,EAA8BoB,MAA9B,CAAqC,EAAE,SAASM,KAAX,EAAkB,SAAS,CAA3B,EAA6B,YAAWpC,MAAMC,QAAN,EAAxC,EAArC,CAAN;;AAIA;AACA;;AAEA;;AAEA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;;AAUA,mBAAKa,IAAL,CAAU,EAAE,SAAS,CAAX,EAAV;AA7C4B;AA8C/B;;AAKKqE,kBAAN,GAAuB;AAAA;;AAAA;;AAEnB,gBAAIC,WAAW,QAAKpC,MAAL,CAAY,IAAZ,CAAf;AACA,gBAAIf,OAAO,EAAX;AACAA,iBAAK9C,IAAL,GAAY,QAAKC,IAAL,CAAU,MAAV,CAAZ;AACA,kBAAME,OAAO,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAnB;AACA0C,iBAAKC,MAAL,GAAc5C,KAAKoB,EAAnB;AACAuB,iBAAKoD,IAAL,GAAY,QAAKjG,IAAL,CAAU,MAAV,CAAZ;AACA6C,iBAAK3B,WAAL,GAAmBN,MAAMC,QAAN,EAAnB;AACAgC,iBAAKqD,OAAL,GAAe,QAAKlG,IAAL,CAAU,SAAV,CAAf;;AAEA,gBAAIC,QAAQ,QAAKA,KAAL,CAAW,WAAX,CAAZ;;AAEA,gBAAIkG,UAAU,QAAKlG,KAAL,CAAW,IAAX,CAAd;AACA,gBAAIuF,SAAS,QAAKvF,KAAL,CAAW,aAAX,CAAb;;AAIA,gBAAImG,QAAQ,MAAMD,QAAQ9F,KAAR,CAAc,EAAEiB,IAAIuB,KAAK9C,IAAX,EAAd,EAAiCO,IAAjC,EAAlB;;AAGA,gBAAI+F,iBAAgB,MAAMb,OAAOnF,KAAP,CAAa,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAY8E,MAAM5F,QAAvC,EAAb,EAAgEF,IAAhE,EAA1B;AACA,gBAAI0C,QAAMqD,eAAehD,SAAzB;AACA,gBAAIR,KAAKoD,IAAL,IAAa,CAAjB,EAAoB;;AAEhBjD,wBAAMA,QAAM,CAAZ;;AAED;AAEF,aAND,MAMO;AACHA,wBAAMA,QAAM,CAAZ;AACA;AACH;AACD,gBAAGA,QAAM,CAAT,EAAW;;AAEPA,wBAAM,CAAN;AACH;AACD,gBAAGA,QAAM,GAAT,EAAa;;AAETA,wBAAM,GAAN;AAEH;;AAID,gBAAIM,WAAS+C,eAAe/C,QAAf,GAAwB,CAArC;;AAEC,kBAAMkC,OAAOnF,KAAP,CAAa,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAY8E,MAAM5F,QAAvC,EAAb,EAAgEkC,MAAhE,CAAuE,EAAC,aAAYM,KAAb,EAAmB,YAAWM,QAA9B;AACzE,4BAAW1C,MAAMC,QAAN;AAD8D,aAAvE,CAAN;;AAKA,mBAAO,QAAKa,IAAL,CAAU,EAAC,SAAQsB,KAAT,EAAV,CAAP;AApDkB;AAuDtB;;AAWD;AACIsD,kBAAN,CAAqB3E,SAArB,EAA+BV,QAA/B,EAAwCgF,IAAxC,EAA6ClG,IAA7C,EAAkD;AAAA;;AAAA;AAChD,gBAAIE,QAAM,QAAKA,KAAL,CAAW,eAAX,CAAV;;AAGA,gBAAIwC,SAAO,QAAKxC,KAAL,CAAW,WAAX,CAAX;;AAEA,gBAAI0F,OAAK,MAAMlD,OAAOpC,KAAP,CAAa,EAACY,UAASA,QAAV,EAAmB,QAAOlB,IAA1B,EAAb,EAA8CO,IAA9C,EAAf;;AAEA,gBAAGM,MAAMG,OAAN,CAAc4E,IAAd,CAAH,EAAuB;AAClB,oBAAInE,QAAM,EAAV;AACAA,sBAAMzB,IAAN,GAAWA,IAAX;AACAyB,sBAAMP,QAAN,GAAeA,QAAf;AACA,oBAAGgF,QAAM,CAAT,EAAW;AACRzE,0BAAM+E,IAAN,GAAW,CAAX;AACH,iBAFA,MAEI;AACD/E,0BAAMgF,IAAN,GAAW,CAAX;AACH;;AAED,sBAAM/D,OAAOpB,GAAP,CAAWG,KAAX,CAAN;AAEH,aAZD,MAYK;;AAED,oBAAGyE,QAAM,CAAT,EAAW;AACP,0BAAMxD,OAAOpC,KAAP,CAAa,EAACiB,IAAGqE,KAAKrE,EAAT,EAAb,EAA2BC,SAA3B,CAAqC,MAArC,EAA6C,CAA7C,CAAN;AACH,iBAFD,MAEK;AACD,0BAAMkB,OAAOpC,KAAP,CAAa,EAACiB,IAAGqE,KAAKrE,EAAT,EAAb,EAA2BC,SAA3B,CAAqC,MAArC,EAA6C,CAA7C,CAAN;AACH;AACJ;;AAGD,gBAAInB,MAAI,MAAMH,MAAMI,KAAN,CAAY,EAACG,UAASmB,SAAV,EAAoBV,UAASA,QAA7B,EAAsC,eAAcL,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAApD,EAAZ,EAA2GR,IAA3G,EAAd;AACA,gBAAGM,MAAMG,OAAN,CAAcX,GAAd,CAAH,EAAsB;;AAElB,oBAAIY,OAAK,EAAT;AACAA,qBAAKR,QAAL,GAAcmB,SAAd;AACAX,qBAAKC,QAAL,GAAcA,QAAd;AACAD,qBAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;AACA,oBAAGmF,QAAM,CAAT,EAAW;AACPjF,yBAAKuF,IAAL,GAAU,CAAV;AACH,iBAFD,MAEK;AACDvF,yBAAKwF,IAAL,GAAU,CAAV;AACH;;AAED,sBAAMvG,MAAMoB,GAAN,CAAUL,IAAV,CAAN;AAEH,aAdD,MAcK;AACD,oBAAGiF,QAAM,CAAT,EAAW;AACP,0BAAMhG,MAAMI,KAAN,CAAY,EAACiB,IAAGlB,IAAIkB,EAAR,EAAZ,EAAyBC,SAAzB,CAAmC,MAAnC,EAA2C,CAA3C,CAAN;AACH,iBAFD,MAEK;AACD,0BAAMtB,MAAMI,KAAN,CAAY,EAACiB,IAAGlB,IAAIkB,EAAR,EAAZ,EAAyBC,SAAzB,CAAmC,MAAnC,EAA2C,CAA3C,CAAN;AACH;AACJ;;AAGD,kBAAM,QAAKkF,YAAL,CAAkBxF,QAAlB,EAA2BU,SAA3B,CAAN;AAtDgD;AAyDjD;AACD;AACO8E,gBAAN,CAAmBxF,QAAnB,EAA4BU,SAA5B,EAAsC;AAAA;;AAAA;AACnC,gBAAI1B,QAAM,QAAKA,KAAL,CAAW,QAAX,CAAV;AACA,gBAAIkB,SAAO,MAAMlB,MAAMI,KAAN,CAAY,EAACiB,IAAGK,SAAJ,EAAZ,EAA4BrB,IAA5B,EAAjB;;AAEA,gBAAGa,OAAOW,SAAP,IAAkB,CAArB,EAAuB;AACrB,oBAAIC,UAAQ,MAAM9B,MAAMI,KAAN,CAAY,EAACyB,WAAUX,OAAOW,SAAlB,EAAZ,EAA0CE,KAA1C,CAAgD,IAAhD,EAAsDC,MAAtD,EAAlB;;AAGA,oBAAG,CAACrB,MAAMG,OAAN,CAAcgB,OAAd,CAAJ,EAA2B;AACvB,wBAAIG,MAAI,EAAR;AACA,yBAAI,IAAIC,IAAR,IAAgBJ,OAAhB,EAAwB;AACpBG,4BAAIE,IAAJ,CAASD,KAAKb,EAAd;AACH;AACD,wBAAIe,MAAI,gHAA8GzB,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAA9G,GAAuJ,kBAAvJ,GAA0KG,QAA1K,GAAmL,oBAAnL,GAAwMiB,IAAII,IAAJ,CAAS,GAAT,CAAxM,GAAsN,GAA9N;AACA,wBAAIC,WAAS,MAAMtC,MAAMuC,KAAN,CAAYH,GAAZ,CAAnB;;AAEA,wBAAII,SAAO,QAAKxC,KAAL,CAAW,eAAX,CAAX;;AAEA,wBAAIG,MAAI,MAAMqC,OAAOpC,KAAP,CAAa,EAACY,UAASA,QAAV,EAAmBT,UAASW,OAAOW,SAAnC,EAA6CZ,aAAYN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAzD,EAAb,EAAiHR,IAAjH,EAAd;AACA,wBAAGM,MAAMG,OAAN,CAAcX,GAAd,CAAH,EAAsB;;AAElB,4BAAIY,OAAK,EAAT;AACAA,6BAAKC,QAAL,GAAcA,QAAd;AACAD,6BAAKR,QAAL,GAAcW,OAAOW,SAArB;AACAd,6BAAKuF,IAAL,GAAUhE,SAAS,CAAT,EAAY,MAAZ,CAAV;;AAEAvB,6BAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;AACAE,6BAAKwF,IAAL,GAAUjE,SAAS,CAAT,EAAY,MAAZ,CAAV;AACA,8BAAME,OAAOpB,GAAP,CAAWL,IAAX,CAAN;AACH,qBAVD,MAUK;;AAED,8BAAMyB,OAAOpC,KAAP,CAAa,EAACiB,IAAGlB,IAAIkB,EAAR,EAAb,EAA0BoB,MAA1B,CAAiC,EAAC6D,MAAKhE,SAAS,CAAT,EAAY,MAAZ,CAAN,EAA0BiE,MAAKjE,SAAS,CAAT,EAAY,MAAZ,CAA/B,EAAjC,CAAN;AAIH;AAEJ;;AAGH,sBAAO,QAAKkE,YAAL,CAAkBxF,QAAlB,EAA2BE,OAAOW,SAAlC,CAAP;AAKC;AA7CkC;AA+CrC;;AAKM4E,cAAN,GAAmB;AAAA;;AAAA;;AAEf,gBAAIV,WAAW,QAAKpC,MAAL,CAAY,IAAZ,CAAf;AACA,gBAAIf,OAAO,EAAX;AACAA,iBAAK9C,IAAL,GAAY,QAAKC,IAAL,CAAU,MAAV,CAAZ;AACA,kBAAME,OAAO,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAnB;AACA0C,iBAAKC,MAAL,GAAc5C,KAAKoB,EAAnB;AACAuB,iBAAKoD,IAAL,GAAY,QAAKjG,IAAL,CAAU,MAAV,CAAZ;AACA6C,iBAAK3B,WAAL,GAAmBN,MAAMC,QAAN,EAAnB;AACAgC,iBAAKqD,OAAL,GAAe,QAAKlG,IAAL,CAAU,SAAV,CAAf;;AAIA,gBAAI2G,YAAU,QAAK1G,KAAL,CAAW,WAAX,CAAd;AACA,gBAAI2G,eAAa,MAAMD,UAAUtG,KAAV,CAAgB,EAACoB,KAAIvB,KAAKoB,EAAV,EAAaJ,aAAYN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAzB,EAAhB,EAAoFR,IAApF,EAAvB;AACA,gBAAGM,MAAMG,OAAN,CAAc6F,YAAd,CAAH,EAA+B;AAC3B,oBAAI5F,OAAK,EAAT;AACAA,qBAAKS,GAAL,GAASvB,KAAKoB,EAAd;AACAN,qBAAKE,WAAL,GAAiBN,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,YAA3B,CAAjB;;AAEA,sBAAM6F,UAAUtF,GAAV,CAAcL,IAAd,CAAN;AACH;;AAGD,gBAAImF,UAAU,QAAKlG,KAAL,CAAW,IAAX,CAAd;AACA,gBAAIuF,SAAS,QAAKvF,KAAL,CAAW,aAAX,CAAb;AACA,gBAAIA,QAAQ,QAAKA,KAAL,CAAW,WAAX,CAAZ;;AAGA,gBAAImG,QAAQ,MAAMD,QAAQ9F,KAAR,CAAc,EAAEiB,IAAIuB,KAAK9C,IAAX,EAAd,EAAiCO,IAAjC,EAAlB;;AAEAuC,iBAAKgE,GAAL,GAAS,QAAK7G,IAAL,CAAU,KAAV,CAAT;;AAEA6C,iBAAKzC,GAAL,GAAS,QAAKJ,IAAL,CAAU,KAAV,CAAT;;AAGA6C,iBAAKrC,QAAL,GAAc4F,MAAM5F,QAApB;AACAqC,iBAAK5B,QAAL,GAAcf,KAAKO,MAAnB;;AAIA,kBAAM0F,QAAQ9F,KAAR,CAAc,EAAEiB,IAAIuB,KAAK9C,IAAX,EAAd,EAAiCwB,SAAjC,CAA2C,OAA3C,EAAoD,CAApD,CAAN;;AAGA,gBAAI8E,iBAAgB,MAAMb,OAAOnF,KAAP,CAAa,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAY8E,MAAM5F,QAAvC,EAAb,EAAgEF,IAAhE,EAA1B;AACA,gBAAI0C,QAAMqD,eAAerD,KAAzB;;AAGA,kBAAM,QAAKsD,cAAL,CAAoBF,MAAM5F,QAA1B,EAAmCN,KAAKO,MAAxC,EAA+CoC,KAAKoD,IAApD,EAAyDpD,KAAK9C,IAA9D,CAAN;AACA,gBAAI8C,KAAKoD,IAAL,IAAa,CAAjB,EAAoB;;AAEhBjD,wBAAMA,QAAM,CAAZ;;AAKD;AAEF,aATD,MASO;AACHA,wBAAMA,QAAM,CAAZ;AACA;AACH;AACD,gBAAGA,QAAM,CAAT,EAAW;;AAEPA,wBAAM,CAAN;AACH;AACD,gBAAGA,QAAM,GAAT,EAAa;;AAETA,wBAAM,GAAN;AAEH;AACD,gBAAIG,UAAQkD,eAAelD,OAAf,GAAuB,CAAnC;;AAEA,gBAAIC,WAASiD,eAAejD,QAAf,GAAwB,CAArC;AACA,gBAAGD,UAAQ,CAAX,EAAa;;AAERA,0BAAQ,CAAR;AACJ;AACA,kBAAMqC,OAAOnF,KAAP,CAAa,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAY8E,MAAM5F,QAAvC,EAAb,EAAgEkC,MAAhE,CAAuE,EAAC,SAAQM,KAAT,EAAe,YAAWpC,MAAMC,QAAhC,EAAyC,WAAUsC,OAAnD;AAC5E,4BAAWC,QADiE,EACxD,YAAWxC,MAAMC,QAAN,EAD6C,EAAvE,CAAN;AAEAwF,2BAAerD,KAAf,GAAqBA,KAArB;;AAKD,gBAAGc,SAASkC,SAASc,OAAlB,KAA4BT,eAAerD,KAA9C,EAAoD;AACjD;AACC,oBAAI6C,cAAc,QAAK5F,KAAL,CAAW,QAAX,CAAlB;AACA,oBAAIkB,SAAS,MAAM0E,YAAYxF,KAAZ,CAAkB,EAAEiB,IAAI8E,MAAM5F,QAAZ,EAAlB,EAA2CF,IAA3C,EAAnB;;AAEI,oBAAI+B,MAAM,2GAA2GlB,OAAOW,SAAlH,GAA8H,sCAA9H,GAAuKX,OAAOG,EAAxL;AACA,oBAAIyF,UAAU,MAAMlB,YAAYrD,KAAZ,CAAkBH,GAAlB,CAApB;;AAGA,oBAAI2E,aAAa,MAAMnB,YAAYxF,KAAZ,CAAkB,EAAEiB,IAAIyF,QAAQ,CAAR,EAAWE,GAAjB,EAAlB,EAA0C3G,IAA1C,EAAvB;;AAEA,oBAAI,CAACM,MAAMG,OAAN,CAAciG,UAAd,CAAD,IAA8BA,WAAWE,IAAX,IAAmB,CAArD,EAAwD;;AAGpD,wBAAIC,MAAK,MAAM3B,OAAOnF,KAAP,CAAa,EAAE,UAAUH,KAAKoB,EAAjB,EAAqB,YAAW0F,WAAW1F,EAA3C,EAAb,EAA8DhB,IAA9D,EAAf;AACA,wBAAGM,MAAMG,OAAN,CAAcoG,GAAd,CAAH,EAAsB;;AAElB,4BAAItE,OAAO,EAAX;AACAA,6BAAKC,MAAL,GAAc5C,KAAKoB,EAAnB;AACAuB,6BAAKrC,QAAL,GAAgBwG,WAAW1F,EAA3B;AACAuB,6BAAKE,KAAL,GAAa,CAAb;AACAF,6BAAKG,KAAL,GAAa,EAAb;AACAH,6BAAKuE,QAAL,GAAcxG,MAAMC,QAAN,EAAd;AACA,8BAAM2E,OAAOnE,GAAP,CAAWwB,IAAX,CAAN;AACH;AAKJ;AAGR;;AAOD;AACA,kBAAM5C,MAAMoB,GAAN,CAAUwB,IAAV,CAAN;;AAGA,gBAAIwE,UAAU,IAAIvG,IAAJ,EAAd;AACAkD,oBAAQC,GAAR,CAAY,eAAZ;AACAD,oBAAQC,GAAR,CAAYoD,QAAQC,UAAR,EAAZ;AACAtD,oBAAQC,GAAR,CAAYoD,QAAQC,UAAR,KAAuB1G,MAAMgD,MAAN,CAAa,IAAb,EAAmB2D,KAAtD;;AAEA,gBAAIC,MAAM5G,MAAMC,QAAN,CAAe,IAAIC,IAAJ,CAASuG,QAAQI,UAAR,CAAmBJ,QAAQC,UAAR,KAAuBxD,SAASlD,MAAMgD,MAAN,CAAa,IAAb,EAAmB2D,KAA5B,CAA1C,CAAT,CAAf,CAAV;;AAEAvD,oBAAQC,GAAR,CAAYuD,GAAZ;AACAxD,oBAAQC,GAAR,CAAY,eAAZ;;AAEA,gBAAIyD,QAAQ,QAAKzH,KAAL,CAAW,SAAX,CAAZ;AACA;AACA,gBAAIG,MAAM,MAAMsH,MAAMrH,KAAN,CAAY,EAAE,QAAQwC,KAAK9C,IAAf,EAAqB,UAAUG,KAAKoB,EAApC,EAAZ,EAAsDhB,IAAtD,EAAhB;AACA,gBAAIM,MAAMG,OAAN,CAAcX,GAAd,CAAJ,EAAwB;AACpB;AACA,oBAAIuH,QAAQ,EAAZ;AACAA,sBAAM5H,IAAN,GAAa,QAAKC,IAAL,CAAU,MAAV,CAAb;AACA,sBAAME,OAAO,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAnB;AACAwH,sBAAM7E,MAAN,GAAe5C,KAAKoB,EAApB;AACAqG,sBAAMjE,IAAN,GAAa,QAAK1D,IAAL,CAAU,MAAV,CAAb;AACA2H,sBAAMP,QAAN,GAAiBxG,MAAMC,QAAN,EAAjB;AACA8G,sBAAMvG,GAAN,GAAY,CAAZ;;AAEAuG,sBAAM5E,KAAN,GAAc,CAAd;AACA,oBAAIsE,UAAU,IAAIvG,IAAJ,EAAd;;AAIA6G,sBAAMH,GAAN,GAAY5G,MAAMC,QAAN,CAAe,IAAIC,IAAJ,CAASuG,QAAQI,UAAR,CAAmBJ,QAAQC,UAAR,KAAuBxD,SAASlD,MAAMgD,MAAN,CAAa,IAAb,EAAmB2D,KAA5B,CAA1C,CAAT,CAAf,CAAZ;;AAEAI,sBAAMnH,QAAN,GAAiB4F,MAAM5F,QAAvB;AACA;;;AAGA,sBAAMkH,MAAMrG,GAAN,CAAUsG,KAAV,CAAN;AACA;AACA,wBAAKC,aAAL,CAAmBD,MAAM5H,IAAzB;AACH,aAxBD,MAwBO;;AAEH;AACA,oBAAI,QAAKC,IAAL,CAAU,MAAV,KAAqBI,IAAIsD,IAA7B,EAAmC;AAC/B;AACA,wBAAImE,QAAQ,EAAZ;AACA,wBAAIzH,IAAIgB,GAAJ,GAAU,CAAd,EAAiB;AACbyG,8BAAMzG,GAAN,GAAYhB,IAAIgB,GAAJ,GAAU,CAAtB;;AAEA,4BAAI4E,WAAW,QAAKpC,MAAL,CAAY,IAAZ,CAAf;AACA,4BAAIkE,OAAO9B,SAAS,SAAS6B,MAAMzG,GAAxB,CAAX;AACA,4BAAIiG,UAAU,IAAIvG,IAAJ,EAAd;;AAGA+G,8BAAML,GAAN,GAAY5G,MAAMC,QAAN,CAAe,IAAIC,IAAJ,CAASuG,QAAQI,UAAR,CAAmBJ,QAAQC,UAAR,KAAuBxD,SAASgE,IAAT,CAA1C,CAAT,CAAf,CAAZ;AACAD,8BAAM9E,KAAN,GAAc,CAAd;;AAEA,8BAAM2E,MAAMrH,KAAN,CAAY,EAAEN,MAAM,QAAKC,IAAL,CAAU,MAAV,CAAR,EAAZ,EAAyC0C,MAAzC,CAAgDmF,KAAhD,CAAN;AAIH;AAIJ,iBAtBD,MAsBO;AACH;;AAEA,wBAAIA,QAAQ,EAAZ;;AAEAA,0BAAMzG,GAAN,GAAY,CAAZ;;AAGA,wBAAI0G,OAAO9B,SAAS,OAAT,CAAX;AACA,wBAAIqB,UAAU,IAAIvG,IAAJ,EAAd;AACA+G,0BAAML,GAAN,GAAY5G,MAAMC,QAAN,CAAe,IAAIC,IAAJ,CAASuG,QAAQI,UAAR,CAAmBJ,QAAQC,UAAR,KAAuBxD,SAASgE,IAAT,CAA1C,CAAT,CAAf,CAAZ;AACAD,0BAAM9E,KAAN,GAAc,CAAd;;AAGA,0BAAM2E,MAAMrH,KAAN,CAAY,EAAEN,MAAM8C,KAAK9C,IAAb,EAAZ,EAAiC2C,MAAjC,CAAwCmF,KAAxC,CAAN;AAOH;;AAGD,wBAAKD,aAAL,CAAmB/E,KAAK9C,IAAxB;AAEH;;AAED,mBAAO,QAAK2B,IAAL,CAAU,EAAC,SAAQsB,KAAT,EAAV,CAAP;AA1Ne;AA6NlB;;AAIK4E,iBAAN,CAAoBG,IAApB,EAA0B;AAAA;;AAAA;AACtB,gBAAI9H,QAAQ,QAAKA,KAAL,CAAW,IAAX,CAAZ;;AAEA,gBAAIwC,SAAS,QAAKxC,KAAL,CAAW,WAAX,CAAb;;AAEA,gBAAImB,MAAMR,MAAMgD,MAAN,CAAa,IAAb,EAAmB2C,IAA7B;;AAEA,gBAAInG,MAAM,MAAMqC,OAAOpC,KAAP,CAAa,EAAE,QAAQ0H,IAAV,EAAb,EAA+BC,KAA/B,CAAqC,IAArC,CAAhB;AACA,gBAAI5H,OAAOgB,GAAX,EAAgB;;AAGZ,oBAAIuE,OAAO,MAAMlD,OAAOpC,KAAP,CAAa,EAAE,QAAQ0H,IAAV,EAAgB9B,MAAM,CAAtB,EAAb,EAAwC+B,KAAxC,CAA8C,IAA9C,CAAjB;;AAEA,oBAAIC,QAAQtC,OAAOvF,GAAP,GAAa,GAAzB;;AAEA,sBAAMH,MAAMI,KAAN,CAAY,EAAEiB,IAAIyG,IAAN,EAAZ,EAA0BrF,MAA1B,CAAiC,EAAE,SAASuF,KAAX,EAAjC,CAAN;AAIH;AAnBqB;AAuBzB;;AAGQC,aAAN,GAAiB;AAAA;;AAAA;AAChB,gBAAI1H,WAAS,QAAKR,IAAL,CAAU,UAAV,CAAb;AACA,gBAAImI,UAAQ,QAAKnI,IAAL,CAAU,SAAV,CAAZ;;AAEA,kBAAM,QAAKuF,KAAL,CAAW,UAAX,EAAsB/E,QAAtB,CAAN;AACA,kBAAM,QAAK+E,KAAL,CAAW,SAAX,EAAqB4C,OAArB,CAAN;;AAEA,mBAAO,QAAKzG,IAAL,CAAU,CAAV,CAAP;AAPgB;AAQnB;;AA7mCmC,CAAxC", "file": "..\\..\\..\\..\\src\\controller\\api\\app\\ti.js", "sourcesContent": ["\nconst BaseRest = require('./rest.js');\nmodule.exports = class extends BaseRest {\n\n\n\n    async jjAction(){\n        let tkid=this.post(\"tkid\")\n        let model=this.model(\"tk\")\n        const user = await this.session('userInfo');\n        let res =await model.where({\"id\":tkid}).find();\n\n        await this.processdotinumjj(res.lessonid,user.school,tkid)\n\n\n        let model3=this.model(\"jj\");\n\n\n\n\n\n        let res3=await model3.where({\"tkid\":tkid,\"schoolid\":user.school,\"create_date\":think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n\n        if(think.isEmpty(res3)){\n\n            let data={};\n            data.tkid=tkid;\n            data.schoolid=user.school;\n            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n            data.lesson=res.lessonid;\n                data.num=1;\n            \n    \n            await model3.add(data)\n\n        }else{\n            await model3.where({id:res3.id}).increment(\"num\", 1)\n\n        }\n\n\n        let data2={}\n        data2.tkid=tkid;\n        data2.schoolid=user.school;\n        data2.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n        data2.lesson=res.lessonid;\n        data2.uid=user.id;\n        await this.model(\"jj_record\").add(data2);\n\n        return this.json({\"state\":1})\n\n\n    }\n\n\n\n\n\n\n\n    //更新当前lesson\n    async processdotinumjj(lessionid,schoolid,tkid){\n        let model=this.model(\"lesson_school\");\n    \n    \n      \n    \n    \n        let res=await model.where({lessonid:lessionid,schoolid:schoolid,\"create_date\":think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n        if(think.isEmpty(res)){\n    \n            let data={};\n            data.lessonid=lessionid;\n            data.schoolid=schoolid;\n            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n          \n                data.num3=1;\n            \n    \n            await model.add(data)\n    \n        }else{\n           \n                await model.where({id:res.id}).increment(\"num3\", 1)\n           \n        }\n    \n    \n        await this.updateparentnum3(schoolid,lessionid);\n    \n    \n      }\n      //更新父类lesson school 表的做题数量\n       async updateparentnum3(schoolid,lessionid){\n          let model=this.model(\"lesson\");\n          let lesson=await model.where({id:lessionid}).find();\n    \n          if(lesson.parent_id!=0){\n            let lessons=await model.where({parent_id:lesson.parent_id}).field(\"id\").select();\n    \n    \n            if(!think.isEmpty(lessons)){\n                let arr=[]\n                for(let item of lessons){\n                    arr.push(item.id);\n                }\n                let sql=\"select IFNULL(sum(num3),0) as num3  from sys_lesson_school where  create_date='\"+think.datetime(new Date(), 'YYYY-MM-DD')+\"'  and schoolid=\"+schoolid+\" and lessonid in (\"+arr.join(\",\")+\")\";\n                let childres=await model.query(sql);\n    \n                let model2=this.model(\"lesson_school\");\n    \n                let res=await model2.where({schoolid:schoolid,lessonid:lesson.parent_id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n                if(think.isEmpty(res)){\n    \n                    let data={};\n                    data.schoolid=schoolid;\n                    data.lessonid=lesson.parent_id;\n                    data.num3=childres[0]['num3']\n    \n                    data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n                   \n                    await model2.add(data);\n                }else{\n    \n                    await model2.where({id:res.id}).update({num3:childres[0]['num3']});\n    \n    \n    \n                }\n    \n            }\n    \n    \n          await  this.updateparentnum3(schoolid,lesson.parent_id);\n    \n    \n            \n            \n          }\n    \n       }\n\n\n\n\n\n\n\n\n\n\n\n\n\n    //获取题\n    async gettiAction() {\n        let model = this.model(\"lesson_user\");\n        let lessonid = this.post(\"lessonid\");\n        const user = await this.session('userInfo');\n        //  let res=await model.where({\"parent_id\":\"0\",\"del_flag\":\"0\"}).order(\"sort asc\").field(\"id,name\").select();\\\n        let tilist = {};\n\n\n\n        let res = await model.where({ \"lessonid\": lessonid, \"userid\": user.id }).find();\n\n        if (think.isEmpty(res)) {\n            let para = {};\n            para.userid = user.id;\n            para.lessonid = lessonid;\n            para.state = 0;\n            para.score = 50;\n            await model.add(para);\n            tilist.state = 0;\n            tilist.score=50;\n            tilist.data = await this.gettestti(lessonid);\n\n        } else {\n            if (res.state == 1) {\n                tilist.state = 1;\n                tilist.data = await this.zsti(lessonid, res.score,res);\n                tilist.score=res.score;\n                tilist.tisetup=res.tisetup3;\n\n            } else {\n\n\n\n                tilist.data = await this.gettestti(lessonid);\n                tilist.state = 0;\n                tilist.score=res.testscore\n                tilist.tisetup=res.tisetup2;\n\n            }\n\n        }\n\n\n\n\n\n\n\n\n        return this.json(tilist);\n\n    }\n\n\n     processinsql(tilist){\n    let sql=\" and id not in ( \"\n\n    let arr=[];\n    for(let a of tilist){\n        arr.push(a.id)\n\n    }\n    sql=sql+\" \"+ `'${arr.join(\"', '\")}'`\n    sql=sql+\" )\"\n    return sql;\n\n\n   }\n    async getnodo(lessonid, userid, score, type, neednum) {\n\n\n        let config=this.config(\"ti\")\n        let score1 = parseInt(score) - parseInt(config.score1);\n        let score2 = parseInt(score) + parseInt(config.score1);\n\n\n\n        let sql = \"SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid + \" and id not in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" ) ORDER BY  times asc limit \" + neednum;\n\n\n        let model = this.model(\"tk\");\n        let tilist = await model.query(sql);\n        console.log(\"===============\", sql)\n        //获取错题库\n        if (tilist.length < neednum) {\n\n            let tmpnum = parseInt(neednum) - tilist.length;\n            let sql2 = \"SELECT * FROM `sys_tk` where  state=1  and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+\" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=0) ORDER BY   times asc limit \" + tmpnum;\n\n            let tilist2 = await model.query(sql2);\n\n            console.log(\"========\", sql2);\n            tilist = tilist.concat(tilist2);\n        }\n\n        //获取对题库\n        if (tilist.length < neednum) {\n\n            let tmpnum = parseInt(neednum) - tilist.length;\n            let sql3 = \"SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=1) ORDER BY   times asc limit \" + tmpnum;\n\n            let tilist3 = await model.query(sql3);\n\n            console.log(\"========\", sql3);\n            tilist = tilist.concat(tilist3);\n        }\n\n\n        //二段分值\n\n        score1 = parseInt(score) - parseInt(config.score2);\n        score2 = parseInt(score) + parseInt(config.score2);\n\n\n        //未做题\n        if (tilist.length < neednum) {\n\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id not in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \") ORDER BY   times asc limit \" + tmpnum;\n          let  tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n\n\n\n        //错题库\n\n        if (tilist.length < neednum) {\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=0) ORDER BY   times asc limit \" + tmpnum;;\n            let   tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n        if (tilist.length < neednum) {\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=1) ORDER BY  times asc limit \" + tmpnum;;\n            let   tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n\n\n        //三段分值\n\n        score1 = parseInt(score) - parseInt(config.score3);\n        score2 = parseInt(score) + parseInt(config.score3);\n\n\n        //未做题\n        if (tilist.length < neednum) {\n\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id not in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \") ORDER BY   times asc limit \" + tmpnum;\n            let   tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n\n\n\n        //错题库\n\n        if (tilist.length < neednum) {\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=0) ORDER BY   times asc limit \" + tmpnum;;\n            let   tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n        if (tilist.length < neednum) {\n            let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid +this.processinsql(tilist)+ \" and id  in (select tkid from sys_tk_user where userid=\" + userid + \" and lessonid=\" + lessonid + \" and state=0 and type=1) ORDER BY  times asc limit \" + tmpnum;;\n            let    tilist3 = await model.query(sql);\n\n            tilist = tilist.concat(tilist3);\n        }\n\n\n//不可出题\n\n\n        score1 = parseInt(score) - parseInt(config.score1);\n        score2 = parseInt(score) + parseInt(config.score1);\n\n         if (tilist.length < neednum) {\n             \n             let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and  del_flag=0  and score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid + \" and id  in (select tkid from sys_tk_user where state=1 and userid=\" + userid + \" and lessonid=\" + lessonid + \" ) ORDER BY  times asc limit \" + tmpnum;\n            let    tilist4 = await model.query(sql);\n\n            tilist = tilist.concat(tilist4);\n                     \n         }\n         \n         \n        score1 = parseInt(score) - parseInt(config.score2);\n        score2 = parseInt(score) + parseInt(config.score2);\n\n         if (tilist.length < neednum) {\n             \n             let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and  score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid + \" and id  in (select tkid from sys_tk_user where state=1 and userid=\" + userid + \" and lessonid=\" + lessonid + \" ) ORDER BY  times asc limit \" + tmpnum;\n            let    tilist4 = await model.query(sql);\n\n            tilist = tilist.concat(tilist4);\n                     \n         }\n         \n         score1 = parseInt(score) - parseInt(config.score3);\n        score2 = parseInt(score) + parseInt(config.score3);\n\n         if (tilist.length < neednum) {\n             \n             let tmpnum = parseInt(neednum) - tilist.length;\n            sql = \"SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and   score <=\" + score2 + \" and score >=\" + score1 + \" and \" + type + \" and lessonid=\" + lessonid + \" and id  in (select tkid from sys_tk_user where state=1 and userid=\" + userid + \" and lessonid=\" + lessonid + \" ) ORDER BY  times asc limit \" + tmpnum;\n            let    tilist4 = await model.query(sql);\n\n            tilist = tilist.concat(tilist4);\n                     \n         }\n         \n         \n         \n\n        return tilist;\n\n\n\n    }\n\n    async zsti(lessonid, score,lesson_user) {\n        let model = this.model(\"tk\");\n\n\n        let modeljx=this.model(\"tk_jx\")\n\n\n        let model2=this.model(\"lesson_user\");\n      \n        const user = await this.session('userInfo');\n        let config = this.config(\"ti\")\n        let list=[];\n        if(lesson_user.tisetup<=4){\n\n             list = await this.getnodo(lessonid, user.id, score, \"(type ='单选' or type='多选')\", 1)\n        }\n\n        if(lesson_user.tisetup>4&&lesson_user.tisetup<7){\n\n             list = await this.getnodo(lessonid, user.id, score, \"type ='填空' \", 1)\n        }\n\n       // let res = await model.where({ \"score\": ['BETWEEN', 40, 100], \"lessonid\": lessonid, \"type\": ['IN', ['单选', \"填空\"]] }).order(\"rand()\").limit(10).select();\n        \n       if(lesson_user.tisetup==7){\n\n            list =  await this.getnodo(lessonid, user.id, score, \"type ='解答' \",1)\n        }\n\n       \n      \n\n     \n\n\n\n        for (var item of list) {\n\n\n            item.op = [\n                {\n                    title: \"A\",\n                    checked: false\n                },\n                {\n                    title: \"B\",\n                    checked: false\n                },\n                {\n                    title: \"C\",\n                    checked: false\n                },\n                {\n                    title: \"D\",\n                    checked: false\n                },\n\n            ]\n\n\n           \n\n\n        }\n\n        let res=list;\n        \n        for(var item of res){\n            \n             let jxlist=await modeljx.where({\"tkid\":item.id,\"del_flag\":0}).select();\n            if(!think.isEmpty(jxlist)){\n                let jxcontent=\"\";\n                for(var jxitem of jxlist){\n                    if(jxitem.type==\"图片\"){\n\n                        jxcontent=jxcontent+\"<div><img name='jx\"+jxitem.id+\"' src='\"+this.config(\"domain\")+jxitem.file+\"' widht='100%' /></div>\"\n                    }\n                    if(jxitem.type==\"视频\"){\n\n                        jxcontent=jxcontent+\"<div><video  name='jx\"+jxitem.id+\"' src='\"+this.config(\"domain\")+jxitem.file+\"' widht='100%' /></div>\"\n                    }\n                }\n               \n                item.jxcontent=jxcontent;\n                console.log(item.jxcontent);\n            }\n\n             item.tmcontent=\"<div><img  src='\"+this.config(\"domain\")+item.tm_p+\"' widht='100%' /></div>\"\n            \n        }\n        \n        \n\n\n        \n        console.log(res);\n        await this.cache('zsti', res);\n        return res;\n    }\n\n\n    async gettestti(lessonid) {\n        let model = this.model(\"tk\");\n        let    modeljx=this.model(\"tk_jx\");\n\n        let lmodel=this.model(\"lesson_user\")\n        const user = await this.session('userInfo');\n        let lesson=await lmodel.where({\"lessonid\":lessonid,\"userid\":user.id}).find();\n        let score=lesson.testscore;\n\n        if(lesson.tisetup2>10){\n\n\n\n            return [];\n        }\n        let config=this.config(\"ti\")\n        let score1 = parseInt(score) - parseInt(config.score1);\n        let score2 = parseInt(score) + parseInt(config.score1);\n\n        let neednum=1;\n\n        let res = await model.where({ \"state\":1,\"del_flag\":0,\"score\": ['BETWEEN', score1, score2], \"lessonid\": lessonid, \"type\": ['IN', ['单选', \"多选\"]] }).order(\"rand()\").limit(1).select();\n        if (res.length < neednum) {\n            let tmpnum = parseInt(neednum) - res.length;\n\n             score1 = parseInt(score) - parseInt(config.score2);\n             score2 = parseInt(score) + parseInt(config.score2);\n\n\n            let res2 = await model.where({ \"state\":1,\"del_flag\":0,\"score\": ['BETWEEN', score1, score2], \"lessonid\": lessonid, \"type\": ['IN', ['单选', \"多选\"]] }).order(\"rand()\").limit(tmpnum).select();\n\n\n            res=res.concat(res2)\n        }\n        if (res.length < neednum) {\n\n            let   tmpnum = parseInt(neednum) - res.length;\n\n            score1 = parseInt(score) - parseInt(config.score3);\n            score2 = parseInt(score) + parseInt(config.score3);\n\n\n            let   res2 = await model.where({ \"state\":1,\"del_flag\":0,\"score\": ['BETWEEN', score1, score2], \"lessonid\": lessonid, \"type\": ['IN', ['单选', \"多选\"]] }).order(\"rand()\").limit(tmpnum).select();\n\n\n           res=res.concat(res2)\n\n        }\n\n\n\n\n        for (var item of res) {\n\n\n            item.op = [\n                {\n                    title: \"A\",\n                    checked: false\n                },\n                {\n                    title: \"B\",\n                    checked: false\n                },\n                {\n                    title: \"C\",\n                    checked: false\n                },\n                {\n                    title: \"D\",\n                    checked: false\n                },\n\n            ]\n\n             \n\n                 let jxlist=await modeljx.where({\"tkid\":item.id,\"del_flag\":0}).select();\n                if(!think.isEmpty(jxlist)){\n\n                    let jxcontent=\"\";\n                    for(var jxitem of jxlist){\n                        if(jxitem.type==\"图片\"){\n\n                            jxcontent=jxcontent+\"<div><img name='jx\"+jxitem.id+\"' src='\"+this.config(\"domain\")+jxitem.file+\"' widht='100%' /></div>\"\n                        }\n                        if(jxitem.type==\"视频\"){\n\n                            jxcontent=jxcontent+\"<div><video  name='jx\"+jxitem.id+\"' src='\"+this.config(\"domain\")+jxitem.file+\"' widht='100%' /></div>\"\n                        }\n                    }\n\n                    item.jxcontent=jxcontent;\n                    console.log(item.jxcontent);\n                }\n               item.tmcontent=\"<div><img  src='\"+this.config(\"domain\")+item.tm_p+\"' widht='100%' /></div>\"\n\n        }\n        \n         await this.cache('testti', res);\n\n        return res;\n    }\n\n\n    async resetlessonscoreAction() {\n\n        let model = this.model(\"lesson_user\");\n        let lessonid = this.post(\"lessonid\");\n        const user = await this.session('userInfo');\n       \n\n        let lessonmodel = this.model(\"lesson\")\n        let lesson = await lessonmodel.where({ id: lessonid }).find();\n\n        let res = await model.where({ \"userid\": user.id, \"lessonid\": lessonid }).find();\n\n\n        await model.where({ \"id\": res.id }).update({ \"testscore\": 50, \"state\": 0,\"tisetup2\":1 });\n\n\n\n        // let sql = \"select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=\" + lesson.parent_id + \" order by sort asc) as a where a.id=\" + lesson.id;\n        // let nexttmp = await lessonmodel.query(sql);\n\n        // console.log(nexttmp[0]);\n\n        // let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();\n\n        // if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {\n\n\n        //     let para = {};\n        //     para.userid = user.id;\n        //     para.lessonid = netxlesson.id;\n        //     para.state = 0;\n        //     para.score = 50;\n        //     await model.add(para);\n\n\n        // }\n\n\n\n\n\n\n\n\n\n        this.json({ \"state\": 1 });\n    }\n\n\n    async updatelessonscoreAction() {\n\n        let model = this.model(\"lesson_user\");\n        let lessonid = this.post(\"lessonid\");\n        const user = await this.session('userInfo');\n        let score = this.post(\"score\");\n\n        let lessonmodel = this.model(\"lesson\")\n        let lesson = await lessonmodel.where({ id: lessonid }).find();\n\n        let res = await model.where({ \"userid\": user.id, \"lessonid\": lessonid }).find();\n\n\n        await model.where({ \"id\": res.id }).update({ \"score\": score, \"state\": 1,\"lasttime\":think.datetime() });\n\n\n\n        // let sql = \"select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=\" + lesson.parent_id + \" order by sort asc) as a where a.id=\" + lesson.id;\n        // let nexttmp = await lessonmodel.query(sql);\n\n        // console.log(nexttmp[0]);\n\n        // let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();\n\n        // if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {\n\n\n        //     let para = {};\n        //     para.userid = user.id;\n        //     para.lessonid = netxlesson.id;\n        //     para.state = 0;\n        //     para.score = 50;\n        //     await model.add(para);\n\n\n        // }\n\n\n\n\n\n\n\n\n\n        this.json({ \"state\": 1 });\n    }\n\n\n\n\n    async dotesttiAction() {\n\n        let ticonfig = this.config(\"ti\");\n        let para = {};\n        para.tkid = this.post(\"tkid\");\n        const user = await this.session('userInfo');\n        para.userid = user.id;\n        para.flag = this.post(\"flag\");\n        para.create_date = think.datetime();\n        para.usetime = this.post(\"usetime\");\n\n        let model = this.model(\"tk_record\");\n\n        let tkmodel = this.model(\"tk\");\n        let lmodel = this.model(\"lesson_user\");\n\n\n\n        let tires = await tkmodel.where({ id: para.tkid }).find();\n   \n\n        let lesson_userres= await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).find();\n        let score=lesson_userres.testscore;\n        if (para.flag == 0) {\n            \n            score=score-3;\n            \n           // await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).decrement(\"score\", 3)\n\n        } else {\n            score=score+3;\n            //await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).increment(\"score\", 3)\n        }\n        if(score<0){\n            \n            score=0;\n        }\n        if(score>100){\n            \n            score=100;\n        \n        }\n\n\n\n        let tisetup2=lesson_userres.tisetup2+1\n\n         await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).update({\"testscore\":score,'tisetup2':tisetup2\n            ,\"lasttime\":think.datetime()\n         });\n\n\n         return this.json({\"score\":score})\n    \n    \n    }\n\n\n\n\n\n\n\n\n\n\n    //更新当前lesson更新作对做错统计\n  async processdotinum(lessionid,schoolid,flag,tkid){\n    let model=this.model(\"lesson_school\");\n\n\n    let model2=this.model(\"tk_school\");\n\n    let res2=await model2.where({schoolid:schoolid,\"tkid\":tkid}).find();\n\n    if(think.isEmpty(res2)){\n         let data2={}\n         data2.tkid=tkid;\n         data2.schoolid=schoolid;\n         if(flag==1){\n            data2.num1=1\n        }else{\n            data2.num2=1;\n        }\n\n        await model2.add(data2)\n\n    }else{\n\n        if(flag==1){\n            await model2.where({id:res2.id}).increment(\"num1\", 1)\n        }else{\n            await model2.where({id:res2.id}).increment(\"num2\", 1)\n        }\n    }\n\n\n    let res=await model.where({lessonid:lessionid,schoolid:schoolid,\"create_date\":think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n    if(think.isEmpty(res)){\n\n        let data={};\n        data.lessonid=lessionid;\n        data.schoolid=schoolid;\n        data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n        if(flag==1){\n            data.num1=1\n        }else{\n            data.num2=1;\n        }\n\n        await model.add(data)\n\n    }else{\n        if(flag==1){\n            await model.where({id:res.id}).increment(\"num1\", 1)\n        }else{\n            await model.where({id:res.id}).increment(\"num2\", 1)\n        }\n    }\n\n\n    await this.updateparent(schoolid,lessionid);\n\n\n  }\n  //更新父类lesson school 表的做题数量\n   async updateparent(schoolid,lessionid){\n      let model=this.model(\"lesson\");\n      let lesson=await model.where({id:lessionid}).find();\n\n      if(lesson.parent_id!=0){\n        let lessons=await model.where({parent_id:lesson.parent_id}).field(\"id\").select();\n\n\n        if(!think.isEmpty(lessons)){\n            let arr=[]\n            for(let item of lessons){\n                arr.push(item.id);\n            }\n            let sql=\"select IFNULL(sum(num1),0) as num1 ,IFNULL(sum(num2),0) as num2 from sys_lesson_school where  create_date='\"+think.datetime(new Date(), 'YYYY-MM-DD')+\"'  and schoolid=\"+schoolid+\" and lessonid in (\"+arr.join(\",\")+\")\";\n            let childres=await model.query(sql);\n\n            let model2=this.model(\"lesson_school\");\n\n            let res=await model2.where({schoolid:schoolid,lessonid:lesson.parent_id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n            if(think.isEmpty(res)){\n\n                let data={};\n                data.schoolid=schoolid;\n                data.lessonid=lesson.parent_id;\n                data.num1=childres[0]['num1']\n\n                data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n                data.num2=childres[0]['num2']\n                await model2.add(data);\n            }else{\n\n                await model2.where({id:res.id}).update({num1:childres[0]['num1'],num2:childres[0]['num2']});\n\n\n\n            }\n\n        }\n\n\n      await  this.updateparent(schoolid,lesson.parent_id);\n\n\n        \n        \n      }\n\n   }\n\n\n\n\n    async dotiAction() {\n\n        let ticonfig = this.config(\"ti\");\n        let para = {};\n        para.tkid = this.post(\"tkid\");\n        const user = await this.session('userInfo');\n        para.userid = user.id;\n        para.flag = this.post(\"flag\");\n        para.create_date = think.datetime();\n        para.usetime = this.post(\"usetime\");\n\n\n\n        let dayrecord=this.model(\"dayrecord\");\n        let dayrecordres=await dayrecord.where({uid:user.id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();\n        if(think.isEmpty(dayrecordres)){\n            let data={};\n            data.uid=user.id;\n            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');\n           \n            await dayrecord.add(data);\n        }\n      \n\n        let tkmodel = this.model(\"tk\");\n        let lmodel = this.model(\"lesson_user\");\n        let model = this.model(\"tk_record\");\n\n\n        let tires = await tkmodel.where({ id: para.tkid }).find();\n\n        para.pic=this.post(\"pic\");\n\n        para.res=this.post(\"res\");\n        \n\n        para.lessonid=tires.lessonid;\n        para.schoolid=user.school;\n\n\n\n        await tkmodel.where({ id: para.tkid }).increment(\"times\", 1);\n\n\n        let lesson_userres= await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).find();\n        let score=lesson_userres.score;\n\n\n        await this.processdotinum(tires.lessonid,user.school,para.flag,para.tkid)\n        if (para.flag == 0) {\n            \n            score=score-3;\n            \n\n\n\n           // await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).decrement(\"score\", 3)\n\n        } else {\n            score=score+3;\n            //await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).increment(\"score\", 3)\n        }\n        if(score<0){\n            \n            score=0;\n        }\n        if(score>100){\n            \n            score=100;\n        \n        }\n        let tisetup=lesson_userres.tisetup+1\n\n        let tisetup3=lesson_userres.tisetup3+1\n        if(tisetup>7){\n\n             tisetup=1\n        }\n         await lmodel.where({ \"userid\": user.id, \"lessonid\": tires.lessonid }).update({\"score\":score,\"lasttime\":think.datetime,'tisetup':tisetup\n         ,\"tisetup3\":tisetup3,\"lasttime\":think.datetime()});\n         lesson_userres.score=score;\n        \n\n        \n\n        if(parseInt(ticonfig.jsscore)<=lesson_userres.score){\n           //解锁下一章节\n            let lessonmodel = this.model(\"lesson\")\n            let lesson = await lessonmodel.where({ id: tires.lessonid  }).find();\n\n                let sql = \"select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=\" + lesson.parent_id + \" order by sort asc) as a where a.id=\" + lesson.id;\n                let nexttmp = await lessonmodel.query(sql);\n\n                \n                let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();\n\n                if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {\n\n\n                    let lmp =await lmodel.where({ \"userid\": user.id, \"lessonid\":netxlesson.id }).find();\n                    if(think.isEmpty(lmp)){\n\n                        let para = {};\n                        para.userid = user.id;\n                        para.lessonid = netxlesson.id;\n                        para.state = 0;\n                        para.score = 50;\n                        para.lasttime=think.datetime();\n                        await lmodel.add(para);\n                    }\n\n                  \n\n\n                }\n\n\n        }\n\n\n\n\n     \n\n        //添加做题记录\n        await model.add(para);\n\n\n        var curTime = new Date();\n        console.log(\"=============\");\n        console.log(curTime.getSeconds());\n        console.log(curTime.getSeconds() + think.config(\"ti\").time1);\n\n        let yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(think.config(\"ti\").time1))));\n\n        console.log(yxq)\n        console.log(\"=============\");\n\n        let mode2 = this.model(\"tk_user\");\n        //查询做题记录\n        let res = await mode2.where({ \"tkid\": para.tkid, \"userid\": user.id }).find();\n        if (think.isEmpty(res)) {\n            //第一次做\n            let para2 = {};\n            para2.tkid = this.post(\"tkid\");\n            const user = await this.session('userInfo');\n            para2.userid = user.id;\n            para2.type = this.post(\"flag\");\n            para2.lasttime = think.datetime();\n            para2.num = 1;\n\n            para2.state = 1;\n            var curTime = new Date();\n\n\n\n            para2.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(think.config(\"ti\").time1))));\n\n            para2.lessonid = tires.lessonid;\n            //插入做题记录\n\n\n            await mode2.add(para2)\n            //更新题目得分\n            this.updatetiscore(para2.tkid);\n        } else {\n\n            //做过\n            if (this.post(\"flag\") == res.type) {\n                //根上一次状态一样\n                let para3 = {};\n                if (res.num < 6) {\n                    para3.num = res.num + 1;\n\n                    let ticonfig = this.config(\"ti\");\n                    let adds = ticonfig[\"time\" + para3.num];\n                    var curTime = new Date();\n\n\n                    para3.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(adds))))\n                    para3.state = 1;\n\n                    await mode2.where({ tkid: this.post(\"tkid\") }).update(para3);\n\n\n\n                }\n\n\n\n            } else {\n                //跟上一次状态不一样\n\n                let para3 = {};\n\n                para3.num = 1;\n\n             \n                let adds = ticonfig[\"time1\"];\n                var curTime = new Date();\n                para3.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(adds))))\n                para3.state = 1;\n\n\n                await mode2.where({ tkid: para.tkid }).update(para3);\n\n\n\n\n\n\n            }\n\n\n            this.updatetiscore(para.tkid);\n\n        }\n\n        return this.json({\"score\":score});\n\n\n    }\n\n\n\n    async updatetiscore(tiid) {\n        let model = this.model(\"tk\");\n\n        let model2 = this.model(\"tk_record\")\n\n        let num = think.config('ti').num1;\n\n        let res = await model2.where({ \"tkid\": tiid }).count(\"id\");\n        if (res >= num) {\n\n\n            let res2 = await model2.where({ \"tkid\": tiid, flag: 0 }).count(\"id\");\n\n            let socre = res2 / res * 100;\n\n            await model.where({ id: tiid }).update({ \"score\": socre });\n\n\n\n        }\n\n\n\n    }\n    \n    \n       async curAction(){\n        let lessonid=this.post(\"lessonid\")\n        let curtino=this.post(\"curtino\");\n        \n        await this.cache(\"lessonid\",lessonid);\n        await this.cache(\"curtino\",curtino);\n        \n        return this.json(1);\n    }\n    \n}"]}