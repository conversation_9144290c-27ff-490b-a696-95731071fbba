<template>
	<el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large">
		<el-form-item prop="user">
			<el-input v-model="form.user" prefix-icon="el-icon-user" clearable :placeholder="$t('login.userPlaceholder')">
<!--				<template #append>-->
<!--					<el-select v-model="userType" style="width: 130px;">-->
<!--						<el-option :label="$t('login.admin')" value="admin"></el-option>-->
<!--						<el-option :label="$t('login.user')" value="user"></el-option>-->
<!--					</el-select>-->
<!--				</template>-->
			</el-input>
		</el-form-item>
		<el-form-item prop="password">
			<el-input v-model="form.password" prefix-icon="el-icon-lock" clearable show-password :placeholder="$t('login.PWPlaceholder')"></el-input>
		</el-form-item>
<!--		<el-form-item style="margin-bottom: 10px;">-->
<!--				<el-col :span="12">-->
<!--					<el-checkbox :label="$t('login.rememberMe')" v-model="form.autologin"></el-checkbox>-->
<!--				</el-col>-->
<!--				<el-col :span="12" class="login-forgot">-->
<!--					<router-link to="/reset_password">{{ $t('login.forgetPassword') }}？</router-link>-->
<!--				</el-col>-->
<!--		</el-form-item>-->
		<el-form-item>
			<el-button type="primary" style="width: 100%;" :loading="islogin" round @click="login">{{ $t('login.signIn') }}</el-button>
		</el-form-item>
<!--		<div class="login-reg">-->
<!--			{{$t('login.noAccount')}} <router-link to="/user_register">{{$t('login.createAccount')}}</router-link>-->
<!--		</div>-->
	</el-form>
</template>

<script>
	export default {
		data() {
			return {
				userType: 'admin',
				form: {
					user: "",
					password: "",
					autologin: true
				},
				rules: {
					user: [
						{required: true, message: this.$t('login.userError'), trigger: 'blur'}
					],
					password: [
						{required: true, message: this.$t('login.PWError'), trigger: 'blur'}
					]
				},
				islogin: false,
			}
		},
		watch:{
			userType(val){
				if(val == 'admin'){
					this.form.user = 'admin'
					this.form.password = 'admin'
				}else if(val == 'user'){
					this.form.user = 'user'
					this.form.password = 'user'
				}
			}
		},
		mounted() {
		},
		methods: {
			async login(){

				var validate = await this.$refs.loginForm.validate().catch(()=>{})
				if(!validate){ return false }

				this.islogin = true
				var data = {
					user: this.form.user,
					password: this.$TOOL.crypto.MD5(this.form.password)
				}
				var userInfo = await this.$API.user.login.get(data);

				if (userInfo.data.state == 1) {
					//弱密码检测
					// //6-20位包含字符、数字和特殊字符
					// let ls = 0;
					// if (this.ruleForm.password !== '') {
					// 	if (this.ruleForm.password.match(/([a-z])+/)) {
					// 		ls++;
					// 	}
					// 	if (this.ruleForm.password.match(/([0-9])+/)) {
					// 		ls++;
					// 	}
					// 	if (this.ruleForm.password.match(/([A-Z])+/)) {
					// 		ls++;
					// 	}
					// 	if (this.ruleForm.password.match(/([\W])+/) && !this.ruleForm.password.match(/(![\u4E00-\u9FA5])+/)) {
					// 		ls++;
					// 	}
					// 	if (this.ruleForm.password.length < 6 || this.ruleForm.password.length > 20) {
					// 		ls = 0;
					// 	}
					// }
					// if (ls < 3) {
					// 	this.$TOOL.data.set("weakPassWord", 1);
					// } else {
					// 	this.$TOOL.data.set("weakPassWord", 0);
					// }
					this.$TOOL.data.set("user", userInfo.data);
					this.$TOOL.data.set("USER_INFO",userInfo.data)
					this.$TOOL.data.set("MENU",userInfo.data.menuList)
					this.$TOOL.data.set("PERMISSIONS",userInfo.data.rolePermissions)
					this.$TOOL.cookie.set("TOKEN", userInfo.data.token, {
						expires: this.form.autologin? 24*60*60 : 0
					})
						this.$router.replace({
					path: '/'
				})
				this.$message.success("Login Success 登录成功")
				} else {
					this.$message.error("用户名或密码错误")

				}

				this.islogin = false;
			},
		}
	}
</script>

<style>
</style>
