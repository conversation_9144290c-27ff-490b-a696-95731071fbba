import config from "./config"
import api from './api'
import tool from './utils/tool'
import http from "./utils/request"
import { permission, rolePermission } from './utils/permission'

import scTable from './components/scTable'
import scTableColumn from './components/scTable/column.js'
import scFilterBar from './components/scFilterBar'
import scUpload from './components/scUpload'
import scUploadMultiple from './components/scUpload/multiple'
import scUploadFile from './components/scUpload/file'
import scFormTable from './components/scFormTable'
import scTableSelect from './components/scTableSelect'
import scPageHeader from './components/scPageHeader'
import scSelect from './components/scSelect'
import scDialog from './components/scDialog'
import scForm from './components/scForm'
import scTitle from './components/scTitle'
import scWaterMark from './components/scWaterMark'
import scQrCode from './components/scQrCode'
import scStatusIndicator from './components/scMini/scStatusIndicator'
import scTrend from './components/scMini/scTrend'
//自写
import treeSelect from './components/treeSelect/index'
import exportTableExcel from './components/export/tableExcel'
import scSearch from './components/scSearch'
import scFormTableV3 from './components/scFormTable/index_v3'


import auth from './directives/auth'
import role from './directives/role'
import time from './directives/time'
import copy from './directives/copy'
import errorHandler from './utils/errorHandler'

import * as elIcons from '@element-plus/icons-vue'
import * as scIcons from './assets/icons'




import Driver from 'driver.js'
import 'driver.js/dist/driver.min.css'

import _ from 'lodash'

const { create, all } = require('mathjs')

const mathConfig = {
	number:'BigNumber',
	precision:20  //精度 20。 precision: BigNumbers的最大有效位数。
}

const math = create(all,mathConfig);

export default {
	install(app) {
		//挂载全局对象
		app.config.globalProperties.$MATH = math;
		app.config.globalProperties.$CONFIG = config;
		app.config.globalProperties.$TOOL = tool;
		app.config.globalProperties.$HTTP = http;
		app.config.globalProperties.$API = api;
		app.config.globalProperties.$AUTH = permission;
		app.config.globalProperties.$ROLE = rolePermission;
		app.config.globalProperties.$_ = _;
		app.config.globalProperties.$driver = new Driver({
			doneBtnText: '完成',              // Text on the final button
			closeBtnText: '关闭',            // Text on the close button for this step
			stageBackground: 'salmon',       // Background color for the staged behind highlighted element
			nextBtnText: '下一步',              // Next button text for this step
			prevBtnText: '上一步',          // Previous button text for this step
		})

		//注册全局组件
		app.component('scTable', scTable);
		app.component('scTableColumn', scTableColumn);
		app.component('scFilterBar', scFilterBar);
		app.component('scUpload', scUpload);
		app.component('scUploadMultiple', scUploadMultiple);
		app.component('scUploadFile', scUploadFile);
		app.component('scFormTable', scFormTable);
		app.component('scFormTableV3', scFormTableV3);
		app.component('scTableSelect', scTableSelect);
		app.component('scPageHeader', scPageHeader);
		app.component('scSelect', scSelect);
		app.component('scDialog', scDialog);
		app.component('scForm', scForm);
		app.component('scTitle', scTitle);
		app.component('scWaterMark', scWaterMark);
		app.component('scQrCode', scQrCode);
		app.component('scStatusIndicator', scStatusIndicator);
		app.component('scTrend', scTrend);
		app.component('treeSelect', treeSelect);
		app.component('exportTableExcel', exportTableExcel);
		app.component('scSearch', scSearch);


		//注册全局指令
		app.directive('auth', auth)
		app.directive('role', role)
		app.directive('time', time)
		app.directive('copy', copy)

		//统一注册el-icon图标
		for(let icon in elIcons){
			app.component(`ElIcon${icon}`, elIcons[icon])
		}
		//统一注册sc-icon图标
		for(let icon in scIcons){
			app.component(`ScIcon${icon}`, scIcons[icon])
		}

		//关闭async-validator全局控制台警告
		window.ASYNC_VALIDATOR_NO_WARNING = 1

		//全局代码错误捕捉
		app.config.errorHandler = errorHandler
	}
}
