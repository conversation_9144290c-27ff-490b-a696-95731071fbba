import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/site/equipment/page`,
		name: "配管站数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/site/equipment/add`,
		name: "配管站数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/site/equipment/save`,
		name: "配管站数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/site/equipment/info`,
		name: "配管站数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	list: {
		url: `${config.API_URL}/site/equipment/list`,
		name: "配管站数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/site/equipment/delete`,
		name: "删除配管站数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
