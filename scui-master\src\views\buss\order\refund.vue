<template>
  <el-dialog title="订单退款" v-model="visible" :width="500" destroy-on-close @closed="$emit('closed')">
    <div class="order-info">
      <div class="info-item">
        <span class="label">订单编号：</span>
        <span class="value">{{ order.order_no }}</span>
      </div>
      <div class="info-item">
        <span class="label">支付方式：</span>
        <span class="value">
          <el-tag v-if="order.pay_type === 1" type="primary" size="small">微信</el-tag>
          <el-tag v-else-if="order.pay_type === 2" type="success" size="small">支付宝</el-tag>
          <el-tag v-else-if="order.pay_type === 3" type="warning" size="small">余额</el-tag>
          <span v-else>-</span>
        </span>
      </div>
      <div class="info-item">
        <span class="label">支付金额：</span>
        <span class="value price">¥{{ order.pay_amount }}</span>
      </div>
      <div class="info-item">
        <span class="label">已消耗天数：</span>
        <span class="value">{{ order.useday }} 天</span>
      </div>
      <div class="info-item">
        <span class="label">剩余天数：</span>
        <span class="value">{{ order.available_days }} 天</span>
      </div>
      <div class="info-item">
        <span class="label">支付时间：</span>
        <span class="value">{{ order.pay_time }}</span>
      </div>
    </div>

    <el-divider></el-divider>

    <el-form :model="form" :rules="rules" ref="refundForm" label-width="100px">
      <el-form-item label="退款金额" prop="refundAmount">
        <el-input-number v-model="form.refundAmount" :precision="2" :min="0.01" :max="order.pay_amount" :step="0.01" style="width: 200px" disabled></el-input-number>
        <div class="refund-tip">
          根据剩余天数自动计算的退款金额
        </div>
      </el-form-item>

      <el-form-item label="退款原因" prop="refundReason">
        <el-select v-model="form.refundReason" placeholder="请选择退款原因" style="width: 100%">
          <el-option label="客户申请退款" value="客户申请退款"></el-option>
          <el-option label="商品问题" value="商品问题"></el-option>
          <el-option label="服务问题" value="服务问题"></el-option>
          <el-option label="订单重复" value="订单重复"></el-option>
          <el-option label="其他原因" value="其他原因"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="退款说明" prop="refundDesc">
        <el-input type="textarea" v-model="form.refundDesc" rows="3" placeholder="请输入退款说明"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible=false">取 消</el-button>
      <el-button type="primary" :loading="isSaving" @click="submitRefund">确认退款</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  emits: ['success', 'closed'],
  data() {
    return {
      visible: false,
      isSaving: false,
      order: {
        id: null,
        order_no: '',
        pay_type: null,
        pay_amount: 0,
        pay_time: '',
        useday: 0,
        available_days: 0
      },
      form: {
        refundAmount: 0,
        refundReason: '',
        refundDesc: ''
      },
      rules: {
        refundAmount: [
          { required: true, message: '请输入退款金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '退款金额必须大于0', trigger: 'blur' }
        ],
        refundReason: [
          { required: true, message: '请选择退款原因', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    'form.refundAmount'(val) {
      // 根据剩余天数自动计算退款金额
      const totalDays = this.order.useday + this.order.available_days;
      const refundAmount = totalDays > 0 
        ? (this.order.pay_amount * this.order.available_days / totalDays).toFixed(2)
        : 0;
      this.form.refundAmount = parseFloat(refundAmount);
    }
  },
  methods: {
    // 打开对话框
    open(orderData) {
      this.visible = true;
      this.order = { ...orderData };
      
      // 初始化退款金额
      const totalDays = this.order.useday + this.order.available_days;
      const refundAmount = totalDays > 0 
        ? (this.order.pay_amount * this.order.available_days / totalDays).toFixed(2)
        : 0;
      this.form.refundAmount = parseFloat(refundAmount);
      
      return this;
    },
    
    // 提交退款
    async submitRefund() {
      this.$refs.refundForm.validate(async (valid) => {
        if (valid) {
          this.isSaving = true;
          
          try {
            // 构建退款数据
            const refundData = {
              orderId: this.order.id,
              orderNo: this.order.order_no,
              refundAmount: this.form.refundAmount,
              refundReason: this.form.refundReason,
              refundDesc: this.form.refundDesc
            };
            

           
            // 调用退款API
            const res = await this.$API.common.util.post("/buss/order/refund", refundData);
            
            if (res.errno === 0) {
              this.$message.success('退款成功');
              this.$emit('success');
              this.visible = false;
            } else {
              this.$alert(res.message || '退款失败', '提示', { type: 'error' });
            }
          } catch (error) {
            console.error('退款处理失败', error);
            this.$alert('退款处理失败', '提示', { type: 'error' });
          } finally {
            this.isSaving = false;
          }
        } else {
          return false;
        }
      });
    }
  }
}
</script>

<style scoped>
.order-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  width: 80px;
  color: #606266;
}

.value {
  flex: 1;
  color: #303133;
}

.price {
  font-weight: bold;
  color: #f56c6c;
}

.refund-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  margin-left: 5px;
}
</style>