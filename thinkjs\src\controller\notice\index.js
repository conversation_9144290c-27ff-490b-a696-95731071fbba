
import API from 'wechat-api';
export default class extends think.Controller {
    async flownoticeAction(){
        let api = new API( this.config("appid") ,this.config("appsecret"));
        let model=this.model("msg");
        let usermodel=this.model("user");
        let res=await model.where({state:0}).select();
        for(let tmp of res){

            //通知待办人
            let sql="select rp.status,rp.sponsor_ids,f.flow_name,f.type,rp.js_time,r.uid,rp.run_id from sys_wf_run_process rp join sys_wf_run  r on rp.run_id=r.id join sys_wf_flow f on f.id= rp.run_flow where  rp.id="+tmp['data'];
            let obj=await model.query(sql);
            if(!think.isEmpty(obj)){
                let userids=obj[0].sponsor_ids.split(",");
                for(var tuser of userids){
                    let user=await usermodel.where({aid:tuser}).find(); //待办人

                    let user2=await usermodel.where({aid:obj[0].uid}).find();//发起人

                    

                    if(obj[0].status==2){ //如果当前流程已经处理完成不发信息
                      await   model.where({id:tmp.id}).update({"state":1})
                        break;
                    }

                   





                    if(user2.id==user.id){ //如果待办人为自己
                      let templateId="d4YZ8jcjGChoSy726_KLQzP9D1h_FCKTxjesjlIGM1o";
                      console.log(1)
                      var data = {
                          "first": {
                            "value":"您好！您有新的待处理事项。",
                            "color":"#173177"
                          },
                          "keyword1":{
                            "value":obj[0].flow_name,
                            "color":"#173177"
                          },
                          "keyword2": {
                              "value":user2.name,
                              "color":"#173177"
                            },
                          "keyword3": {
                            "value":think.datetime(obj[0].js_time*1000),
                            "color":"#173177"
                          },
                          "keyword4": {
                              "value":"您的申请未提交成功或被退回，请您进入待办事项列表进行处理",
                              "color":"#173177"
                            },
                            

                          

                          
                          "remark":{
                            "value":"祝您幸福圆满，六时吉祥！",
                            "color":"#173177"
                          }
                       };

                       console.log(data);

                       let r= await  new Promise((resolve, reject) =>{

                          api.sendTemplate(user.openid, templateId, "http://oa.winwaynet.cn/wechat/index/", data, async function(err,res){
                              console.log(err);
                              console.log(res);
                              if(think.isEmpty(err)){
           
                                   resolve(true)
           
                              }else{
                                  resolve(false)
                              }
           
                          });
           
                      });
           
                   if(r){ //发送成功
                       await   model.where({id:tmp.id}).update({"state":1})
                   }
                        await   model.where({id:tmp.id}).update({"state":1})
                        break;
                    }



                //    console.log(tmp);


                    //通知发起人 审核 进度
                    if(user2.id!=user.id){
                                          

                      let sql2="select  sponsor_text  from sys_wf_run_process where run_id="+obj[0]['run_id']+" and id <>"+tmp['data']+" order by js_time desc limit 1"

                    let qres=await model.query(sql2);
                    let username="";
                    
                    if(!think.isEmpty(qres)){
                      username=qres[0].sponsor_text;
                    }

                    let messtr="您好！您提交的"+obj[0].flow_name+"，待"+user.name+"审核";
                    if(!think.isEmpty(username)&&username!=user2.name){

                      messtr="您好！您提交的"+obj[0].flow_name+"，"+username+"已处理完成，待"+user.name+"审核";
                    }



                    let tid="buhZa6jMhMlPSwegrGVTAn2nuN-pJLQS29Zq_esshqw"
                    var data = {
                      "first": {
                        "value":messtr,
                        "color":"#173177"
                      },
                      "keyword1":{
                        "value":obj[0].flow_name,
                        "color":"#173177"
                      },
                      "keyword2": {
                          "value":"待审核",
                          "color":"#173177"
                        },
                      "keyword3": {
                        "value":think.datetime(obj[0].js_time*1000),
                        "color":"#173177"
                      },


                      

                      
                      "remark":{
                        "value":"祝您幸福圆满，六时吉祥！",
                        "color":"#173177"
                      }
                    };


                    console.log(data)

                    let rr= await  new Promise((resolve, reject) =>{

                      api.sendTemplate(user2.openid, tid, "http://oa.winwaynet.cn/wechat/index/", data, async function(err,res){
                          console.log(err);
                          console.log(res);
                          if(think.isEmpty(err)){

                                resolve(true)

                          }else{
                              resolve(false)
                          }

                      });

                    });





                    }












                    let flag=false;

                    if(obj[0].flow_name=='借款'||obj[0].flow_name=='用章申请'||obj[0].flow_name=='请假'){
                          flag=true;
                    }else{
                      //非借 款 用章 请假 类的 不通知 王洪涛
                      if(!think.isEmpty(user)&&!think.isEmpty(user.openid)&&user.name!='王洪涛'){
                        flag=true
                      }else{
                        flag=false;
                      }
                    }


                    if(flag){
                        let templateId="d4YZ8jcjGChoSy726_KLQzP9D1h_FCKTxjesjlIGM1o";
                        console.log(1)
                        var data = {
                            "first": {
                              "value":"您好！您有新的待审核的事项。",
                              "color":"#173177"
                            },
                            "keyword1":{
                              "value":obj[0].flow_name,
                              "color":"#173177"
                            },
                            "keyword2": {
                                "value":user2.name,
                                "color":"#173177"
                              },
                            "keyword3": {
                              "value":think.datetime(obj[0].js_time*1000),
                              "color":"#173177"
                            },
                            "keyword4": {
                                "value":"",
                                "color":"#173177"
                              },
                              
                            
                            "remark":{
                              "value":"祝您幸福圆满，六时吉祥！",
                              "color":"#173177"
                            }
                         };

                         console.log(data);

                         let r= await  new Promise((resolve, reject) =>{

                            api.sendTemplate(user.openid, templateId, "", data, async function(err,res){
                                console.log(err);
                                console.log(res);
                                if(think.isEmpty(err)){
             
                                     resolve(true)
             
                                }else{
                                    resolve(false)
                                    
                                }
             
                            });
             
                        });
             
                     if(r){
                         await   model.where({id:tmp.id}).update({"state":1})
                     }
                     await   model.where({id:tmp.id}).update({"state":1})




                    }else{
                        await   model.where({id:tmp.id}).update({"state":1})

                    }
                }



               // buhZa6jMhMlPSwegrGVTAn2nuN-pJLQS29Zq_esshqw

            }






         
            





            break;
            //通知发起人










        }
        

  
  
  
  
  
        return this.json("success");
      }



    async zjlnoticeAction(){
      let api = new API( this.config("appid") ,this.config("appsecret"));
      let usermodel=this.model("user");
      let u=await usermodel.where({"name":"王洪涛"}).find();

      console.log(u);
      
        let sql="SELECT p2.process_name as nextname,f.sponsor_ids,f.run_id,f.run_flow_process as flow_process,p.process_name,f.id as run_process,r.from_table as wf_type ,r.from_id as wf_fid,w.flow_name,w.type,f.wf_action FROM `sys_wf_run_process` `f` INNER JOIN `sys_wf_flow` `w` ON `f`.`run_flow`=`w`.`id` INNER JOIN `sys_wf_run` `r` ON `f`.`run_id`=`r`.`id`  INNER JOIN  sys_wf_flow_process  p on p.id=r.run_flow_process left JOIN  sys_wf_flow_process  p2 on p2.id=p.process_to WHERE ( (f.auto_person != 5 and FIND_IN_SET("+u.aid+",f.sponsor_ids)) or(f.auto_person=5 and FIND_IN_SET("+u.aid+",f.sponsor_ids))  )  and  `f`.`status` = '0' AND `r`.`status` = '0'";

      let res=await usermodel.query(sql);
      console.log(res);
      if(res.length>0){

        let templateId="d4YZ8jcjGChoSy726_KLQzP9D1h_FCKTxjesjlIGM1o";
                       
                        var data = {
                            "first": {
                              "value":"您好！您有"+res.length+"项待审核的事项。",
                              "color":"#173177"
                            },
                            "keyword1":{
                              "value":"",
                              "color":"#173177"
                            },
                            "keyword2": {
                              "value":"",
                                "color":"#173177"
                              },
                            "keyword3": {
                              "value":"",
                              "color":"#173177"
                            },
                            "keyword4": {
                                "value":"",
                                "color":"#173177"
                              },
                              
                            
                            "remark":{
                              "value":"祝您幸福圆满，六时吉祥！",
                              "color":"#173177"
                            }
                         };


                         let r= await  new Promise((resolve, reject) =>{

                          api.sendTemplate(u.openid, templateId, "", data, async function(err,res){
                              console.log(err);
                              console.log(res);
                              if(think.isEmpty(err)){
           
                                   resolve(true)
           
                              }else{
                                  resolve(false)
                              }
           
                          });
           
                      });

      }

      this.json("ok")



    }


    async sendwechatAction(){

      let api = new API( this.config("appid") ,this.config("appsecret"));
      let model=this.model("notice");
     
      let res=await model.where({state:0,"sendtype":2,"del_flag":0}).select();
      var templateId= '0jGmeM34kQEROVEGRxHwusZoX2ZGGE9eCHg5WMmM_bs';
      var url= null;
      let usermodel=this.model("user");
      for(var item of res){
       
        let user =await usermodel.where({id:item.touserid}).find();
        console.log(user);

        var data = {
          "first": {
            "value":item.content,
            "color":"#173177"
          },
          "keyword1":{
            "value":item.bussstate,
            "color":"#173177"
          },
          "keyword2": {
            "value":item.create_date,
            "color":"#173177"
          },
          "remark":{
            "value":"祝您幸福圆满，六时吉祥！",
            "color":"#173177"
          }
       };
       
       
       if(think.isEmpty(user.openid)){

        await model.where({id:item.id}).update({"state":-1})

       }else{
        let r= await  new Promise((resolve, reject) =>{

               api.sendTemplate(user.openid, templateId, url, data, async function(err,res){
                   console.log(err);
                   console.log(res);
                   if(think.isEmpty(err)){

                        resolve(true)

                   }else{
                       resolve(false)
                   }

               });

           });

        if(r){
            await   model.where({id:item.id}).update({"state":1})
        }



       }
       
    
       

      }






        this.json("success")
    }


    async getwebAction(){
      let model=this.model("notice");
      const user = await this.session('userInfo');
      let res=await model.where({"touserid":user.id,state:0}).select();

     

      this.json(res);


    }



    async testAction(){
      let para=[
    ];

    let model=this.model("test");

    let sql="select tree  from sys_test where tree like '工程_%' GROUP BY tree" ;
    let res=await model.query(sql);

    for(var item of res){
      console.log(item);

      let arr=item.tree.split("_");

      console.log(arr);

      let brr;
      for(var one of arr){
          let r=await model.where({name:one}).find();
          if(think.isEmpty(r)){
            let m={}
            m.name=one;
            m.pid=brr;
           brr= await model.add(m);
          }else{

            brr=r.id;
          }
      }

      await model.where({tree:item.tree}).update({pid:brr});


    }




    return this.json(1);



    }

}