<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/9 15:53:28
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="场所" prop="site_info_id">
					<sc-select v-model="form.site_info_id"
					           :selectConfig="selectConfig.siteInfoId"
					           clearable  :apiObj="$API.siteInfo.list"
					           filterable :disabled="mode=='show'" :method="method.siteInfoId"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="类别" prop="type">
					<sc-select v-model="form.type"
							   :selectConfig="selectConfig.type"
							   clearable dic="equipmentType"
							   filterable :disabled="mode=='show'"
							   style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="型号" prop="model">
					<el-input v-model="form.model" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="启用日期" prop="enable_date">
					<el-date-picker
						v-model="form.enable_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="更新日期" prop="renew_date">
					<el-date-picker
						v-model="form.renew_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="禁用日期" prop="disable_date">
					<el-date-picker
						v-model="form.disable_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",
				site_info_id:"",
				type: "",

				model: "",

				enable_date: "",

				renew_date: "",

				disable_date: "",


			},
			//验证规则
			rules: {

				site_info_id: [
					{required: true, message: '请选择场所',trigger: 'change'}
				],

				type: [
					{required: true, message: '请选择类别',trigger: 'change'}
				],

				model: [
					{required: true, message: '请输入型号'}
				],

				enable_date: [
					{required: true, message: '请输入启用日期'}
				],

				renew_date: [
					{required: true, message: '请输入更新日期'}
				],

				disable_date: [
					{required: true, message: '请输入禁用日期'}
				],


			},
			selectConfig: {
				type: {
					label: 'name',
					value: 'key'
				},
				siteInfoId:{
					label: 'site_name',
					value: 'id'
				}
			},
			method:{
				siteInfoId:"post"
			}
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
