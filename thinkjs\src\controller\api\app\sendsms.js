module.exports = class extends think.Controller {

async indexAction(){

    let model3 = this.model("student");
    let res3=await model3.where({"phone":this.get("mobile")}).find();

    if(!think.isEmpty(res3)){

       return this.json({"state":0,"msg":"该手机已注册过，请核实后再注册"});
    }

     let model=this.model("sms");
        let tel=this.get("mobile");

        console.log(this.get());
    
    
        let sql="select * from buss_sms where create_date>=DATE_SUB(NOW(),INTERVAL 1 MINUTE) and mobile =%s";
    
        
    
        let sqls = model.parseSql(sql, tel);
    
       
    
        let res= await model.query(sqls);
        console.log(res);
    
        if(res.length==0){
    
          var charactors="1234567890";
    
          var value='',i,j;
          
          for(j=1;j<=6;j++){
          
          i = parseInt(10*Math.random()); 　
          
          value = value + charactors.charAt(i);
          
          }
      
        
         await model.add({"mobile":tel,"code":value,create_date:think.datetime()});
      
          var request = require('request');
          request.post({url:'https://gyytz.market.alicloudapi.com/sms/smsSend', form:{
            mobile:tel,
            param:"**验证码**:"+value+"",
            smsSignId:"84fe9b7a448e49c093d2427470c76663",
            templateId:"d4b8856026eb4ed79649d555c0d0922e"
      
      
          },
          headers: {
            "Authorization": "APPCODE 19e2c4c0c9024ab6b71c562de528d4d2",
          },
        }, function(error, response, body) {
            console.log(error)
          if (!error && response.statusCode == 200) {
            console.log(body)
      
            let res=JSON.parse(body);

            
            
          }
      
          });
      
    
    
        }
    
        return this.json({"state":1,"msg":"已发送"});
}

}