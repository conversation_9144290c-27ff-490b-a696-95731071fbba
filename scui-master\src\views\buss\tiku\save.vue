<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    fullscreen="true"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      :model="form"
      :height="height"
      :rules="rules"
      ref="dialogForm"
      label-width="80px"
      label-position="left"
    >
      <el-row :style="rowsytle">
        <el-col :span="8">
          <el-card :style="rowsytle">
            <template #header>
              <div class="card-header">
                <span>题干</span>
              </div>
            </template>
            <div style="height: 50%; overflow: auto; border: 1px solid #ddd; margin-bottom: 10px; position: relative;">
              <div style="width: 120%; height: 120%; min-width: 500px; min-height: 300px;">
                <el-image
                  style="width: 100%; height: 100%; cursor: pointer; display: block;"
                  :src="form.tm_p"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  fit="fill"
                  @click="openCropper"
                />
              </div>
            </div>

            <div style="height: 50%; overflow: auto; border: 1px solid #ddd; position: relative;">
              <div style="width: 120%; height: 120%; min-width: 500px; min-height: 300px;">
                <el-image
                  style="width: 100%; height: 100%; cursor: pointer; display: block;"
                  :src="form.tm_p_new"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  fit="fill"
                />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card style="height: 100%">
            <template #header>
              <div class="card-header">
                <span>解析</span>
              </div>
            </template>
            <el-image
              style="height: 100%"
              :src="form.jx_p"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              fit="cover"
            />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card style="height: 100%">
            <template #header>
              <div class="card-header">
                <span>答案</span>
              </div>
            </template>
            <el-row style="height: 33%">
              <el-col>

                 <span style="font-size:18px">单选题</span>

                <el-form-item label="答案">
                    <el-radio-group v-model="form.ans">
                      <el-radio
                        v-for="item in dxlist"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.value }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="">
                    <el-button icon="el-icon-plus" @click="dxadd" circle />

                    <el-button icon="el-icon-check" @click="savedx2"
                      >保存并读取下一个</el-button
                    >
                  </el-form-item>


                  <el-divider />


                 <span style="font-size:18px">多选题</span>

                
                  <el-form-item label="答案">
                    <el-checkbox-group v-model="form.ans2">
                      <el-checkbox
                        v-for="item in duoxuanlist"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.value }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="">
                    <el-button icon="el-icon-plus" @click="duoxuanadd" circle />

                    <el-button icon="el-icon-check" @click="saveduoxuan2"
                      >保存并读取下一个</el-button
                    >
                  </el-form-item>

  <el-divider />
             <span style="font-size:18px">填空题</span>
                <el-form-item label="">
                  <el-button icon="el-icon-check" @click="savetk"
                    >保存并读取下一个</el-button
                  >
                </el-form-item>
  <el-divider />
             <span style="font-size:18px">解答题</span>

        
                <el-form-item label="">
                 
                  <el-button icon="el-icon-check" @click="savejd2"
                    >保存并读取下一个</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>

         

        
          </el-card>
        </el-col>
      </el-row>
    </el-form>

    <div  style="    position: absolute;
    right: 60px;
    top: 42px;">
          <el-button icon="el-icon-back" @click="before"
                      >上一题</el-button
                    >
                      <el-button icon="el-icon-right" @click="next"
                      >下一题</el-button
                    >
    </div>
    <template #footer>
      <el-button @click="close()">取 消</el-button>
    </template>

    <!-- 裁剪对话框 -->
    <el-dialog 
      title="题干图片裁剪" 
      v-model="cropperVisible" 
      width="600px" 
      destroy-on-close
      append-to-body
    >
      <sc-cropper 
        :src="cropperImageSrc" 
        :compress="0.8" 
        :aspectRatio="NaN"
        ref="cropper"
        v-if="cropperVisible"
      />
      <template #footer>
        <el-button @click="cropperVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveCroppedImage" :loading="uploadLoading">
          保存裁剪
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>




<script>
import scCropper from '@/components/scCropper'

export default {
  emits: ["success", "closed"],
  components: {
    scCropper
  },
  data() {
    return {
      array: [1],
      height: "",
      rowsytle: {
        height: "",
      },
      mode: "add",
      titleMap: {
        add: "上传",
        edit: "编辑",
      },
      url: "/static/upload/26b6ea56-7049-425a-a659-64defef541bf/t4.png",
      btnloading: false,
      visible: false,
      isSaveing: false,
      menuList: [],
      fileViewList: [],
      uploadApi: this.$API.common.upload,

      ifprocess: false,
      dxlist: [{ value: "A" }, { value: "B" }, { value: "C" }, { value: "D" }],

      duoxuanlist: [
        { value: "A" },
        { value: "B" },
        { value: "C" },
        { value: "D" },
      ],

      cropperVisible: false,
      cropperImageSrc: '',
      uploadLoading: false,
      form: {
        id: "",
        tm_p: "",
        jx_p: "",
        score: "",
        tm_p_new: "",
        ans: "",
        ans2: [],
        value: [],
        lessonid: 0,
      },
      rules: {
        name: [{ required: true, message: "请输入", trigger: "blur" }],

        parent: [{ required: true, trigger: "blur", message: "请选择" }],
      },
      dict: [],
      dicProps: {
        value: "id",
        label: "name",
        checkStrictly: true,
        emitPath: false,
      },
      props: {
        menu: {
          type: Object,
          default: () => {},
        },
      },
      selectConfig: {
        score: {
          label: "name",
          value: "name",
        },
      },
      menuProps: {
        value: "id",
        emitPath: false,
        label: "title",
        checkStrictly: true,
      },
    };
  },
  mounted() {
    this.getDic();
    this.getOffice();
  },
  created() {
    this.height = document.documentElement.clientHeight - 180;

    this.rowsytle.height = this.height + "px";
  },
  methods: {
    async savedx() {
      if (!this.$TOOL.isEmpty(this.form.ans)) {
        await this.$API.tk.savedx.post({
          id: this.form.id,
          ans: this.form.ans,
        });
        this.$message.success("保存成功");
      } else {
        this.$alert("请选择正确答案", "提示", { type: "error" });
      }
    },

    async saveduoxuan() {
      if (!this.$TOOL.isEmpty(this.form.ans2)) {
        await this.$API.tk.saveduoxuan.post({
          id: this.form.id,
          ans: this.form.ans2.join(","),
        });
        this.form.ans2 = [];
        this.$message.success("保存成功");
      } else {
        this.$alert("请选择正确答案", "提示", { type: "error" });
      }
    },

    async savedx2() {
      if (!this.$TOOL.isEmpty(this.form.ans)) {
        await this.$API.tk.savedx.post({
          id: this.form.id,
          ans: this.form.ans,
        });
        await this.next();
        this.$message.success("保存成功");
      } else {
        this.$alert("请选择正确答案", "提示", { type: "error" });
      }
    },

    async saveduoxuan2() {
      if (!this.$TOOL.isEmpty(this.form.ans2)) {
        await this.$API.tk.saveduoxuan.post({
          id: this.form.id,
          ans: this.form.ans2.join(","),
        });
        await  this.next();
        this.form.ans2 = [];
        this.$message.success("保存成功");
      } else {
        this.$alert("请选择正确答案", "提示", { type: "error" });
      }
    },

    async savetk() {
      await this.$API.tk.savetk.post({ id: this.form.id, ans: "" });
      await  this.next();
      this.$message.success("保存成功");
    },
    async savetk2() {
      if (!this.$TOOL.isEmpty(this.form.value)) {
        await this.$API.tk.savetk.post({
          id: this.form.id,
          ans: JSON.stringify(this.form.value),
        });

        this.$message.success("保存成功");
      } else {
        this.$alert("请填写正确答案", "提示", { type: "error" });
      }
    },

    close() {
      this.$emit("success", this.form, this.mode);
      this.visible = false;
    },

    async savejd() {
      await this.$API.tk.savejd.post({ id: this.form.id });
      console.log("====", this.form.value);
      this.$message.success("保存成功");
    },
    async savejd2() {
      await this.$API.tk.savejd.post({ id: this.form.id });
      await  this.next();
      this.$message.success("保存成功");
    },

    async getnext() {
      let next = await this.$API.tk.getnext2.get({
        lessonid: this.form.lessonid,
        num:this.form.num
      });
      if (next.length == 0) {
        this.$alert("本分类无待处理内容", "提示", { type: "error" });
      } else {
        this.form.ans = "";
        this.array = [1];
        this.form.value = [];
        this.form.id = next[0].id;
        this.form.tm_p = next[0].tm_p;
        this.form.jx_p = next[0].jx_p;
        this.form.num=next[0].num
      }
    },

async before(){


         let next = await this.$API.tk.before.get({
        lessonid: this.form.lessonid,
        num:this.form.num,
      });
      if (next.length == 0) {
        this.$alert("已到最后！", "提示", { type: "error" });
      } else {
       this.setData(next);
      }
    },

    async next(){


         let next = await this.$API.tk.next.get({
        lessonid: this.form.lessonid,
        num:this.form.num,
      });
      if (next.length == 0) {
        this.$alert("已到最后！", "提示", { type: "error" });
      } else {
       this.setData(next);
      }
    },

    dxadd() {
      console.log(this.form.ans);
      // 生成新元素的value值
      const newValue = String.fromCharCode(65 + this.dxlist.length);

      // 添加新元素
      this.dxlist.push({ value: newValue });
    },
    async process() {
      this.btnloading = true;
      let res = await this.$API.tk.process.post({ file: this.form.zip });
      this.form.buss_id = res.data.buss_id;
      this.$message.success("解析完成");
      this.ifprocess = true;
      this.btnloading = false;
    },
    fileSuccess(response) {
      const suffix = response.data.file_name.substr(
        response.data.file_name.lastIndexOf(".") + 1
      ); // 文件后缀
      this.fileViewList.push({
        suffix: suffix,
        name: response.data.file_name,
        url: response.data.src,
        new_name: response.data.new_name,
        id: response.data.new_name,
      });
      this.$message.success(`文件上传成功`);
      this.ifprocess = false;
      return false;
    },
    beforeRemove(file) {
      this.fileViewList.map((r, index) => {
        if (r.name == file.name) {
          this.form.files = this.form.files.replace(
            "/static/upload/" + file.name + ",",
            ""
          );
          this.fileViewList.splice(index, 1);
        }
      });
    },
    async getOffice() {
      var res = await this.$API.lesson.list.get();
      this.menuList = res;
    },

    addtk() {
      this.array.push(1); //通过添加array的值，增加input的个数
    },
    del(index) {
      this.form.value.splice(index, 1); //先删除form中value对应索引的值
      this.array.splice(index, 1); //然后删除array对应索引的值，实现点击删除按钮，减少input框效果
    },

    //显示
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },
    //获取字典列表
    async getDic() {
      var res = await this.$API.dict.list.get();
      this.dict = res.data;
    },
    //表单提交方法
    submit() {
      if (this.ifprocess) {
        this.$refs.dialogForm.validate(async (valid) => {
          if (valid) {
            this.isSaveing = true;

            let res = await this.$API.tk.saveimp.post(this.form);
            this.isSaveing = false;
            if (res.state == 1) {
              this.$emit("success", this.form, this.mode);
              this.visible = false;
              this.$message.success("操作成功");
            } else {
              this.$alert(res.msg, "提示", { type: "error" });
            }
          }
        });
      } else {
        this.$alert("请先解析压缩包内容！", "提示", { type: "error" });
      }
    },
    //表单注入数据
    setData(data, mode) {
      //可以和上面一样单个注入，也可以像下面一样直接合并进去
      this.titleMap.edit = "题号：" + data.no+"类型："+data.type;
      this.form.ans = "";
      this.form.ans2 = [];
      this.form.value = [];
      this.array = [1];
      Object.assign(this.form, data);

   

      if (data.type == "单选") {
        console.log(data);
        this.form.ans = data.ans;
      }
      if (data.type == "多选") {
        this.form.ans2 = data.ans.split(",");
      }
    },

    // 打开裁剪器
    openCropper() {
      if (this.form.tm_p) {
        this.cropperImageSrc = this.form.tm_p;
        this.cropperVisible = true;
      } else {
        this.$message.warning('没有题干图片可以裁剪');
      }
    },

    // 保存裁剪后的图片
    async saveCroppedImage() {
      if (!this.$refs.cropper) {
        this.$message.error('裁剪器未初始化');
        return;
      }

      this.uploadLoading = true;
      try {
        // 获取裁剪后的文件
        this.$refs.cropper.getCropFile(async (file) => {
          try {
            // 创建FormData上传
            const formData = new FormData();
            formData.append('file', file);
            
            // 上传到服务器
            const response = await this.$API.common.upload.post(formData);

            console.log(response);
            
            if (response && response.data && response.data.src) {
              // 更新题干图片路径
              this.form.tm_p_new = response.data.src;
              

  await this.$API.common.util.post('/sys/tk/updatetmnew',{id:this.form.id,tm_p_new:this.form.tm_p_new});

              this.cropperVisible = false;
            } else {
              this.$message.error('上传失败，请重试');
            }
          } catch (error) {
            console.error('上传失败:', error);
            this.$message.error('上传失败: ' + (error.message || '未知错误'));
          } finally {
            this.uploadLoading = false;
          }
        }, 'cropped_image.jpg', 'image/jpeg');
      } catch (error) {
        console.error('裁剪失败:', error);
        this.$message.error('裁剪失败: ' + (error.message || '未知错误'));
        this.uploadLoading = false;
      }
    },
  },
};
</script>

<style></style>
