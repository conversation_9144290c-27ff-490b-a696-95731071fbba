<template>
	<el-container>
		
		<el-container>
			<el-header>
				<div class="left-panel">
				
	  <div style="margin-left:20px">
	 <el-link type="success" style="padding-left:8px">未批改总数：{{num}}</el-link>
  <el-button  style="margin-left:10px" type="primary" icon="el-icon-check" @click="table_show">开始批改</el-button>
					
				</div>
				
				</div>
				<div class="right-panel">
					
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					
					<el-table-column label="学生" prop="name" width="150" sortable='custom'></el-table-column>
			 
					<el-table-column label="科目" prop="lessonname" width="200"></el-table-column>
				 
					<el-table-column label="添加时间" prop="create_date" width="150" sortable='custom'></el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="140">
						 
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>

</template>

<script>
import saveDialog from './save'

export default {
	name: 'user',
	components: {
		saveDialog
	},
	data() {
		return {

			dialog: {
				save: false
			},
			num:0,
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.score.page,
			selection: [],
			search: {
				name: null
			},
			defaultProps: {

				label: 'name'
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup()
		this.getNum() // Initial count query
    	this.startTimer() // Start periodic queries
	},
	methods: {
		//添加


	 startTimer() {
      this.timer = setInterval(() => {
        this.getNum()
      }, 10000) // 10 seconds
    },
    // Add new method to get count
    async getNum() {
      try {
        const res = await this.$API.score.count.get()
      
          this.num = res
        
      } catch (error) {
        console.error('Failed to fetch count:', error)
      }
    },
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.teacher.del.post(reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.role.select.get();
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {

			this.$refs.table.upData(this.search);

		},
		//本地更新数据
		handleSuccess() {
			this.dialog.save = false;
			this.$refs.table.refresh();

		}
 

	 

	}
}
</script>

<style>
</style>
