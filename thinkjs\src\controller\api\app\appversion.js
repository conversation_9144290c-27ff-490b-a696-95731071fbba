module.exports = class extends think.Controller {

    async checkAction(){
        let model=this.model("appversion");
        let version=this.post("version")

        let res=await model.where({version:version}).find();

        let res2=await model.limit(1).order("id desc").select();

        if(!think.isEmpty(res)){
            if(res.id!=res2[0].id){



                return this.json({
                    "code": 0,
                    "platform": "android",
                    "version": res2[0].version,
                    "downUrl": res2[0].file,
                    "updateContent":res2[0].content,
                   "force" :res2[0].ifforce,
                })
            }else{

                return this.json({"code":1})
            }



        }else{
           
                return this.json({
                    "code": 0,
                    "platform": "android",
                    "version": res2[0].version,
                    "downUrl": res2[0].file,
                    "updateContent":res2[0].content,
                   "force" :res2[0].ifforce,
                })
        }

    }


}