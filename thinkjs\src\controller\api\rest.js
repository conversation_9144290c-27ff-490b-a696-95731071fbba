const config = require('../../config/config.js');
const _ = require('lodash');
module.exports = class extends think.Controller {
  async __before(action) {

    const that = this;
    const ignoreIndex = _.findIndex(config.ignoreUrl, function (o) {
      return that.ctx.originalUrl.startsWith(o);
    });
    if (ignoreIndex < 0) {
      that.userInfo = await that.session('userInfo');
      if(think.isEmpty(that.userInfo)||that.userInfo.name=="TokenExpiredError"){

        that.userInfo=null;
      }
      if (think.isEmpty(that.userInfo)) {
        // that.ctx.status = 401;
        return that.ctx.fail(401, '请登录后操作');
      }
    }
  }
};
