<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/9 13:43:14
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="配管站名称" prop="control_site">
					<el-input v-model="form.control_site" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="所属管理站" prop="control_belong">
					<el-input v-model="form.control_belong" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="合同开始日期" prop="contract_start_date">

					<el-date-picker
						v-model="form.contract_start_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="合同结束日期" prop="contract_end_date">
					<el-date-picker
						v-model="form.contract_end_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="日均销量任务" prop="average_task">
					<el-input v-model="form.average_task" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员姓名" prop="controller_name">
					<el-input v-model="form.controller_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="从业时间（年）" prop="controller_working_time">
					<el-input v-model="form.controller_working_time" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员生日" prop="controller_birthday">
					<el-date-picker
						v-model="form.controller_birthday"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员性别" prop="controller_gender">
					<sc-select v-model="form.controller_gender"
					           :selectConfig="selectConfig.gender"
					           clearable dic="gender"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员身份证号" prop="controller_idcard">
					<el-input v-model="form.controller_idcard" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员党派" prop="controller_party">
					<sc-select v-model="form.controller_party"
					           :selectConfig="selectConfig.party"
					           clearable dic="party"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员地址" prop="controller_address">
					<el-input v-model="form.controller_address" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="配管员电话" prop="controller_phone">
					<el-input v-model="form.controller_phone" clearable></el-input>
				</el-form-item>
			</el-col>
<!--			<el-col :lg="12">-->
<!--				<el-form-item label="配管站" prop="control_id">-->
<!--					<el-input v-model="form.control_id" clearable></el-input>-->
<!--				</el-form-item>-->
<!--			</el-col>-->
		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",

				control_site: "",

				control_belong: "",

				contract_satart_date: "",

				contract_end_date: "",

				average_task: "",

				controller_name: "",

				controller_working_time: "",

				controller_birthday: "",

				controller_gender: "",

				controller_idcard: "",

				controller_party: "",

				controller_address: "",

				controller_phone: "",

				control_id: "",

			},
			//验证规则
			rules: {

				control_site: [
					{required: true, message: '请输入配关站名称'}
				],

				control_belong: [
					{required: true, message: '请输入所属管理站'}
				],

				contract_start_date: [
					{required: true, message: '请输入合同开始日期'}
				],

				contract_end_date: [
					{required: true, message: '请输入合同结束日期'}
				],

				average_task: [
					{required: true, message: '请输入日均销量任务'}
				],

				controller_name: [
					{required: true, message: '请输入配管员姓名'}
				],

				controller_working_time: [
					{required: true, message: '请输入从业时间（年）'}
				],

				controller_birthday: [
					{required: true, message: '请输入配管员生日'}
				],

				controller_gender: [
					{required: true, message: '请输入配管员性别'}
				],

				controller_idcard: [
					{required: true, message: '请输入配管员身份证号'}
				],

				controller_party: [
					{required: true, message: '请输入配管员党派'}
				],

				controller_address: [
					{required: true, message: '请输入配管员地址'}
				],

				controller_phone: [
					{required: true, message: '请输入配管员电话'}
				],

				control_id: [
					{required: true, message: '请输入配管站'}
				],

			},
			selectConfig: {
				gender:{
					label: 'name',
					value: 'key'
				},
				education:{
					label: 'name',
					value: 'key'
				},
				party:{
					label: 'name',
					value: 'key'
				},
			}
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
