<template>

	<el-main style="padding:0 20px;">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="请求接口">{{ data.originalUrl }}</el-descriptions-item>
			<el-descriptions-item label="请求方法">{{ data.method }}</el-descriptions-item>
			<el-descriptions-item label="状态代码">{{ data.status }}</el-descriptions-item>
			<el-descriptions-item label="日志名">{{ data.log_name }}</el-descriptions-item>
			<el-descriptions-item label="日志时间">{{ data.create_date }}</el-descriptions-item>
		</el-descriptions>
		<el-collapse v-model="activeNames" style="margin-top: 20px;">
			<el-collapse-item title="错误类型" name="1" v-if="data.level =='error'">
				<el-alert :title="data.err_name" type="error"
				          :closable="false"></el-alert>
			</el-collapse-item>
			<el-collapse-item title="返回数据" name="body" v-if="data.level =='info'">
				<div class="code">
					{{ data.body }}
				</div>
			</el-collapse-item>
			<el-collapse-item title="错误堆栈" name="body" v-if="data.level =='error'">
				<div class="code">
					{{ data.err_stack }}
				</div>
			</el-collapse-item>
		</el-collapse>
	</el-main>
</template>

<!--<script setup>-->
<!--import {ref} from 'vue'-->

<!--let data = ref({})-->

<!--let aa = ref("ada1")-->
<!--const activeNames = ref(['1'])-->

<!--const typeMap = ref(-->
<!--	{-->
<!--		'info': "info",-->
<!--		'warn': "warning",-->
<!--		'error': "error"-->
<!--	}-->
<!--)-->
<!--//函数也可以直接引用,而不用返回-->
<!--const setData = (getData) => {-->

<!--	data = getData-->
<!--	console.log("12345",data)-->
<!--	aa="你啊"-->
<!--}-->

<!--defineExpose({-->
<!--	setData-->
<!--})-->
<!--</script>-->
<script>
export default {
	data() {
		return {
			data: {},
			activeNames: ['1'],
			typeMap: {
				'info': "info",
				'warn': "warning",
				'error': "error"
			}
		}
	},
	methods: {
		setData(data) {
			this.data = data
		}
	}
}
</script>

<style scoped>
.code {
	background: #848484;
	padding: 15px;
	color: #fff;
	font-size: 12px;
	border-radius: 4px;
}
</style>
