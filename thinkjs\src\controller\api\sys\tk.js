const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {



  async jxpageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const model = this.model('tk_jx');
    where['del_flag'] = 0;
   // where['state']=0;
    let order='`create_date` asc';
    let orderstr=this.get("order");
    if(orderstr=="descending"){
      order=" "+this.get("prop")+" desc";

    }

    if(orderstr=="ascending"){
      order=" "+this.get("prop")+" asc";

    }
    const response = await model.page(page, rows).where(where).order(order).countSelect();
    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      msg: ''
    };
    return this.json(respData);
  }

  async page2Action() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const model = this.model('tk');
    where['del_flag'] = 0;
   // where['state']=0;
    let order='`num` asc';
    let orderstr=this.get("order");
    if(orderstr=="descending"){
      order=" "+this.get("prop")+" desc";

    }

    if(orderstr=="ascending"){
      order=" "+this.get("prop")+" asc";

    }
    const response = await model.page(page, rows).where(where).order(order).countSelect();
    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      msg: ''
    };
    return this.json(respData);
  }

    async pageAction() {
        let respData = {};
   


        const page = this.get('page') ? this.get('page') : 1;
        const rows = this.get('pageSize') ? this.get('pageSize') : 20;
        const where = this.get('where') ? JSON.parse(this.get('where')) : {};
        const model = this.model('tk');
        where['del_flag'] = 0;

      if(!think.isEmpty(where)&&!think.isEmpty(where.lessonid)){
        await this.updatelesson(where.lessonid);

      }
       
        if(this.get("state")==1||this.get("state")==0){
          where['state']=this.get("state")
        }

        let order='`num` asc';
        let orderstr=this.get("order");
        if(orderstr=="descending"){
          order=" "+this.get("prop")+" desc";

        }

        if(orderstr=="ascending"){
          order=" "+this.get("prop")+" asc";

        }
        const response = await model.page(page, rows).where(where).order(order).countSelect();
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          msg: ''
        };
        return this.json(respData);
      }

      async removeAction() {
        let respData = {};
        const id = this.post('id') ? this.post('id') : null;
        if (think.isEmpty(id)) {
          respData = {
            code: 400,
            data: {},
            msg: '缺少必要的参数'
          };
        } else {
          const model = this.model('tk');
          let res=await model.where({id:id}).find();
          await model.where({id: id}).update({del_flag: 1, update_date: think.datetime()});


          
        const fs = require('fs');
        
        // 文件路径
        const filePath = think.ROOT_PATH +"/www/"+res.jx_p;
        
       
       
        const filePath2 = think.ROOT_PATH +"/www/"+res.tm_p;
        
      

        fs.unlinkSync(filePath);
        fs.unlinkSync(filePath2);

          await this.updatelesson(res.lessonid);
          respData = {
            code: 200,
            data: {},
            msg: '成功'
          };
        }
        return this.json(respData);
      }
      async dynamicScoring(questions, minScore, maxScore) {
        const numQuestions = questions.length;
        
        if (numQuestions === 1) {
            // 如果数组长度为1，则将该题目的评分设置为分数段的中间值
            questions[0].score = minScore;
        } else {
            const scoreInterval = ((maxScore - minScore) / (numQuestions - 1));
    
            // 设置第一个题目的评分为最低分值
            questions[0].score = minScore;
    
            // 设置中间题目的评分
            for (let i = 1; i < numQuestions - 1; i++) {
                questions[i].score = questions[i - 1].score + scoreInterval;
            }
    
            // 设置最后一个题目的评分为最高分值
            questions[numQuestions - 1].score = maxScore;
        }
    
        return questions;
    }
    

    async updatepicAction(){
      let id=this.post("id");
      let img=this.post("pic")
      let model=this.model("tk");
      await model.where({id:id}).update({"tm_p":img});


     let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)


    }

      async saveimpAction(){
        const model = this.model('tktmp');
        let lessonid=this.post("parent");
        const tkmodel = this.model('tk');
        let buss_id=this.post("buss_id")
        let lmodel = this.model("lesson")
        let jxmodel=this.model("tk_jx");

        let list= await model.where({"buss_id":buss_id,"type":"t"}).order("num asc").select();
        console.log(list);
        let lesson=await lmodel.where({"id":lessonid}).find();
        let arr=[];
        let jxarr=[];


        let selectnum =await tkmodel.where({"lessonid":lessonid}).order("num desc").limit(1).select();
      let start=1;
      if(think.isEmpty(selectnum)){
        start=1;
      }else{
        start=selectnum[0].num;
      }

        var i=1;
        for(var item of list){
          let jx=await model.where({"buss_id":buss_id,"type":"j","num":item.num}).find();

          let para={};
          para.id=think.uuid();
          para.tm=item.text;
          para.jx="";
          
        
          para.tm_p=item.file;
          para.jx_p=jx.file
          para.state=0;
          para.lessonid=lessonid;
          para.buss_id=buss_id;

          
          const numbers = item.filename.match(/\d+/g); // 使用正则表达式匹配字符串中的数字
        


            para.num=numbers[0];
          
            para.no=lesson.code2+para.num;
          i++;

         
          await this.addData(para);

          jxarr.push({
            tkid:para.id,
            name:jx.filename,
            file:jx.file,
            create_date :think.datetime(),
            type:'图片'

          })

        
          arr.push(para);
        

          await model.where({id:item.id}).delete()
          await model.where({id:jx.id}).delete()

        }

        const numbers = this.post("score").match(/\d+/g);

 
        const extractedNumbers = numbers.map(num => parseInt(num));

        console.log("===========",extractedNumbers)

        this.dynamicScoring(arr,extractedNumbers[0],extractedNumbers[1])
        console.log("===========")
        console.log(arr);
        console.log("===========")
        await tkmodel.addMany(arr);
        await jxmodel.addMany(jxarr);
       
        await this.updatelesson(lessonid);
        


     let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)
      }


      async savedxAction(){
        let model=this.model("tk");
        let para=this.post();

        await model.where({id:para.id}).update({type:"单选",ans:para.ans,"state":1})

        let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)

      }

      
      async saveduoxuanAction(){
        let model=this.model("tk");
        let para=this.post();

        await model.where({id:para.id}).update({type:"多选",ans:para.ans,"state":1})

        let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)

      }
      


      async updatetmnewAction(){
        let model=this.model("tk");
        let para=this.post();
        await model.where({id:para.id}).update({tm_p_new:para.tm_p_new});
      }


      async savetkAction(){
        let model=this.model("tk");
        let para=this.post();

        await model.where({id:para.id}).update({type:"填空",ans:para.ans,"state":1})

        let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)

      }

      async savejdAction(){
        let model=this.model("tk");
        let para=this.post();

        await model.where({id:para.id}).update({type:"解答","state":1})

        let   respData = {
          code: 200,
          state: 1,
          msg: '成功'
        };

        return this.json(respData)

      }
     

      async getfiles(path){   
        const fs = require('fs')
          return fs.readdirSync(path,{encoding:'utf8', withFileTypes:true})
      }
    




      async getnumAction(){
        let lessonid=this.get("lessonid");

        let model=this.model("tk");


        let model2=this.model("lesson")
        let selectlession=await model2.where({"id":lessonid}).find();

        let alllessids= await model2.where({"parent_ids":["like",selectlession.parent_ids+"%"]}).field("id").select();


        console.log(alllessids);



        let ids=alllessids.map(obj => obj.id);
    


        let res=await model.where({"lessonid":["in",ids],del_flag:0}).count("*");
        let max=0;
        console.log(res)
        if(!res){
          max=0;

        }else{
          max=res
        }

        return this.json({num:max});


      }


      async getnextAction(){
        let lessonid=this.get("lessonid");

        let model=this.model("tk");

        let res=await model.where({"lessonid":lessonid,del_flag:0,state:0}).order("num asc").limit(1).select();
        return this.json(res);


      }



      
      async getnext2Action(){
        let lessonid=this.get("lessonid");

        let model=this.model("tk");

        let res=await model.where({"lessonid":lessonid,del_flag:0,num:[">",parseInt(this.get("num"))]}).order("num asc").limit(1).select();
        return this.json(res);


      }

      async beforeAction(){
        let lessonid=this.get("lessonid");
        let num =this.get("num")
        let model=this.model("tk");

        let res=await model.query("select * from sys_tk where del_flag='0' and lessonid="+lessonid+"   and num =(select max(num) from sys_tk where num<"+num+" and del_flag='0')");

        return this.json(res[0]);


      }

      async nextAction(){
        let lessonid=this.get("lessonid");
        let num =this.get("num")
        let model=this.model("tk");

        let res=await model.query("select * from sys_tk where del_flag='0' and lessonid="+lessonid+"   and num =(select min(num) from sys_tk where num>"+num+" and del_flag='0')");

        return this.json(res[0]);


      }



      async processAction(){
        let file=this.post("file");
        let respData = {};
        if(think.isEmpty(file)){

          respData = {
            code: 400,
            data: {},
            msg: '缺少必要的参数'
          };
        }else{
          var admZip = require('adm-zip');


          const uploadpath = think.ROOT_PATH + '/www'+file;
        

          const admzip = new admZip(uploadpath);
          think.mkdir(uploadpath.replace(".zip","/"));


          

          admzip.extractAllTo(uploadpath.replace(".zip","/"));

          let files=await this.getfiles(uploadpath.replace(".zip","/"));

        let  filename = file.split('/').pop().replace(".zip","")
        let arr=[];
        for(var tmp of files){
          let one={};
          one.id=think.uuid();
          one.buss_id=filename;
          one.file=file.replace(".zip","/")+tmp.name;
          one.filename=tmp.name;
          let str=tmp.name;
          
          if(str.indexOf("t")>=0){

            one.type="t"
          }
          if(str.indexOf("j")>=0){

            one.type="j"
          }

          
        //  let tk={};
         
        //  tk.id=think.uuid();
        //  tk.
         
          const numbers = tmp.name.match(/\d+/g);
          one.num=numbers[0];
          arr.push(one);


          console.log(arr);
        }

        let model2=this.model("tktmp")
        await model2.where({"buss_id":filename}).delete();
        await model2.addMany(arr);


          var AipOcrClient = require("baidu-aip-sdk").ocr;

          // 设置APPID/AK/SK
          var APP_ID = "9689501";
          var API_KEY = "QCptgVCgKuhWRRGv74Tg6EjG";
          var SECRET_KEY = "eGcUMS6NTYmaUssbqRLXEcGvPySF4iFx";
          
          // 新建一个对象，建议只保存一个对象调用服务接口
          var client = new AipOcrClient(APP_ID, API_KEY, SECRET_KEY);
          


          let t=await model2.where('type="t" and (text = "" OR  text is null ) ' ).find();
          console.log(t);
      
          while(!think.isEmpty(t)){
             
               
            
             var fs = require('fs');
             var image =fs.readFileSync( think.ROOT_PATH +"/www"+t.file).toString("base64");
             let text="";
      await       client.generalBasic(image).then(function(result) {
           
                 console.log(result);

                 if(!think.isEmpty(result.words_result)){
                  for(let r of result.words_result){
                    text=text+r.words;
                        
        
                  }
                  return text;

                 }else{
                  return "";
                 }
                  
                    
                   console.log(text);
               }).then(function(text){
                model2.where({"id":t.id}).update({"text":text});
               }).catch(function(err) {
                   // 如果发生网络错误.childs
                   console.log(err);
                     //tmps.push(t);
               });
               t= await model2.where('type="t" and (text = "" OR  text is null ) ' ).find();
               
 
             
         }
 


          respData = {
            code: 200,
            data: {buss_id:filename},
            msg: '成功'
          };

        }

        return this.json(respData);
      }

}