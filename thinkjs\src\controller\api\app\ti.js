
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {



    async jjAction(){
        let tkid=this.post("tkid")
        let model=this.model("tk")
        const user = await this.session('userInfo');
        let res =await model.where({"id":tkid}).find();

        await this.processdotinumjj(res.lessonid,user.school,tkid)


        let model3=this.model("jj");





        let res3=await model3.where({"tkid":tkid,"schoolid":user.school,"create_date":think.datetime(new Date(), 'YYYY-MM-DD')}).find();

        if(think.isEmpty(res3)){

            let data={};
            data.tkid=tkid;
            data.schoolid=user.school;
            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
            data.lesson=res.lessonid;
                data.num=1;
            
    
            await model3.add(data)

        }else{
            await model3.where({id:res3.id}).increment("num", 1)

        }


        let data2={}
        data2.tkid=tkid;
        data2.schoolid=user.school;
        data2.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
        data2.lesson=res.lessonid;
        data2.uid=user.id;
        await this.model("jj_record").add(data2);

        return this.json({"state":1})


    }







    //更新当前lesson
    async processdotinumjj(lessionid,schoolid,tkid){
        let model=this.model("lesson_school");
    
    
      
    
    
        let res=await model.where({lessonid:lessionid,schoolid:schoolid,"create_date":think.datetime(new Date(), 'YYYY-MM-DD')}).find();
        if(think.isEmpty(res)){
    
            let data={};
            data.lessonid=lessionid;
            data.schoolid=schoolid;
            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
          
                data.num3=1;
            
    
            await model.add(data)
    
        }else{
           
                await model.where({id:res.id}).increment("num3", 1)
           
        }
    
    
        await this.updateparentnum3(schoolid,lessionid);
    
    
      }
      //更新父类lesson school 表的做题数量
       async updateparentnum3(schoolid,lessionid){
          let model=this.model("lesson");
          let lesson=await model.where({id:lessionid}).find();
    
          if(lesson.parent_id!=0){
            let lessons=await model.where({parent_id:lesson.parent_id}).field("id").select();
    
    
            if(!think.isEmpty(lessons)){
                let arr=[]
                for(let item of lessons){
                    arr.push(item.id);
                }
                let sql="select IFNULL(sum(num3),0) as num3  from sys_lesson_school where  create_date='"+think.datetime(new Date(), 'YYYY-MM-DD')+"'  and schoolid="+schoolid+" and lessonid in ("+arr.join(",")+")";
                let childres=await model.query(sql);
    
                let model2=this.model("lesson_school");
    
                let res=await model2.where({schoolid:schoolid,lessonid:lesson.parent_id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();
                if(think.isEmpty(res)){
    
                    let data={};
                    data.schoolid=schoolid;
                    data.lessonid=lesson.parent_id;
                    data.num3=childres[0]['num3']
    
                    data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
                   
                    await model2.add(data);
                }else{
    
                    await model2.where({id:res.id}).update({num3:childres[0]['num3']});
    
    
    
                }
    
            }
    
    
          await  this.updateparentnum3(schoolid,lesson.parent_id);
    
    
            
            
          }
    
       }













    //获取题
    async gettiAction() {
        let model = this.model("lesson_user");
        let lessonid = this.post("lessonid");
        const user = await this.session('userInfo');
        //  let res=await model.where({"parent_id":"0","del_flag":"0"}).order("sort asc").field("id,name").select();\
        let tilist = {};



        let res = await model.where({ "lessonid": lessonid, "userid": user.id }).find();

        if (think.isEmpty(res)) {
            let para = {};
            para.userid = user.id;
            para.lessonid = lessonid;
            para.state = 0;
            para.score = 50;
            await model.add(para);
            tilist.state = 0;
            tilist.score=50;
            tilist.data = await this.gettestti(lessonid);

        } else {
            if (res.state == 1) {
                tilist.state = 1;
                tilist.data = await this.zsti(lessonid, res.score,res);
                tilist.score=res.score;
                tilist.tisetup=res.tisetup3;

            } else {



                tilist.data = await this.gettestti(lessonid);
                tilist.state = 0;
                tilist.score=res.testscore
                tilist.tisetup=res.tisetup2;

            }

        }








        return this.json(tilist);

    }


     processinsql(tilist){
    let sql=" and id not in ( "

    let arr=[];
    for(let a of tilist){
        arr.push(a.id)

    }
    sql=sql+" "+ `'${arr.join("', '")}'`
    sql=sql+" )"
    return sql;


   }
    async getnodo(lessonid, userid, score, type, neednum) {


        let config=this.config("ti")
        let score1 = parseInt(score) - parseInt(config.score1);
        let score2 = parseInt(score) + parseInt(config.score1);



        let sql = "SELECT * FROM `sys_tk` where state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid + " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " ) ORDER BY  times asc limit " + neednum;


        let model = this.model("tk");
        let tilist = await model.query(sql);
        console.log("===============", sql)
        //获取错题库
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            let sql2 = "SELECT * FROM `sys_tk` where  state=1  and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+" and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc limit " + tmpnum;

            let tilist2 = await model.query(sql2);

            console.log("========", sql2);
            tilist = tilist.concat(tilist2);
        }

        //获取对题库
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            let sql3 = "SELECT * FROM `sys_tk` where   state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY   times asc limit " + tmpnum;

            let tilist3 = await model.query(sql3);

            console.log("========", sql3);
            tilist = tilist.concat(tilist3);
        }


        //二段分值

        score1 = parseInt(score) - parseInt(config.score2);
        score2 = parseInt(score) + parseInt(config.score2);


        //未做题
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY   times asc limit " + tmpnum;
          let  tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }




        //错题库

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc limit " + tmpnum;;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY  times asc limit " + tmpnum;;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }



        //三段分值

        score1 = parseInt(score) - parseInt(config.score3);
        score2 = parseInt(score) + parseInt(config.score3);


        //未做题
        if (tilist.length < neednum) {

            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id not in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + ") ORDER BY   times asc limit " + tmpnum;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }




        //错题库

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=0) ORDER BY   times asc limit " + tmpnum;;
            let   tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }

        if (tilist.length < neednum) {
            let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid +this.processinsql(tilist)+ " and id  in (select tkid from sys_tk_user where userid=" + userid + " and lessonid=" + lessonid + " and state=0 and type=1) ORDER BY  times asc limit " + tmpnum;;
            let    tilist3 = await model.query(sql);

            tilist = tilist.concat(tilist3);
        }


//不可出题


        score1 = parseInt(score) - parseInt(config.score1);
        score2 = parseInt(score) + parseInt(config.score1);

         if (tilist.length < neednum) {
             
             let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0  and score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid + " and id  in (select tkid from sys_tk_user where state=1 and userid=" + userid + " and lessonid=" + lessonid + " ) ORDER BY  times asc limit " + tmpnum;
            let    tilist4 = await model.query(sql);

            tilist = tilist.concat(tilist4);
                     
         }
         
         
        score1 = parseInt(score) - parseInt(config.score2);
        score2 = parseInt(score) + parseInt(config.score2);

         if (tilist.length < neednum) {
             
             let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and  score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid + " and id  in (select tkid from sys_tk_user where state=1 and userid=" + userid + " and lessonid=" + lessonid + " ) ORDER BY  times asc limit " + tmpnum;
            let    tilist4 = await model.query(sql);

            tilist = tilist.concat(tilist4);
                     
         }
         
         score1 = parseInt(score) - parseInt(config.score3);
        score2 = parseInt(score) + parseInt(config.score3);

         if (tilist.length < neednum) {
             
             let tmpnum = parseInt(neednum) - tilist.length;
            sql = "SELECT * FROM `sys_tk` where  state=1 and  del_flag=0 and   score <=" + score2 + " and score >=" + score1 + " and " + type + " and lessonid=" + lessonid + " and id  in (select tkid from sys_tk_user where state=1 and userid=" + userid + " and lessonid=" + lessonid + " ) ORDER BY  times asc limit " + tmpnum;
            let    tilist4 = await model.query(sql);

            tilist = tilist.concat(tilist4);
                     
         }
         
         
         

        return tilist;



    }

    async zsti(lessonid, score,lesson_user) {
        let model = this.model("tk");


        let modeljx=this.model("tk_jx")


        let model2=this.model("lesson_user");
      
        const user = await this.session('userInfo');
        let config = this.config("ti")
        let list=[];
        if(lesson_user.tisetup<=4){

             list = await this.getnodo(lessonid, user.id, score, "(type ='单选' or type='多选')", 1)
        }

        if(lesson_user.tisetup>4&&lesson_user.tisetup<7){

             list = await this.getnodo(lessonid, user.id, score, "type ='填空' ", 1)
        }

       // let res = await model.where({ "score": ['BETWEEN', 40, 100], "lessonid": lessonid, "type": ['IN', ['单选', "填空"]] }).order("rand()").limit(10).select();
        
       if(lesson_user.tisetup==7){

            list =  await this.getnodo(lessonid, user.id, score, "type ='解答' ",1)
        }

       
      

     



        for (var item of list) {


            item.op = [
                {
                    title: "A",
                    checked: false
                },
                {
                    title: "B",
                    checked: false
                },
                {
                    title: "C",
                    checked: false
                },
                {
                    title: "D",
                    checked: false
                },

            ]


           


        }

        let res=list;
        
        for(var item of res){
            
             let jxlist=await modeljx.where({"tkid":item.id,"del_flag":0}).select();
            if(!think.isEmpty(jxlist)){
                let jxcontent="";
                for(var jxitem of jxlist){
                    if(jxitem.type=="图片"){

                        jxcontent=jxcontent+"<div><img name='jx"+jxitem.id+"' src='"+this.config("domain")+jxitem.file+"' widht='100%' /></div>"
                    }
                    if(jxitem.type=="视频"){

                        jxcontent=jxcontent+"<div><video  name='jx"+jxitem.id+"' src='"+this.config("domain")+jxitem.file+"' widht='100%' /></div>"
                    }
                }
               
                item.jxcontent=jxcontent;
                console.log(item.jxcontent);
            }

             item.tmcontent="<div><img  src='"+this.config("domain")+item.tm_p+"' widht='100%' /></div>"
            
        }
        
        


        
        console.log(res);
        await this.cache('zsti', res);
        return res;
    }


    async gettestti(lessonid) {
        let model = this.model("tk");
        let    modeljx=this.model("tk_jx");

        let lmodel=this.model("lesson_user")
        const user = await this.session('userInfo');
        let lesson=await lmodel.where({"lessonid":lessonid,"userid":user.id}).find();
        let score=lesson.testscore;

        if(lesson.tisetup2>10){



            return [];
        }
        let config=this.config("ti")
        let score1 = parseInt(score) - parseInt(config.score1);
        let score2 = parseInt(score) + parseInt(config.score1);

        let neednum=1;

        let res = await model.where({ "state":1,"del_flag":0,"score": ['BETWEEN', score1, score2], "lessonid": lessonid, "type": ['IN', ['单选', "多选"]] }).order("rand()").limit(1).select();
        if (res.length < neednum) {
            let tmpnum = parseInt(neednum) - res.length;

             score1 = parseInt(score) - parseInt(config.score2);
             score2 = parseInt(score) + parseInt(config.score2);


            let res2 = await model.where({ "state":1,"del_flag":0,"score": ['BETWEEN', score1, score2], "lessonid": lessonid, "type": ['IN', ['单选', "多选"]] }).order("rand()").limit(tmpnum).select();


            res=res.concat(res2)
        }
        if (res.length < neednum) {

            let   tmpnum = parseInt(neednum) - res.length;

            score1 = parseInt(score) - parseInt(config.score3);
            score2 = parseInt(score) + parseInt(config.score3);


            let   res2 = await model.where({ "state":1,"del_flag":0,"score": ['BETWEEN', score1, score2], "lessonid": lessonid, "type": ['IN', ['单选', "多选"]] }).order("rand()").limit(tmpnum).select();


           res=res.concat(res2)

        }




        for (var item of res) {


            item.op = [
                {
                    title: "A",
                    checked: false
                },
                {
                    title: "B",
                    checked: false
                },
                {
                    title: "C",
                    checked: false
                },
                {
                    title: "D",
                    checked: false
                },

            ]

             

                 let jxlist=await modeljx.where({"tkid":item.id,"del_flag":0}).select();
                if(!think.isEmpty(jxlist)){

                    let jxcontent="";
                    for(var jxitem of jxlist){
                        if(jxitem.type=="图片"){

                            jxcontent=jxcontent+"<div><img name='jx"+jxitem.id+"' src='"+this.config("domain")+jxitem.file+"' widht='100%' /></div>"
                        }
                        if(jxitem.type=="视频"){

                            jxcontent=jxcontent+"<div><video  name='jx"+jxitem.id+"' src='"+this.config("domain")+jxitem.file+"' widht='100%' /></div>"
                        }
                    }

                    item.jxcontent=jxcontent;
                    console.log(item.jxcontent);
                }
               item.tmcontent="<div><img  src='"+this.config("domain")+item.tm_p+"' widht='100%' /></div>"

        }
        
         await this.cache('testti', res);

        return res;
    }


    async resetlessonscoreAction() {

        let model = this.model("lesson_user");
        let lessonid = this.post("lessonid");
        const user = await this.session('userInfo');
       

        let lessonmodel = this.model("lesson")
        let lesson = await lessonmodel.where({ id: lessonid }).find();

        let res = await model.where({ "userid": user.id, "lessonid": lessonid }).find();


        await model.where({ "id": res.id }).update({ "testscore": 50, "state": 0,"tisetup2":1 });



        // let sql = "select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=" + lesson.parent_id + " order by sort asc) as a where a.id=" + lesson.id;
        // let nexttmp = await lessonmodel.query(sql);

        // console.log(nexttmp[0]);

        // let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();

        // if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {


        //     let para = {};
        //     para.userid = user.id;
        //     para.lessonid = netxlesson.id;
        //     para.state = 0;
        //     para.score = 50;
        //     await model.add(para);


        // }









        this.json({ "state": 1 });
    }


    async updatelessonscoreAction() {

        let model = this.model("lesson_user");
        let lessonid = this.post("lessonid");
        const user = await this.session('userInfo');
        let score = this.post("score");

        let lessonmodel = this.model("lesson")
        let lesson = await lessonmodel.where({ id: lessonid }).find();

        let res = await model.where({ "userid": user.id, "lessonid": lessonid }).find();


        await model.where({ "id": res.id }).update({ "score": score, "state": 1,"lasttime":think.datetime() });



        // let sql = "select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=" + lesson.parent_id + " order by sort asc) as a where a.id=" + lesson.id;
        // let nexttmp = await lessonmodel.query(sql);

        // console.log(nexttmp[0]);

        // let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();

        // if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {


        //     let para = {};
        //     para.userid = user.id;
        //     para.lessonid = netxlesson.id;
        //     para.state = 0;
        //     para.score = 50;
        //     await model.add(para);


        // }









        this.json({ "state": 1 });
    }




    async dotesttiAction() {

        let ticonfig = this.config("ti");
        let para = {};
        para.tkid = this.post("tkid");
        const user = await this.session('userInfo');
        para.userid = user.id;
        para.flag = this.post("flag");
        para.create_date = think.datetime();
        para.usetime = this.post("usetime");

        let model = this.model("tk_record");

        let tkmodel = this.model("tk");
        let lmodel = this.model("lesson_user");



        let tires = await tkmodel.where({ id: para.tkid }).find();
   

        let lesson_userres= await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).find();
        let score=lesson_userres.testscore;
        if (para.flag == 0) {
            
            score=score-3;
            
           // await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).decrement("score", 3)

        } else {
            score=score+3;
            //await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).increment("score", 3)
        }
        if(score<0){
            
            score=0;
        }
        if(score>100){
            
            score=100;
        
        }



        let tisetup2=lesson_userres.tisetup2+1

         await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).update({"testscore":score,'tisetup2':tisetup2
            ,"lasttime":think.datetime()
         });


         return this.json({"score":score})
    
    
    }










    //更新当前lesson更新作对做错统计
  async processdotinum(lessionid,schoolid,flag,tkid){
    let model=this.model("lesson_school");


    let model2=this.model("tk_school");

    let res2=await model2.where({schoolid:schoolid,"tkid":tkid}).find();

    if(think.isEmpty(res2)){
         let data2={}
         data2.tkid=tkid;
         data2.schoolid=schoolid;
         if(flag==1){
            data2.num1=1
        }else{
            data2.num2=1;
        }

        await model2.add(data2)

    }else{

        if(flag==1){
            await model2.where({id:res2.id}).increment("num1", 1)
        }else{
            await model2.where({id:res2.id}).increment("num2", 1)
        }
    }


    let res=await model.where({lessonid:lessionid,schoolid:schoolid,"create_date":think.datetime(new Date(), 'YYYY-MM-DD')}).find();
    if(think.isEmpty(res)){

        let data={};
        data.lessonid=lessionid;
        data.schoolid=schoolid;
        data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
        if(flag==1){
            data.num1=1
        }else{
            data.num2=1;
        }

        await model.add(data)

    }else{
        if(flag==1){
            await model.where({id:res.id}).increment("num1", 1)
        }else{
            await model.where({id:res.id}).increment("num2", 1)
        }
    }


    await this.updateparent(schoolid,lessionid);


  }
  //更新父类lesson school 表的做题数量
   async updateparent(schoolid,lessionid){
      let model=this.model("lesson");
      let lesson=await model.where({id:lessionid}).find();

      if(lesson.parent_id!=0){
        let lessons=await model.where({parent_id:lesson.parent_id}).field("id").select();


        if(!think.isEmpty(lessons)){
            let arr=[]
            for(let item of lessons){
                arr.push(item.id);
            }
            let sql="select IFNULL(sum(num1),0) as num1 ,IFNULL(sum(num2),0) as num2 from sys_lesson_school where  create_date='"+think.datetime(new Date(), 'YYYY-MM-DD')+"'  and schoolid="+schoolid+" and lessonid in ("+arr.join(",")+")";
            let childres=await model.query(sql);

            let model2=this.model("lesson_school");

            let res=await model2.where({schoolid:schoolid,lessonid:lesson.parent_id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();
            if(think.isEmpty(res)){

                let data={};
                data.schoolid=schoolid;
                data.lessonid=lesson.parent_id;
                data.num1=childres[0]['num1']

                data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
                data.num2=childres[0]['num2']
                await model2.add(data);
            }else{

                await model2.where({id:res.id}).update({num1:childres[0]['num1'],num2:childres[0]['num2']});



            }

        }


      await  this.updateparent(schoolid,lesson.parent_id);


        
        
      }

   }




    async dotiAction() {

        let ticonfig = this.config("ti");
        let para = {};
        para.tkid = this.post("tkid");
        const user = await this.session('userInfo');
        para.userid = user.id;
        para.flag = this.post("flag");
        para.create_date = think.datetime();
        para.usetime = this.post("usetime");



        let dayrecord=this.model("dayrecord");
        let dayrecordres=await dayrecord.where({uid:user.id,create_date:think.datetime(new Date(), 'YYYY-MM-DD')}).find();
        if(think.isEmpty(dayrecordres)){
            let data={};
            data.uid=user.id;
            data.create_date=think.datetime(new Date(), 'YYYY-MM-DD');
           
            await dayrecord.add(data);
        }
      

        let tkmodel = this.model("tk");
        let lmodel = this.model("lesson_user");
        let model = this.model("tk_record");


        let tires = await tkmodel.where({ id: para.tkid }).find();

        para.pic=this.post("pic");

        para.res=this.post("res");
        

        para.lessonid=tires.lessonid;
        para.schoolid=user.school;



        await tkmodel.where({ id: para.tkid }).increment("times", 1);


        let lesson_userres= await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).find();
        let score=lesson_userres.score;


        await this.processdotinum(tires.lessonid,user.school,para.flag,para.tkid)
        if (para.flag == 0) {
            
            score=score-3;
            



           // await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).decrement("score", 3)

        } else {
            score=score+3;
            //await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).increment("score", 3)
        }
        if(score<0){
            
            score=0;
        }
        if(score>100){
            
            score=100;
        
        }
        let tisetup=lesson_userres.tisetup+1

        let tisetup3=lesson_userres.tisetup3+1
        if(tisetup>7){

             tisetup=1
        }
         await lmodel.where({ "userid": user.id, "lessonid": tires.lessonid }).update({"score":score,"lasttime":think.datetime,'tisetup':tisetup
         ,"tisetup3":tisetup3,"lasttime":think.datetime()});
         lesson_userres.score=score;
        

        

        if(parseInt(ticonfig.jsscore)<=lesson_userres.score){
           //解锁下一章节
            let lessonmodel = this.model("lesson")
            let lesson = await lessonmodel.where({ id: tires.lessonid  }).find();

                let sql = "select * from (select * , LEAD(id) OVER (ORDER BY sort asc ) AS id2 from sys_lesson   where parent_id=" + lesson.parent_id + " order by sort asc) as a where a.id=" + lesson.id;
                let nexttmp = await lessonmodel.query(sql);

                
                let netxlesson = await lessonmodel.where({ id: nexttmp[0].id2 }).find();

                if (!think.isEmpty(netxlesson) && netxlesson.ifgl == 1) {


                    let lmp =await lmodel.where({ "userid": user.id, "lessonid":netxlesson.id }).find();
                    if(think.isEmpty(lmp)){

                        let para = {};
                        para.userid = user.id;
                        para.lessonid = netxlesson.id;
                        para.state = 0;
                        para.score = 50;
                        para.lasttime=think.datetime();
                        await lmodel.add(para);
                    }

                  


                }


        }




     

        //添加做题记录
        await model.add(para);


        var curTime = new Date();
        console.log("=============");
        console.log(curTime.getSeconds());
        console.log(curTime.getSeconds() + think.config("ti").time1);

        let yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(think.config("ti").time1))));

        console.log(yxq)
        console.log("=============");

        let mode2 = this.model("tk_user");
        //查询做题记录
        let res = await mode2.where({ "tkid": para.tkid, "userid": user.id }).find();
        if (think.isEmpty(res)) {
            //第一次做
            let para2 = {};
            para2.tkid = this.post("tkid");
            const user = await this.session('userInfo');
            para2.userid = user.id;
            para2.type = this.post("flag");
            para2.lasttime = think.datetime();
            para2.num = 1;

            para2.state = 1;
            var curTime = new Date();



            para2.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(think.config("ti").time1))));

            para2.lessonid = tires.lessonid;
            //插入做题记录


            await mode2.add(para2)
            //更新题目得分
            this.updatetiscore(para2.tkid);
        } else {

            //做过
            if (this.post("flag") == res.type) {
                //根上一次状态一样
                let para3 = {};
                if (res.num < 6) {
                    para3.num = res.num + 1;

                    let ticonfig = this.config("ti");
                    let adds = ticonfig["time" + para3.num];
                    var curTime = new Date();


                    para3.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(adds))))
                    para3.state = 1;

                    await mode2.where({ tkid: this.post("tkid") }).update(para3);



                }



            } else {
                //跟上一次状态不一样

                let para3 = {};

                para3.num = 1;

             
                let adds = ticonfig["time1"];
                var curTime = new Date();
                para3.yxq = think.datetime(new Date(curTime.setSeconds(curTime.getSeconds() + parseInt(adds))))
                para3.state = 1;


                await mode2.where({ tkid: para.tkid }).update(para3);






            }


            this.updatetiscore(para.tkid);

        }

        return this.json({"score":score});


    }



    async updatetiscore(tiid) {
        let model = this.model("tk");

        let model2 = this.model("tk_record")

        let num = think.config('ti').num1;

        let res = await model2.where({ "tkid": tiid }).count("id");
        if (res >= num) {


            let res2 = await model2.where({ "tkid": tiid, flag: 0 }).count("id");

            let socre = res2 / res * 100;

            await model.where({ id: tiid }).update({ "score": socre });



        }



    }
    
    
       async curAction(){
        let lessonid=this.post("lessonid")
        let curtino=this.post("curtino");
        
        await this.cache("lessonid",lessonid);
        await this.cache("curtino",curtino);
        
        return this.json(1);
    }
    
}