const _ = require('lodash');
const moment = require('moment');
const Decimal = require("decimal.js");
module.exports = {
  /**
   * 数组转换为树
   * @param list 原始数组
   * @param idstr 子id属性名
   * @param pidstr 父id属性名
   * @returns {*[]}
   */
  transTreeArray(list, idstr, pidstr) {
    const result = [];
    const temp = {};
    for (let i = 0; i < list.length; i++) {
      temp[list[i][idstr]] = list[i]; // 将数组转成对象类型
    }
    for (let j = 0; j < list.length; j++) {
      const tempVp = temp[list[j][pidstr]]; // 获取每一个子对象的父对象
      if (tempVp) {
        // 判断父对象是否存在，如果不存在直接将对象放到第一层
        if (!tempVp['children']) {
          tempVp['children'] = [];
        } // 如果父元素的nodes对象不存在，则创建数组
        tempVp['children'].push(list[j]); // 将本对象压入父对象的nodes数组
      } else {
        result.push(list[j]); // 将不存在父对象的对象直接放入一级目录
      }
    }

    return result;
  },
  md5(str) {
    return think.md5(str) + 'jsxdev';
  },

  /**
   * 查询基本条件
   * @param data
   */
  selectWhere(data) {
    if (think.isEmpty(data)) {
      data = {del_flag: 0};
    } else {
      if (think.isArray(data)) {
        data.map(r => {
          r.del_flag = 0;
        });
      } else {
        data.del_flag = 0;
      }
    }
    return data;
  },

  /**
   * 自动更新热度
   * @param bussid
   * @param type
   */

  async updateclick(bussid, type) {
    const userInfo = await this.session('userInfo');
    const model = this.model('clicksort');

    const res = await model.where({bussid: bussid, type: type, userid: userInfo.id}).find();
    if (think.isEmpty(res)) {
      const data = {};
      data.id = think.uuid();
      data.userid = userInfo.id;
      data.bussid = bussid;
      data.type = type;
      data.click = 1;
      await model.add(data);
    } else {
      await model.where({id: res.id}).increment('click', 1);
    }
  },

  /**
   * 添加基本数据
   * @param data
   */










  async updatelesson(lessonid){
      let model=this.model("lesson");
      let model2=this.model("tk");

      let count1=await model2.where({"lessonid":lessonid,"del_flag":0}).count();
      let count2 =await model2.where({"lessonid":lessonid,"del_flag":0,"state":0}).count();

      await model.where({id:lessonid}).update({"num1":count1,"num2":count2});

      this.updatelessonparent(lessonid);


  },

  async updatelessonparent(id){
    let model=this.model("lesson");
    let lesson=await model.where({"id":id}).find();
    if(lesson.parent_id>0){

      let num1=await model.where({"parent_id":lesson.parent_id}).sum("num1");
      let num2=await model.where({"parent_id":lesson.parent_id}).sum("num2");

      await model.where({"id":lesson.parent_id}).update({"num1":num1,"num2":num2});
   await   this.updatelessonparent(lesson.parent_id);


    }

   
    




  }
,




  async addDataNoId(data) {
    const userInfo = await this.session('userInfo');
    if (think.isArray(data)) {
      data.map(r => {
        delete r.id;
        r.create_date = think.datetime();
        r.update_date = think.datetime();

        if (think.isEmpty(r.create_by)) {
          r.create_by = userInfo.id;
        }
        r.update_by = userInfo.id;
        r.create_by2 = userInfo.aid;
        if (think.isEmpty(r.office_id)) {
          r.office_id = userInfo.office_id;
        }
        r.del_flag = 0;
      });
    } else {
      delete data.id;
      data.create_date = think.datetime();
      data.update_date = think.datetime();
      if (think.isEmpty(data.create_by)) {
        data.create_by = userInfo.id;
      }
      data.update_by = userInfo.id;
      data.create_by2 = userInfo.aid;
      if (think.isEmpty(data.office_id)) {
        data.office_id = userInfo.office_id;
      }
      data.del_flag = 0;
    }
    data.yz1 = 0;
    data.yz2 = 0;
    const omodel = this.model('office');
    const umodel = this.model('user');
    const office = await omodel.where({id: userInfo.office_id}).find();
    console.log(userInfo);
    console.log(office);

    let u;

    data.level = 0;
    if (office.level == 1) {
      data.level = 1;
      data.yz1 = 0;
      data.yz2 = 0;

      if (office.masterid == userInfo.id) {
        data.level = 0;
        data.yz1 = userInfo.aid;
      } else {
        u = await umodel.where({id: office.masterid}).find();

        data.yz1 = u.aid;
      }
    }

    if (office.level == 2) {
      data.level = 2;
      if (office.masterid == userInfo.id) {
        data.level = 1;
        const office2 = await omodel.where({id: office.parent_id}).find();
        const u2 = await umodel.where({id: office2.masterid}).find();
        data.yz1 = u2.aid;
      } else {
        u = await umodel.where({id: office.masterid}).find();
        data.yz2 = u.aid;
        const office2 = await omodel.where({id: office.parent_id}).find();
        const u2 = await umodel.where({id: office2.masterid}).find();
        data.yz1 = u2.aid;
      }
    }
    if (office.level == 3) {
      data.level = 2;
      const upoffice = await omodel.where({id: office.parent_id}).find();
      if (upoffice.masterid == userInfo.id) {
        data.level = 1;
        const office2 = await omodel.where({id: upoffice.parent_id}).find();
        const u2 = await umodel.where({id: office2.masterid}).find();
        data.yz1 = u2.aid;
      } else {
        u = await umodel.where({id: upoffice.masterid}).find();
        data.yz2 = u.aid;
        const office2 = await omodel.where({id: upoffice.parent_id}).find();
        const u2 = await umodel.where({id: office2.masterid}).find();
        data.yz1 = u2.aid;
      }
    }
    if (office.level == 99) {
      data.level = 2;
      const peopleOffice = await omodel.where({id: office.parent_id}).find();
      const upoffice = await omodel.where({id: peopleOffice.parent_id}).find();
      u = await umodel.where({id: upoffice.masterid}).find();
      data.yz2 = u.aid;
      const office2 = await omodel.where({id: upoffice.parent_id}).find();
      const u2 = await umodel.where({id: office2.masterid}).find();
      data.yz1 = u2.aid;

      console.log({id: office.parent_id})
      console.log(peopleOffice)


      if (peopleOffice.id == 21) {
        let u2 = await umodel.where({id: peopleOffice.masterid}).find();
        data.level = 1;
        data.yz1 = u2.aid;

      } else {
        let upoffice = await omodel.where({id: peopleOffice.parent_id}).find();
        u = await umodel.where({id: upoffice.masterid}).find();
        data.yz2 = u.aid;
        let office2 = await omodel.where({id: upoffice.parent_id}).find();
        let u2 = await umodel.where({id: office2.masterid}).find();
        data.yz1 = u2.aid;
      }


    }
    // console.log(data);

    return data;
  },
  /**
   * 添加基本数据,id为int随机
   * @param data
   * @param idType 设定一个int类型的id, 范围在 [0, 511]之间
   */
  async addData(data, idType = 0) {
    const userInfo = await this.session('userInfo');
    if (think.isArray(data)) {
      data.map(r => {
        r.id = think.uuid();
        r.create_date = think.datetime();
        r.update_date = think.datetime();
        if (think.isEmpty(r.create_by)) {
          r.create_by = userInfo.id;
        }
        r.update_by = userInfo.id;
        if (think.isEmpty(r.office_id)) {
          r.office_id = userInfo.office_id;
        }
        r.del_flag = 0;
      });
    } else {
      data.id = think.uuid();
      data.create_date = think.datetime();
      data.update_date = think.datetime();
      if (think.isEmpty(data.create_by)) {
        data.create_by = userInfo.id;
      }
      data.update_by = userInfo.id;
      if (think.isEmpty(data.office_id)) {
        data.office_id = userInfo.office_id;
      }
      data.del_flag = 0;
    }
    return data;
  },

  /**
   * 添加基本数据,id为int随机
   * @param data
   * @param idType 设定一个int类型的id, 范围在 [0, 511]之间
   */
  async addDataNoDate(data, idType = 0) {
    const userInfo = await this.session('userInfo');
    if (think.isArray(data)) {
      data.map(r => {
        r.id = think.uuid();
        //    r.create_date = think.datetime();
        //  r.update_date = think.datetime();
        if (think.isEmpty(r.create_by)) {
          r.create_by = userInfo.id;
        }
        r.update_by = userInfo.id;
        if (think.isEmpty(r.office_id)) {
          r.office_id = userInfo.office_id;
        }
        r.del_flag = 0;
      });
    } else {
      data.id = think.uuid();
      //    data.create_date = think.datetime();
      //  data.update_date = think.datetime();
      if (think.isEmpty(data.create_by)) {
        data.create_by = userInfo.id;
      }
      data.update_by = userInfo.id;
      if (think.isEmpty(data.office_id)) {
        data.office_id = userInfo.office_id;
      }
      data.del_flag = 0;
    }
    return data;
  },

  /**
   * 更新基本数据
   */
  async updateData(data = {}) {
    const userInfo = await this.session('userInfo');
    if (think.isArray(data)) {
      data.map(r => {
        r.update_date = think.datetime();
        r.update_by = userInfo.id;
      });
    } else {
      data.update_date = think.datetime();
      data.update_by = userInfo.id;
    }
    return data;
  },
  /**
   * 删除数据基本数据
   */
  async deleteData(data = {}) {
    const userInfo = await this.session('userInfo');
    data.update_date = think.datetime();
    data.update_by = userInfo.id;
    data.del_flag = 1;
    return data;
  },
  /**
   * 获取join字段名
   * @returns {Promise<string>}
   * @param data[{
   *    tableName 表名称,
   *    as 关联名称,
   *    prefix 前缀
   * }]
   */
  async getJoinField(data) {
    // let sql='select COLUMN_NAME from information_schema.COLUMNS ' +
    //   "where table_name= '" + tableName + "' and table_schema ='" + tableSchema + "';  "
    let fields = '';
    data.map(async v => {
      const sql = ' SHOW COLUMNS FROM `' + v.tableName + '`';
      const field = await think.model('').query(sql);
      field.map(r => {
        fields += v.as + '.' + r.Field + ' as ' + v.prefix + '_' + r.Field + ',';
      });
    });
    // 去掉末尾的,
    _.trimEnd(fields, ',');
    return fields;
  },
  async dataScopeFilter(main, mainAlias, userAlias, officeAlias) {
    // 只支持单个角色
    const user = await this.session('userInfo');
    const where = {};
    // where['_logic'] = 'AND';
    let key = '';
    const rolelist = await this.model('role').alias('r').join(['sys_user_role ur ON  ur.role_id=r.id']).where({'ur.user_id': user.id}).select();
    for (var role of rolelist) {
      if (role.data_scope == '1') {
        key = mainAlias + '.create_by';
        where[key] = user.id;
      } else if (role.data_scope == '2') {
        key = mainAlias + '.office_id';
        where[key] = user.office_id;
        where[mainAlias + '.del_flag'] = 0;
      } else {

      }
      where[mainAlias + '.del_flag'] = 0;
      // else if (role.data_scope == '3') {
      //   const office = await this.model('office').where({'id': user.office_id, 'del_flag': '0'}).find();
      //   key = officeAlias + '.id';
      //   where[key] = user.office_id;
      //   key = officeAlias + '.parent_ids';
      //   where[key] = ['like', office.parent_ids + '%'];
      // } else if (role.data_scope == '-5') {
      //   const offices = await this.model('office').alias('o').join([' sys_role_office sr on o.id=sr.office_id']).where({
      //     'sr.role_id': role.id,
      //     'o.del_flag': '0'
      //   }).field('o.id').select();
      //   if (!think.isEmpty(offices)) {
      //     const ids = [];
      //     for (var o of offices) {
      //       ids.push(o.id);
      //     }
      //     key = main + '.office_id';
      //     where[key] = ['in', ids];
      //   }
      // }
    }
    Object.assign(main, where);
    return where;
  },
  /**
   * 角色数据过滤，有额外情况
   * @param alias 主表的别名
   * @param scopeName 过滤字段
   * @param exclude 额外过滤
   * @returns {Promise<string>} sqlwhere
   */
  async dataScope(Alias, scopeName = 'office_id') {
    const user = await this.session('userInfo');
    let office=await this.model("office").where({id:user.id}).find();


    let sql = '';
    const rolelist = await this.model('role').alias('r').join(['sys_user_role ur ON  ur.role_id=r.id']).where({'ur.user_id': user.id}).select();
    for (const role of rolelist) {
      sql += '(';
      const offices = role.data_office_scope.split(',');
      let officeSQl = Alias + '.' + scopeName + ' IN (' + user.office_id + ',';
      offices.map(val => {
        officeSQl += "'" + val + "',";
      });
      officeSQl = officeSQl.substring(0, officeSQl.lastIndexOf(','));
      officeSQl += ') ';
      sql += officeSQl;
      if (role.data_scope == '1') {
        sql += 'AND ' + Alias + ".create_by ='" + user.id + "'";
      }

      if (role.data_scope == '3') {
        sql += 'or '+  "o.parent_ids like '" + office.parent_ids + "%'";
      }

      sql += ') OR ';
    }
    sql += ' (1=0) AND (' + Alias + ".del_flag='0')";
    return sql;
  },

  /**
   * 部门数据过滤，有额外情况
   * @param alias 主表的别名
   * @param scopeName 过滤字段
   * @param exclude 额外过滤
   * @returns {Promise<string>} sqlwhere
   */
  async dataScopeOffice(alias, scopeName = 'office_id', exclude = []) {
    const user = await this.session('userInfo');
    let sql = '';
    let model = this.model('user');
    if (exclude.includes(parseInt(user.office_id))) {
      sql = alias + ".del_flag='0'";
    } else {
      let officeids = await model.query(" SELECT getLeafNodeList(" + parseInt(user.office_id) + ") AS office_ids");
      sql += '(';
      const offices = officeids[0].office_ids.split(',');
      let officeSQl = alias + '.' + scopeName + ' IN (' + user.office_id + ',';
      offices.map(val => {
        officeSQl += "'" + val + "',";
      });
      officeSQl = officeSQl.substring(0, officeSQl.lastIndexOf(','));
      officeSQl += ') ';
      sql += officeSQl;
      sql += ')';

      sql += ' AND (' + alias + ".del_flag='0')";
    }

    return sql;
  },

  /**
   * 获取join字段名
   * @returns {Promise<string>}
   * @param data[{
   *
   *    type :报销  默认为报销
   *    touserid 目标用户
   *    content  提醒内容 （如：财务审核通过）
   *    bussid   业务主表的ID
   *    name  审核人姓名
   *    bussstate 通过
   *    menuname 点击跳转的页面 name
   *    bussname （报销为 报销金额（2000元）   此处用于显示提示概览  ）
   *    title (默认为 审核进度提醒)
   * }]
   */

  async addnotice(data = {}) {
    data.id = think.uuid();
    data.create_date = think.datetime();
    data.state = 0;

    if (think.isEmpty(data.title)) {
      data.title = '审核进度提醒';
    }

    if (think.isEmpty(data.type)) {
      data.type = '报销';
    }
    const model = this.model('notice');
    data.sendtype = 2;
    await model.add(data);
    data.id = think.uuid();
    data.sendtype = 3;
    await model.add(data);
  },

  /*
   *
   结束流程
   data.flowid 流程id：
   data.id 业务id int
   */
  async endflow(obj) {
    const userInfo = await this.session('userInfo');

    const rolemodel = this.model('user_role');
    const roleres = await rolemodel.alias('c').join(['sys_role r on c.role_id=r.id ']).where({'user_id': userInfo.id}).select();

    let request = require('async-request'),

      response;

    try {
      response = await request('http://www.test.com/index/wf/wfdo.html?uid=' + userInfo.aid + '&role=' + roleres[0].aid, {
        // This example demonstrates all of the supported options.
        // Request method (uppercase): POST, DELETE, ...
        method: 'POST',
        data: {
          act: 'endflow',
          bill_id: obj.id,
          bill_table: obj.bill_table
        }
      });
      console.log(response);
    } catch (err) {
      console.log(err);
    }
  },
  /*
  *
  启动流程
  data.flowid 流程id：
  data.id 业务id int
  */
  async startflow(data = {}) {
    const userInfo = await this.session('userInfo');

    const rolemodel = this.model('user_role');
    const roleres = await rolemodel.alias('c').join(['sys_role r on c.role_id=r.id ']).where({'user_id': userInfo.id}).select();

    console.log(userInfo);
    console.log(roleres);

    const flowm = this.model('wf_flow');
    const flow = await flowm.where({'id': data.flowid}).find();
    if (!think.isEmpty(roleres)) {
      let request = require('async-request'),

        response;

      try {
        response = await request('http://www.test.com/index/wf/wfdo.html?uid=' + userInfo.aid + '&role=' + roleres[0].aid, {
          // This example demonstrates all of the supported options.
          // Request method (uppercase): POST, DELETE, ...
          method: 'POST',
          data: {
            act: 'start',
            wf_type: flow.type,
            wf_id: flow.id,
            wf_fid: data.id,
            check_con: '启动流程'
          }
        });

        const mumodel = this.model('muser');
        const bussid = data.id;
        const type = flow.type;
        const users = await mumodel.where({bussid: bussid, type: type, userid: userInfo.aid}).select();

        if (think.isEmpty(users)) {
          const para = {};
          para.bussid = bussid;
          para.userid = userInfo.aid;
          para.type = type;
          await mumodel.add(para);
        }

        console.log('=================');
        console.log(response);
        const sql = "SELECT  f.run_id,f.run_flow_process as flow_process,f.id as run_process,r.from_table as wf_type ,r.from_id as wf_fid FROM `sys_wf_run_process` `f` INNER JOIN `sys_wf_flow` `w` ON `f`.`run_flow`=`w`.`id` INNER JOIN  `sys_wf_run` `r` ON `f`.`run_id`=`r`.`id`  INNER JOIN  sys_wf_flow_process  p on p.id=r.run_flow_process WHERE    `f`.`status` = '0' AND `r`.`status` = '0'  and r.from_id=" + data.id;
        let para = await rolemodel.query(sql);

        if (!think.isEmpty(para)) {
          para = para[0];
        }

        console.log(para);

        response = await request('http://www.test.com/index/wf/wfdo.html?act=do&wf_op=ok&wf_type=' + flow.type + '&wf_fid=' + data.id + '&sup=&uid=' + userInfo.aid + '&role=' + roleres[0].aid, {
          // This example demonstrates all of the supported options.
          // Request method (uppercase): POST, DELETE, ...
          method: 'POST',
          data: {
            wf_mode: '0',
            check_con: '提交申请',
            run_id: para['run_id'],
            run_process: para['run_process'],
            flow_process: para['flow_process'],
            submit_to_save: 'ok',
            sup: ''
          }
        });

        console.log(response);
      } catch (e) {
        console.log(e);
        return false;
      }

      return true;
    } else {
      return false;
    }
  },

  async redoflow(data = {}) {
    const flowm = this.model('wf_flow');
    const flow = await flowm.where({'id': data.flowid}).find();
    const userInfo = await this.session('userInfo');

    const rolemodel = this.model('user_role');
    const roleres = await rolemodel.alias('c').join(['sys_role r on c.role_id=r.id ']).where({'user_id': userInfo.id}).select();
    // ok&wf_type='+flow.type+'&wf_fid='+data.id+'&sup=&uid='+userInfo.aid+'&role='+roleres[0].aid, {
    const url = 'http://www.test.com/index/wf/wfdo.html?act=do&wf_op=ok&wf_type=' + flow.type + '&wf_fid=' + data.id + '&sup=1&datatype=json&uid=' + userInfo.aid + '&role=' + roleres[0].aid;
    let request = require('async-request'), response;

    // console.log(url);

    try {
      response = await request(url, {
        method: 'POST',
        data: {

          check_con: '重新发起',
          wf_mode: 0,
          npid: data.npid,
          run_id: data.run_id,
          run_process: data.run_process,
          flow_process: data.flow_process,
          submit_to_save: 'ok',
          sup: ''

        }
      });
    } catch (e) {
      return false;
    }
  },

  /*
  *
  流程记录
  data.flowid 流程id：
  data.id 业务id int
  */
  async flowlog(data = {}) {
    const userInfo = await this.session('userInfo');

    const rolemodel = this.model('user_role');
    const roleres = await rolemodel.alias('c').join(['sys_role r on c.role_id=r.id ']).where({'user_id': userInfo.id}).select();

    const flowm = this.model('wf_flow');
    const flow = await flowm.where({'id': data.flowid}).find();
    let request = require('async-request'),

      response;

    //   console.log('http://www.test.com/index/wf/wfdo.html?act=do&wf_op=log&wf_type='+flow.type+'&wf_fid='+data.id+'&datatype=json&uid='+userInfo.aid+'&role='+roleres[0].aid)
    try {
      response = await request('http://www.test.com/index/wf/wfdo.html?act=do&wf_op=log&wf_type=' + flow.type + '&wf_fid=' + data.id + '&datatype=json&uid=' + userInfo.aid + '&role=' + roleres[0].aid, {
        // This example demonstrates all of the supported options.
        // Request method (uppercase): POST, DELETE, ...
        method: 'GET'

      });
      // console.log(JSON.parse(response.body))
      const logs = JSON.parse(response.body);
      for (var log of logs) {
        log.dateline = think.datetime(log.dateline * 1000);
      }

      return logs;
    } catch (e) {
      console.log(e);
      return null;
    }
  },

  /**
   * 获取当前时间内各营人数
   * @param date 时间
   * @param officeWhere where条件
   * @returns {Promise<void>}
   */
  async getratio(date = new Date(), officeWhere = {}) {
    date = moment(date).format('YYYY-MM-01');
    const officeModel = this.model('office');
    //获取各营人数
    let res = await officeModel
      .alias('o')
      .field(' o.`name`,v.*')
      .join("INNER JOIN (select office_id,type2,sum(ratio) as ratio from sys_hrm_enter where del_flag='0' and type='正式员工' and starttime <= '" + date + "' and (endtime is null or endtime>='" + date + "') GROUP BY type2,office_id ) as v on v.office_id= o.id")
      .where(officeWhere)
      .select();
    //各营重复分组
    let newRes = _.groupBy(res, 'office_id');
    let results = [];
    //合并各营结果
    Object.keys(newRes).map(k => {
      let obj = {
        name: newRes[k][0].name,
        office_id: newRes[k][0].office_id,
        num1: 0,
        num2: 0
      };
      newRes[k].map(item => {
        if (item.type2 == '公司') {
          obj.num1 += item.ratio;
        } else {
          obj.num2 += item.ratio;
        }
      });
      results.push(obj);
    });
    return results;
  },

  /**
   * 获取当前时间内工作时间
   * @param beginDate
   * @param endDate
   * @param officeWhere where条件
   * @returns {Promise<void>}
   */
  async getPeopleTime(beginDate, endDate, officeId) {


    let result = {
      officeId: officeId,
      time: 0
    }
    let begin = moment(beginDate);
    let end = moment(endDate);

    const hrmEnterModel = this.model('hrm_enter');
    const holidaysModel = this.model('holidays');
    //循环计算每月结果
    for (let i = begin.month(); i <= end.month(); i++) {
      let beginMonth = moment(beginDate).month(i).startOf("month").format("YYYY-MM-DD");
      let endMonth = moment(beginDate).month(i).endOf("month").format("YYYY-MM-DD");
      if (begin.month() == i) {
        beginMonth = beginDate;
      }
      if (end.month() == i) {
        endMonth = endDate;
      }
      //获取当月员工入职情况
      let entryTimes = await hrmEnterModel
        .where("del_flag='0' and type='正式员工' and starttime <= '" + endMonth + "' and (endtime is null or endtime>='" + endMonth + "')")
        .where({office_id: officeId})
        .select();
      if (entryTimes.length > 0) {
        //获取当月员工入职时间
        for (let item of entryTimes) {
          let starttime = moment(item.starttime).isSameOrBefore(beginMonth) ? beginMonth : item.starttime;
          let endtime = think.isEmpty(item.endtime) || moment(item.endtime).isSameOrAfter(endMonth) ? endMonth : item.endtime;
          let workData = await holidaysModel.where({
            date: ['between', starttime, endtime],
            del_flag: 0,
            type: 0
          }).count("id");
          result.time += new Decimal(workData).mul(new Decimal(item.ratio)).toNumber();
        }
      }
    }
    return result;
  }
};
