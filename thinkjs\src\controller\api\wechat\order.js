
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {



    async queryresultAction(){
        let user = await this.session("userInfo");
        let orderNo = this.get("orderNo");
        let model = this.model("order");
        let order = await model.where({user_id:user.id,order_no:orderNo}).find();
        return this.success(order);
    }


    async queryAction(){
        let user = await this.session("userInfo");
        
        let model = this.model("order");
        let order = await model.where({user_id:user.id, }).alias("c")
        .join("sys_product as p on c.product_id=p.id")
        .order("create_time desc").limit(10).field("c.*,p.name as product_name").select();
        return this.success(order);
    }


    async querylistAction(){
        let user = await this.session("userInfo");
        let param=this.get();
        let page=this.get("page");
        let pageSize=this.get("pageSize");
        let status=this.get("status"); 
        let where={};
        if(!think.isEmpty(status)){
            where["pay_status"]=status;

        }
        let keyword=this.get("keyword");
        if(!think.isEmpty(keyword)){

            where["order_no"]=['like',"%"+keyword+"%"]
        }


        let model = this.model("order");
        let order = await model.where({user_id:user.id }).alias("c")
        .join("sys_product as p on c.product_id=p.id")
        .where(where).order("create_time desc").field("c.*,p.name as product_name").page(page,pageSize).countSelect();

      
        const data = {};
    
        data.code = 200;
        let totalPage = Math.ceil(order.count/pageSize);
            
        data.errno=0;
        data.data = {total_pages: totalPage, list: order.data};

        return this.json(data);


    }

    async cancelAction(){
        let user = await this.session("userInfo");

        let id=this.post("orderId");
        let ordermodel = this.model("order");
        await ordermodel.where({"id":id,"user_id":user.id,"pay_status":0}).delete()
        return this.json({"errno":0})


    }


    async repayAction(){

        let user = await this.session("userInfo");

        let id=this.get("id");
        let ordermodel = this.model("order");
      let order=  await ordermodel.where({"id":id,"user_id":user.id,"pay_status":0}).find()
      return this.json({"data":order,"errno":0})

    }

    async createAction(){
        let user = await this.session("userInfo");
        let productId = this.post("productId");
        let model = this.model("product");
        let ordermodel = this.model("order");
        let product = await model.where({id:productId}).find();
        



        if(think.isEmpty(product)){
            return this.fail(301,'套餐不存在');
        }


        let school=await this.model("school").where({"id":product.schoolid}).find();

        
        if(think.isEmpty(school)){
            return this.fail(301,'套餐不存在');
        }


        if(school.canusetime<product.duration){
            return this.fail(301,'该机构可用时长已用尽，请联系管理员或其他相关人员！');

        }






  


        let order = {
            schoolid:product.schoolid,
            user_id:user.id,
            product_id:productId,
            product_name:product.name,
            total_amount:product.price,
            duration:product.duration,
            //写一个订单号生成方法，年月日加顺序号
            order_no:await this.genorderno(product.schoolid),
            order_status:0,
            pay_type:1,
            pay_status:0,
            pay_amount:product.price,
            create_time:think.datetime(),
            update_time:think.datetime(),
            address:JSON.stringify(this.post("addressInfo"))
        }
       ordermodel.where({user_id:user.id,pay_status:0,pay_type:1})
     .where("TO_DAYS(create_time)=TO_DAYS(now())").delete();

     let id=   await ordermodel.add(order);
     order.id=id;
        return this.success(order);
    }

    async genorderno(schoolid){

        let school=await this.model("school").where({"id":schoolid}).find();
        let model = this.model("order");
        let num = await model.where("TO_DAYS(create_time)=TO_DAYS(now())").count();
       let shortname=school.shortname;
       if(think.isEmpty(shortname)){
        shortname="JSJY";
       }
        let orderno = shortname+"_"+think.datetime("YYYYMMDDHHmm")+num;
        return orderno;
    }

   
    
}