module.exports = (options, app) => {
  return (ctx, next) => {
    // 定义开始时间
    const start = new Date();
    // 定义间隔时间
    let ms;
    let err = null;
    // 调用 next 统计后续执行逻辑的所有时间
    return next().catch(e => {
      err = e; // 这里先将错误保存在一个错误对象上，方便统计出错情况下的执行时间
    }).then(async () => {
      // 获取用户信息
      let userInfo = {
        id: 'guest'
      };
      if (!think.isNullOrUndefined(await ctx.session('userInfo'))) {
        userInfo = await ctx.session('userInfo');
      }
      ms = new Date() - start;
      // 记录响应日志
      const ctxInfo = {
        id: think.uuid(),
        level: 'info',
        log_name: decodeURIComponent(ctx.headers.apiname),
        method: ctx.method,
        originalUrl: ctx.originalUrl,
        ip: ctx.ip,
        query: JSON.stringify(ctx.query),
        post: JSON.stringify(ctx.post()),
        headers: JSON.stringify(ctx.request.headers),
        // body: JSON.stringify(ctx.body),
        time: ms.toString(),
        status: ctx.status,
        create_by: userInfo.id,
        responses: JSON.stringify(ctx.response),
        create_date: think.datetime(),
        update_date: think.datetime(),
        del_flag: 0
      };

      if (ctx.headers.needLog) {
        const model = think.model('log');
        await model.add(ctxInfo);
      }

      if (err != null) {
        // 记录错误日志
        const ctxInfo = {
          id: think.uuid(),
          level: 'error',
          log_name: decodeURIComponent(ctx.headers.apiname),
          method: ctx.method,
          originalUrl: ctx.originalUrl,
          ip: ctx.ip,
          query: JSON.stringify(ctx.query),
          post: JSON.stringify(ctx.post()),
          headers: JSON.stringify(ctx.request.headers),
          body: JSON.stringify(ctx.body),
          time: ms.toString(),
          status: ctx.status,
          responses: JSON.stringify(ctx.response),
          err_name: err.name,
          err_message: err.message,
          err_stack: err.stack,
          create_by: userInfo.id,
          create_date: think.datetime(),
          update_date: think.datetime(),
          del_flag: 0
        };
        if (ctx.headers.needLog) {
          const model = think.model('log');
          await model.add(ctxInfo);
        }
        return Promise.reject(err); // 如果后续执行逻辑有错误，则将错误返回
      }
    });
  };
};
