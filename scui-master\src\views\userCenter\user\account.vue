<template>
<!--	<el-alert-->
<!--		title="异步组件动态加载使用了正处于试验阶段的<Suspense>组件, 其API和使用方式可能会改变. <Suspense> is an experimental feature and its API will likely change."-->
<!--		type="warning" show-icon style="margin-bottom: 15px;" />-->

	<el-card shadow="never" header="个人信息">
		<el-form ref="form" :model="userInfo" label-width="120px" style="margin-top:20px;" :rules="userInfoRules">
			<el-form-item label="账号" prop="login_name">
				<el-input v-model="userInfo.login_name" disabled></el-input>
				<div class="el-form-item-msg">账号信息用于登录，系统不允许修改</div>
			</el-form-item>
			<el-form-item label="姓名" prop="name">
				<el-input v-model="userInfo.name"></el-input>
			</el-form-item>
			
			<el-form-item label="头像" prop="avatar">
				<sc-upload v-model="userInfo.avatar" title="头像" :cropper="true" :compress="1" :aspectRatio="1 / 1"
					:apiObj="uploadApi"></sc-upload>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="updateInfo('form')">保存</el-button>
			</el-form-item>
		</el-form>
	</el-card>
</template>

<script>
export default {
	data() {
		const validatePassword = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请输入密码'));
			} else {
				//6-20位包含字符、数字和特殊字符
				var ls = 0;
				if (this.passwordForm.newPassword !== '') {
					if (this.passwordForm.newPassword.match(/([a-z])+/)) {
						ls++;
					}
					if (this.passwordForm.newPassword.match(/([0-9])+/)) {
						ls++;
					}
					if (this.passwordForm.newPassword.match(/([A-Z])+/)) {
						ls++;
					}
					if (this.passwordForm.newPassword.match(/([\W])+/) && !this.passwordForm.newPassword.match(/(![\u4E00-\u9FA5])+/)) {
						ls++;
					}
					if (this.passwordForm.newPassword.length < 6 || this.passwordForm.newPassword.length > 20) {
						callback(new Error('要求6-20位字符'));
						ls = 0;
					}
					if (this.passwordForm.newPassword.match(/([\u4E00-\u9FA5])+/)) {
						callback(new Error('不能包含中文字符'));
						ls = 0;
					}
					switch (ls) {
						case 0:
							this.passwordPercent = 0;
							callback(new Error('数字、小写字母、大写字母以及特殊字符中四选三'));
							break;
						case 1:
							this.passwordPercent = 33;
							callback(new Error('数字、小写字母、大写字母以及特殊字符中四选三'));
							break;
						case 2:
							this.passwordPercent = 66;
							callback(new Error('数字、小写字母 、大写字母以及特殊字符中四选三'));
							break;
						case 3:
						case 4:
							this.passwordPercent = 100;
							break;
						default:
							this.passwordPercent = 0;
							break;
					}
				}
				callback();
			}
		};
		const validateConfirmPassword = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请输入密码'));
			} else {
				if (this.passwordForm.confirmPassword !== this.passwordForm.newPassword) {
					callback(new Error('两次输入的密码不一致'));
					// this.$refs.ruleForm.validateField('checkPass');
				}
				callback();
			}
		};
		var validatorPhone = function (rule, value, callback) {
			if (value === '') {
				callback(new Error('手机号不能为空'))
			} else if (!/^1\d{10}$/.test(value)) {
				callback(new Error('手机号格式错误'))
			} else {
				callback();
			}
		}

		var validatorEmail = function (rule, value, callback) {
			if (value === '') {
				callback(new Error('邮箱不能为空'))
			} else if (!/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
				callback(new Error('邮箱格式错误'))
			} else {
				callback();
			}
		}
		return {
			passwordPercent: 0,
			//头像上传地址
			uploadApi: this.$API.common.avatarUpload,
			//头像显示地址
			avatar: "",
			activities: [
			],

			selectConfig: {
				showbar: {
					label: 'name',
					value: 'name'
				},
			},
			//密码表单
			passwordForm: {
				oldPassword: "",
				newPassword: "",
				confirmPassword: "",
			},
			//个人信息
			userInfo: {
				login_name: null,
				name: null,
				email: null,
				mobile: null,
				avatar: null,
				birthday: null,
				role: null,
			},

			disForm: {
				showbar: "",
			},
			//更改个人信息验证规则
			userInfoRules: {
				login_name: [
					{ required: true, message: '缺少用户名！', trigger: 'blur' },
				],
				name: [
					{ required: true, message: '请输入姓名！', trigger: 'blur' },
				],
				email: [
					{ required: true, validator: validatorEmail, trigger: 'blur' },
				],
				mobile: [
					{ required: true, validator: validatorPhone, trigger: 'blur' }
				],
			},

			displayRules: {
				showbar: [
					{ required: true, trigger: 'blur' },
				],

			},
			//更改密码验证规则
			passwordRules: {
				oldPassword: [
					{ required: true, message: '请输入旧密码', trigger: 'blur' },
				],
				newPassword: [
					{ required: true, validator: validatePassword, trigger: ['blur', 'change'] }
				],
				confirmPassword: [
					{ required: true, validator: validateConfirmPassword, trigger: ['blur', 'change'] }
				]
			},
			config: {
				theme: '1',
				diy: true,
				tags: true,
				msg: true
			},

		}
	},
	methods: {
		passwordPercentFormat(percentage) {
			return percentage === 100 ? '符合' : `不符`;
		},
		//初始化方法
	async	init() {

			let res= await this.$API.user.currinfo.post();
			let userInfo =res.data;
			
			this.userInfo = userInfo;
			console.log(this.userInfo)
		},
		//更新密码
		async updatePassword(formName) {
			this.$refs[formName].validate(async (valid) => {
				if (valid) {
					//加密旧密码验证
					let oldPasswordMD5 = this.$TOOL.crypto.MD5(this.passwordForm.oldPassword);
					if (oldPasswordMD5 !== this.userInfo.password) {
						this.$message.error("旧密码不正确")
						this.$refs.passForm.resetFields();
					} else if (this.passwordForm.confirmPassword !== this.passwordForm.newPassword) {
						this.$message.error("两次输入的新面貌密码不一致")
						this.passwordForm.newPassword = null;
						this.passwordForm.confirmPassword = null;
					} else {
						let response = await this.$API.user.personalSavePassWord.post({
							password: this.passwordForm.newPassword,
							id: this.userInfo.id
						})
						if (response.code == "200") {
							this.$router.replace({ path: '/login' });
						}
					}

				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},

		//更新用户信息
		async updateInfo(formName) {
			this.$refs[formName].validate(async (valid) => {
				if (valid) {
					let response = await this.$API.user.personalSaveInfo.post(this.userInfo)
					if (response.code == "200") {
						//刷新当前路由
						this.$router.replace({ path: '/refresh' });
					}
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},

		// eslint-disable-next-line
		async updatedis(formName) {


			let response = await this.$API.user.display.post(this.disForm)
			if (response.code == "200") {
				//刷新当前路由
				this.$router.replace({ path: '/refresh' });
			}

		},

	},
	created() {
		this.init();

	}
}
</script>

<style>
</style>
