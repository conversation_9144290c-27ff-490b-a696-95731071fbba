import config from "@/config";
import http from "@/utils/request";

const office = {
	officeList: {
		url: `${config.API_URL}/office/list`,
		name: "部门树",
		post: async function (data) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name,data);

		}
	},
	tree: {
		url: `${config.API_URL}/office/tree2`,
		name: "部门树2",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	},
	list: {
		url: `${config.API_URL}/office/tree`,
		name: "部门树",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	},
	billselect: {
		url: `${config.API_URL}/office/billselect`,
		name: "部门下拉",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name,params);

		}
	},
	select: {
		url: `${config.API_URL}/office/getselect`,
		name: "部门下拉",
		get: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.get(this.url, this.name,params);

		}
	}
	, save: {
		url: `${config.API_URL}/office/save`,
		name: "保存部门",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}, remove: {
		url: `${config.API_URL}/office/remove`,
		name: "删除部门",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},
	getbyid: {
		url: `${config.API_URL}/office/getbyid`,
		name: "删除部门",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	}


};

export default office;
