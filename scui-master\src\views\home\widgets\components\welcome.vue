<template>
	<el-card shadow="hover" header="">
		<div class="welcome">
		
			
			<!-- 销售统计图表 -->
			<div class="sales-statistics">
				<div class="sales-header">
					<h3>销售数据统计</h3>
					<div class="filter-bar">
						<el-date-picker
							v-model="chartDateRange"
							type="daterange"
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							:shortcuts="dateShortcuts"
							:clearable="true"
							:editable="false"
							@change="handleDateChange">
						</el-date-picker>
						<el-button type="primary" @click="refreshChart">
							<el-icon><refresh /></el-icon>
							刷新
						</el-button>
					</div>
				</div>
				<div class="chart-container" ref="pieChartRef"></div>
			</div>
			
		
		</div>
	</el-card>
</template>

<script>
	import * as echarts from 'echarts'
	import { Refresh } from '@element-plus/icons-vue'
	
	export default {
		title: "欢迎",
		icon: "el-icon-present",
		description: "项目特色以及文档链接",
		components: {
			Refresh
		},
		data() {
			return {
				chartDateRange: null,
				pieChart: null,
				chartData: [],
				// 添加日期快捷选项
				dateShortcuts: [
					{
						text: '最近一周',
						value: () => {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
							return [start, end]
						}
					},
					{
						text: '最近一个月',
						value: () => {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
							return [start, end]
						}
					},
					{
						text: '最近三个月',
						value: () => {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
							return [start, end]
						}
					}
				]
			}
		},
		mounted() {
			this.$nextTick(() => {
				this.initPieChart();
				this.fetchChartData();
			});
		},
		methods: {
			godoc(){
				
			},
			
			// 初始化饼图
			initPieChart() {
				if (this.$refs.pieChartRef) {
					this.pieChart = echarts.init(this.$refs.pieChartRef);
					window.addEventListener('resize', this.resizeChart);
				}
			},
			
			// 调整图表大小
			resizeChart() {
				if (this.pieChart) {
					this.pieChart.resize();
				}
			},
			
			// 渲染饼图
			renderPieChart() {
				if (!this.pieChart) return;
				
				const option = {
					title: {
						text: '商品销售统计',
						left: 'center',
						top: 0
					},
					tooltip: {
						trigger: 'item',
						formatter: '{a} <br/>{b} : {c} ({d}%)'
					},
					legend: {
						orient: 'vertical',
						left: 'left',
						top: 'center',
						data: this.chartData.map(item => item.name)
					},
					series: [
						{
							name: '销售数量',
							type: 'pie',
							radius: '55%',
							center: ['50%', '60%'],
							data: this.chartData,
							emphasis: {
								itemStyle: { 
									shadowBlur: 10,
									shadowOffsetX: 0,
									shadowColor: 'rgba(0, 0, 0, 0.5)'
								}
							}
						}
					]
				};
				
				this.pieChart.setOption(option, true);
			},
			
			// 获取图表数据
			async fetchChartData() {
				try {
					let chartData = [];
					// 构建请求参数
					const params = {};
					// 检查日期范围是否存在且有效
					if (this.chartDateRange && Array.isArray(this.chartDateRange) && this.chartDateRange.length === 2) {
						params.startDate = this.chartDateRange[0];
						params.endDate = this.chartDateRange[1];
						console.log('请求参数:', params);
					} else {
						console.log('未设置日期范围或日期范围无效');
					}
					
					try {
						// 调用后端API获取实际数据
						const res = await this.$API.common.util.post(`/buss/tj/welcome`, params);
						console.log('API返回数据:', res);
						
						if (res.errno === 0 && res.data && res.data.length > 0) {
							// 如果API返回成功且有数据，使用实际数据
							chartData = res.data.map(item => ({
								value: item.count, // 可以根据需要选择count或total_amount
								name: item.product_name
							}));
						} else {
							// 如果API返回无数据，使用演示数据
							throw new Error('No data returned from API');
						}
					} catch (apiError) {
						 
					}
					
					// 更新图表数据并渲染
					this.chartData = chartData;
					
					// 确保DOM已更新后再渲染图表
					this.$nextTick(() => {
						if (this.pieChart) {
							// 销毁并重新创建图表以确保完全刷新
							this.pieChart.dispose();
							this.pieChart = null;
							this.$nextTick(() => {
								this.initPieChart();
								this.renderPieChart();
							});
						} else {
							this.renderPieChart();
						}
					});
					
				} catch (error) {
					console.error('获取图表数据出错:', error);
				}
			},
			
			// 日期范围变化
			handleDateChange(val) {
				console.log('日期范围变化:', val);
				// 直接使用日期选择器返回的值
				this.chartDateRange = val;
				// 刷新图表数据
				this.fetchChartData();
			},
			
			// 刷新图表
			refreshChart() {
				// 强制刷新图表数据
				this.fetchChartData();
			}
		},
		beforeUnmount() {
			// 清理事件监听和图表实例
			if (this.pieChart) {
				window.removeEventListener('resize', this.resizeChart);
				this.pieChart.dispose();
				this.pieChart = null;
			}
		}
	}
</script>

<style scoped>
	.welcome {}
	.welcome .logo {text-align: center;}
	.welcome .logo img {vertical-align: bottom;width: 100px;height: 100px;margin-bottom: 20px;}
	.welcome .logo h2 {font-size: 30px;font-weight: normal;display: flex;align-items: center;justify-content: center;}

	.tips {margin-top: 20px;padding:0 40px;}
	.tips-item {display: flex;align-items: center;justify-content: center;padding:7.5px 0;}
	.tips-item-icon {width: 40px;height:40px;display: flex;align-items: center;justify-content: center;border-radius: 50%;font-size: 18px;margin-right: 20px;color: var(--el-color-primary);background: rgba(180,180,180,0.1);}
	.tips-item-message {flex: 1;font-size: 14px;}

	/* 销售统计样式 */
	.sales-statistics {margin-top: 30px; padding: 0 20px;}
	.sales-header {display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; margin-bottom: 15px;}
	.sales-header h3 {margin: 0; font-size: 18px; font-weight: 500;}
	.filter-bar {display: flex; gap: 10px; margin: 10px 0;}
	.chart-container {width: 100%; height: 300px; background-color: #fff; border-radius: 4px;}

	.actions {text-align: center;margin: 40px 0 20px 0;}
</style>
