<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/10 15:38:25
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="奖惩" prop="type">
					<el-input v-model="form.type" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="项目" prop="project">
					<el-input v-model="form.project" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="人员" prop="name">
					<el-input v-model="form.name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="时间" prop="date">
					<el-date-picker
						v-model="form.date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="24">
				<el-form-item label="事由" prop="note">
					<el-input v-model="form.note" :autosize="{ minRows: 2, maxRows: 4 }"
							  type="textarea" clearable></el-input>
				</el-form-item>
			</el-col>

			<el-col :lg="24">
				<el-form-item label="整改" prop="rectification">
					<el-input v-model="form.rectification" :autosize="{ minRows: 2, maxRows: 4 }"
							  type="textarea" clearable></el-input>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",

				type: "",

				project: "",

				date: "",

				note: "",

				name: "",

				rectification: "",

			},
			//验证规则
			rules: {

				type: [
					{required: true, message: '请输入奖惩'}
				],

				project: [
					{required: true, message: '请输入项目'}
				],

				date: [
					{required: true, message: '请输入时间'}
				],

				note: [
					{required: true, message: '请输入事由'}
				],

				name: [
					{required: true, message: '请输入人员'}
				],

				rectification: [
					{required: true, message: '请输入整改'}
				],

			},
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
