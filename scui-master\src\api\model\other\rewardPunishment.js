import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/other/rewardpunishment/page`,
		name: "奖惩数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/other/rewardpunishment/add`,
		name: "奖惩数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/other/rewardpunishment/save`,
		name: "奖惩数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/other/rewardpunishment/info`,
		name: "奖惩数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/other/rewardpunishment/delete`,
		name: "删除奖惩数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
