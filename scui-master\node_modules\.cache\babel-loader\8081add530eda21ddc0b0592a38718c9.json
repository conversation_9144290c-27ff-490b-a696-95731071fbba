{"ast": null, "code": "export default {\n  name: 'system',\n\n  data() {\n    return {\n      sys: {},\n      ti: {},\n      msg: {\n        open: true,\n        appKey: \"\",\n        secretKey: \"\"\n      }\n    };\n  },\n\n  methods: {\n    async init() {\n      let res = await this.$API.common.getconfig.post({\n        id: 1\n      });\n\n      if (res.data) {\n        this.sys = JSON.parse(res.data);\n      }\n\n      let res2 = await this.$API.common.getconfig.post({\n        id: 2\n      });\n\n      if (res2.data) {\n        this.msg = JSON.parse(res2.data);\n      }\n\n      let res3 = await this.$API.common.getconfig.post({\n        id: 3\n      });\n\n      if (res3.data) {\n        this.ti = JSON.parse(res3.data);\n      }\n    },\n\n    //更新用户信息\n    async updateInfo(id) {\n      let response = null;\n\n      if (id == 1) {\n        response = await this.$API.common.saveconfig.post({\n          data: JSON.stringify(this.sys),\n          id: 1\n        });\n      }\n\n      if (id == 2) {\n        response = await this.$API.common.saveconfig.post({\n          data: JSON.stringify(this.msg),\n          id: 2\n        });\n      }\n\n      if (id == 3) {\n        response = await this.$API.common.saveconfig.post({\n          data: JSON.stringify(this.ti),\n          id: 3\n        });\n      }\n\n      if (response.code == \"200\") {\n        //刷新当前路由\n        this.$router.replace({\n          path: '/refresh'\n        });\n      }\n    }\n\n  },\n\n  created() {\n    this.init();\n  }\n\n};", "map": {"version": 3, "mappings": "AAiIC,eAAe;EACdA,IAAI,EAAE,QADQ;;EAEdC,IAAI,GAAG;IACN,OAAO;MACNC,GAAG,EAAE,EADC;MAKNC,EAAE,EAAE,EALE;MAQNC,GAAG,EAAE;QACJC,IAAI,EAAE,IADF;QAEJC,MAAM,EAAE,EAFJ;QAGJC,SAAS,EAAE;MAHP;IARC,CAAP;EAeA,CAlBa;;EAoBdC,OAAO,EAAE;IAEJ,MAAMC,IAAN,GAAa;MAEH,IAAIC,GAAG,GAAE,MAAM,KAAKC,IAAL,CAAUC,MAAV,CAAiBC,SAAjB,CAA2BC,IAA3B,CAAgC;QAACC,EAAE,EAAC;MAAJ,CAAhC,CAAf;;MAIA,IAAGL,GAAG,CAACT,IAAP,EAAY;QAET,KAAKC,GAAL,GAASc,IAAI,CAACC,KAAL,CAAWP,GAAG,CAACT,IAAf,CAAT;MACH;;MAEb,IAAIiB,IAAI,GAAE,MAAM,KAAKP,IAAL,CAAUC,MAAV,CAAiBC,SAAjB,CAA2BC,IAA3B,CAAgC;QAACC,EAAE,EAAC;MAAJ,CAAhC,CAAhB;;MAIa,IAAGG,IAAI,CAACjB,IAAR,EAAa;QAEV,KAAKG,GAAL,GAASY,IAAI,CAACC,KAAL,CAAWC,IAAI,CAACjB,IAAhB,CAAT;MACH;;MAGZ,IAAIkB,IAAI,GAAE,MAAM,KAAKR,IAAL,CAAUC,MAAV,CAAiBC,SAAjB,CAA2BC,IAA3B,CAAgC;QAACC,EAAE,EAAC;MAAJ,CAAhC,CAAhB;;MAIY,IAAGI,IAAI,CAAClB,IAAR,EAAa;QAEV,KAAKE,EAAL,GAAQa,IAAI,CAACC,KAAL,CAAWE,IAAI,CAAClB,IAAhB,CAAR;MACH;IAKJ,CAnCF;;IAuCC;IACA,MAAMmB,UAAN,CAAiBL,EAAjB,EAAqB;MAET,IAAIM,QAAQ,GAAC,IAAb;;MAClB,IAAGN,EAAE,IAAE,CAAP,EAAS;QACPM,QAAQ,GAAG,MAAM,KAAKV,IAAL,CAAUC,MAAV,CAAiBU,UAAjB,CAA4BR,IAA5B,CAAiC;UAACb,IAAI,EAACe,IAAI,CAACO,SAAL,CAAe,KAAKrB,GAApB,CAAN;UAA+Ba,EAAE,EAAC;QAAlC,CAAjC,CAAjB;MACF;;MACA,IAAGA,EAAE,IAAE,CAAP,EAAS;QACPM,QAAQ,GAAG,MAAM,KAAKV,IAAL,CAAUC,MAAV,CAAiBU,UAAjB,CAA4BR,IAA5B,CAAiC;UAACb,IAAI,EAACe,IAAI,CAACO,SAAL,CAAe,KAAKnB,GAApB,CAAN;UAA+BW,EAAE,EAAC;QAAlC,CAAjC,CAAjB;MACF;;MAGA,IAAGA,EAAE,IAAE,CAAP,EAAS;QACPM,QAAQ,GAAG,MAAM,KAAKV,IAAL,CAAUC,MAAV,CAAiBU,UAAjB,CAA4BR,IAA5B,CAAiC;UAACb,IAAI,EAACe,IAAI,CAACO,SAAL,CAAe,KAAKpB,EAApB,CAAN;UAA8BY,EAAE,EAAC;QAAjC,CAAjC,CAAjB;MACF;;MAEkB,IAAIM,QAAQ,CAACG,IAAT,IAAiB,KAArB,EAA4B;QACxB;QACA,KAAKC,OAAL,CAAaC,OAAb,CAAqB;UAAEC,IAAI,EAAE;QAAR,CAArB;MACJ;IAGX;;EA7DF,CApBK;;EAqFbC,OAAO,GAAG;IACD,KAAKnB,IAAL;EAEJ;;AAxFQ,CAAf", "names": ["name", "data", "sys", "ti", "msg", "open", "appKey", "secret<PERSON>ey", "methods", "init", "res", "$API", "common", "getconfig", "post", "id", "JSON", "parse", "res2", "res3", "updateInfo", "response", "saveconfig", "stringify", "code", "$router", "replace", "path", "created"], "sourceRoot": "", "sources": ["C:\\jsjy\\jsjy\\scui-master\\src\\views\\setting\\system\\index.vue"], "sourcesContent": ["<template>\n\t<el-main>\n\t\t<el-card shadow=\"never\">\n\t\t\t<el-tabs tab-position=\"top\">\n\n\t\t\t\t<el-tab-pane label=\"系统设置\">\n\t\t\t\t\t<el-form ref=\"form\" :model=\"sys\" label-width=\"100px\" style=\"margin-top: 20px;\">\n\t\t\t\t\t\t<el-form-item label=\"系统名称\">\n\t\t\t\t\t\t\t<el-input v-model=\"sys.name\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"LogoUrl\">\n\t\t\t\t\t\t\t<el-input v-model=\"sys.logoUrl\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"登录开关\">\n\t\t\t\t\t\t\t<el-switch v-model=\"sys.login\"></el-switch>\n\t\t\t\t\t\t\t<div class=\"el-form-item-msg\" data-v-b33b3cf8=\"\">关闭后普通用户无法登录，仅允许管理员角色登录</div>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"密码验证规则\">\n\t\t\t\t\t\t\t<el-input v-model=\"sys.passwordRules\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"版权信息\">\n\t\t\t\t\t\t\t<el-input type=\"textarea\" :autosize=\"{minRows: 4}\" v-model=\"sys.copyright\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"updateInfo(1)\">保存</el-button>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t</el-form>\n\t\t\t\t</el-tab-pane>\n\n\t\t\t\t<el-tab-pane label=\"短信配置\">\n\t\t\t\t\t<el-form ref=\"form\" :model=\"msg\" label-width=\"100px\" style=\"margin-top: 20px;\">\n\t\t\t\t\t\t<el-form-item label=\"短信开关\">\n\t\t\t\t\t\t\t<el-switch v-model=\"msg.open\"></el-switch>\n\t\t\t\t\t\t\t<div class=\"el-form-item-msg\" data-v-b33b3cf8=\"\">关闭后用户无法收到短信，但日志中将记录</div>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"appKey\">\n\t\t\t\t\t\t\t<el-input v-model=\"msg.appKey\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"secretKey\">\n\t\t\t\t\t\t\t<el-input v-model=\"msg.secretKey\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"updateInfo(2)\">保存</el-button>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t</el-form>\n\t\t\t\t</el-tab-pane>\n\n\n\n\t\t\t\t<el-tab-pane label=\"题目设置\">\n\t\t\t\t\t<el-form ref=\"form\" :model=\"ti\" label-width=\"200px\" style=\"margin-top: 20px;\">\n\t\t\t\t\t\t \n\t\t\t\t\t\t<el-form-item label=\"第1次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time1\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"第2次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time2\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"第3次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time3\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"第4次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time4\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"第5次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time5\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"第6次时间间隔（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.time6\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\n\n\t\t\t\t\t\t<el-form-item label=\"题目计分起始数\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.num1\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\n\n\t\t\t\t\t\t<el-form-item label=\"题目不足+-分数1\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.score1\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t<el-form-item label=\"题目不足+-分数2\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.score2\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"题目不足+-分数3\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.score3\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\n\n\t\t\t\t\t\t<el-form-item label=\"选择题出题数量\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.tinum1\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\n\n\t\t\t\t\t\t<el-form-item label=\"填空题出题数量\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.tinum2\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t<el-form-item label=\"简答题出题数量\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.tinum3\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n                      <el-form-item label=\"解锁下一章节分值\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.jsscore\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\n\n\t\t\t\t\t\t   <el-form-item label=\"章节长时间未作提醒时间1（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.lessontime1\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t   <el-form-item label=\"章节长时间未作提醒时间2（秒）\">\n\t\t\t\t\t\t\t<el-input v-model=\"ti.lessontime2\"></el-input>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"updateInfo(3)\">保存</el-button>\n\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t</el-form>\n\t\t\t\t</el-tab-pane>\n\t\t\t\t<el-tab-pane label=\"关于我们\">\n\t\t\t\t\t\n\t\t\t\t</el-tab-pane>\n\n\n\t\t\t</el-tabs>\n\t\t</el-card>\n\t</el-main>\n</template>\n\n<script>\n\texport default {\n\t\tname: 'system',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsys: {\n\t\t\t\t\t\n\t\t\t\t},\n\n\t\t\t\tti: {\n\t\t\t\t\t\n\t\t\t\t},\n\t\t\t\tmsg: {\n\t\t\t\t\topen: true,\n\t\t\t\t\tappKey: \"\",\n\t\t\t\t\tsecretKey: \"\"\n\t\t\t\t},\n\t\t\t\n\t\t\t}\n\t\t},\n\t\n\t\tmethods: {\n\n       async\tinit() {\n    \n                 let res= await this.$API.common.getconfig.post({id:1});\n\n\t\t\t\t \n\n                 if(res.data){\n\n                    this.sys=JSON.parse(res.data);\n                 }\n\n\t\t\t\tlet\tres2= await this.$API.common.getconfig.post({id:2});\n\n\t\t\t\t \n\n                 if(res2.data){\n\n                    this.msg=JSON.parse(res2.data);\n                 }\n\n\n\t\t\t\t let\tres3= await this.$API.common.getconfig.post({id:3});\n\n\t\t\t\t \n\n                 if(res3.data){\n\n                    this.ti=JSON.parse(res3.data);\n                 }\n\n\n\n             \n            },\n           \n           \n    \n            //更新用户信息\n            async updateInfo(id) {\n             \n                        let response=null;\n\t\t\t\t\t\tif(id==1){\n\t\t\t\t\t\t\t\tresponse=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.sys),id:1});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(id==2){\n\t\t\t\t\t\t\t\tresponse=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.msg),id:2});\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\tif(id==3){\n\t\t\t\t\t\t\t\tresponse=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.ti),id:3});\n\t\t\t\t\t\t}\n\n                        if (response.code == \"200\") {\n                            //刷新当前路由\n                            this.$router.replace({ path: '/refresh' });\n                        }\n                   \n              \n            },\n\n\t\t\t \n\t\t},\n\t\t created() {\n            this.init();\n    \n        }\n\t}\n</script>\n\n<style>\n</style>\n"]}, "metadata": {}, "sourceType": "module"}