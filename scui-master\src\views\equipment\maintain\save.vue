<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/10 16:45:04
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="设备" prop="equipment_id">
					<sc-select v-model="form.equipment_id"
							   :selectConfig="selectConfig.equipment"
							   clearable :apiObj="$API.siteEquipment.list"
							   filterable :disabled="mode=='show'" :method="method.equipment"
							   style="width: 100%">
						<template #option="{data}">
							<span style="float: left">{{ data.type }}</span>
							<span style="float: right;color: #999; font-size: 13px">
								{{ data.site_info_name }}
							</span>
						</template>
					</sc-select>

				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="设备维护项目" prop="equipment_maintain">
					<el-input v-model="form.equipment_maintain" clearable></el-input>
				</el-form-item>
			</el-col>

			<el-col :lg="24">
				<el-form-item label="维修维护时间" prop="equipment_maintain_date">
					<el-date-picker
						v-model="form.equipment_maintain_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="24">
				<el-form-item label="维修维护说明" prop="equipment_maintain_note">
					<el-input v-model="form.equipment_maintain_note" :autosize="{ minRows: 2, maxRows: 4 }"
							  type="textarea" clearable></el-input>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",

				equipment_id: "",

				equipment_maintain: "",

				equipment_maintain_note: "",

				equipment_maintain_date: "",

			},
			//验证规则
			rules: {

				equipment_id: [
					{required: true, message: '请选择设备'}
				],

				equipment_maintain: [
					{required: true, message: '请输入设备维护项目'}
				],

				equipment_maintain_note: [
					{required: true, message: '请输入维修维护说明'}
				],

				equipment_maintain_date: [
					{required: true, message: '请输入维修维护时间'}
				],

			},
			selectConfig: {
				equipment: {
					label: 'type',
					value: 'id'
				},
			},
			method: {
				equipment: "post"
			}
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
