function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {

    tabsAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            let model = _this.model("lesson");
            const user = yield _this.session('userInfo');

            let res = yield model.where({ "type": user.level, "del_flag": 0, "parent_id": 0 }).field("id,name").select();

            return _this.json(res);
        })();
    }

    teacherAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            let model = _this2.model("user");
            let id = _this2.post("id");
            console.log(id);
            let res = yield model.where({ "id": id, "del_flag": 0 }).field("*").find();
            delete res.password;

            let domain = _this2.config("domain");
            if (!think.isEmpty(res.skinfo)) {

                res.skinfo = res.skinfo.replace(/src="\/static\/upload/g, `src="${domain}/static/upload`);
            }

            return _this2.json(res);
        })();
    }

    jisuanAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const user = yield _this3.session('userInfo');
            let model = _this3.model("bk_countflag");
            let datetime = think.datetime();
            let data = yield model.where({ userid: user.id }).find();
            if (!think.isEmpty(data)) {
                datetime = data.lasttime;
            }

            datetime = think.datetime(datetime, "YYYY-MM-DD");

            let model2 = _this3.model("bk");
            let res = yield model2.where({ schoolid: user.school, del_flag: 0, state: 2, fbdate: { ">=": datetime } }).select();

            let model3 = _this3.model("bk_user");
            for (let item of res) {
                let tmp = yield model3.where({ bkid: item.id, uid: user.id }).find();
                if (think.isEmpty(tmp)) {
                    //计算错题
                    let tklist = yield _this3.model("bk_list").where({ bkid: item.id }).field("tkid").select();
                    tklist = tklist.map(function (item) {
                        return item.tkid;
                    });

                    let errorlist = yield _this3.model("tk_record").where({ userid: user.id, flag: 0, tkid: ["in", tklist] }).where("create_date >= '" + think.datetime(new Date(), 'YYYY-MM-01') + "'").field("tkid").select();

                    //计算讲解

                    let jjlist = yield _this3.model("jj_record").where({ uid: user.id, tkid: ["in", tklist] }).field("tkid").select();
                    //计算分数段


                    let score = yield _this3.model("lesson_user").where({ userid: user.id, lessonid: ["in", item.lesson.split(",")] }).field("score").find();

                    if (think.isEmpty(score)) {
                        score = 50;
                    } else {
                        score = score.score;
                    }

                    let max = score + 10;
                    let min = score - 10;

                    let scorelist = yield _this3.model("tk").where({ lessonid: ["in", item.lesson.split(",")], score: ["between", [min, max]], "id": ["in", tklist] }).field("id").select();

                    let para = {};
                    para.bkid = item.id;
                    if (errorlist.length > 0) {
                        para.iferror = 1;
                    } else {
                        para.iferror = 0;
                    }
                    if (jjlist.length > 0) {
                        para.ifjj = 1;
                    } else {
                        para.ifjj = 0;
                    }
                    if (scorelist.length > 0) {
                        para.ifscore = 1;
                    } else {
                        para.ifscore = 0;
                    }

                    para.state = 0;
                    para.uid = user.id;
                    para.create_date = think.datetime();
                    para.errorid = errorlist.map(function (item) {
                        return item.tkid;
                    }).join(",");
                    para.jjid = jjlist.map(function (item) {
                        return item.tkid;
                    }).join(",");
                    para.scoreid = scorelist.map(function (item) {
                        return item.id;
                    }).join(",");
                    yield model3.add(para);

                    item.state = 1;
                }
            }
        })();
    }

    listAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            let model = _this4.model("bk_user");
            let lessonid = _this4.post("lessonid");
            const user = yield _this4.session('userInfo');
            console.log(user);

            console.log(_this4.post());

            let where = {};

            where['c.schoolid'] = user.school;
            where['c.del_flag'] = '0';
            where['c.state'] = ["!=", 1];
            where['toplesson'] = lessonid;
            const page = _this4.post('page') ? _this4.post('page') : 1;
            const rows = _this4.post('pageSize') ? _this4.post('pageSize') : 20;
            let iferror = _this4.post("iferror");

            let state = _this4.post("state");
            if (!think.isEmpty(state)) {
                if (state == 1) {

                    where["c.state"] = 1;
                } else if (state == 2) {

                    where["c.state"] = ["in", "2,3"];
                }
            }

            if (!think.isEmpty(iferror) && iferror == 1) {

                where["bu.iferror"] = 1;
            }
            let ifjj = _this4.post("ifjj");

            if (!think.isEmpty(ifjj) && ifjj == 1) {

                where["bu.ifjj"] = 1;
            }

            let res = yield model.alias("bu").where(where).page(page, rows).join("left join sys_bk as c on c.id=bu.bkid").join("left join sys_user t on t.id=c.teacherid").field("bu.iferror,bu.ifjj,bu.ifscore,c.*,t.name,t.avatar,DATE_FORMAT(c.create_date, '%m-%d %H:%i') as d").order("c.create_date desc").countSelect();
            let data = {};

            for (let item of res.data) {
                item.lessonname = item.lessonname.replace(/,/g, '<br>');
            }

            data.code = 200;
            data.count = res.count;
            data.data = res.data;
            data.msg = '';

            return _this4.json(data);
        })();
    }

    infoAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            let model = _this5.model("bk_list");
            let id = _this5.post("id");
            let res = yield model.alias("c").join("sys_tk t on t.id=c.tkid").where({ bkid: id }).field("c.*,t.jx_p,t.tm_p").select();

            return _this5.json(res);
        })();
    }
    listtmAction() {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            let res = yield _this6.model("bk_list").alias("c").join("sys_tk t on t.id=c.tkid").where({ "bkid": _this6.post("id") }).field("c.*,t.no,t.type").select();
            let id = _this6.post("id");
            let user = yield _this6.session("userInfo");
            let res2 = yield _this6.model("bk_user").where({ bkid: id, uid: user.id }).find();

            if (!think.isEmpty(res2)) {
                let errorlist = res2.errorid.split(",");
                let jjlist = res2.jjid.split(",");
                let scorelist = res2.scoreid.split(",");

                res.forEach(function (item) {
                    if (errorlist.includes(item.id)) {
                        item.error = 1;
                    }
                    if (jjlist.includes(item.id)) {
                        item.jj = 1;
                    }
                    if (scorelist.includes(item.id)) {
                        item.score = 1;
                    }
                });
            }

            return _this6.json({ data: res });
        })();
    }

    countflagAction() {
        var _this7 = this;

        return _asyncToGenerator(function* () {
            const user = yield _this7.session('userInfo');
            let model = _this7.model("bk_countflag");
            let data = {};
            data.userid = user.id;
            data.lasttime = think.datetime();
            yield model.where({ userid: user.id }).delete();
            yield model.add(data);
            return _this7.json({ code: 200, msg: "", data: {} });
        })();
    }

    countAction() {
        var _this8 = this;

        return _asyncToGenerator(function* () {

            const user = yield _this8.session('userInfo');
            let model = _this8.model("bk_countflag");
            let datetime = think.datetime();
            let data = yield model.where({ userid: user.id }).find();
            if (!think.isEmpty(data)) {
                datetime = data.lasttime;
            }
            let model2 = _this8.model("bk");
            let res = yield model2.where({ schoolid: user.school, del_flag: 0, state: 2, fbdate: { ">=": datetime } }).count();
            return _this8.json({ code: 200, msg: "", data: { count: res } });
        })();
    }

};
//# sourceMappingURL=bk.js.map