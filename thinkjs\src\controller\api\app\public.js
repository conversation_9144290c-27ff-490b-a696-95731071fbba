
const {copyFile} = require('fs/promises');
module.exports = class extends think.Controller {

    async uploadAction() {
        const param = this.get();
        const action = this.get('action');
    
    
        const path = require('path');
        const fs = require('fs');
          const file = this.file('file');
          const filepath = file.path;
    
          const nameArr = file.name.split('.');
          const basename = think.uuid() + '.' + nameArr[nameArr.length - 1];
          const YYYYMMDD = think.datetime(new Date(), 'YYYY-MM-DD');
          const staticPath = path.resolve(think.ROOT_PATH, 'www/static');
          const uploadPath = path.resolve(staticPath, 'upload');
          const relativePath = path.resolve(uploadPath, YYYYMMDD);
    
          // 文件夹不存在则创建
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath);
          }
    
          if (!fs.existsSync(relativePath)) {
            fs.mkdirSync(relativePath);
          }
    
          await copyFile(filepath, path.resolve(relativePath, `${basename}`));
     
          this.json({
            state: 'SUCCESS',
            url: `/static/upload/${YYYYMMDD}/${basename}`,
            title: basename,
            original: file.name
          });
        
    
 
      }
    
  };
  