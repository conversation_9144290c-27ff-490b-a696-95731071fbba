const BaseRest = require('../rest.js');

module.exports = class extends BaseRest {


    async saveconfigAction() {
        let data = this.post("data");
        let model = this.model("config");

        let id=this.post("id");
        console.log(data);
        if(id==1){
            think.config('system', JSON.parse(data));

        }
        if(id==2){
            think.config('sms', JSON.parse(data));

        }
        if(id==3){
            think.config('ti', JSON.parse(data));

        }
        let res=await model.where({id:id}).find();
        if(think.isEmpty(res)){
            let data={};
            data.id=id;
            data.data=data;
            await model.add(data);

        }else{
            await model.where({ id: id }).update({ "data": data });
        }

       

        this.json({ code: 200 });



    }


    async getconfigAction() {

        let model = this.model("config");

        let id=this.post("id")

        let res = await model.where({ id: id }).find();

        this.json(res);



    }
}