<template>
	<el-main>
		<el-card shadow="never">
			<el-tabs tab-position="top">

				<el-tab-pane label="系统设置">
					<el-form ref="form" :model="sys" label-width="100px" style="margin-top: 20px;">
						<el-form-item label="系统名称">
							<el-input v-model="sys.name"></el-input>
						</el-form-item>
						<el-form-item label="LogoUrl">
							<el-input v-model="sys.logoUrl"></el-input>
						</el-form-item>
						<el-form-item label="登录开关">
							<el-switch v-model="sys.login"></el-switch>
							<div class="el-form-item-msg" data-v-b33b3cf8="">关闭后普通用户无法登录，仅允许管理员角色登录</div>
						</el-form-item>
						<el-form-item label="密码验证规则">
							<el-input v-model="sys.passwordRules"></el-input>
						</el-form-item>
						<el-form-item label="版权信息">
							<el-input type="textarea" :autosize="{minRows: 4}" v-model="sys.copyright"></el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="updateInfo(1)">保存</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>

				<el-tab-pane label="短信配置">
					<el-form ref="form" :model="msg" label-width="100px" style="margin-top: 20px;">
						<el-form-item label="短信开关">
							<el-switch v-model="msg.open"></el-switch>
							<div class="el-form-item-msg" data-v-b33b3cf8="">关闭后用户无法收到短信，但日志中将记录</div>
						</el-form-item>
						<el-form-item label="appKey">
							<el-input v-model="msg.appKey"></el-input>
						</el-form-item>
						<el-form-item label="secretKey">
							<el-input v-model="msg.secretKey"></el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="updateInfo(2)">保存</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>



				<el-tab-pane label="题目设置">
					<el-form ref="form" :model="ti" label-width="200px" style="margin-top: 20px;">
						 
						<el-form-item label="第1次时间间隔（秒）">
							<el-input v-model="ti.time1"></el-input>
						</el-form-item>
						<el-form-item label="第2次时间间隔（秒）">
							<el-input v-model="ti.time2"></el-input>
						</el-form-item>
						<el-form-item label="第3次时间间隔（秒）">
							<el-input v-model="ti.time3"></el-input>
						</el-form-item>
						<el-form-item label="第4次时间间隔（秒）">
							<el-input v-model="ti.time4"></el-input>
						</el-form-item>
						<el-form-item label="第5次时间间隔（秒）">
							<el-input v-model="ti.time5"></el-input>
						</el-form-item>
						<el-form-item label="第6次时间间隔（秒）">
							<el-input v-model="ti.time6"></el-input>
						</el-form-item>
						

						<el-form-item label="题目计分起始数">
							<el-input v-model="ti.num1"></el-input>
						</el-form-item>


						<el-form-item label="题目不足+-分数1">
							<el-input v-model="ti.score1"></el-input>
						</el-form-item>

						<el-form-item label="题目不足+-分数2">
							<el-input v-model="ti.score2"></el-input>
						</el-form-item>
						<el-form-item label="题目不足+-分数3">
							<el-input v-model="ti.score3"></el-input>
						</el-form-item>


						<el-form-item label="选择题出题数量">
							<el-input v-model="ti.tinum1"></el-input>
						</el-form-item>


						<el-form-item label="填空题出题数量">
							<el-input v-model="ti.tinum2"></el-input>
						</el-form-item>
						<el-form-item label="简答题出题数量">
							<el-input v-model="ti.tinum3"></el-input>
						</el-form-item>
                      <el-form-item label="解锁下一章节分值">
							<el-input v-model="ti.jsscore"></el-input>
						</el-form-item>
						

						   <el-form-item label="章节长时间未作提醒时间1（秒）">
							<el-input v-model="ti.lessontime1"></el-input>
						</el-form-item>
						   <el-form-item label="章节长时间未作提醒时间2（秒）">
							<el-input v-model="ti.lessontime2"></el-input>
						</el-form-item>
						
						
						<el-form-item>
							<el-button type="primary" @click="updateInfo(3)">保存</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>


			</el-tabs>
		</el-card>
	</el-main>
</template>

<script>
	export default {
		name: 'system',
		data() {
			return {
				sys: {
					
				},

				ti: {
					
				},
				msg: {
					open: true,
					appKey: "",
					secretKey: ""
				},
			
			}
		},
	
		methods: {

       async	init() {
    
                 let res= await this.$API.common.getconfig.post({id:1});

				 

                 if(res.data){

                    this.sys=JSON.parse(res.data);
                 }

				let	res2= await this.$API.common.getconfig.post({id:2});

				 

                 if(res2.data){

                    this.msg=JSON.parse(res2.data);
                 }


				 let	res3= await this.$API.common.getconfig.post({id:3});

				 

                 if(res3.data){

                    this.ti=JSON.parse(res3.data);
                 }



             
            },
           
           
    
            //更新用户信息
            async updateInfo(id) {
             
                        let response=null;
						if(id==1){
								response=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.sys),id:1});
						}
						if(id==2){
								response=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.msg),id:2});
						}


						if(id==3){
								response=  await this.$API.common.saveconfig.post({data:JSON.stringify(this.ti),id:3});
						}

                        if (response.code == "200") {
                            //刷新当前路由
                            this.$router.replace({ path: '/refresh' });
                        }
                   
              
            },

			 
		},
		 created() {
            this.init();
    
        }
	}
</script>

<style>
</style>
