const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {
  async tagsAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('tag');
    let userInfo = await this.session('userInfo');
    where['create_by'] = userInfo.id;
    const response = await model.where(where).find();
    if (think.isEmpty(response)) {
      where.tags = '[]';
      let addData = await this.addData(where);
      await model.add(addData);
      respData = {
        code: 200,
        data: addData,
        msg: ''
      };
    } else {
      respData = {
        code: 200,
        data: response,
        msg: ''
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const where = this.post() ? this.post() : {};
    const model = this.model('tag');
    const response = await model.where({create_by: where.id}).update({tags: where.tags,type: where.type});
    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }
};
