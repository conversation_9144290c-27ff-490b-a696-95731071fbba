import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/site/seller/page`,
		name: "销售人员数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/site/seller/add`,
		name: "销售人员数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/site/seller/save`,
		name: "销售人员数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/site/seller/info`,
		name: "销售人员数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/site/seller/delete`,
		name: "删除销售人员数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
