<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		
		<el-container>
			<el-header>
				<div class="left-panel">
				
				</div>
				<div class="right-panel">
					<div class="right-panel-search">
						<el-input v-model="search.name" placeholder="登录账号 / 姓名" clearable></el-input>
						<el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
					</div>
				</div>
			</el-header>
			<el-main class="nopadding" >
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="姓名" prop="name" width="150" sortable='custom'></el-table-column>
					<el-table-column label="所属模块" prop="office_name" width="200"></el-table-column>
				</scTable>
			</el-main>
		</el-container>
	
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="select()">选择</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
            search:{
                name:""
            },
			mode: "add",
			titleMap: {
				add: '选择用户',
				edit: '选择用户',
				show: '选择用户'
			},
			menuList: null,
			visible: false,
            apiObj: this.$API.user.list,
			selection: [],
			isSaveing: false,
			//菜单树配置
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true,
			},
			//表单数据
			form: {
				id: "",
				login_name: "",
				name: "",
				group: "",
				office_id: "",
			},
			//验证规则
			rules: {
				
			},
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this;
		},
		//加载树数据
		async getGroup() {
			//加载角色树
			var role = await this.$API.role.select.get();
			this.groups = role;
			this.menuList= await this.$API.office.list.get();
		},

		 selectionChange(selection) {
      this.selection = selection;
    },
        upsearch() {

			this.$refs.table.upData(this.search);
		
		},
		async select() {


      if(this.selection){
		



         this.$emit("success", this.selection);
			this.$emit('closed')
		//this.visible=false;
      }else{
        this.$alert("未选中文件", "提示", { type: "error" });

      }




    },
		//表单注入数据
		setData(data) {
			this.form.id = data.id
			this.form.login_name = data.login_name
			this.form.name = data.name
			this.form.role = data.group
			this.form.office_id = parseInt(data.office_id);
			//this.menuList = data.office;

			
		}
	}
}
</script>

<style>
</style>
