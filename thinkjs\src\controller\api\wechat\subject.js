
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {
    
    
        async listAction(){
            
            let model=this.model("lesson");
            let user =await this.session("userInfo");
            
            let lesson=await model.where({"type":user.level,"parent_id":0,"del_flag":0}).select();
            
            return this.success(lesson)
            
            
        }
    
    
    
}