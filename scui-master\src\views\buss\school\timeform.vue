<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				 
				<el-col :span="12">
					<el-form-item label="时长(天)" prop="num">
						<el-input v-model="form.num" placeholder="时长" clearable></el-input>
					</el-form-item>
				</el-col>

              

		<el-col :span="12">
		  	<el-form-item label="说明" prop="remarks">
						<el-input v-model="form.remarks" placeholder="说明" clearable></el-input>
					</el-form-item>
		</el-col>

				
            
		
			</el-row>
			 
    <el-row>
        <el-col :span="12">
            <el-form-item label="类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择类型">
                    <el-option label="增加" value="增加"></el-option>
                    <el-option label="减少" value="减少"></el-option>
                </el-select>
            </el-form-item>
        </el-col>
    </el-row>

			 
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	 
	data() {
		return {
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
			mode: "add",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			menuList: null,
			visible: false,
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true
			},
			isSaveing: false,
		 
			//表单数据
			form: {
			   num:0,
				schoolid:0,
                type:"增加",
			 
                remarks:""
				 
			},
			//验证规则
			rules: {
			 
				num: [
					{required: true, message: '请输入时长'}
				],
                type: [
					{required: true, message: '请选择类型'}
				],
		 
			
				
				 
			},
			props: {
				label: 'name',
				value: 'id'


			},
			apiObj: this.$API.user.listselect,
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		 
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},

		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => { 
			 
				if (valid) {

 
				

					this.isSaveing = true;

              
  
					let res = await  this.$API.common.util.post(`/buss/school/savetime`, this.form)
					this.isSaveing = false;
					if (res.code == 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				} else {
					return false;
				}
			})
		},
		//表单注入数据
	async	setData(data) {
			this.form.schoolid=data.schoolid;

		}
	}
}
</script>

<style>
</style>
