import config from "@/config";
import http from "@/utils/request";

const role = {
	select: {
		url: `${config.API_URL}/role/select`,
		name: "角色选择列表",
		get: async function () {
			return await http.get(this.url);
		}
	},
	list: {
		url: `${config.API_URL}/role/list`,
		name: "角色列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

	remove: {
		url: `${config.API_URL}/role/remove`,
		name: "删除角色",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

	save: {
		url: `${config.API_URL}/role/save`,
		name: "保存角色",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
	savep: {
		url: `${config.API_URL}/role/savep`,
		name: "保存权限",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},

	getmenus: {
		url: `${config.API_URL}/role/getmenus`,
		name: "查询已有权限",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	}
};
export default role;
