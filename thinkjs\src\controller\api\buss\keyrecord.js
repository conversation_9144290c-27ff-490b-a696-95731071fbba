const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {


  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const learnLogModel = this.model('keyrecord');
    where['p.del_flag'] = 0;
    
    //let dataScopeWhere = await this.dataScope('p');

    const userInfo = await this.session('userInfo');
            

    if(userInfo.name!="系统管理员"){

      let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

      if(think.isEmpty(schoolres)){
          where["p.schoolid"]=0
         
      }else{

          where["p.schoolid"]=["in",schoolres.map(item => item.id)];
      }

   
    }


    const response = await learnLogModel
      .alias('p')
      
     .join(["buss_school l on l.id=p.schoolid"])
     .join(["sys_user u on u.id=p.create_by"])
     .join(["left join buss_keytype t on t.id=p.type"])
     .join(["sys_yx yx on yx.id=p.yx"])
     .join(["sys_lesson le on le.id=p.lesson"])
      .page(page, rows).where(where)
      .order('p.create_date desc').field("p.*,le.name as lessonname,l.name as schoolname,u.name as uname,t.name as typename,yx.name as yxname").countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }


  async ffkeyAction(){
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    let key=await this.model("key").where({"id":id}).find();
    let data=await this.model("key").where({"id":id}).update({"state":1});
    await this.model("keyrecord").where({"id":key.keyrecord}).decrement("num2",1);
    respData = {
      code: 200,
      data: {},
      message: '成功'
    };
    return this.json(respData);
  }


  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    let data=await this.model("keyrecord").where({"id":id}).find();
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('keyrecord');
      await model.where({id: id}).update(await this.deleteData());

      await this.model("key").where({"keyrecord":id}).update({"del_flag":1});

     //因为增加了日期类型，此处先关闭
     //await this.model("school").where({"id":data.schoolid}).increment("canusetime",data.num2*data.usetime);


      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }


  //生成6位数随机数 不需要大小值，只需要6位数
 async getRandomInt(){
    const num = Math.floor(Math.random() * 1000000);
    return num.toString().padStart(6, '0');
 }

  async saveAction() {
    let userInfo=await this.session("userInfo");
    let respData = {};
    const model = this.model('keyrecord');
    let num=this.post("num");

    let data={};
    data.schoolid=this.post("schoolid");
    data.title=this.post("title");
    data.num=num;
    data.num2=num;
    data.yuanjia=this.post("yuanjia");
    data.price=this.post("price");
    data.usetime=this.post("usetime");
    data.type2=this.post("type2");
    data.todate=this.post("todate");
    data.keystart=this.post("keystart");
    data.len=this.post("len");
    data.yxq=this.post("yxq");
    
    data.create_by=userInfo.id;
    data.create_date=think.datetime();
   data.type=this.post("type");

   data.yx=this.post("yx");
   data.allusetime=this.post("usetime")*this.post("num");
    console.log(data);

    let keystartres=await this.model("keyrecord").where({"keystart":data.keystart}).find();
    if(!think.isEmpty(keystartres)){
        respData = {
            code: 400,
            data: {},
            message: '激活码规则重复！请重新输入！'
          };
          return this.json(respData);
    }

  
    data.nj=this.post("nj");





    if(data.keystart.indexOf("&")<0){

      respData = {
        code: 400,
        data: {},
        message: '激活码命名规则错误，必须包含&'
      };
      return this.json(respData);
    }
    let id=  await model.add(data);
    let arry=[];



    let keysarr=await this.generateKeys(data.keystart,num);
    console.log(keysarr);

    for(let k of keysarr){
      let keytmp={};

      // if(data.type=="随机数"){
      //   let randomnum=await this.getRandomInt();
      //   keytmp.key=data.keystart+randomnum;
      // }else{ 
      //   //按顺序
      //   keytmp.key=data.keystart+i.toString().padStart(data.len, '0');
      // }
      keytmp.key=k;
      keytmp.keyrecord=id;
      keytmp.create_date=think.datetime();
      keytmp.state=0;
      keytmp.yxq=this.post("yxq");
      keytmp.state2=0;
      keytmp.create_by=userInfo.id;
      arry.push(keytmp);
      

        // let one={};
        // this.addData(one);
        // one.keyrecord=this.post("keyrecordstart")+think.uuid("v1");
        // one.state=0;
        // one.school=this.post("school");
        // //arry.push(one);

        // await model.add(one);
    }
    //console.log(arry);
await this.model("key").addMany(arry);

//上看看

//写个测试
//因为增加了日期类型，此处先关闭
//await this.model("school").where({"id":data.schoolid}).decrement("canusetime",data.allusetime);
      
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    
    return this.json(respData);
  }



  async generateKeys(pattern, quantity) {
    // 计算顺序号的位数
    const serialLength = Math.max(quantity.toString().length, 2); // 至少2位
  
    return Array.from({ length: quantity }, (_, index) => {
      const serial = (index + 1).toString().padStart(serialLength, '0'); // 生成顺序号
  
      return pattern
        // 替换 * 为随机数
        .replace(/\*/g, () => Math.floor(Math.random() * 10).toString()) // 生成0-9的随机数
        // 替换 # 为随机字母
        .replace(/#/g, () => {
          const letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // 排除易混淆字符
          return letters[Math.floor(Math.random() * letters.length)];
        })
        // 替换 & 为补零的顺序号，逐个替换
        .replace(/&/g, () => serial);
    });
  }

  async getkeyAction(){
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    let data=await this.model("key").where({"keyrecord":id}).select();
    respData = {
      code: 200,
      data: data,
      message: ''
    };
    return this.json(respData);
  }

  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('keyrecord');
    where['p.del_flag'] = 0;
    const response = await model
      .alias('p')
      .field('p.*')
      .where(where)
      .select();
    respData = {
      code: 200,
      data: response,
      message: ''
    };
    return this.json(respData);
  }


 
};
