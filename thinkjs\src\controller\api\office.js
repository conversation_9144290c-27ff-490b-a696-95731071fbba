const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {


  async removeAction() {
    let id = this.post("id");

    let model = this.model("office")
    await model.where({"id": id}).update({"del_flag": 1});

    return this.json({"state": 1});
  }


  async getbyidAction() {
    let id = this.post("id");

    let model = this.model("office")
    let res = await model.where({"id": id}).find();

    return this.json(res);


  }
  async listAction() {
    let where = this.post("where")?this.post("where"):null;

    let model = this.model("office")
    let res = await model.where(where).select();

    return this.json({
      code:200,
      data:res,
      msg: "Success"
    });


  }


  async saveAction() {
    let model = this.model("office");
    let data = this.post();
    // Object.assign(data,data.meta);


    data.parent_id = data.parent;


    let parent = await model.where({"id": data.parent}).find();
    if (think.isEmpty(data.id)) {

      let id = await model.add(data);

      await model.where({"id": id}).update({"parent_ids": parent.parent_ids + "," + id});

    } else {
      let id = await model.where({"id": data.id}).update(data);
      await model.where({"id": data.id}).update({"parent_ids": parent.parent_ids + "," + data.id});
    }
    return this.json({state: 1})


  }

  async tree2Action() {

    let model = this.model("office");
    let usermodel = this.model("user");
    let where = this.get("where") ? JSON.parse(this.get("where")) : {}
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();

    let tree  = this.transTreeArray(data,'id','parent_id');

    this.json(tree);
  }

  async treeAction() {

    let model = this.model("office");
    let usermodel = this.model("user");
    let where = this.get("where") ? JSON.parse(this.get("where")) : {}
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();


    var tree = [];
    for (var t of data) {
      if (t.parent_id == '0') {
        let obj = {};
        obj.id = t.id;
        obj.sort = t.sort;
        obj.name = t.name;
        obj.title = t.name;
        obj.level = t.level;
        obj.people_number = t.people_number;
        obj.people_outside = t.people_outside;
        let user = await usermodel.where({"id": t.masterid}).field("id,name").find();
        obj.master = user;
        obj.path = t.path;

        obj.component = t.component;
        let meta = {};
        meta.title = t.name;
        meta.icon = t.icon;
        meta.type = t.type;
        obj.type = t.type

        obj.meta = meta;

        await this.getchild(obj, data);
        tree.push(obj);

      }

    }
    this.json(tree);

  }


  async getchild(tree, menus) {
    tree.hasChildren = false;
    let usermodel = this.model("user");
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id) {
        let obj = {};
        obj.id = t.id;
        let user = await usermodel.where({"id": t.masterid}).field("id,name").find();
        obj.master = user;
        obj.name = t.name;
        obj.title = t.name;
        obj.sort = t.sort;
        obj.people_number = t.people_number;
        obj.people_outside = t.people_outside;
        obj.level = t.level;
        obj.path = t.path;
        obj.type = t.type

        obj.component = t.component;
        let meta = {};
        meta.title = t.name;
        meta.icon = t.icon;
        meta.type = t.type;

        obj.meta = meta;
        await this.getchild(obj, menus);
        childs.push(obj);
      }
    }
    tree.children = childs;


  }

  async dictAction() {

  }


async  getselectAction(){
  let model = this.model("office");

  let where = this.get("where") ? JSON.parse(this.get("where")) : {}

    where.type=2;
    where.id=[">",1];
    where.del_flag = 0;
    let data = await model.where(where).order(["sort asc"]).select();

    return this.json(data);


}

  async  billselectAction(){
    let model = this.model("office");

    let where = this.post("where") ? this.post("where"): {}

    let data = await model.where(where).order(["sort asc"]).select();

    return this.json(data);


  }
}
