<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="类型名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入类型名称" clearable></el-input>
					</el-form-item>
				</el-col>

                 
				
				<el-col :span="12">
					<el-form-item label="多次使用" prop="allow_multiple_use">
						<el-radio-group v-model="form.allow_multiple_use">
							<el-radio :label="1">允许</el-radio>
							<el-radio :label="0">不允许</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				
				<el-col :span="24">
					<el-form-item label="类型描述" prop="description">
						<el-input type="textarea" v-model="form.description" rows="4" placeholder="请输入类型描述"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增激活码类型',
				edit: '编辑激活码类型',
				show: '查看激活码类型'
			},
			visible: false,
			isSaveing: false,
			
			// 表单数据
			form: {
				name: "",
				duration: 30,
				allow_multiple_use: 0,
				description: ""
			},
			
			// 验证规则
			rules: {
				name: [
					{required: true, message: '请输入类型名称', trigger: 'blur'},
					{min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}
				],
				 
			}
		}
	},
	methods: {
		// 显示对话框
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			if (mode === 'add') {
				this.resetForm();
			}
			return this
		},
		
		// 表单提交方法
		async submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					
					try {
						const res = await this.$API.keytype.save.post(this.form);
						
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode);
							this.visible = false;
							this.$message.success("操作成功");
						} else {
							this.$alert(res.message, "提示", {type: 'error'});
						}
					} catch (error) {
						console.error('保存激活码类型失败', error);
						this.$alert('保存激活码类型失败', "提示", {type: 'error'});
					} finally {
						this.isSaveing = false;
					}
				} else {
					return false;
				}
			});
		},
		
		// 表单注入数据
		setData(data) {
			this.form.id = data.id;
			this.form.name = data.name;
			this.form.duration = data.duration;
			this.form.allowMultipleUse = data.allowMultipleUse;
			this.form.description = data.description;
			return this;
		},
		
		// 重置表单
		resetForm() {
			if (this.$refs.dialogForm) {
				this.$refs.dialogForm.resetFields();
			}
			this.form = {
				name: "",
				duration: 30,
				allowMultipleUse: 0,
				description: ""
			};
		}
	}
}
</script>

<style scoped>
.el-input-number {
	width: 100%;
}
</style>