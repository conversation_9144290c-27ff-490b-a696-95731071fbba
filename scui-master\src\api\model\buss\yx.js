import config from "@/config"
import http from "@/utils/request"

const yx = {
  page: {
    url: `${config.API_URL}/buss/yx/page`,
    name: "获取营销人员列表",
    get: async function (params) {
        return await http.get(this.url, this.name, params);
    }
  },
  save: {
    url: `${config.API_URL}/buss/yx/save`,
    name: "保存营销人员",
    post:  async function (params) {
        return await http.post(this.url, this.name, params);
    }
  },
  delete: {
    url: `${config.API_URL}/buss/yx/delete`,
    name: "删除营销人员",
    post: async function (params) {
        return await http.post(this.url, this.name, params);
    }
  }
}
export default yx;