<template>
	<el-container>
		<el-aside width="300px">
			<el-container>
				<el-header>
					<el-input placeholder="输入关键字进行过滤" v-model="menuFilterText" clearable></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree ref="menu" class="menu" default-expand-all="true"  node-key="name" :data="menuList" :props="menuProps" draggable highlight-current :expand-on-click-node="false" check-strictly show-checkbox :filter-node-method="menuFilterNode" @node-click="menuClick">

						<template #default="{node, data}">
							
							<span class="custom-tree-node">
								<span class="label">{{ node.label }}</span>
								&nbsp;&nbsp;
								<el-button-group>
										<el-button icon="el-icon-plus" type="primary" size="small" @click.stop="add(node,data)"></el-button>
								</el-button-group>
                                
							</span>
						</template>

					</el-tree>
				</el-main>
				<el-footer style="height:51px;">
					<el-button type="primary" size="mini" icon="el-icon-plus" @click="add()"></el-button>
					<el-button type="danger" size="mini" v-if="isadmin" plain icon="el-icon-delete" @click="delMenu"></el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container>
			<el-main class="nopadding" style="padding:20px;">
				<save ref="save" :menu="menuList" @getOffice="getOffice"></save>
			</el-main>
		</el-container>
	</el-container>
</template>

<script>

	let newMenuIndex = 1;
	import save from './save'

	export default {
		name: "settingMenu",
		components: {
			save
		},
		data(){
			return {
				isadmin:false,
				menuList: [],
				menuProps: {
					label: (data)=>{
						return data.meta.title
					}
				},
				menuFilterText: ""
			}
		},
		watch: {
			menuFilterText(val){
				this.$refs.menu.filter(val);
			}
		},
		mounted() {

			var userInfo = this.$TOOL.data.get("USER_INFO");


		 
			if(userInfo.id==1||userInfo.id=="1"){
				this.isadmin=true;
			}
			this.getOffice();
		},
		methods: {
			//加载树数据
			async getOffice(){
				var res = await this.$API.lesson.list.get();
				this.menuList = res;
			},
			//树点击
			menuClick(data, node){
				console.log(data);
					console.log(node);
				var pid = node.level==1?undefined:node.parent.data.id;
				this.$refs.save.setData(data, pid)
			},
			//树过滤
			menuFilterNode(value, data){
				if (!value) return true;
				var targetText = data.name;
				return targetText.indexOf(value) !== -1;
			},
			//增加
			add(node){
				var newMenuName = "未命名" + newMenuIndex++;
				var newMenuData = {
					name: newMenuName,
					path: "",
					component: "",
					meta:{
						title: newMenuName,
						type: "menu"
					}
				}
				if(node){
					this.$refs.menu.append(newMenuData, node)
					var lastNode = node.childNodes[node.childNodes.length-1]
					this.$refs.menu.setCurrentKey(lastNode.data.name)
					var pid = node.data.name;
					this.$refs.save.setData(newMenuData, pid)
				}else{
					this.$refs.menu.append(newMenuData)
					var newNode = this.menuList[this.menuList.length-1]
					this.$refs.menu.setCurrentKey(newNode.name)
					this.$refs.save.setData(newMenuData)
				}

			},
			//删除菜单
		async	delMenu(){
				var CheckedNodes = this.$refs.menu.getCheckedNodes()
				if(CheckedNodes.length == 0){
					this.$message.warning("请选择需要删除的项")
					return false;
				}

				let flag=false;
				for(var item of CheckedNodes){

					let res=	await this.$API.lesson.remove.post(item);

					 
					if(res.state==0){
					flag=true;
					this.$message.error("该分类下存在子分类或题目")


					}else{
					this.$refs.menu.remove(item)
					}
				}



				 

				console.log(2)
				console.log(flag)
				if(!flag){
					this.$message.success("删除成功")

				}
				
				this.getOffice();

			}
		}
	}
</script>

<style scoped>
.custom-tree-node {display: flex;flex: 1;align-items: center;justify-content: space-between;font-size: 14px;padding-right: 24px;height:100%;}
.custom-tree-node .code {font-size: 12px;color: #999;}
.custom-tree-node .do {display: none;}
.custom-tree-node:hover .code {display: none;}
.custom-tree-node:hover .do {display: inline-block;}
</style><style scoped>
.custom-tree-node {display: flex;flex: 1;align-items: center;justify-content: space-between;font-size: 14px;padding-right: 24px;height:100%;}
.custom-tree-node .code {font-size: 12px;color: #999;}
.custom-tree-node .do {display: none;}
.custom-tree-node:hover .code {display: none;}
.custom-tree-node:hover .do {display: inline-block;}
</style>