import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/sell/play/page`,
		name: "销售数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/sell/play/add`,
		name: "销售数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/sell/play/save`,
		name: "销售数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/sell/play/info`,
		name: "销售数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	list: {
		url: `${config.API_URL}/sell/play/list`,
		name: "销售数据列表",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/sell/play/delete`,
		name: "删除销售数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
