<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    :width="600"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      :model="form"
      :rules="rules"
      :disabled="mode == 'show'"
      ref="dialogForm"
      label-width="100px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">



          <el-form-item label="学校" prop="schoolid">
              <el-select
         
              v-model="form.schoolid"
              placeholder="请选择学校"
            >
              <el-option label="请选择" value="0">请选择</el-option>
              <el-option
                v-for="item in schoollist"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
       
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年级" prop="nj">
            <el-select
              v-model="form.nj"
              placeholder="请选择科目"
              clearable
            >
              <el-option label="初中" value="初中"></el-option>
              <el-option label="高中" value="高中"></el-option>
            </el-select>
            <div class="el-form-item-msg"></div>
          </el-form-item>
        </el-col>
		<el-col :span="12">
			<el-form-item label="类型" prop="type2">
			<!-- 下拉 -->
			<el-select v-model="form.type2" placeholder="请选择类型">
				<el-option label="天数" value="天数">天数</el-option>
		 
			</el-select>
			</el-form-item>
		</el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="活动名称" prop="num">
            <el-input v-model="form.title" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="可用时长（天）" prop="usetime" v-if="form.type2=='天数'">
            <el-input v-model="form.usetime" clearable></el-input>
          </el-form-item>
		  <!-- 日期输入 -->
		  <el-form-item label="可用日期" prop="usetime" v-if="form.type2=='指定日期'">
			<el-date-picker
			  type="date"
			  placeholder="选择日期"
			  v-model="form.todate"
			  style="width: 100%"
			    :editable="false"
           
              format="YYYY-MM-DD HH:mm:ss"
              time-format="HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
       
			></el-date-picker>
		  </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="激活码命名" prop="keystart">
            <span
              >命名规则:输入*插入一个随机数，输入#插入一个随机字母，输入 &
              插入补0的顺序数</span
            >
            <el-input v-model="form.keystart" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生成数量" prop="num">
            <el-input type="number" v-model="form.num" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="激活码有效期" prop="yxq">
            <el-date-picker
              type="datetime"
              placeholder="选择日期"
              :editable="false"
              v-model="form.yxq"
              format="YYYY-MM-DD HH:mm:ss"
              time-format="HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

 
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="type">
            <!-- 编辑模式下显示下拉选择 -->
            <el-select
              v-if="mode != 'show'"
              v-model="form.type"
              placeholder="请选择分类"
            >
              <el-option label="请选择" value="0">请选择</el-option>
              <el-option
                v-for="item in keytypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <!-- 查看模式下显示文本 -->
            <span v-else>{{ form.typename }}</span>
          </el-form-item>
        </el-col>

  
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="mode != 'show'"
        type="primary"
        :loading="isSaveing"
        @click="submit()"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<script>
export default {
  emits: ["success", "closed"],
  data() {
    const validatePrice = (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error("优惠价必须大于0"));
      } else {
        callback();
      }
    };

    return {
      keytypeList: [],
      selectConfig: {
        userLevel: {
          label: "name",
          value: "name",
        },
      },
      apiObj: this.$API.school.listselect,
      mode: "add",
      school: {},
      titleMap: {
        add: "新增",
        edit: "编辑",
        show: "查看",
      },
      menuList: [],
      visible: false,
      isSaveing: false,
      //菜单树配置
      menuProps: {
        value: "id",
        emitPath: false,
        label: "title",
        checkStrictly: true,
      },
      props: {
        label: "name",
        value: "id",
      },
      menuProps2: {
        value: "id",

        label: "title",

        emitPath: false, // 只返回选中节点的值
        leaf: "leaf",
      },

	  schoollist:[],
      //表单数据
      form: {
        schoolid: "",
        num: 0,
        keystart: "",
        yxq: null,
        type: "",
        yuanjia: 0,
        typename: "",
        price: 0,
        nj: "",
        type2:"天数"
      },
      //验证规则
      rules: {
        title: [{ required: true, message: "请输入活动名称" }],
        yxq: [{ required: true, message: "请输入有效期" }],
        
        keystart: [{ required: true, message: "请输入激活码前缀" }],

        num: [{ required: true, message: "请输入生成数量" }],
        yuanjia: [{ required: true, message: "请输入原价" }],
      
        type: [{ required: true, message: "请选择分类" }],
		schoolid:[{ required: true, message: "请选择学校" }],

    
        lesson: [{ required: true, message: "请选择科目", trigger: "blur" }],

      },
      //所需数据选项
      groups: [],
      //规则树配置
      groupsProps: {
        value: "id",
        multiple: true,
        label: "name",
        checkStrictly: true,
      },
    };
  },
  async mounted() {
    this.getSchool();
    this.getKeytypeList();
    // 不再需要获取课程数据，直接使用下拉选项
	var res2=await this.$API.common.util.post(`/buss/school/list`, {});
	this.schoollist=res2.data;
  },
  methods: {
    //显示
    open(mode = "add") {
      console.log(mode);
      this.mode = mode;
      this.visible = true;
      return this;
    },

    async getSchool() {
      let res = await this.$API.common.util.post(`/buss/school/getschool`, {});

      this.school = res;
    },
    async getKeytypeList() {
      let res = await this.$API.common.util.post(
        `/buss/keytype/listselect`,
        {}
      );
      this.keytypeList = res;

      let res2 = await this.$API.common.util.post(`/buss/yx/listselect`, {});
      this.yxlist = res2;
    },
    //加载树数据
    async getGroup() {},
    //表单提交方法
    submit() {
      if (this.form.nj == "") {
        this.$message.error("请选择年级");
        return false;
      }

 
	if(this.form.type2=='天数'){
		if(this.form.usetime==null){
			this.$message.error("请选择时长");
			return false;
		}
	}

      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          this.isSaveing = true;
          //计算 总用时 是否够用，
       
      

          //计算激活码长度
          this.form.len = this.form.num.toString().length;
          

          let res = await this.$API.common.util.post(
            `/buss/keyrecord/save`,
            this.form
          );
          this.isSaveing = false;
          if (res.code == 200) {
            this.$emit("success", this.form, this.mode);
            this.visible = false;
            this.$message.success("操作成功");
          } else {
            this.$alert(res.message, "提示", { type: "error" });
          }
        } else {
          return false;
        }
      });
    },
    //表单注入数据
    async setData(data) {
      this.form = data;
      console.log(data);
      await this.getKeytypeList();
      console.log(this.keytypeList);
      const type = this.keytypeList.find((item) => item.id == data.type);

      this.form.typename = type.name;
      console.log(this.form.typename);
    },
    // 获取分类名称
  },
};
</script>

<style>
</style>
