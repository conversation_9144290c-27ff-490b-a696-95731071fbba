const Base = require('./base.js');

module.exports = class extends Base {
 async indexAction() {

  
      let model=this.model("tk");

      let res=await model.where({"score":['BETWEEN', 40, 60],"lessonid":95,"type": ['IN', ['单选', "多选"]]}).order("rand()").limit(10).select();

      console.log(res);





    return this.display();
  }

  async registerAction(){
 
    return this.json({
      sn:"HR202503130001ks",
    });
  }
  async testAction(){

    console.log(this.post());
    console.log(this.get());
    return this.json({
      relay3:1,
      errmsg:"",
      data:{}
    });
  }
};
