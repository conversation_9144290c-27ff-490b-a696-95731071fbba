<template>
  <el-container>
    <el-container>
      <el-header>
        <div class="left-panel">
          <el-button type="primary" icon="el-icon-plus" @click="add"></el-button>
        </div>
        <div class="right-panel">
          <div class="right-panel-search">
            <el-input v-model="search.keyword" placeholder="姓名/编号" clearable></el-input>
            <el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
          </div>
        </div>
      </el-header>
      <el-main class="nopadding">
        <scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
          remoteFilter>
          <el-table-column type="selection" width="50"></el-table-column>
             <el-table-column label="学校名称" prop="office_name" width="120"></el-table-column>
          <el-table-column label="营销编号" prop="code" width="120"></el-table-column>
          <el-table-column label="姓名" prop="name" width="150"></el-table-column>
          <el-table-column label="二维码" width="120">
            <template #default="scope">
              <el-image 
                v-if="scope.row.pic" 
                :src="scope.row.pic" 
                style="width: 80px; height: 80px"
                 
              ></el-image>
              <span v-else>暂无二维码</span>
            </template>
          </el-table-column>
          <el-table-column label="添加人" prop="create_by" width="120"></el-table-column>
          <el-table-column label="添加时间" prop="create_date" width="150" sortable='create_date'></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="140">
            <template #default="scope">
              <el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
              </el-button>
              <el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
              </el-button>
              <el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
  </el-container>

  <save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
</template>

<script>
import saveDialog from './save'

export default {
  name: 'yx',
  components: {
    saveDialog
  },
  data() {
    return {
      dialog: {
        save: false
      },
      apiObj: this.$API.yx.page,
      selection: [],
      search: {
        keyword: null
      }
    }
  },
  mounted() {
    console.log(this.$API.yx.page)
  },
  methods: {
    // 添加
    add() {
      this.dialog.save = true
      this.$nextTick(() => {
      
        this.$refs.saveDialog.open('add');
      })
    },
    // 编辑
    table_edit(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('edit').setData(row)
      })
    },
    // 查看
    table_show(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('show').setData(row)
      })
    },
    // 删除
    async table_del(row, index) {
      var reqData = { id: row.id }
      var res = await this.$API.yx.delete.post(reqData);
      if (res.code == 200) {
        // 这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功")
      } else {
        this.$alert(res.message, "提示", { type: 'error' })
      }
    },
    // 表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    // 搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    // 本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    }
  }
}
</script>

<style>
.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: auto !important;
  padding-top: 10px;
  padding-bottom: 10px;
}

.left-panel {
  display: flex;
  align-items: center;
}

.right-panel {
  display: flex;
}

.right-panel-search {
  display: flex;
  align-items: center;
}

.right-panel-search .el-input, .right-panel-search .el-select {
  margin-right: 10px;
  width: 180px;
}
</style>