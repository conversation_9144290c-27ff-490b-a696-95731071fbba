<template>
	<el-container>
		
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-button type="primary" icon="el-icon-plus" @click="add"></el-button>


				 
				</div>
				<div class="right-panel">
					<div class="right-panel-search">
						<el-input v-model="search.name" placeholder="学校名称" clearable></el-input>
						<el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
					</div>
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange"  stripe remoteSort
				remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					 
					<el-table-column label="学校名称" prop="schoolname" width="150"  ></el-table-column>
					<el-table-column label="年级" prop="nj" width="70"></el-table-column>
           			<el-table-column label="学科" prop="lessonname" width="70"></el-table-column>
					<el-table-column label="活动名称" prop="title" width="200"  ></el-table-column>
					<el-table-column label="分类" prop="typename" width="100"  ></el-table-column>
					<el-table-column label="类型" prop="type2" width="100"  ></el-table-column>
					<el-table-column label="时长" prop="usetime" width="100"  ></el-table-column>
					<el-table-column label="数量" prop="num" width="100"  ></el-table-column>
					<el-table-column label="可用数量" prop="num2" width="100"  ></el-table-column>
				 
					<el-table-column label="总用时（月）" prop="allusetime" width="100"  ></el-table-column>
				 	<el-table-column label="有效期" prop="yxq" width="100"  ></el-table-column>
				 
              
					
					<el-table-column label="添加时间" prop="create_date" width="150" sortable='create_date'></el-table-column>
					 
					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
							<el-button type="text" size="small" @click="table_showkey(scope.row, scope.$index)">查看激活码
							</el-button>
							
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button type="text" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
	<show-dialog v-if="dialog.key" ref="showDialog" @success="handleSuccess" @closed="dialog.key=false"></show-dialog>
</template>

<script>
import saveDialog from './save'
import showDialog from './showkey'

export default {
	name: 'user',
	components: {
		saveDialog,
		showDialog
	},
	data() {
		return {

			dialog: {
				save: false,
				key:false
			},
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.keyrecord.page,
			selection: [],
			search: {
				name: null
			},
			defaultProps: {

				label: 'name'
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//添加
	 async	add() {
			this.dialog.save = true
			//	let row={};
			
			//	row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add');
			})
		},
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},

table_showkey(row) {
			this.dialog.key = true
			this.$nextTick(() => {
				this.$refs.showDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.common.util.post(`/buss/keyrecord/delete`, reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.role.select.get();
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {

			this.$refs.table.upData(this.search);

		},
		//本地更新数据
		handleSuccess() {

			this.$refs.table.refresh();

		}
	 

		 

	}
}
</script>

<style>
</style>
