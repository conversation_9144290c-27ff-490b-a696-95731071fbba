const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {


  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const learnLogModel = this.model('zsd');
    where['p.del_flag'] = 0;
    
    //let dataScopeWhere = await this.dataScope('p');

    const response = await learnLogModel
      .alias('p')
      .field('p.*,u.name AS create_name')
      .join('sys_user u ON p.`create_by`=u.`id`')
      .page(page, rows).where(where)
      .order('p.create_date desc').countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }



  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('zsd');
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const model = this.model('zsd');
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      await model.add(await this.addData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    } else {
      await model.where({id: id}).update(await this.updateData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('zsd');
    where['p.del_flag'] = 0;
    const response = await model
      .alias('p')
      .field('p.*')
      .where(where)
      .select();
    respData = {
      code: 200,
      data: response,
      message: ''
    };
    return this.json(respData);
  }


  async numAction(){
    const model = this.model('zsd');
    let  where={};
    where['p.del_flag'] = 0;
    let lessonid=this.get("lessonid");
    where['lessonid']=lessonid;
    let num2=await model.where({"video":['!=', ''],"del_flag":0,"lessonid":lessonid}).count()
    let num=await model.where({"pic":['!=', ''],"del_flag":0,"lessonid":lessonid}).count()

    this.json({num:num,num2:num2})
  }
};
