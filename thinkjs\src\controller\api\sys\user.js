const BaseRest = require('../rest.js');


import API from 'wechat-api';
module.exports = class extends BaseRest {






  async currinfoAction() {
    let respData = {};
    let user = await this.session('userInfo');
    const model = this.model('user');
    let response={};
  
      response = await model.where({'del_flag': 0, 'id': user.id}).find();

       let model2=this.model("display");
       let res=await model2.where({userid:user.id,}).find();
       if(think.isEmpty(res)){
         await model2.add({userid:user.id,showbar:"是"});
         response['display']={id:user.id,showbar:1};
       }else{
   
         response['display']=res;
       }
    
    




    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }




  async infoAction() {
    let respData = {};
    const id = this.post('id');
    

    const aid=this.post("aid");
    const model = this.model('user');
    let response={};
    if(think.isEmpty(id)){
       response = await model.where({'del_flag': 0, 'aid': aid}).find();
    }else{
       response = await model.where({'del_flag': 0, 'id': id}).find();

       let model2=this.model("display");
       let res=await model2.where({userid:id}).find();
       if(think.isEmpty(res)){
         await model2.add({userid:id,showbar:"是"});
         response['display']={id:id,showbar:1};
       }else{
   
         response['display']=res;
       }
    }
    




    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }



async getqrAction(){
  let api = new API(this.config("appid"), this.config("appsecret"));
  const model = this.model('user');
  let user = await this.session('userInfo');
  let res=await model.where({id:user.id}).find();

    let ticket=await new Promise((resolve, reject) => {
    api.createTmpQRCode("bind_"+res.id, 60*60*24, async function(err,result){
      console.log(err);

      if(think.isEmpty(err)){
        resolve(result.ticket)

      }else{
        resolve(null)
      }
     

    });

  });

  let url="";
  console.log(23424242423)
  if(!think.isEmpty(ticket)){
    url=    api.showQRCodeURL(ticket);
  }

  
    
return    this.json({qrurl:url});

}

  async dictAction() {
    let respData = {};
    const model = this.model('user');
    const response = await model.where({'del_flag': 0}).select();
    respData = {
      code: 200,
      data: response,
      msg: ''
    };
    return this.json(respData);
  }

  async savepasswordAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const model = this.model('user');
      this.post()['update_date'] = think.datetime();
      let password = this.post('password');
      password = this.md5(password);
      await model.where({id: id}).update({password: password});
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }

  async saveinfoAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const model = this.model('user');
      this.post()['update_date'] = think.datetime();
      await model.where({id: id}).update(this.post());
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }


  async delfilterAction() {
    const id = this.post('id');

    const model = this.model('filter');
    await model.where({'id': id}).delete();

    return this.json({'code': 200});
  }

  async savedisplayAction() {
    const data = this.post();
    delete data.userid;
    let user = await this.session('userInfo');

    const model = this.model('display');
    await model.where({'userid': user.id}).update(data);

    return this.json({'code': 200});
  }

  


  async getfilterAction() {
    let model = this.model("filter");
    let user = await this.session('userInfo');
    let type=this.post("type")
    let where = { userid: user.id,type:type};
    let res = await model.where(where).select();
 
 


    return this.json(res);
}

async teacherAction(){
  let userInfo = await this.session('userInfo');
  let model=this.model("user");
 let where={"type":2,del_flag:"0"};
  if(userInfo.name!="系统管理员"){

    let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

    if(think.isEmpty(schoolres)){
        where["schoolid"]=userInfo.schoolid
      
    }else{

        where["schoolid"]=["in",schoolres.map(item => item.id)];
    }

 
  }

  let res=await  model.where(where).select();
  return this.json(    {
    code: 200,
    data: res,
    msg: '成功'
  });



}

async savefilterAction() {
    let model = this.model("filter");
    let data = this.post();

    if (think.isEmpty(data.id)) {
        data.id = think.uuid();
        let user = await this.session('userInfo');
        data.userid=user.id;
        await model.add(data);
    } else {
        await model.where({ "id": data.id }).update(data);
    }

return    this.json({ code: 200 })
}



};
