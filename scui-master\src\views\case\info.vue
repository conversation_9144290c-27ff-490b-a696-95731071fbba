<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/9/28 08:58:16
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="120px"
	         label-position="left">

             <el-tabs type="border-card" class="tab" >
    <el-tab-pane  label="信件登记">


<el-card class="box-card" >
    <template #header>
      <div class="card-header">
        <span>信访人信息</span>
        
      </div>
    </template>

	<el-descriptions :column="4" border size="mini"  :class="des">
                            <el-descriptions-item label="姓名" label-class-name="deslable">  
                                {{xfr.xm}}
                            </el-descriptions-item>
							<el-descriptions-item label="手机号"  label-class-name="deslable">  
                                {{xfr.sjh}}
                            </el-descriptions-item>
							<el-descriptions-item label="证件类型"  label-class-name="deslable">  
                                {{xfr.zjlxmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="证件号码"  label-class-name="deslable">  
                                {{xfr.zjhm}}
                            </el-descriptions-item>

							<el-descriptions-item label="住址"  label-class-name="deslable">  
                                {{xfr.zzmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="详细地址"  label-class-name="deslable">  
                                {{xfr.zzmcxq}}
                            </el-descriptions-item>

	</el-descriptions>

   		

  </el-card>



<el-card class="box-card" style="margin-top: 5px;">
    <template #header>
      <div class="card-header">
        <span>信访诉求</span>
        
      </div>
    </template>

	<el-descriptions :column="4" border size="mini"   class="des">
                            <el-descriptions-item label="问题属地"  label-class-name="deslable">  
                                {{base.wtsdmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="信访目的"  label-class-name="deslable">  
                                {{base.xfmdmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="热点问题"  label-class-name="deslable">  
                                {{base.rdwtmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="内容分类"  label-class-name="deslable">  
                                {{base.nrflmc}}
                            </el-descriptions-item>

							<el-descriptions-item label="关键词"  label-class-name="deslable">  
                                {{base.keywords}}
                            </el-descriptions-item>
							<el-descriptions-item label="信件来源" :span="3"  label-class-name="deslable">
                                {{base.xjlymc}}
                            </el-descriptions-item>

							<el-descriptions-item label="概括" :span="4"  label-class-name="deslable">  
                                {{base.gkxx}}
                            </el-descriptions-item>

							<el-descriptions-item label="信件材料" :span="4"  label-class-name="deslable">  
                                <el-table :data="xjcl" style="width: 100%">
								<el-table-column prop="no" label="序号" width="80" />
								<el-table-column prop="fjlxmc" label="附件名称"   />
								<el-table-column prop="createTime" label="附件上传时间" />
								<el-table-column prop="scrmc" label="经办人" />
								<el-table-column prop="" label="操作" />
							</el-table>




                            </el-descriptions-item>

	</el-descriptions>

   		

  </el-card>


  <el-card class="box-card" style="margin-top: 5px;">
    <template #header>
      <div class="card-header">
        <span>受理情况</span>
        
      </div>
    </template>

	<el-descriptions :column="4" border size="mini"   class="des">
                            <el-descriptions-item label="涉及人数"  label-class-name="deslable">  
                                {{base.sjrs}}
                            </el-descriptions-item>
							<el-descriptions-item label="所属系统"  label-class-name="deslable">  
                                {{base.ssxtmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="专项标识"  label-class-name="deslable">  
                                {{base.zxbzmc}}
                            </el-descriptions-item>
							<el-descriptions-item label="限办日期"  label-class-name="deslable">  
                                {{base.xbjzsj}}
                            </el-descriptions-item>

							<el-descriptions-item label="产生信访原因"  label-class-name="deslable">  
                                {{base.xfyymc}}
                            </el-descriptions-item>
							<el-descriptions-item label="涉众标识" :span="1"  label-class-name="deslable">
                                {{base.xjlymc}}
                            </el-descriptions-item>

							<el-descriptions-item label="信访日期" :span="1"  label-class-name="deslable">  
                                {{base.scxfrq}}
                            </el-descriptions-item>
 

							<el-descriptions-item label="港澳台标识" :span="1"  label-class-name="deslable">  
                                 <el-checkbox ></el-checkbox>
                            </el-descriptions-item>


							<el-descriptions-item label="标识" :span="3"  label-class-name="deslable">  
								<el-row >
									<el-col :span="3">
										
										<el-form-item label="本机构初次标识" prop="site_name">
											<el-checkbox v-model="base.bjgccbz" ></el-checkbox>
										</el-form-item>
									</el-col>

									<el-col :span="3">
										
										<el-form-item label="全系统初次标识" prop="site_name">
											<el-checkbox v-model="base.bjgccbz" ></el-checkbox>
										</el-form-item>
									</el-col>

									<el-col :span="3">
										
										<el-form-item label="重大紧急标识" prop="site_name">
											<el-checkbox v-model="base.zdjjbz" ></el-checkbox>
										</el-form-item>
									</el-col>

									<el-col :span="3">
										
										<el-form-item label="积案标识" prop="site_name">
											<el-checkbox v-model="base.jabz" ></el-checkbox>
										</el-form-item>
									</el-col>

									<el-col :span="3">
										
										<el-form-item label="是否同意公开" prop="site_name">
											<el-checkbox v-model="base.gkbz" ></el-checkbox>
										</el-form-item>
									</el-col>
								

									<el-col :span="3">
										
										<el-form-item label="联名标识" prop="site_name">
											<el-checkbox v-model="base.lmbz" ></el-checkbox>
										</el-form-item>
									</el-col>

									<el-col :span="3">
										
										<el-form-item label="敏感件" prop="site_name">
											<el-checkbox v-model="base.mgjbz" ></el-checkbox>
										</el-form-item>
									</el-col>
									<el-col :span="3">
										
										<el-form-item label="敏感词汇" prop="site_name">
											{{base.mgch}}
										</el-form-item>
									</el-col>



									<el-col :span="3">
										
										<el-form-item label="扬言件" prop="site_name">
											<el-checkbox v-model="base.yybz" ></el-checkbox>
										</el-form-item>
									</el-col>
									<el-col :span="3">
										
										<el-form-item label="扬言词汇" prop="site_name">
											{{base.yych}}
										</el-form-item>
									</el-col>
								
 
									<el-col :span="3">
										
										<el-form-item label="匿名" prop="site_name">
											<el-checkbox v-model="base.nmbz" ></el-checkbox>
										</el-form-item>
									</el-col>
								</el-row>
                            </el-descriptions-item>
 

	</el-descriptions>

   		

  </el-card>


  <el-card class="box-card" style="margin-top: 5px;">
    <template #header>
      <div class="card-header">
        <span>历次信访活动</span>
        
      </div>
    </template>

	<el-table :data="his" style="width: 100%">
								<el-table-column prop="no" label="序号" width="80" />
								<el-table-column prop="xfjbh" label="信访件编号"   />
								<el-table-column prop="djjgmc" label="登记单位" />
								<el-table-column prop="xfrq" label="信访日期" />

								<el-table-column prop="xfxsmc" label="信访形式" />


								<el-table-column prop="checkFlag" label="初重标识"  :formatter="formatterStatus"/>
								<el-table-column prop="zjblfsmc" label="办理方式" />

								<el-table-column prop="zjqxmc" label="去向" />

								<el-table-column prop="djsj" label="登记日期" />

								<el-table-column prop="djrmc" label="登记人" />

					 
							</el-table>



</el-card>




	</el-tab-pane>
    <el-tab-pane label="信件办理">信件办理</el-tab-pane>
    <el-tab-pane label="流程跟踪">流程跟踪</el-tab-pane>
    <el-tab-pane label="附件信息">附件信息</el-tab-pane>
  </el-tabs>

		
	</el-form>
</template>

<script>
export default {
	props: {
		mode: { type: String, default: "add" }
	},
	data() {
		return {
			//表单数据

			xfr:{},
			base:{},
			xjcl:[],
			his:[],
			form: {
				id:"",

				site_no: "",

				site_name: "",

				site_address: "",

				site_phone: "",

				site_type: "",

				site_area: "",

				site_class: "",

				site_hosue: "",
				site_enable:"",
				site_add_date:"",
				site_revoke_date:"",
				site_revoke_note:"",
				site_bond:"",

			},
			//验证规则
			rules: {

			

			},
			selectConfig:{
				siteType:{
					label: 'name',
					value: 'key'
				},
				siteStar:{
					label: 'name',
					value: 'key'
				},
				siteArea:{
					label: 'name',
					value: 'key'
				},
				siteClass:{
					label: 'name',
					value: 'key'
				},
				siteHouse:{
					label: 'name',
					value: 'key'
				},
				yesNo:{
					label: 'name',
					value: 'key'
				},
			}
		}
	},
	mounted(){

	},
	methods: {
		//表单提交方法
		submit(callback){
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				}else{
					return false;
				}
			})
		},


		formatterStatus(row, column) {
			if (row.checkFlag == 0 || row.checkFlag == '0') {
				return "初件"
			}else{
				return "重件"
			}
			

		},

 
		//表单注入数据
	async setData(data){
			
			Object.assign(this.form, data)
			let res=await this.$API.caselist.base.post({"xfjbh":data.xfjbh});
			this.xfr=res.xfr;
			this.base=res.base;
			this.xjcl=res.xjcl;
			this.his=res.his;
			if(this.base.bjgccbz==1){

				this.base.bjgccbz=true;
			}
			if(this.base.bjgccbz==1){

			this.base.bjgccbz=true;
			}



			if(this.base.nmbz==1){

			this.base.nmbz=true;
			}


			if(this.base.yybz==1){

			this.base.yybz=true;
			}

			if(this.base.mgjbz==1){

this.base.mgjbz=true;
}
if(this.base.lmbz==1){

this.base.lmbz=true;
}

if(this.base.gkbz==1){

this.base.gkbz=true;
}
	
if(this.base.jabz==1){

this.base.jabz=true;
}	
if(this.base.zdjjbz==1){

this.base.zdjjbz=true;
}	  		 		     

			
		}
	}
}
</script>

<style >
.tab  .el-tabs__nav
{ 
    margin-left: 40px;
}
.tab .el-tabs__header{
    background-color: #4187fc !important;
}

.tab .el-tabs__item{
    margin-top: 5px !important;
    height: 40px !important;
    border-top-left-radius: 5px !important;
    border-top-right-radius: 5px !important;
    color:rgb(255, 255, 255) !important;
}

.tab   .is-active{

 
    color:#4187fc !important;
}

</style>

