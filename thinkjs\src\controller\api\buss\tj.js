const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {

 
    
    /**
     * 欢迎页销售数据统计
     * 用于欢迎页展示的销售数据饼图
     */
    async welcomeAction() {
        // 获取日期范围参数
        // 尝试获取POST参数
        const requestBody = this.post();
        let startDate = '';
        let endDate = '';
        let schools = requestBody.schools || [8288, 10545]; // 默认学校ID
        
        if (requestBody && typeof requestBody === 'object') {
            startDate = requestBody.startDate || '';
            endDate = requestBody.endDate || '';
            // 如果前端传递了学校参数，优先使用前端传递的
            if (requestBody.schools) {
                schools = Array.isArray(requestBody.schools) ? requestBody.schools : [requestBody.schools];
            }
        }
        
        console.log('欢迎页查询参数:', { startDate, endDate, schools, requestBody }); // 添加日志便于调试
        
        // 构建查询条件
        let where = {
            'o.pay_status': 1  // 只统计已支付订单
        };
        
        // 添加学校筛选条件
        if (schools && schools.length > 0) {
            where['s.school'] = ['IN', schools];
        }
        
        // 添加日期范围条件
        if (startDate && endDate) {
            where['o.create_time'] = ['BETWEEN', `${startDate} 00:00:00`, `${endDate} 23:59:59`];
        } else if (startDate) {
            where['o.create_time'] = ['>=', `${startDate} 00:00:00`];
        } else if (endDate) {
            where['o.create_time'] = ['<=', `${endDate} 23:59:59`];
        }
        
        try {
            // 使用联表查询获取销售数据
            const orderModel = this.model('order').alias('o');
            const data = await orderModel
            .join({
                table: 'product',
                join: 'inner',
                as: 'p',
                on: ['o.product_id', 'p.id']
            })
            .join(["buss_student as s on s.id=o.user_id"])
            .field('p.name as product_name, sum(o.pay_amount) as total_amount, count(*) as count')
            .where(where)
            .group('p.name')
            .order('total_amount DESC')
            .select();
            
            console.log('欢迎页查询结果:', data); // 添加日志便于调试
            
            // 返回统计结果
            return this.success(data);
        } catch (error) {
            console.error('销售统计查询出错:', error);
            return this.fail('查询出错');
        }
    }
}