<template>
	<el-container>
		
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-button type="primary" icon="el-icon-plus" @click="add"></el-button>


				 
				</div>
				<div class="right-panel">
					<div class="right-panel-search">
						<el-input v-model="search.name" placeholder="学校名称" clearable></el-input>
						<el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
					</div>
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					 
					<el-table-column label="学校名称" prop="name" width="150" sortable='custom'></el-table-column>
					<el-table-column label="负责人" prop="mastername" width="150" sortable='custom'></el-table-column>
					<el-table-column label="学校简称" prop="shortname" width="150" sortable='custom'></el-table-column>
						
					<el-table-column label="可用时长（天）" prop="canusetime" width="150" sortable='custom'></el-table-column>
						<el-table-column label="今日消耗" prop="todaycost" width="150" sortable='custom'></el-table-column>
					<el-table-column label="是否公开" prop="type" width="200">

                        <template #default="scope">
						<el-tag v-if="scope.row.type==0">非公开</el-tag>
						<el-tag v-if="scope.row.type==1" type="success">公开</el-tag>
					</template>

                    </el-table-column>
					
					<el-table-column label="添加时间" prop="create_date" width="150" sortable='custom'></el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="380">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
							<el-button type="text" size="small" @click="table_gzh(scope.row, scope.$index)">公众号参数设置
							</el-button>
							<el-button type="text" size="small" @click="table_menu(scope.row, scope.$index)">公众号菜单管理
							</el-button>
							<el-button type="text" size="small" @click="table_pay(scope.row, scope.$index)">微信支付参数设置
							</el-button>
								<el-button type="text" size="small" @click="table_time(scope.row, scope.$index)">时长管理
							</el-button>
				 
							<el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
							</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button type="text" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
	<timemanage v-if="dialog.timemanage" ref="timemanage" @success="handleSuccess" @closed="dialog.timemanage=false"></timemanage>
	<gzh v-if="dialog.gzh" ref="gzh" @success="handleSuccess" @closed="dialog.gzh=false"></gzh>
	<pay v-if="dialog.pay" ref="pay" @success="handleSuccess" @closed="dialog.pay=false"></pay>
	<menu-manage v-if="dialog.menu" ref="menuManage" @success="handleSuccess" @closed="dialog.menu=false"></menu-manage>
</template>

<script>
import saveDialog from './save'
import timemanage from './timemanage'
import gzh from './gzh'
import pay from './pay'
import menuManage from './menu-manage'
export default {
	name: 'user',
	components: {
		saveDialog,
		timemanage,
		gzh,
		pay,
		menuManage
	},
	data() {
		return {

			dialog: {
				save: false,
				timemanage: false,
				gzh: false,
				pay: false,
				menu: false
			},
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.school.page,
			selection: [],
			search: {
				name: null
			},
			defaultProps: {

				label: 'name'
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//添加
	 async	add() {
			this.dialog.save = true
			//	let row={};
			
			//	row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add');
			})
		},
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},

		table_gzh(row) {
			this.dialog.gzh = true
			this.$nextTick(() => {
				this.$refs.gzh.open('show').setData({id:row.id,gzhconfig:row.gzhconfig})
			})
		},
		table_pay(row) {
			this.dialog.pay = true
			this.$nextTick(() => {
				this.$refs.pay.open('show').setData({id:row.id})
			})
		},

		table_menu(row) {
			this.dialog.menu = true
			this.$nextTick(() => {
				this.$refs.menuManage.open().setData({school_id: row.id, school_name: row.name})
			})
		},

		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},


	table_time(row) {
			this.dialog.timemanage = true
			this.$nextTick(() => {
				this.$refs.timemanage.open('show').setData({schoolid:row.id})
			})
		},

		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.school.remove.post(reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.role.select.get();
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {

			this.$refs.table.upData(this.search);

		},
		//本地更新数据
		handleSuccess() {

			this.$refs.table.refresh();

		}
		,

		open() {
			this.$prompt('请输入密码', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',

			}).then(async ({value}) => {

				await this.$API.user.personalSavePassWord.post({"password": value, "id": this.selection[0].id});


				this.$message({
					type: 'success',
					message: '修改成功'
				});
			}).catch(() => {

			});
		}

	}
}
</script>

<style>
</style>
