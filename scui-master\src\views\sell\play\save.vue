<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/13 14:35:38
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px" label-position="left">

		<el-form-item label="地区" prop="sell_area">
			<el-input v-model="form.sell_area" clearable></el-input>
		</el-form-item>

		<el-form-item label="站点" prop="sell_site_info_id">
			<el-input v-model="form.sell_site_info_id" clearable></el-input>
		</el-form-item>

		<el-form-item label="销售类型" prop="sell_type">
			<el-input v-model="form.sell_type" clearable></el-input>
		</el-form-item>

		<el-form-item label="销售时间" prop="sell_date">
			<el-date-picker
				v-model="form.sell_date"
				type="date"
				value-format="YYYY-MM-DD"
				style="width:100%"
			>
			</el-date-picker>
		</el-form-item>

		<el-form-item label="期号" prop="sell_no">
			<el-input v-model="form.sell_no" clearable></el-input>
		</el-form-item>

		<el-form-item label="销售额" prop="sell_all_money">
			<el-input v-model="form.sell_all_money" clearable></el-input>
		</el-form-item>

		<el-form-item label="有效销售额" prop="sell_valid_money">
			<el-input v-model="form.sell_valid_money" clearable></el-input>
		</el-form-item>

	</el-form>
</template>

<script>
export default {
	props: {
		mode: { type: String, default: "add" }
	},
	data() {
		return {
			//表单数据
			form: {
				id:"",

				sell_area: "",

				sell_site_info_id: "",

				sell_type: "",

				sell_date: "",

				sell_no: "",

				sell_all_money: "",

				sell_valid_money: "",

			},
			//验证规则
			rules: {

				sell_area: [
					{required: true, message: '请输入地区'}
				],

				sell_site_info_id: [
					{required: true, message: '请输入站点'}
				],

				sell_type: [
					{required: true, message: '请输入销售类型'}
				],

				sell_date: [
					{required: true, message: '请输入销售时间'}
				],

				sell_no: [
					{required: true, message: '请输入期号'}
				],

				sell_all_money: [
					{required: true, message: '请输入销售额'}
				],

				sell_valid_money: [
					{required: true, message: '请输入有效销售额'}
				],

			},
		}
	},
	mounted(){

	},
	methods: {
		//表单提交方法
		submit(callback){
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				}else{
					return false;
				}
			})
		},
		//表单注入数据
		setData(data){
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
