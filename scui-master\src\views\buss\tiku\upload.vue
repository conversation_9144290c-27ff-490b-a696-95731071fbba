<template>
    <el-dialog :title="titleMap[mode]" v-model="visible" :width="1200" destroy-on-close @closed="$emit('closed')">
        <el-form :model="form" :rules="rules" ref="dialogForm" label-width="80px" label-position="left">



            <el-form-item label="学科" prop="parent">
                <el-cascader v-model="form.parent" :options="menuList" :props="menuProps" :show-all-levels="false"
                    clearable></el-cascader>
                <div class="el-form-item-msg">

                </div>
            </el-form-item>


            <el-form-item label="压缩包" prop="parent">
                <sc-upload-file v-model="form.zip" title="附件"  accept=".zip"
					:apiObj="uploadApi"  :limit="1"  :maxSize="100">
                    <el-button type="primary" icon="el-icon-upload">上传附件</el-button>
                </sc-upload-file>
		

            </el-form-item>

            <el-form-item label="" prop="parent">
               
                <el-button type="primary" icon="el-icon-Tools" @click="process()">解析</el-button>

            </el-form-item>

        </el-form>
        <template #footer>
            <el-button @click="visible = false">取 消</el-button>
            <el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
        </template>
    </el-dialog>
   


 

</template>




<script>
export default {
    emits: ['success', 'closed'],
    data() {
        return {
            mode: "add",
            titleMap: {
                add: '上传',
                edit: '编辑'
            },
            visible: false,
            isSaveing: false,
            menuList: [],
            fileViewList: [],
            uploadApi: this.$API.common.upload,
            form: {
                id: "",
                parent: "",
                zip:""
            },
            rules: {
                name: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],

                "parent": [
                    { required: true, trigger: "blur", message: "请选择" }
                ]
            },
            dict: [],
            dicProps: {
                value: "id",
                label: "name",
                checkStrictly: true,
                emitPath: false
            },
            props: {
                menu: {
                    type: Object, default: () => {
                    }
                }
            }
            ,

            menuProps: {
                value: "id",
                emitPath: false,
                label: "title",
                checkStrictly: true
            },
        }
    },
    mounted() {
        this.getDic();
        this.getOffice();
    },
    methods: {

     async    process(){
            console.log(this.form.zip)

            await this.$API.tk.process.post({"file":this.form.zip});
        },
        fileSuccess(response) {
            const suffix = response.data.file_name.substr(
                response.data.file_name.lastIndexOf(".") + 1
            ); // 文件后缀
            this.fileViewList.push({
                suffix: suffix,
                name: response.data.file_name,
                url: response.data.src,
                new_name: response.data.new_name,
                id: response.data.new_name,
            });
            this.$message.success(`文件上传成功`);
            return false;
        },
        beforeRemove(file) {
            this.fileViewList.map((r, index) => {
                if (r.name == file.name) {
                    this.form.files = this.form.files.replace(
                        "/static/upload/" + file.name + ",",
                        ""
                    );
                    this.fileViewList.splice(index, 1);
                }
            });
        },
        async getOffice() {
            var res = await this.$API.lesson.list.get();
            this.menuList = res;
        },

        //显示
        open(mode = 'add') {
            this.mode = mode;
            this.visible = true;
            return this;
        },
        //获取字典列表
        async getDic() {
            var res = await this.$API.dict.list.get();
            this.dict = res.data;
        },
        //表单提交方法
        submit() {
            this.$refs.dialogForm.validate(async (valid) => {
                if (valid) {
                    this.isSaveing = true;

                    let res = await this.$API.tk.save.post(this.form);
                    this.isSaveing = false;
                    if (res.state == 1) {
                        await this.$TOOL.dictInit();
                        this.$emit('success', this.form, this.mode)
                        this.visible = false;
                        this.$message.success("操作成功")
                    } else {
                        this.$alert(res.msg, "提示", { type: 'error' })
                    }
                }
            })
        },
        //表单注入数据
        setData(data, mode) {
            //可以和上面一样单个注入，也可以像下面一样直接合并进去
            Object.assign(this.form, data)
            this.mode = mode;
        }
    }
}
</script>

<style></style>
