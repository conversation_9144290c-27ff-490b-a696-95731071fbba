import config from "@/config";
import http from "@/utils/request";

const notice = {

    getweb: {
		url: `${config.API_URL}/notice/getweb`,
		name: "网页消息提示列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

    updateone:{

        url: `${config.API_URL}/notice/updateone`,
		name: "更新已读",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}

    }

    ,

    updateall:{

        url: `${config.API_URL}/notice/updateall`,
		name: "更新已读",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}

    }
};
export default notice;
