const {copyFile} = require('fs/promises');
export default class extends think.Controller {
    
    async indexAction(){
        
        let id=this.get("id");
        let model=think.model("user");
        let res=await model.where({id:id}).find();
        console.log(res);

        if(think.isEmpty(res.openid)){
            console.log(123123123123123);
            await this.cache(id+"_gourl", this.ctx.path);
            return this.redirect("/wechat/index/?id="+id)
        }

        this.assign("id",id);
        console.log(this.ctx.path)
        return this.display(); 



    }

    async savefileAction(){


       
        return this.json("dsd")
    }


    async rotateAction(){
        const { Image } = require('image-js');
        let p=think.ROOT_PATH + this.get("path");
        let image = await Image.load(p);
         image.rotate(90,null).save(p);

         this.json({"name":think.uuid})
    }

    
  async uploadAction() {
    

    let respData = {};
    const file = this.file('file');
  
    const filepath = file.path;
    const fileobj = {};
    const filename = file.name;
    const uploadpath = think.ROOT_PATH + '/www/static/upload/';
    // 创建该目录
    think.mkdir(uploadpath);
    // 提取出用 ‘/' 隔开的path的最后一部分。
    const suffix = filename.substr(filename.lastIndexOf('.') + 1); // 文件后缀
    const name = filename.substr(0, filename.lastIndexOf('.')); // 文件后缀
    fileobj['name'] = name;
    const new_name = think.uuid();
    const newfilename = new_name + '.' + suffix;
    const basename = file.originalFilename;// 因为本系统不允许上传同名主题，所以文件名就直接使用主题名
    // 将上传的文件（路径为filepath的文件）移动到第二个参数所在的路径，并改为第二个参数的文件名。
    await copyFile(filepath, uploadpath + '/' + newfilename);

    let model=this.model("user");
    let user= await model.where({id:this.post("id")}).find();

    
    if(!think.isEmpty(user)){

        const fileModel = this.model('file');
        const addData ={
          file_name: name,
          path: '/static/upload/' + newfilename,
          suffix: suffix,
        create_date:think.datetime(),
        create_by  : this.post("id"),
        del_flag:0,
        office_id:user.office_id
        };
        addData.id = new_name;
        await fileModel.add(addData);
        respData = {
          code: 200,
          data: {
            file_name: name + '.' + suffix,
            new_name: new_name,
            src: '/static/upload/' + newfilename
          },
          uploaded: true,
          message: 'Upload'
        };
        return   this.json(respData);
    }else{

        return   this.json({code:-1});
    }
  
  
  }

}