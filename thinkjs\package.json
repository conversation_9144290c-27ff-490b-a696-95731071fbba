{"name": "jsjy", "description": "jsjy", "version": "1.0.0", "author": "jsjy", "scripts": {"dev": "webpack-dev-server --disableHostCheck=true", "start": "node --openssl-legacy-provider development.js", "pm2": "pm2 start pm2.json", "test": "THINK_UNIT_TEST=1 nyc ava test/ && nyc report --reporter=html", "compile": "babel --no-babelrc src/ --presets think-node --out-dir app/", "lint": "eslint src/", "lint-fix": "eslint --fix src/"}, "dependencies": {"@alicloud/sms-sdk": "^1.1.6", "adm-zip": "^0.5.10", "async-request": "^1.2.0", "axios": "^1.8.4", "baidu-aip-sdk": "^4.16.10", "compressing": "^1.9.0", "cryptojs": "^2.5.3", "custom-soffice-to-pdf": "^1.0.0", "decimal.js": "^10.3.1", "ejsexcel": "^3.6.2", "got": "^11.8.3", "https": "^1.0.0", "image-js": "^0.33.0", "kcors": "^2.2.2", "koa-cors": "^0.0.16", "koa-jwt": "^4.0.1", "mysql2": "^2.3.0", "node-cron": "^3.0.0", "qr": "^0.2.4", "qr-image": "^3.2.0", "qrcode": "^1.5.4", "request": "^2.88.2", "sass-loader": "^13.0.0", "tencentcloud-sdk-nodejs": "^4.0.1054", "tenpay": "^2.1.18", "think-axios": "^0.1.5", "think-cache": "^1.0.0", "think-cache-file": "^1.0.8", "think-logger3": "^1.0.0", "think-model": "^1.0.0", "think-model-mysql": "^1.0.0", "think-sequelize": "^1.3.4", "think-session": "^1.0.0", "think-session-file": "^1.0.5", "think-session-jwt": "^1.1.1", "think-view": "^1.0.0", "think-view-nunjucks": "^1.0.1", "think-websocket": "^1.0.8", "think-websocket-socket.io": "^1.0.12", "think-websocket-ws": "^1.0.4", "think-wechat": "^1.1.0", "thinkjs": "^3.0.0", "uuid-int": "^3.1.0", "wechat-api": "^1.35.1", "wechat-oauth": "^1.5.0", "xml2js": "^0.6.2"}, "devDependencies": {"ava": "^0.18.0", "babel-cli": "^6.24.1", "babel-preset-think-node": "^1.0.0", "eslint": "^4.2.0", "eslint-config-think": "^1.0.0", "lodash": "^4.17.21", "node-notifier": "^5.0.2", "nyc": "^7.0.0", "think-babel": "^1.0.3", "think-inspect": "0.0.2", "think-watcher": "^3.0.0"}, "repository": "", "license": "MIT", "engines": {"node": ">=6.0.0"}, "readmeFilename": "README.md", "thinkjs": {"metadata": {"name": "winway", "description": "winway", "author": "winway", "babel": true}, "projectName": "winway", "template": "/opt/homebrew/lib/node_modules/think-cli/default_template", "clone": false, "isMultiModule": false}}