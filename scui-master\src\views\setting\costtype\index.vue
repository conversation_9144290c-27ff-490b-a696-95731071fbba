<template>
	<el-container>
		<el-aside width="300px" v-loading="showCosttypeloading">
			<el-container>
				<el-header>
					<el-input placeholder="输入关键字进行过滤" v-model="costtypeFilterText" clearable></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree ref="costtype" class="menu" node-key="id" :data="costtypeList" :props="costtypeProps"
							 :highlight-current="true" :expand-on-click-node="false" :filter-node-method="dicFilterNode"
							 @node-click="dicClick">
						<template #default="{node, data}">
							<span class="custom-tree-node">
								<span class="label">{{ node.label }}</span>
								<span class="code">{{ data.type }}</span>
								<span class="do">
									<i class="el-icon-edit" @click.stop="dicEdit(data)"></i>
									<i class="el-icon-delete" @click.stop="dicDel(node, data)"></i>
								</span>
							</span>
						</template>
					</el-tree>
				</el-main>
				<el-footer style="height:51px;">
					<el-button type="primary" size="mini" icon="el-icon-plus" style="width: 100%;" @click="addDic">
						费用分类（大类）
					</el-button>
				</el-footer>
			</el-container>
		</el-aside>
		<el-container class="is-vertical">
			<el-header>
				<div class="left-panel">
					<el-button type="primary" icon="el-icon-plus" @click="addInfo"></el-button>
					<!--					<el-button type="danger" plain icon="el-icon-delete" :disabled="selection.length==0"-->
					<!--					           @click="batch_del"></el-button>-->
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="listApi" row-key="id" :params="listApiParams"
						 @selection-change="selectionChange" stripe :paginationLayout="'prev, pager, next'">
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="" width="50">
						<template #default>
							<el-tag class="move" style="cursor: move;"><i class="el-icon-d-caret"></i></el-tag>
						</template>
					</el-table-column>
					<el-table-column label="名称" prop="name" width="150"></el-table-column>
					<el-table-column label="键值" prop="key" width="150"></el-table-column>
					<el-table-column label="是否有效" prop="enable" width="100">
						<template #default="scope">
							<el-switch v-if="scope.row.enable" v-model="scope.row.enable"
									   @change="changeSwitch($event, scope.row)" :loading="scope.row.$switch_enable"
									   active-value="1" inactive-value="0"></el-switch>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
							</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button type="text" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<costtype-dialog v-if="dialog.costtype" ref="costtypeDialog" @success="handleDicSuccess"
					 @closed="dialog.costtype=false"></costtype-dialog>

	<list-dialog v-if="dialog.list" ref="listDialog" @success="handleListSuccess"
				 @closed="dialog.list=false"></list-dialog>

</template>

<script>
import costtypeDialog from './costtype'
import listDialog from './list'
import Sortable from 'sortablejs'

export default {
	name: 'costtype',
	components: {
		costtypeDialog,
		listDialog
	},
	data() {
		return {
			dialog: {
				costtype: false,
				info: false
			},
			showCosttypeloading: true,
			costtypeList: [],
			costtypeFilterText: '',
			costtypeProps: {
				label: 'name'
			},
			listApi: null,
			listApiParams: {},
			selection: []
		}
	},
	watch: {
		costtypeFilterText(val) {
			this.$refs.costtype.filter(val);
		}
	},
	mounted() {
		this.getCosttype()
		this.rowDrop()
	},
	methods: {
		//加载树数据
		async getCosttype() {
			const res = await this.$API.costtype.list.get();
			this.showCosttypeloading = false;
			this.costtypeList = res.data;
			//获取第一个节点,设置选中 & 加载明细列表
			const firstNode = this.costtypeList[0];
			if (firstNode) {
				this.$nextTick(() => {
					this.$refs.costtype.setCurrentKey(firstNode.id)
				})
				this.listApiParams = {
					where: JSON.stringify({tree_id: firstNode.id,})
				}
				this.listApi = this.$API.costtype.infoPage;
			}
		},
		//树过滤
		dicFilterNode(value, data) {
			if (!value) return true;
			let targetText = data.name + data.code;
			return targetText.indexOf(value) !== -1;
		},
		//树增加
		addDic() {
			this.dialog.costtype = true
			this.$nextTick(() => {
				this.$refs.costtypeDialog.open()
			})
		},
		//编辑树
		dicEdit(data) {
			this.dialog.costtype = true
			this.$nextTick(() => {
				let editNode = this.$refs.costtype.getNode(data.id);
				let editNodeParentId = editNode.level == 1 ? undefined : editNode.parent.data.id
				data.parentId = editNodeParentId
				this.$refs.costtypeDialog.open('edit').setData(data)
			})
		},
		//树点击事件
		dicClick(data) {
			this.$refs.table.upData({
				where: JSON.stringify({
					tree_id: data.id
				})
			})
		},
		//删除树
		dicDel(node, data) {
			this.$confirm(`确定删除 ${data.name} 项吗？`, '提示', {
				type: 'warning'
			}).then(() => {
				this.showCosttypeloading = true;
				//删除节点是否为高亮当前 是的话 设置第一个节点高亮
				//let dicCurrentKey = this.$refs.costtype.getCurrentKey();

				this.$API.costtype.delete.post({id: data.id})
				this.getCosttype();
				//本地删除
				// this.$refs.costtype.remove(data.id)
				// if(dicCurrentKey == data.id){
				// 	let firstNode = this.costtypeList[0];
				// 	if(firstNode){
				// 		this.$refs.costtype.setCurrentKey(firstNode.id);
				// 		this.$refs.table.upData({
				// 			id: firstNode.id
				// 		})
				// 	}else{
				// 		this.listApi = null;
				// 		this.$refs.table.tableData = []
				// 	}
				// }
				this.showCosttypeloading = false;
				this.$message.success("操作成功")
			}).catch(() => {

			})
		},
		//行拖拽
		rowDrop() {
			const _this = this
			const tbody = this.$refs.table.$el.querySelector('.el-table__body-wrapper tbody')
			Sortable.create(tbody, {
				handle: ".move",
				animation: 300,
				ghostClass: "ghost",
				onEnd({newIndex, oldIndex}) {
					const tableData = _this.$refs.table.tableData
					const currRow = tableData.splice(oldIndex, 1)[0]
					tableData.splice(newIndex, 0, currRow)
					_this.$message.success("排序成功")
				}
			})
		},
		//添加明细
		addInfo() {
			this.dialog.list = true
			this.$nextTick(() => {
				let dicCurrentKey = this.$refs.costtype.getCurrentKey();
				const data = {
					name: "",
					key: "",
					enable: "1",
					tree_id: dicCurrentKey
				}
				this.$refs.listDialog.open().setData(data, "add")
			})
		},
		//编辑明细
		table_edit(row) {
			this.dialog.list = true
			this.$nextTick(() => {
				this.$refs.listDialog.open('edit').setData(row, "update")
			})
		},
		//删除明细
		async table_del(row, index) {
			let reqData = {id: row.id}
			let res = await this.$API.costtype.infoDelete.post(reqData);
			if (res.code == 200) {
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？`, '提示', {
				type: 'warning'
			}).then(() => {
				const loading = this.$loading();
				this.selection.forEach(item => {
					this.$refs.table.tableData.forEach((itemI, indexI) => {
						if (item.id === itemI.id) {
							this.$refs.table.tableData.splice(indexI, 1)
						}
					})
				})
				loading.close();
				this.$message.success("操作成功")
			}).catch(() => {

			})
		},
		//提交明细
		saveList() {
			this.$refs.listDialog.submit(async (formData) => {
				this.isListSaveing = true;
				let res = await this.$API.user.save.post(formData);
				this.isListSaveing = false;
				if (res.code == 200) {
					//这里选择刷新整个表格 OR 插入/编辑现有表格数据
					this.listDialogVisible = false;
					this.$message.success("操作成功")
				} else {
					this.$alert(res.message, "提示", {type: 'error'})
				}
			})
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//表格内开关事件
		async changeSwitch(val, row) {
			//1.还原数据
			row.enable = row.enable == '1' ? '0' : '1'
			//2.执行加载
			row.$switch_enable = true;
			//3.等待接口返回后改变值
			console.log("row", val)
			let response = await this.$API.costtype.infoSave.post({
				enable: val,
				id: row.id
			})
			if (response.code == "200") {
				delete row.$switch_enable;
				row.enable = val;
				this.$message.success(`操作成功id:${row.id} val:${val}`)
			} else {
				this.$message.error(`操作失败id:${row.id} val:${val}`)
			}


		},
		//远程更新数据
		async handleDicSuccess() {
			await this.getCosttype()
			// if(mode=='add'){
			// 	data.id = new Date().getTime()
			// 	if(this.costtypeList.length > 0){
			// 		this.$refs.table.upData({
			// 			code: data.code
			// 		})
			// 	}else{
			// 		this.listApiParams = {
			// 			code: data.code
			// 		}
			// 		this.listApi = this.$API.costtype.info;
			// 	}
			// 	this.$refs.costtype.append(data, data.parentId[0])
			// 	this.$refs.costtype.setCurrentKey(data.id)
			// }else if(mode=='edit'){
			// 	let editNode = this.$refs.costtype.getNode(data.id);
			// 	//判断是否移动？
			// 	let editNodeParentId =  editNode.level==1?undefined:editNode.parent.data.id
			// 	if(editNodeParentId != data.parentId){
			// 		let obj = editNode.data;
			// 		this.$refs.costtype.remove(data.id)
			// 		this.$refs.costtype.append(obj, data.parentId[0])
			// 	}
			// 	Object.assign(editNode.data, data)
			// }
		},
		//本地更新数据
		handleListSuccess(data, mode) {
			if (mode == 'add') {
				data.id = new Date().getTime()
				this.$refs.table.tableData.push(data)
			} else if (mode == 'edit') {
				this.$refs.table.tableData.filter(item => item.id === data.id).forEach(item => {
					Object.assign(item, data)
				})
			}
		}
	}
}
</script>

<style scoped>
.custom-tree-node {
	display: flex;
	flex: 1;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 24px;
	height: 100%;
}

.custom-tree-node .code {
	font-size: 12px;
	color: #999;
}

.custom-tree-node .do {
	display: none;
}

.custom-tree-node .do i {
	margin-left: 5px;
	color: #999;
	padding: 5px;
}

.custom-tree-node .do i:hover {
	color: #333;
}

.custom-tree-node:hover .code {
	display: none;
}

.custom-tree-node:hover .do {
	display: inline-block;
}
</style>
