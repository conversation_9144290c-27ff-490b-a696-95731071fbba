<template>
	<el-container>
		
		<el-container>
			<el-header>
				<div class="left-panel">
	<sc-search ref="searchType"
						   :data="filterInfo.data"
						   :field-list="filterInfo.fieldList"
						    
						   @handleReset="handleReset"
						   @handleFilter="handleFilter"
				/>

				
				</div>
				<div class="right-panel">
					
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort pagination="none"
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					
			 <el-table-column label="批改老师" prop="teacher" width="150" sortable='custom'></el-table-column>
			 
					 
				 
					 
					<el-table-column label="日期" prop="pg_date" width="150" sortable='custom'></el-table-column>
			 
					
			 
	            <el-table-column label="数量" prop="num" width="150" sortable='num'></el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>

</template>

<script>
import saveDialog from './save'
import dayjs from 'dayjs'
export default {
	name: 'user',
	components: {
		saveDialog
	},
	data() {
		return {
     filterInfo: {
				data: {
					'c.create_by': null,
					 
					'c.pg_date': {
						"c.pg_date": [
							"between",
							`${dayjs().startOf('month').format('YYYY-MM-DD')},${dayjs().endOf('month').format('YYYY-MM-DD')}`
						]
					},

				},
				fieldList: [
				   
					{
						label: '批改时间', type: 'date', value: 'c.pg_date', dateType: 'daterange'
						, valueFormat: 'YYYY-MM-DD'
					},
						{
						label: '教师', type: 'scSelect',
						value: 'c.teacherid', method: "post", apiObj: this.$API.user.teacher, selectConfig: {
							label: 'name',
							value: 'id'
						}
					},

				],
				//组件尺寸
				size: 'mini',
				//配置项
				btnStyle: [
					{icon: 'el-icon-search', text: '过滤', type: 'primary'},
					{icon: 'el-icon-refresh', text: '重置'}
				]
			},
			dialog: {
				save: false
			},
			num:0,
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.score.tongji,
			selection: [],
			search: {
				name: null,
				dateRange: [],
			},
			defaultProps: {

				label: 'name'
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup()
		 
    	
	},
	methods: {
		//添加
 handleReset(data) {
			this.params.where = {};
        
			this.$refs.table.upData(this.params);
		},
		//焦点失去事件
		handleFilter(data) {
			let params = {where: {}};
			this.filterInfo.fieldList.map(r => {
				 		if (!this.$TOOL.isEmpty(data[r.value])) {
							console.log(r);
				if (r.type == "date") {
							if (r.dateType.indexOf('range') > -1) {
								params.where[r.value] = ['between', data['c.pg_date'][0] + "," +
								data['c.pg_date'][1]]
							} else {
								params.where[r.value] = data[r.value];
							}

						} else if (r.type == "input") {
							params.where[r.value] = ['like', '%' + data[r.value] + '%'];
						} else {
							params.where[r.value] = data[r.value];
						}
					}
				}
			)

			console.log("123123");
			this.$refs.table.upData(params);
		},

 
 
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.teacher.del.post(reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.role.select.get();
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {
			const params = { ...this.search };
			
			// Format date range if selected
			if (this.search.dateRange && this.search.dateRange.length === 2) {
				params.startDate = this.search.dateRange[0];
				params.endDate = this.search.dateRange[1];
			}
			delete params.dateRange; // Remove dateRange from params
			
			this.$refs.table.upData(params);
		},
		//本地更新数据
		handleSuccess() {
			this.dialog.save = false;
			this.$refs.table.refresh();

		}
 

	 

	}
}
</script>

<style>
</style>
