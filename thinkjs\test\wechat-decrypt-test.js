const crypto = require('crypto');

/**
 * 微信公众号加解密测试
 */
class WechatCryptoTest {
  constructor() {
    // 测试配置
    this.config = {
      appid: 'wx19db7a1f925ec299',
      secret: 'b1da3d4739676a46f234f6c8896d3155',
      token: 'jsjywechat',
      encodingAESKey: 'lvxBRL5LNCKlOl1EvYN3YgH5tepeRWVDMdVCAKOJnsi' // 示例密钥
    };
  }

  /**
   * 生成测试用的加密消息
   */
  generateTestEncryptedMessage() {
    const testXml = `<xml>
<ToUserName><![CDATA[toUser]]></ToUserName>
<FromUserName><![CDATA[fromUser]]></FromUserName>
<CreateTime>1348831860</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[this is a test]]></Content>
<MsgId>1234567890123456</MsgId>
</xml>`;

    try {
      const encrypted = this.aesEncrypt(testXml, this.config.encodingAESKey, this.config.appid);
      const timeStamp = parseInt(Date.now() / 1000).toString();
      const nonce = this.generateNonce();
      const msgSignature = this.generateMsgSignature(this.config.token, timeStamp, nonce, encrypted);

      return {
        xml: {
          ToUserName: 'toUser',
          FromUserName: 'fromUser',
          CreateTime: timeStamp,
          MsgType: 'text',
          Encrypt: encrypted,
          MsgSignature: msgSignature,
          TimeStamp: timeStamp,
          Nonce: nonce
        }
      };
    } catch (error) {
      console.error('生成测试加密消息失败:', error);
      return null;
    }
  }

  /**
   * AES加密（与控制器中的方法相同）
   */
  aesEncrypt(msg, encodingAESKey, appid) {
    const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
    const random = crypto.randomBytes(16);
    const msgBuffer = Buffer.from(msg, 'utf8');
    const msgLenBuffer = Buffer.alloc(4);
    msgLenBuffer.writeUInt32BE(msgBuffer.length, 0);
    const appidBuffer = Buffer.from(appid, 'utf8');
    const dataBuffer = Buffer.concat([random, msgLenBuffer, msgBuffer, appidBuffer]);
    const paddedData = this.addPKCS7Padding(dataBuffer);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
    cipher.setAutoPadding(false);
    const encrypted = Buffer.concat([cipher.update(paddedData), cipher.final()]);
    const result = Buffer.concat([iv, encrypted]);
    return result.toString('base64');
  }

  /**
   * AES解密（与控制器中的方法相同）
   */
  aesDecrypt(encryptData, encodingAESKey, appid) {
    const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
    const encryptedBuffer = Buffer.from(encryptData, 'base64');
    const iv = encryptedBuffer.slice(0, 16);
    const ciphertext = encryptedBuffer.slice(16);
    const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
    decipher.setAutoPadding(false);
    let decrypted = Buffer.concat([decipher.update(ciphertext), decipher.final()]);
    decrypted = this.removePKCS7Padding(decrypted);
    const msgLen = decrypted.readUInt32BE(16);
    const msg = decrypted.slice(20, 20 + msgLen).toString('utf8');
    const receivedAppId = decrypted.slice(20 + msgLen).toString('utf8');
    
    if (receivedAppId !== appid) {
      throw new Error(`AppId验证失败: 期望${appid}, 实际${receivedAppId}`);
    }
    
    return msg;
  }

  /**
   * 添加PKCS7填充
   */
  addPKCS7Padding(buffer) {
    const blockSize = 32;
    const paddingLength = blockSize - (buffer.length % blockSize);
    const padding = Buffer.alloc(paddingLength, paddingLength);
    return Buffer.concat([buffer, padding]);
  }

  /**
   * 移除PKCS7填充
   */
  removePKCS7Padding(buffer) {
    if (buffer.length === 0) return buffer;
    const paddingLength = buffer[buffer.length - 1];
    if (paddingLength < 1 || paddingLength > 32) {
      throw new Error('无效的PKCS7填充');
    }
    for (let i = buffer.length - paddingLength; i < buffer.length; i++) {
      if (buffer[i] !== paddingLength) {
        throw new Error('PKCS7填充验证失败');
      }
    }
    return buffer.slice(0, buffer.length - paddingLength);
  }

  /**
   * 生成随机字符串
   */
  generateNonce(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成消息签名
   */
  generateMsgSignature(token, timeStamp, nonce, encryptMsg) {
    const tmpArr = [token, timeStamp, nonce, encryptMsg].sort();
    const tmpStr = tmpArr.join('');
    return crypto.createHash('sha1').update(tmpStr).digest('hex');
  }

  /**
   * 运行测试
   */
  async runTest() {
    console.log('=== 微信公众号加解密测试开始 ===\n');

    try {
      // 1. 测试加密
      console.log('1. 测试消息加密...');
      const testMessage = `<xml>
<ToUserName><![CDATA[toUser]]></ToUserName>
<FromUserName><![CDATA[fromUser]]></FromUserName>
<CreateTime>1348831860</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[this is a test]]></Content>
<MsgId>1234567890123456</MsgId>
</xml>`;

      const encrypted = this.aesEncrypt(testMessage, this.config.encodingAESKey, this.config.appid);
      console.log('✓ 消息加密成功');
      console.log('加密结果长度:', encrypted.length);

      // 2. 测试解密
      console.log('\n2. 测试消息解密...');
      const decrypted = this.aesDecrypt(encrypted, this.config.encodingAESKey, this.config.appid);
      console.log('✓ 消息解密成功');
      console.log('解密结果:\n', decrypted);

      // 3. 验证加解密一致性
      console.log('\n3. 验证加解密一致性...');
      if (decrypted.replace(/\s+/g, '') === testMessage.replace(/\s+/g, '')) {
        console.log('✓ 加解密一致性验证通过');
      } else {
        console.log('✗ 加解密一致性验证失败');
        console.log('原始消息:', testMessage);
        console.log('解密消息:', decrypted);
      }

      // 4. 测试签名生成
      console.log('\n4. 测试消息签名...');
      const timeStamp = parseInt(Date.now() / 1000).toString();
      const nonce = this.generateNonce();
      const signature = this.generateMsgSignature(this.config.token, timeStamp, nonce, encrypted);
      console.log('✓ 消息签名生成成功');
      console.log('签名:', signature);
      console.log('时间戳:', timeStamp);
      console.log('随机数:', nonce);

      console.log('\n=== 所有测试通过 ===');

    } catch (error) {
      console.error('\n✗ 测试失败:', error.message);
      console.error('错误详情:', error);
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new WechatCryptoTest();
  test.runTest();
}

module.exports = WechatCryptoTest; 