<template>
    <div class="school-info-container">
        <sc-page-header title="学校信息管理" />
        
        <div class="content-wrapper">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                <!-- 基本信息 -->
                <el-tab-pane label="基本信息" name="basic">
                    <div class="tab-content">
                        <el-form 
                            ref="basicForm" 
                            :model="basicForm" 
                            :rules="basicRules" 
                            label-width="120px"
                            v-loading="basicLoading"
                        >
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="学校名称" prop="name">
                                        <el-input v-model="basicForm.name" readonly placeholder="请输入学校名称" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="学校简称" prop="shortname">
                                        <el-input v-model="basicForm.shortname" readonly placeholder="请输入学校简称" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                       
                             
                            
                            
                        </el-form>
                    </div>
                </el-tab-pane>
                
                <!-- 公众号参数设置 -->
                <el-tab-pane label="公众号设置" name="wechat">
                    <div class="tab-content">
                        <el-form 
                            ref="wechatForm" 
                            :model="wechatForm" 
                            :rules="wechatRules" 
                            label-width="120px"
                            v-loading="wechatLoading"
                        >
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="公众号APPID" prop="appid">
                                        <el-input v-model="wechatForm.appid" placeholder="请输入公众号APPID" clearable maxlength="50" show-word-limit></el-input>
                                        <div class="form-tip">在微信公众平台-开发-基本配置中获取</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="AppSecret" prop="secret">
                                        <el-input v-model="wechatForm.secret" type="password" placeholder="请输入AppSecret" clearable maxlength="100" show-password></el-input>
                                        <div class="form-tip">在微信公众平台-开发-基本配置中获取，请妥善保管</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="Token令牌" prop="token">
                                        <el-input v-model="wechatForm.token" placeholder="请输入Token令牌" clearable maxlength="32"></el-input>
                                        <div class="form-tip">用于验证消息的令牌，由英文或数字组成，长度为3-32字符</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="消息加密密钥">
                                        <el-input v-model="wechatForm.encodingAESKey" placeholder="请输入EncodingAESKey（可选）" clearable maxlength="43"></el-input>
                                        <div class="form-tip">消息加密密钥，43位字符组成，可选配置</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="服务器地址(URL)">
                                        <el-input :value="serverUrl" readonly>
                                            <template #append>
                                                <el-button @click="copyToClipboard(serverUrl)">
                                                    <el-icon><el-icon-copy-document /></el-icon>
                                                    复制
                                                </el-button>
                                            </template>
                                        </el-input>
                                        <div class="form-tip">请将此地址配置到微信公众平台-开发-基本配置-服务器配置中</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="备注说明">
                                        <el-input 
                                            v-model="wechatForm.remarks" 
                                            type="textarea" 
                                            :autosize="{minRows: 3, maxRows: 6}" 
                                            placeholder="请输入备注说明"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-form-item>
                                <el-button type="primary" @click="saveWechatConfig" :loading="wechatSaving">保存公众号配置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>
                
                <!-- 菜单管理 -->
                <el-tab-pane label="菜单管理" name="menu">
                    <div class="tab-content">
                        <div class="menu-header">
                            <div class="title-container">
                                <h3>公众号菜单管理</h3>
                                <div class="buttons">
                                    <el-button type="primary" @click="addTopMenu" :disabled="topMenus.length >= 3">添加一级菜单</el-button>
                                    <el-button type="success" @click="publishMenu" :loading="menuPublishing">发布菜单</el-button>
                                    <el-button type="danger" @click="deleteAllMenus" :disabled="topMenus.length === 0">清空菜单</el-button>
                                </div>
                            </div>
                            <div class="tips">
                                <p>提示：一级菜单最多3个，每个一级菜单下最多5个二级菜单</p>
                                <p>菜单发布后，24小时内生效</p>
                            </div>
                        </div>
                        
                        <div class="menu-content" v-loading="menuLoading">
                            <div class="menu-preview">
                                <div class="phone-container">
                                    <div class="phone-header">菜单预览</div>
                                    <div class="phone-content">
                                        <!-- 二级菜单弹出层 -->
                                        <div 
                                            v-for="menu in topMenus" 
                                            :key="'submenu-' + menu.id" 
                                            class="submenu-popup"
                                            v-show="activeMenuId === menu.id && getSubMenus(menu.id).length > 0"
                                        >
                                            <div 
                                                v-for="subMenu in getSubMenus(menu.id)" 
                                                :key="subMenu.id"
                                                class="submenu-item"
                                                @click.stop="selectMenu(subMenu.id)"
                                            >
                                                {{ subMenu.name }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="phone-footer">
                                        <div class="menu-bar">
                                            <div 
                                                v-for="menu in topMenus" 
                                                :key="menu.id" 
                                                class="menu-item"
                                                :class="{ active: activeMenuId === menu.id }"
                                                @click="toggleSubMenu(menu.id)"
                                            >
                                                <span class="menu-name">{{ menu.name }}</span>
                                                <span v-if="getSubMenus(menu.id).length > 0" class="menu-arrow">▲</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="menu-editor">
                                <div class="menu-list">
                                    <el-tree
                                        ref="menuTree"
                                        :data="menuTreeData"
                                        node-key="id"
                                        default-expand-all
                                        :expand-on-click-node="false"
                                        highlight-current
                                        :props="{ label: 'name' }"
                                    >
                                        <template #default="{ node, data }">
                                            <div class="custom-tree-node">
                                                <span>{{ node.label }}</span>
                                                <span class="actions">
                                                    <el-button 
                                                        v-if="data.parent_id === 0 && getSubMenus(data.id).length < 5" 
                                                        type="text" 
                                                        @click="addSubMenu(data)"
                                                    >
                                                        添加子菜单
                                                    </el-button>
                                                    <el-button type="text" @click="moveUp(data)">上移</el-button>
                                                    <el-button type="text" @click="moveDown(data)">下移</el-button>
                                                    <el-button type="text" @click="editMenu(data)">编辑</el-button>
                                                    <el-button type="text" @click="deleteMenu(data)">删除</el-button>
                                                </span>
                                            </div>
                                        </template>
                                    </el-tree>
                                </div>
                                
                                <div class="menu-form" v-if="currentMenu">
                                    <el-form ref="menuForm" :model="currentMenu" :rules="menuRules" label-width="100px">
                                        <el-form-item label="菜单名称" prop="name">
                                            <el-input v-model="currentMenu.name" placeholder="请输入菜单名称"></el-input>
                                            <div class="form-tip">
                                                {{ currentMenu.parent_id === 0 ? '一级菜单名称不超过4个汉字' : '二级菜单名称不超过7个汉字' }}
                                            </div>
                                        </el-form-item>
                                        
                                        <el-form-item label="显示顺序" prop="sort_order">
                                            <el-input-number v-model="currentMenu.sort_order" :min="0" :max="99" controls-position="right"></el-input-number>
                                            <div class="form-tip">数字越小排序越靠前，同级菜单中的排序</div>
                                        </el-form-item>
                                        
                                        <el-form-item label="菜单类型" prop="menu_type" v-if="currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0">
                                            <el-radio-group v-model="currentMenu.menu_type" @change="handleMenuTypeChange">
                                                <el-radio :label="1">跳转网页</el-radio>
                                                <el-radio :label="2">点击事件</el-radio>
                                                <el-radio :label="3">小程序</el-radio>
                                                <el-radio :label="4">课程服务</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        
                                        <template v-if="currentMenu.menu_type === 1 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
                                            <el-form-item label="跳转链接" prop="url">
                                                <el-input v-model="currentMenu.url" placeholder="请输入完整的URL，以http或https开头"></el-input>
                                            </el-form-item>
                                        </template>
                                        
                                        <template v-if="currentMenu.menu_type === 2 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
                                            <el-form-item label="事件KEY" prop="menu_key">
                                                <el-input v-model="currentMenu.menu_key" placeholder="请输入事件KEY"></el-input>
                                                <div class="form-tip">用于标识事件，开发者可据此识别用户点击的菜单</div>
                                            </el-form-item>
                                        </template>
                                        
                                        <template v-if="currentMenu.menu_type === 3 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
                                            <el-form-item label="小程序APPID" prop="appid">
                                                <el-input v-model="currentMenu.appid" placeholder="请输入小程序APPID"></el-input>
                                            </el-form-item>
                                            <el-form-item label="页面路径" prop="pagepath">
                                                <el-input v-model="currentMenu.pagepath" placeholder="请输入小程序页面路径"></el-input>
                                                <div class="form-tip">例如：pages/index/index</div>
                                            </el-form-item>
                                        </template>
                                        
                                        <template v-if="currentMenu.menu_type === 4 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
                                            <el-form-item label="课程服务链接">
                                                <el-input v-model="currentMenu.url" disabled></el-input>
                                                <div class="form-tip">课程服务链接已预设</div>
                                            </el-form-item>
                                        </template>
                                        
                                        <el-form-item>
                                            <el-button type="primary" @click="saveMenu">保存</el-button>
                                            <el-button @click="cancelMenuEdit">取消</el-button>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                
                                <div class="menu-form-empty" v-else>
                                    <div class="empty-tip">
                                        <el-icon><el-icon-info-filled /></el-icon>
                                        <p>请先选择或添加菜单</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                
                <!-- 微信支付设置 -->
                <el-tab-pane label="支付设置" name="pay">
                    <div class="tab-content">
                        <el-form 
                            ref="payForm" 
                            :model="payForm" 
                            :rules="payRules" 
                            label-width="120px"
                            v-loading="payLoading"
                        >
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="公众号APPID" prop="appid">
                                        <el-input v-model="payForm.appid" placeholder="请输入公众号APPID" clearable maxlength="50" show-word-limit></el-input>
                                        <div class="form-tip">在微信公众平台-开发-基本配置中获取</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="商户号" prop="mch_id">
                                        <el-input v-model="payForm.mch_id" placeholder="请输入微信支付商户号" clearable maxlength="20"></el-input>
                                        <div class="form-tip">在微信支付商户平台获取</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="API密钥" prop="key">
                                        <el-input v-model="payForm.key" type="password" placeholder="请输入API密钥" clearable maxlength="100" show-password></el-input>
                                        <div class="form-tip">在微信支付商户平台-API安全中设置，请妥善保管</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="支付结果通知地址" prop="notify_url">
                                        <el-input v-model="payForm.notify_url" placeholder="请输入支付结果通知地址">
                                            <template #append>
                                                <el-button @click="copyToClipboard(payForm.notify_url)" v-if="payForm.notify_url">
                                                    <el-icon><el-icon-copy-document /></el-icon>
                                                    复制
                                                </el-button>
                                            </template>
                                        </el-input>
                                        <div class="form-tip">微信支付结果通知接收地址</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="支付证书" prop="cert_path">
                                        <sc-upload-file 
                                            v-model="payForm.cert_path" 
                                            title="证书"  
                                            :apiObj="uploadApi"  
                                            :limit="1"
                                        >
                                            <el-button type="primary" icon="el-icon-upload">上传证书</el-button>
                                        </sc-upload-file>
                                        <div class="form-tip">在微信支付商户平台下载的证书文件,用于退款等操作</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="备注说明">
                                        <el-input 
                                            v-model="payForm.remarks" 
                                            type="textarea" 
                                            :autosize="{minRows: 3, maxRows: 6}" 
                                            placeholder="请输入备注说明"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-form-item>
                                <el-button type="primary" @click="savePayConfig" :loading="paySaving">保存支付配置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import sysConfig from "@/config";

export default {
    name: 'SchoolInfo',
    data() {
        return {
            activeTab: 'basic',
            schoolId: 0,
            
            // 基本信息
            basicLoading: false,
            basicSaving: false,
            basicForm: {
                id: 0,
                name: '',
                shortname: '',
                master: '',
                type: false,
                remarks: ''
            },
            basicRules: {
                name: [
                    { required: true, message: '请输入学校名称', trigger: 'blur' }
                ],
                shortname: [
                    { required: true, message: '请输入学校简称', trigger: 'blur' }
                ],
                master: [
                    { required: true, message: '请选择负责人', trigger: 'blur' }
                ]
            },
            
            // 公众号设置
            wechatLoading: false,
            wechatSaving: false,
            wechatForm: {
                id: 0,
                appid: '',
                secret: '',
                token: '',
                encodingAESKey: '',
                remarks: ''
            },
            wechatRules: {
                appid: [
                    { required: true, message: '请输入公众号APPID', trigger: 'blur' },
                    { pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'APPID格式不正确', trigger: 'blur' }
                ],
                secret: [
                    { required: true, message: '请输入AppSecret', trigger: 'blur' },
                    { min: 32, max: 32, message: 'AppSecret长度必须为32位', trigger: 'blur' }
                ],
                token: [
                    { required: true, message: '请输入Token令牌', trigger: 'blur' },
                    { min: 3, max: 32, message: 'Token长度为3-32个字符', trigger: 'blur' },
                    { pattern: /^[a-zA-Z0-9]+$/, message: 'Token只能由英文字母和数字组成', trigger: 'blur' }
                ]
            },
            
            // 菜单管理
            menuLoading: false,
            menuPublishing: false,
            menus: [],
            currentMenu: null,
            activeMenuId: null,
            menuIdCounter: 1000,
            menuRules: {
                name: [
                    { required: true, message: '请输入菜单名称', trigger: 'blur' },
                    { validator: this.validateMenuName, trigger: 'blur' }
                ],
                url: [
                    { required: true, message: '请输入跳转链接', trigger: 'blur' },
                    { pattern: /^https?:\/\//, message: '链接必须以http或https开头', trigger: 'blur' }
                ],
                menu_key: [
                    { required: true, message: '请输入事件KEY', trigger: 'blur' }
                ],
                appid: [
                    { required: true, message: '请输入小程序APPID', trigger: 'blur' }
                ],
                pagepath: [
                    { required: true, message: '请输入小程序页面路径', trigger: 'blur' }
                ]
            },
            
            // 支付设置
            payLoading: false,
            paySaving: false,
            payForm: {
                id: 0,
                appid: '',
                mch_id: '',
                key: '',
                notify_url: '',
                cert_path: '',
                remarks: ''
            },
            payRules: {
                appid: [
                    { required: true, message: '请输入公众号APPID', trigger: 'blur' },
                    { pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'APPID格式不正确', trigger: 'blur' }
                ],
                mch_id: [
                    { required: true, message: '请输入微信支付商户号', trigger: 'blur' },
                    { pattern: /^\d{10}$/, message: '商户号格式不正确（10位数字）', trigger: 'blur' }
                ],
                key: [
                    { required: true, message: '请输入API密钥', trigger: 'blur' },
                    { min: 6, message: 'API密钥长度不能少于6位', trigger: 'blur' }
                ],
                notify_url: [
                    { required: true, message: '请输入支付结果通知地址', trigger: 'blur' },
                    { pattern: /^https?:\/\//, message: '通知地址必须以http://或https://开头', trigger: 'blur' }
                ],
                cert_path: [
                    { required: true, message: '请上传支付证书', trigger: 'change' }
                ]
            },
            
            // 其他配置
            userProps: {
                label: 'name',
                value: 'id'
            },
            userApiObj: this.$API.user.listselect,
            uploadApi: this.$API.common.upload
        }
    },
    
    computed: {
        serverUrl() {
            return `${window.location.origin}/we/wechat/${this.schoolId}`
        },
        
        topMenus() {
            return this.menus.filter(menu => menu.parent_id === 0).sort((a, b) => a.sort_order - b.sort_order)
        },
        
        menuTreeData() {
            const buildTree = (parentId = 0) => {
                return this.menus
                    .filter(menu => menu.parent_id === parentId)
                    .sort((a, b) => a.sort_order - b.sort_order)
                    .map(menu => ({
                        ...menu,
                        children: buildTree(menu.id)
                    }))
            }
            return buildTree()
        }
    },
    
    mounted() {
        this.init()
    },
    
    methods: {
        async init() {
            // 获取学校ID，可能来自路由参数或用户信息
            this.schoolId = 0
            await this.loadBasicInfo()
        },
        
        // Tab切换处理
        handleTabChange(tabName) {
            switch(tabName) {
                case 'basic':
                    this.loadBasicInfo()
                    break
                case 'wechat':
                    this.loadWechatConfig()
                    break
                case 'menu':
                    this.loadMenus()
                    break
                case 'pay':
                    this.loadPayConfig()
                    break
            }
        },
        
        // 基本信息相关方法
        async loadBasicInfo() {
            this.basicLoading = true
            try {
                const res = await this.$API.school.info.post({ id: this.schoolId })
                console.log(res);
                if (res) {
                    this.basicForm = { ...this.basicForm, ...res }
                }
            } catch (error) {
                this.$message.error('加载基本信息失败：' + error.message)
            } finally {
                this.basicLoading = false
            }
        },
        
        async saveBasicInfo() {
            this.$refs.basicForm.validate(async (valid) => {
                if (valid) {
                    if (!this.basicForm.master || !this.basicForm.master.id) {
                        this.$message.error("请设置负责人")
                        return
                    }
                    
                    this.basicSaving = true
                    try {
                        const formData = { ...this.basicForm }
                        formData.uid = this.basicForm.master.id
                        formData.type = this.basicForm.type ? 1 : 0
                        
                        const res = await this.$API.school.save.post(formData)
                        if (res.code === 200) {
                            this.$message.success("基本信息保存成功")
                        } else {
                            this.$message.error(res.message || "保存失败")
                        }
                    } catch (error) {
                        this.$message.error('保存失败：' + error.message)
                    } finally {
                        this.basicSaving = false
                    }
                }
            })
        },
        
        // 公众号配置相关方法
        async loadWechatConfig() {
            this.wechatLoading = true
            try {
                const res = await this.$API.school.info.post({ id: this.schoolId })
                if (res && res.gzhconfig) {
                    this.wechatForm = { ...this.wechatForm, ...JSON.parse(res.gzhconfig) }
                } else {
                    this.wechatForm = { ...this.wechatForm, ...res }
                }
                this.wechatForm.id = this.schoolId
            } catch (error) {
                this.$message.error('加载公众号配置失败：' + error.message)
            } finally {
                this.wechatLoading = false
            }
        },
        
        async saveWechatConfig() {
            this.$refs.wechatForm.validate(async (valid) => {
                if (valid) {
                    this.wechatSaving = true
                    try {
                        const res = await this.$API.common.util.post('/buss/school/gzh', this.wechatForm)
                        if (res.code === 200) {
                            this.$message.success("公众号配置保存成功")
                        } else {
                            this.$message.error(res.message || "保存失败")
                        }
                    } catch (error) {
                        this.$message.error('保存失败：' + error.message)
                    } finally {
                        this.wechatSaving = false
                    }
                }
            })
        },
        
        // 菜单管理相关方法
        async loadMenus() {
            this.menuLoading = true
            try {
                console.log('开始加载菜单数据')
                const res = await this.$API.common.util.post(`/buss/wechat/list?school_id=${this.schoolId}`)
                if (res.data) {
                    console.log('获取菜单数据成功:', res.data)
                    const sortedData = [...res.data].sort((a, b) => {
                        if (a.parent_id !== b.parent_id) return 0
                        return (a.sort_order || 0) - (b.sort_order || 0)
                    })
                    this.menus = sortedData
                    
                    if (this.menus.length > 0) {
                        const maxId = Math.max(...this.menus.map(menu => menu.id))
                        this.menuIdCounter = maxId + 1
                    }
                    
                    if (this.currentMenu) {
                        const updatedMenu = this.menus.find(m => m.id === this.currentMenu.id)
                        if (updatedMenu) {
                            this.currentMenu = JSON.parse(JSON.stringify(updatedMenu))
                        }
                    }
                    
                    this.$nextTick(() => {
                        if (this.$refs.menuTree) {
                            this.$refs.menuTree.setCurrentKey(null)
                            if (this.currentMenu) {
                                this.$refs.menuTree.setCurrentKey(this.currentMenu.id)
                            }
                        }
                    })
                } else {
                    this.menus = []
                }
            } catch (error) {
                this.$message.error('加载菜单失败：' + error.message)
                this.menus = []
            } finally {
                this.menuLoading = false
            }
        },
        
        addTopMenu() {
            if (this.topMenus.length >= 3) {
                this.$message.warning('一级菜单最多3个')
                return
            }
            
            const siblings = this.topMenus
            const maxSortOrder = siblings.length > 0 
                ? Math.max(...siblings.map(m => m.sort_order || 0)) 
                : -1
                
            const newMenu = {
                id: this.menuIdCounter++,
                school_id: this.schoolId,
                parent_id: 0,
                name: '新菜单',
                menu_type: 1,
                url: '',
                menu_key: '',
                appid: '',
                pagepath: '',
                sort_order: maxSortOrder + 10,
                status: 1
            }
            
            if (newMenu.menu_type === 4) {
                newMenu.url = sysConfig.SITE_URL + "/wx/index/" + this.schoolId
            }
            
            this.menus.push(newMenu)
            this.selectMenu(newMenu.id)
        },
        
        addSubMenu(parentMenu) {
            const subMenus = this.getSubMenus(parentMenu.id)
            if (subMenus.length >= 5) {
                this.$message.warning('二级菜单最多5个')
                return
            }
            
            const maxSortOrder = subMenus.length > 0 
                ? Math.max(...subMenus.map(m => m.sort_order || 0)) 
                : -1
                
            const newMenu = {
                id: this.menuIdCounter++,
                school_id: this.schoolId,
                parent_id: parentMenu.id,
                name: '新子菜单',
                menu_type: 1,
                url: '',
                menu_key: '',
                appid: '',
                pagepath: '',
                sort_order: maxSortOrder + 10,
                status: 1
            }
            
            if (newMenu.menu_type === 4) {
                newMenu.url = sysConfig.SITE_URL + "/wx/index/" + this.schoolId
            }
            
            this.menus.push(newMenu)
            this.selectMenu(newMenu.id)
        },
        
        moveUp(menu) {
            const siblings = menu.parent_id === 0 
                ? this.menus.filter(m => m.parent_id === 0)
                : this.menus.filter(m => m.parent_id === menu.parent_id)
                
            const sortedSiblings = [...siblings].sort((a, b) => a.sort_order - b.sort_order)
            const currentIndex = sortedSiblings.findIndex(m => m.id === menu.id)
            
            if (currentIndex === 0) {
                this.$message.info('已经是第一个菜单')
                return
            }
            
            const prevMenu = sortedSiblings[currentIndex - 1]
            const currentSort = menu.sort_order
            
            menu.sort_order = prevMenu.sort_order
            prevMenu.sort_order = currentSort
            
            this.menuLoading = true
            
            this.$API.common.util.post('/buss/wechat/updateSort', {
                id: menu.id,
                school_id: this.schoolId,
                sort_order: menu.sort_order
            }).then(res => {
                if (res.errno !== 0) {
                    this.$message.error('保存菜单顺序失败')
                    return Promise.reject(new Error('保存菜单顺序失败'))
                }
                
                return this.$API.common.util.post('/buss/wechat/updateSort', {
                    id: prevMenu.id,
                    school_id: this.schoolId,
                    sort_order: prevMenu.sort_order
                })
            }).then(res => {
                if (res && res.errno !== 0) {
                    this.$message.error('保存菜单顺序失败')
                    return Promise.reject(new Error('保存菜单顺序失败'))
                }
                
                return this.loadMenus()
            }).then(() => {
                this.$nextTick(() => {
                    if (this.$refs.menuTree) {
                        this.$refs.menuTree.setCurrentKey(menu.id)
                    }
                })
            }).catch(err => {
                this.$message.error('保存菜单顺序失败: ' + (err.message || '未知错误'))
            }).finally(() => {
                this.menuLoading = false
            })
        },
        
        moveDown(menu) {
            const siblings = menu.parent_id === 0 
                ? this.menus.filter(m => m.parent_id === 0)
                : this.menus.filter(m => m.parent_id === menu.parent_id)
                
            const sortedSiblings = [...siblings].sort((a, b) => a.sort_order - b.sort_order)
            const currentIndex = sortedSiblings.findIndex(m => m.id === menu.id)
            
            if (currentIndex === sortedSiblings.length - 1) {
                this.$message.info('已经是最后一个菜单')
                return
            }
            
            const nextMenu = sortedSiblings[currentIndex + 1]
            const currentSort = menu.sort_order
            
            menu.sort_order = nextMenu.sort_order
            nextMenu.sort_order = currentSort
            
            this.menuLoading = true
            
            this.$API.common.util.post('/buss/wechat/updateSort', {
                id: menu.id,
                school_id: this.schoolId,
                sort_order: menu.sort_order
            }).then(res => {
                if (res.errno !== 0) {
                    this.$message.error('保存菜单顺序失败')
                    return Promise.reject(new Error('保存菜单顺序失败'))
                }
                
                return this.$API.common.util.post('/buss/wechat/updateSort', {
                    id: nextMenu.id,
                    school_id: this.schoolId,
                    sort_order: nextMenu.sort_order
                })
            }).then(res => {
                if (res && res.errno !== 0) {
                    this.$message.error('保存菜单顺序失败')
                    return Promise.reject(new Error('保存菜单顺序失败'))
                }
                
                return this.loadMenus()
            }).then(() => {
                this.$nextTick(() => {
                    if (this.$refs.menuTree) {
                        this.$refs.menuTree.setCurrentKey(menu.id)
                    }
                })
            }).catch(err => {
                this.$message.error('保存菜单顺序失败: ' + (err.message || '未知错误'))
            }).finally(() => {
                this.menuLoading = false
            })
        },
        
        editMenu(menu) {
            this.selectMenu(menu.id)
        },
        
        async deleteMenu(menu) {
            try {
                if (menu.parent_id === 0) {
                    const subMenus = this.getSubMenus(menu.id)
                    if (subMenus.length > 0) {
                        await this.$confirm('删除此菜单将同时删除其下的所有子菜单，是否继续？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                    }
                }
                
                try {
                    const res = await this.$API.common.util.post('/buss/wechat/delete', {
                        id: menu.id,
                        school_id: this.schoolId
                    })
                    
                    if (res.errno === 0) {
                        if (menu.parent_id === 0) {
                            this.menus = this.menus.filter(item => item.parent_id !== menu.id)
                        }
                        
                        this.menus = this.menus.filter(item => item.id !== menu.id)
                        
                        if (this.currentMenu && this.currentMenu.id === menu.id) {
                            this.currentMenu = null
                            this.activeMenuId = null
                        }
                        
                        if (menu.parent_id === 0) {
                            this.topMenus.forEach((item, index) => {
                                item.sort_order = index
                            })
                        } else {
                            const siblings = this.getSubMenus(menu.parent_id)
                            siblings.forEach((item, index) => {
                                item.sort_order = index
                            })
                        }
                        
                        this.$message.success('删除成功')
                    } else {
                        this.$message.error('删除失败：' + res.message)
                    }
                } catch (error) {
                    this.$message.error('删除失败：' + error.message)
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除失败：' + error.message)
                }
            }
        },
        
        validateMenuName(rule, value, callback) {
            if (!value) {
                callback(new Error('请输入菜单名称'))
                return
            }
            
            const menu = this.currentMenu
            if (!menu) {
                callback()
                return
            }
            
            const getLength = str => {
                let len = 0
                for (let i = 0; i < str.length; i++) {
                    if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
                        len += 2
                    } else {
                        len++
                    }
                }
                return len
            }
            
            const nameLength = getLength(value)
            if (menu.parent_id === 0 && nameLength > 8) {
                callback(new Error('一级菜单名称不能超过4个汉字或8个字符'))
            } else if (menu.parent_id !== 0 && nameLength > 14) {
                callback(new Error('二级菜单名称不能超过7个汉字或14个字符'))
            } else {
                callback()
            }
        },
        
        saveMenu() {
            if (!this.currentMenu) return
            
            this.$refs.menuForm.validate(async valid => {
                if (!valid) return
                
                try {
                    const res = await this.$API.common.util.post('/buss/wechat/save', this.currentMenu)
                    
                    if (res.errno === 0) {
                        const index = this.menus.findIndex(item => item.id === this.currentMenu.id)
                        if (index !== -1) {
                            this.menus.splice(index, 1, this.currentMenu)
                        } else {
                            this.menus.push(this.currentMenu)
                        }
                        
                        this.$message.success('保存成功')
                    } else {
                        this.$message.error('保存失败：' + res.message)
                    }
                } catch (error) {
                    this.$message.error('保存失败：' + error.message)
                }
            })
        },
        
        handleMenuTypeChange(value) {
            if (value === 4) {
                this.currentMenu.url = sysConfig.SITE_URL + "/wx/index/" + this.schoolId
            }
        },
        
        cancelMenuEdit() {
            this.currentMenu = null
            this.activeMenuId = null
        },
        
        async publishMenu() {
            if (this.topMenus.length === 0) {
                this.$message.warning('没有可发布的菜单')
                return
            }
            
            const validationError = this.validateMenusForPublish()
            if (validationError) {
                this.$message.error(validationError)
                return
            }
            
            try {
                this.menuPublishing = true
                
                const menuData = {
                    button: this.topMenus
                        .sort((a, b) => a.sort_order - b.sort_order)
                        .map(menu => {
                            const subMenus = this.getSubMenus(menu.id)
                            const menuItem = {
                                name: menu.name
                            }
                        
                        if (subMenus.length > 0) {
                            menuItem.sub_button = subMenus
                                .sort((a, b) => a.sort_order - b.sort_order)
                                .map(sub => {
                                    if (!sub.name) {
                                        this.$message.warning(`子菜单缺少名称，已自动设置为"菜单项"`)
                                        sub.name = "菜单项"
                                    }
                                    return this.formatMenuButton(sub)
                                })
                        } else {
                            Object.assign(menuItem, this.formatMenuButton(menu))
                        }
                        
                        return menuItem
                    })
                }
                
                const res = await this.$API.common.util.post('/buss/wechat/publish', {
                    school_id: this.schoolId,
                    menu_data: JSON.stringify(menuData)
                })
                
                if (res.errno === 0) {
                    this.$message.success('菜单发布成功，24小时内生效')
                } else {
                    this.$message.error('发布失败：' + res.message)
                }
            } catch (error) {
                this.$message.error('发布失败：' + error.message)
            } finally {
                this.menuPublishing = false
            }
        },
        
        validateMenusForPublish() {
            for (const menu of this.topMenus) {
                if (!menu.name) {
                    return `一级菜单缺少名称`
                }
                
                const getLength = str => {
                    let len = 0
                    for (let i = 0; i < str.length; i++) {
                        if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
                            len += 2
                        } else {
                            len++
                        }
                    }
                    return len
                }
                
                if (getLength(menu.name) > 8) {
                    return `一级菜单"${menu.name}"名称过长，不能超过4个汉字或8个字符`
                }
                
                const subMenus = this.getSubMenus(menu.id)
                if (subMenus.length > 0) {
                    for (const subMenu of subMenus) {
                        if (!subMenu.name) {
                            return `菜单"${menu.name}"的子菜单缺少名称`
                        }
                        
                        if (getLength(subMenu.name) > 14) {
                            return `菜单"${menu.name}"的子菜单"${subMenu.name}"名称过长，不能超过7个汉字或14个字符`
                        }
                        
                        if (subMenu.menu_type === 1 && !subMenu.url) {
                            return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少URL`
                        }
                        
                        if (subMenu.menu_type === 2 && !subMenu.menu_key) {
                            return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少事件KEY`
                        }
                        
                        if (subMenu.menu_type === 3) {
                            if (!subMenu.appid) {
                                return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少小程序APPID`
                            }
                            if (!subMenu.pagepath) {
                                return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少小程序页面路径`
                            }
                        }
                    }
                } else {
                    if (menu.menu_type === 1 && !menu.url) {
                        return `菜单"${menu.name}"缺少URL`
                    }
                    
                    if (menu.menu_type === 2 && !menu.menu_key) {
                        return `菜单"${menu.name}"缺少事件KEY`
                    }
                    
                    if (menu.menu_type === 3) {
                        if (!menu.appid) {
                            return `菜单"${menu.name}"缺少小程序APPID`
                        }
                        if (!menu.pagepath) {
                            return `菜单"${menu.name}"缺少小程序页面路径`
                        }
                    }
                }
            }
            
            return null
        },
        
        formatMenuButton(menu) {
            const result = {
                name: menu.name || "菜单项"
            }
            
            switch (menu.menu_type) {
                case 1:
                    result.type = 'view'
                    result.url = menu.url
                    break
                case 2:
                    result.type = 'click'
                    result.key = menu.menu_key
                    break
                case 3:
                    result.type = 'miniprogram'
                    result.url = menu.url || 'http://mp.weixin.qq.com'
                    result.appid = menu.appid
                    result.pagepath = menu.pagepath
                    break
                case 4:
                    result.type = 'view'
                    result.url = sysConfig.SITE_URL + "/wx/index/" + this.schoolId
                    break
            }
            
            return result
        },
        
        async deleteAllMenus() {
            try {
                await this.$confirm('确定要清空所有菜单吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                
                try {
                    const res = await this.$API.common.util.post('/buss/wechat/batchSave', {
                        school_id: this.schoolId,
                        menus: []
                    })
                    
                    if (res.errno === 0) {
                        this.menus = []
                        this.currentMenu = null
                        this.activeMenuId = null
                        this.$message.success('已清空所有菜单')
                    } else {
                        this.$message.error('清空菜单失败：' + res.message)
                    }
                } catch (error) {
                    this.$message.error('清空菜单失败：' + error.message)
                }
            } catch (error) {
                // 用户取消操作
            }
        },
        
        getSubMenus(parentId) {
            return this.menus.filter(menu => menu.parent_id === parentId).sort((a, b) => a.sort_order - b.sort_order)
        },
        
        toggleSubMenu(menuId) {
            if (this.activeMenuId === menuId) {
                this.activeMenuId = null
                this.currentMenu = null
            } else {
                this.activeMenuId = menuId
                this.currentMenu = JSON.parse(JSON.stringify(this.menus.find(menu => menu.id === menuId)))
            }
        },
        
        selectMenu(menuId) {
            this.activeMenuId = menuId
            this.currentMenu = JSON.parse(JSON.stringify(this.menus.find(menu => menu.id === menuId)))
            if (this.currentMenu && this.currentMenu.menu_type === 4) {
                this.currentMenu.url = sysConfig.SITE_URL + "/wx/index/" + this.schoolId
            }
        },
        
        // 支付配置相关方法
        async loadPayConfig() {
            this.payLoading = true
            try {
                const res = await this.$API.school.info.post({ id: this.schoolId })
                if (res && res.wxpayconfig) {
                    this.payForm = { ...this.payForm, ...JSON.parse(res.wxpayconfig) }
                } else {
                    this.payForm = { ...this.payForm, ...res }
                }
                this.payForm.id = this.schoolId
                
                // 调用初始化方法加载更多配置
                await this.initPayConfig()
            } catch (error) {
                this.$message.error('加载支付配置失败：' + error.message)
            } finally {
                this.payLoading = false
            }
        },
        
        async initPayConfig() {
            try {
                const res = await this.$API.common.util.post('/buss/school/getwxpay', this.payForm)
                if (res.data) {
                    this.payForm = { ...this.payForm, ...res.data }
                }
            } catch (error) {
                console.log('初始化支付配置失败：' + error.message)
            }
        },
        
        async savePayConfig() {
            this.$refs.payForm.validate(async (valid) => {
                if (valid) {
                    this.paySaving = true
                    try {
                        const res = await this.$API.common.util.post('/buss/school/wxpay', this.payForm)
                        if (res.errno === 0) {
                            this.$message.success("支付配置保存成功")
                        } else {
                            this.$message.error(res.message || "保存失败")
                        }
                    } catch (error) {
                        this.$message.error('保存失败：' + error.message)
                    } finally {
                        this.paySaving = false
                    }
                }
            })
        },
        
        // 工具方法
        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text)
                this.$message.success('已复制到剪贴板')
            } catch (error) {
                const textArea = document.createElement('textarea')
                textArea.value = text
                document.body.appendChild(textArea)
                textArea.select()
                document.execCommand('copy')
                document.body.removeChild(textArea)
                this.$message.success('已复制到剪贴板')
            }
        }
    }
}
</script>

<style scoped>
.school-info-container {
    padding: 20px;
}

.content-wrapper {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
    padding: 20px 0;
}

.form-tip {
    color: #909399;
    font-size: 12px;
    line-height: 1.5;
    margin-top: 4px;
}

/* 菜单管理样式 */
.menu-header {
    margin-bottom: 20px;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.title-container h3 {
    margin: 0;
    color: #303133;
}

.buttons {
    display: flex;
    gap: 10px;
}

.tips {
    color: #909399;
    font-size: 12px;
    line-height: 1.5;
}

.menu-content {
    display: flex;
    gap: 20px;
    min-height: 500px;
}

.menu-preview {
    flex: 0 0 300px;
}

.phone-container {
    border: 2px solid #ddd;
    border-radius: 20px;
    width: 280px;
    height: 500px;
    background: #f5f5f5;
    position: relative;
    overflow: hidden;
}

.phone-header {
    background: #007aff;
    color: white;
    text-align: center;
    padding: 10px;
    font-size: 14px;
}

.phone-content {
    height: 400px;
    background: white;
    position: relative;
}

.phone-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f8f8;
    border-top: 1px solid #e5e5e5;
}

.menu-bar {
    display: flex;
    height: 50px;
}

.menu-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-right: 1px solid #e5e5e5;
    transition: background-color 0.3s;
    font-size: 12px;
    color: #666;
}

.menu-item:last-child {
    border-right: none;
}

.menu-item:hover {
    background: #e5e5e5;
}

.menu-item.active {
    background: #007aff;
    color: white;
}

.menu-name {
    font-size: 11px;
}

.menu-arrow {
    font-size: 8px;
    margin-top: 2px;
}

.submenu-popup {
    position: absolute;
    bottom: 50px;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e5e5;
    max-height: 200px;
    overflow-y: auto;
}

.submenu-item {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    font-size: 13px;
    color: #333;
}

.submenu-item:hover {
    background: #f5f5f5;
}

.submenu-item:last-child {
    border-bottom: none;
}

.menu-editor {
    flex: 1;
    display: flex;
    gap: 20px;
}

.menu-list {
    flex: 0 0 300px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 10px;
    max-height: 500px;
    overflow-y: auto;
}

.custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    font-size: 13px;
}

.custom-tree-node .actions {
    display: none;
}

.custom-tree-node:hover .actions {
    display: block;
}

 .menu-form, .menu-form-empty {
     flex: 1;
     border: 1px solid #e5e5e5;
     border-radius: 4px;
     padding: 20px;
     max-height: 500px;
     overflow-y: auto;
 }
 
 .menu-form-empty {
     display: flex;
     justify-content: center;
     align-items: center;
 }
 
 .empty-tip {
     text-align: center;
     color: #909399;
 }
 
 .empty-tip i {
     font-size: 48px;
     margin-bottom: 10px;
 }

/* 响应式设计 */
@media (max-width: 1200px) {
    .menu-content {
        flex-direction: column;
    }
    
    .menu-preview {
        flex: none;
        align-self: center;
    }
    
    .menu-editor {
        flex-direction: column;
    }
    
    .menu-list {
        flex: none;
    }
}
</style>
