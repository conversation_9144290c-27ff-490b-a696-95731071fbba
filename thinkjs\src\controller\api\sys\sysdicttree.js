const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {
  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    where['del_flag'] = 0;
    const model = this.model('dict_tree');
    const response = await model.where(where).select();
    // 转换为树
    const data = this.transTreeArray(response, 'id', 'pid');
    respData = {
      code: 200,
      data: data,
      msg: ''
    };
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const model = this.model('dict_tree');
      this.post()['update_date'] = think.datetime();
      await model.where({id: id}).update(this.post());
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }

  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const dictTree = this.model('dict_tree');
      const dictInfo = this.model('dict_info');
      await dictTree.where({id: id}).update({del_flag: 1, update_date: think.datetime()});
      await dictInfo.where({tree_id: id}).update({del_flag: 1,update_date:think.datetime()});
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }

  async addAction() {
    let respData = {};
    const model = this.model('dict_tree');
    this.post()['create_date'] = think.datetime();
    this.post()['id'] = think.uuid();
    this.post()['del_flag'] = 0;
    await model.add(this.post());
    respData = {
      code: 200,
      data: {},
      msg: '成功'
    };
    return this.json(respData);
  }
};
