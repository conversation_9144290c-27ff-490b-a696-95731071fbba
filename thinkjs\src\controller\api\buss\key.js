const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {


  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const learnLogModel = this.model('key');
    where['p.del_flag'] = 0;
    
    //let dataScopeWhere = await this.dataScope('p');

    const response = await learnLogModel
      .alias('p')
      .field('p.*')
     .join(["buss_school l on l.id=p.school"])
     .join(["sys_user u on u.id=p.create_by"])
      .page(page, rows).where(where)
      .order('p.create_date desc').field("p.*,l.name as schoolname,u.name as uname").countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }



  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('key');
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const model = this.model('key');
    let num=this.post("num");
    let arry=[];


    let keysarr=this.generateKeys(this.post("keystart"),num);


    console.log(keysarr);


    for(let tmp of keysarr){
        let one={};
        this.addData(one);
        one.key=tmp

       
        one.state=0;
        one.school=this.post("school");
        //arry.push(one);

        await model.add(one);
    }
    //console.log(arry);
   
      
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    
    return this.json(respData);
  }

  async generateKeys(pattern, quantity) {
    // 计算顺序号的位数
    const serialLength = Math.max(quantity.toString().length, 2); // 至少2位
  
    return Array.from({ length: quantity }, (_, index) => {
      const serial = (index + 1).toString().padStart(serialLength, '0'); // 生成顺序号
  
      return pattern
        // 替换 * 为随机数
        .replace(/\*/g, () => Math.floor(Math.random() * 10).toString()) // 生成0-9的随机数
        // 替换 # 为随机字母
        .replace(/#/g, () => {
          const letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // 排除易混淆字符
          return letters[Math.floor(Math.random() * letters.length)];
        })
        // 替换 & 为补零的顺序号，逐个替换
        .replace(/&/g, () => serial);
    });
  }
  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('key');
    where['p.del_flag'] = 0;
    const response = await model
      .alias('p')
      .field('p.*')
      .where(where)
      .select();
    respData = {
      code: 200,
      data: response,
      message: ''
    };
    return this.json(respData);
  }


 
};
