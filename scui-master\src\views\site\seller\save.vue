<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/9 14:02:59
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
			 label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="销售员" prop="sale_name">
					<el-input v-model="form.sale_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="代销者" prop="sale_replace_name">
					<el-input v-model="form.sale_replace_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="性别" prop="sale_gender">
					<sc-select v-model="form.sale_gender"
					           :selectConfig="selectConfig.gender"
					           clearable dic="gender"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="生日" prop="sale_birthday">
					<el-date-picker
						v-model="form.sale_birthday"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="身份证号" prop="sale_idcard">
					<el-input v-model="form.sale_idcard" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="文化程度" prop="sale_education">
					<sc-select v-model="form.sale_education"
					           :selectConfig="selectConfig.education"
					           clearable dic="education"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="从业时间" prop="sale_work_time">
					<el-input v-model="form.sale_work_time" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="党派" prop="sale_party">
					<sc-select v-model="form.sale_party"
					           :selectConfig="selectConfig.party"
					           clearable dic="party"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="地址" prop="sale_address">
					<el-input v-model="form.sale_address" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="联系电话" prop="sale_phone">
					<el-input v-model="form.sale_phone" clearable></el-input>
				</el-form-item>
			</el-col>
		</el-row>

	</el-form>
</template>

<script>
export default {
	props: {
		mode: {type: String, default: "add"}
	},
	data() {
		return {
			//表单数据
			form: {
				id: "",

				sale_name: "",

				sale_replace_name: "",

				sale_gender: "",

				sale_birthday: "",

				sale_idcard: "",

				sale_education: "",

				sale_work_time: "",

				sale_party: "",

				sale_address: "",

				sale_phone: "",

			},
			//验证规则
			rules: {

				sale_name: [
					{required: true, message: '请输入销售员'}
				],

				sale_replace_name: [
					{required: true, message: '请输入代销者'}
				],

				sale_gender: [
					{required: true, message: '请输入性别'}
				],

				sale_birthday: [
					{required: true, message: '请输入生日'}
				],

				sale_idcard: [
					{required: true, message: '请输入身份证号'}
				],

				sale_education: [
					{required: true, message: '请输入文化程度'}
				],

				sale_work_time: [
					{required: true, message: '请输入从业时间'}
				],

				sale_party: [
					{required: true, message: '请输入党派'}
				],

				sale_address: [
					{required: true, message: '请输入地址'}
				],

				sale_phone: [
					{required: true, message: '请输入联系电话'}
				],

			},
			selectConfig: {
				gender:{
					label: 'name',
					value: 'key'
				},
				education:{
					label: 'name',
					value: 'key'
				},
				party:{
					label: 'name',
					value: 'key'
				},
			}
		}
	},
	mounted() {

	},
	methods: {
		//表单提交方法
		submit(callback) {
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
