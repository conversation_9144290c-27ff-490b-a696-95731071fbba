 <template>
  <el-dialog title="公众号菜单管理" v-model="visible" :width="1200" destroy-on-close @closed="$emit('closed')">
    <div class="menu-container" v-loading="loading">
      <div class="menu-header">
        <div class="title-container">
          <h3>{{ schoolName }}公众号菜单管理</h3>
          <div class="buttons">
            <el-button type="primary" @click="addTopMenu" :disabled="topMenus.length >= 3">添加一级菜单</el-button>
            <el-button type="success" @click="publishMenu" :loading="publishing">发布菜单</el-button>
            <el-button type="danger" @click="deleteAllMenus" :disabled="topMenus.length === 0">清空菜单</el-button>
          </div>
        </div>
        <div class="tips">
          <p>提示：一级菜单最多3个，每个一级菜单下最多5个二级菜单</p>
          <p>菜单发布后，24小时内生效</p>
        </div>
      </div>
      
      <div class="menu-preview">
        <div class="phone-container">
          <div class="phone-header">公众号菜单预览</div>
          <div class="phone-content">
            <!-- 二级菜单弹出层 -->
            <div 
              v-for="menu in topMenus" 
              :key="'submenu-' + menu.id" 
              class="submenu-popup"
              v-show="activeMenuId === menu.id && getSubMenus(menu.id).length > 0"
            >
              <div 
                v-for="subMenu in getSubMenus(menu.id)" 
                :key="subMenu.id"
                class="submenu-item"
                :class="{ active: activeMenuId === subMenu.id }"
                @click.stop="selectMenu(subMenu.id)"
              >
                {{ subMenu.name }}
              </div>
            </div>
          </div>
          <div class="phone-footer">
            <div class="menu-bar">
              <div 
                v-for="menu in topMenus" 
                :key="menu.id" 
                class="menu-item"
                :class="{ active: activeMenuId === menu.id || getSubMenus(menu.id).some(sub => sub.id === activeMenuId) }"
                @click="toggleSubMenu(menu.id)"
              >
                <span class="menu-name">{{ menu.name }}</span>
                <span v-if="getSubMenus(menu.id).length > 0" class="menu-arrow" :class="{ 'arrow-up': activeMenuId === menu.id }">{{ activeMenuId === menu.id ? '▼' : '▲' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="menu-editor">
        <div class="menu-list">
          <el-tree
            ref="menuTree"
            :data="menuTreeData"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            highlight-current
            :props="{ label: 'name' }"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span class="actions">
                  <el-button 
                    v-if="data.parent_id === 0 && getSubMenus(data.id).length < 5" 
                    type="text" 
                    @click="addSubMenu(data)"
                  >
                    添加子菜单
                  </el-button>
                  <el-button type="text" @click="moveUp(data)">上移</el-button>
                  <el-button type="text" @click="moveDown(data)">下移</el-button>
                  <el-button type="text" @click="editMenu(data)">编辑</el-button>
                  <el-button type="text" @click="deleteMenu(data)">删除</el-button>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
        
        <div class="menu-form" v-if="currentMenu">
          <el-form ref="form" :model="currentMenu" :rules="rules" label-width="100px">
            <el-form-item label="菜单名称" prop="name">
              <el-input v-model="currentMenu.name" placeholder="请输入菜单名称"></el-input>
              <div class="form-tip">
                {{ currentMenu.parent_id === 0 ? '一级菜单名称不超过4个汉字' : '二级菜单名称不超过7个汉字' }}
              </div>
            </el-form-item>
            
            <el-form-item label="显示顺序" prop="sort_order">
              <el-input-number v-model="currentMenu.sort_order" :min="0" :max="99" controls-position="right"></el-input-number>
              <div class="form-tip">数字越小排序越靠前，同级菜单中的排序</div>
            </el-form-item>
            
            <el-form-item label="菜单类型" prop="menu_type" v-if="currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0">
              <el-radio-group v-model="currentMenu.menu_type" @change="handleMenuTypeChange">
                <el-radio :label="1">跳转网页</el-radio>
                <el-radio :label="2">点击事件</el-radio>
                <el-radio :label="3">小程序</el-radio>
                <el-radio :label="4">课程服务</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <template v-if="currentMenu.menu_type === 1 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
              <el-form-item label="跳转链接" prop="url">
                <el-input v-model="currentMenu.url" placeholder="请输入完整的URL，以http或https开头"></el-input>
              </el-form-item>
            </template>
            
            <template v-if="currentMenu.menu_type === 2 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
              <el-form-item label="事件KEY" prop="menu_key">
                <el-input v-model="currentMenu.menu_key" placeholder="请输入事件KEY"></el-input>
                <div class="form-tip">用于标识事件，开发者可据此识别用户点击的菜单</div>
              </el-form-item>
            </template>
            
            <template v-if="currentMenu.menu_type === 3 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
              <el-form-item label="小程序APPID" prop="appid">
                <el-input v-model="currentMenu.appid" placeholder="请输入小程序APPID"></el-input>
              </el-form-item>
              <el-form-item label="页面路径" prop="pagepath">
                <el-input v-model="currentMenu.pagepath" placeholder="请输入小程序页面路径"></el-input>
                <div class="form-tip">例如：pages/index/index</div>
              </el-form-item>
            </template>
            
            <template v-if="currentMenu.menu_type === 4 && (currentMenu.parent_id !== 0 || getSubMenus(currentMenu.id).length === 0)">
              <el-form-item label="课程服务链接">
                <el-input v-model="currentMenu.url" disabled></el-input>
                <div class="form-tip">课程服务链接已预设为：{{url}}</div>
              </el-form-item>
            </template>
            
            <el-form-item>
              <el-button type="primary" @click="saveMenu">保存</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="menu-form-empty" v-else>
          <div class="empty-tip">
            <el-icon><el-icon-info-filled /></el-icon>
            <p>请先选择或添加菜单</p>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import sysConfig from "@/config";
export default {
  name: 'menu-manage',
  emits: ['success', 'closed'],
  data() {
    return {
      visible: false,
      loading: false,
      publishing: false,
      schoolId: 0,
      schoolName: '',
      menus: [],
      url:sysConfig.SITE_URL+"/wx/index/"+this.schoolId,
      currentMenu: null,
      activeMenuId: null,
      menuIdCounter: 1000,
      rules: {
        name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' },
          { validator: this.validateMenuName, trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入跳转链接', trigger: 'blur' },
          { pattern: /^https?:\/\//, message: '链接必须以http或https开头', trigger: 'blur' }
        ],
        menu_key: [
          { required: true, message: '请输入事件KEY', trigger: 'blur' }
        ],
        appid: [
          { required: true, message: '请输入小程序APPID', trigger: 'blur' }
        ],
        pagepath: [
          { required: true, message: '请输入小程序页面路径', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    topMenus() {
      // 按排序字段排序
      return this.menus
        .filter(menu => menu.parent_id === 0)
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
    },
    menuTreeData() {
      // 按排序字段排序
      const topMenus = this.menus
        .filter(menu => menu.parent_id === 0)
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
        
      return topMenus.map(menu => {
        const subMenus = this.getSubMenus(menu.id);
        return {
          ...menu,
          children: subMenus.length > 0 ? subMenus : undefined
        };
      });
    }
  },
  mounted() {
    // 添加点击空白处关闭子菜单的事件监听
    document.addEventListener('click', this.handleOutsideClick);
  },
  beforeUnmount() {
    // 移除事件监听
    document.removeEventListener('click', this.handleOutsideClick);
  },
  methods: {
    open() {
      this.visible = true;
      return this;
    },
    
    setData(data) {
      this.schoolId = data.school_id;
      this.url=sysConfig.SITE_URL+"/wx/index/"+this.schoolId;
      this.schoolName = data.school_name || '学校';
      this.loadMenus();
    },
    
    async loadMenus() {
      this.loading = true;
      try {
        console.log('开始加载菜单数据');
        // 调用API获取菜单数据
        const res = await this.$API.common.util.post(`/buss/wechat/list?school_id=${this.schoolId}`);
        if (res.data) {
          console.log('获取菜单数据成功:', res.data);
          // 直接使用this而不是that，避免引用问题
          // 确保菜单按sort_order排序
          const sortedData = [...res.data].sort((a, b) => {
            if (a.parent_id !== b.parent_id) return 0; // 只排序同级菜单
            return (a.sort_order || 0) - (b.sort_order || 0);
          });
          this.menus = sortedData; // 使用排序后的数据
          
          // 找出最大ID，用于后续添加菜单
          console.log('加载菜单数据:', this.menus);
          if (this.menus.length > 0) {
            const maxId = Math.max(...this.menus.map(menu => menu.id));
            this.menuIdCounter = maxId + 1;
          }
          
          // 如果当前有选中的菜单，更新它的数据
          if (this.currentMenu) {
            const updatedMenu = this.menus.find(m => m.id === this.currentMenu.id);
            if (updatedMenu) {
              this.currentMenu = JSON.parse(JSON.stringify(updatedMenu));
            }
          }
          
          // 强制刷新菜单树视图
          this.$nextTick(() => {
            if (this.$refs.menuTree) {
              // 先清空当前选中，再重新设置
              this.$refs.menuTree.setCurrentKey(null);
              if (this.currentMenu) {
                this.$refs.menuTree.setCurrentKey(this.currentMenu.id);
              }
            }
          });
        } else {
          this.menus = [];
        }
      } catch (error) {
        this.$message.error('加载菜单失败：' + error.message);
        this.menus = [];
      } finally {
        this.loading = false;
      }
    },
    
    getSubMenus(parentId) {
      return this.menus.filter(menu => menu.parent_id === parentId)
        .sort((a, b) => a.sort_order - b.sort_order);
    },
    
    selectMenu(id) {
      this.activeMenuId = id;
      this.currentMenu = JSON.parse(JSON.stringify(this.menus.find(menu => menu.id === id)));
      // 如果是课程服务类型，确保URL是固定的
      if (this.currentMenu && this.currentMenu.menu_type === 4) {
        this.currentMenu.url = this.url;
      }
    },
    
    toggleSubMenu(id) {
      // 如果点击的是当前激活的菜单，则关闭子菜单
      if (this.activeMenuId === id) {
        this.activeMenuId = null;
        this.currentMenu = null;
      } else {
        // 否则激活该菜单
        this.activeMenuId = id;
        this.currentMenu = JSON.parse(JSON.stringify(this.menus.find(menu => menu.id === id)));
      }
    },
    
    handleOutsideClick(event) {
      // 如果点击的不是菜单项或子菜单项，则关闭子菜单
      const menuContainer = document.querySelector('.menu-preview');
      if (menuContainer && !menuContainer.contains(event.target)) {
        this.activeMenuId = null;
      }
    },
    
    addTopMenu() {
      if (this.topMenus.length >= 3) {
        this.$message.warning('一级菜单最多3个');
        return;
      }
      
      // 计算合适的排序值
      const siblings = this.topMenus;
      const maxSortOrder = siblings.length > 0 
        ? Math.max(...siblings.map(m => m.sort_order || 0)) 
        : -1;
        
      const newMenu = {
        id: this.menuIdCounter++,
        school_id: this.schoolId,
        parent_id: 0,
        name: '新菜单',
        menu_type: 1,
        url: '',
        menu_key: '',
        appid: '',
        pagepath: '',
        sort_order: maxSortOrder + 10, // 使用比最大值大10的值，便于后续插入
        status: 1
      };
      
      // 如果是课程服务类型，设置固定URL
      if (newMenu.menu_type === 4) {
        newMenu.url = this.url;
      }
      
      this.menus.push(newMenu);
      this.selectMenu(newMenu.id);
    },
    
    addSubMenu(parentMenu) {
      const subMenus = this.getSubMenus(parentMenu.id);
      if (subMenus.length >= 5) {
        this.$message.warning('二级菜单最多5个');
        return;
      }
      
      // 计算合适的排序值
      const maxSortOrder = subMenus.length > 0 
        ? Math.max(...subMenus.map(m => m.sort_order || 0)) 
        : -1;
        
      const newMenu = {
        id: this.menuIdCounter++,
        school_id: this.schoolId,
        parent_id: parentMenu.id,
        name: '新子菜单',
        menu_type: 1,
        url: '',
        menu_key: '',
        appid: '',
        pagepath: '',
        sort_order: maxSortOrder + 10, // 使用比最大值大10的值，便于后续插入
        status: 1
      };
      
      // 如果是课程服务类型，设置固定URL
      if (newMenu.menu_type === 4) {
        newMenu.url = this.url;
      }
      
      this.menus.push(newMenu);
      this.selectMenu(newMenu.id);
    },
    

    
    moveUp(menu) {
      // 获取同级菜单
      const siblings = menu.parent_id === 0 
        ? this.menus.filter(m => m.parent_id === 0)
        : this.menus.filter(m => m.parent_id === menu.parent_id);
        
      // 按sort_order排序
      const sortedSiblings = [...siblings].sort((a, b) => a.sort_order - b.sort_order);
      
      // 找到当前菜单的索引
      const currentIndex = sortedSiblings.findIndex(m => m.id === menu.id);
      
      // 如果是第一个，不做任何操作
      if (currentIndex === 0) {
        this.$message.info('已经是第一个菜单');
        return;
      }
      
      // 与前一个交换顺序
      const prevMenu = sortedSiblings[currentIndex - 1];
      const currentSort = menu.sort_order;
      
      // 交换sort_order
      menu.sort_order = prevMenu.sort_order;
      prevMenu.sort_order = currentSort;
      
      // 显示加载状态
      this.loading = true;
      
      // 更新第一个菜单数据
      this.$API.common.util.post('/buss/wechat/updateSort', {
        id: menu.id,
        school_id: this.schoolId,
        sort_order: menu.sort_order
      }).then(res => {
        if (res.errno !== 0) {
          this.$message.error('保存菜单顺序失败');
          return Promise.reject(new Error('保存菜单顺序失败'));
        }
        
        // 更新第二个菜单数据
        return this.$API.common.util.post('/buss/wechat/updateSort', {
          id: prevMenu.id,
          school_id: this.schoolId,
          sort_order: prevMenu.sort_order
        });
      }).then(res => {
        if (res && res.errno !== 0) {
          this.$message.error('保存菜单顺序失败');
          return Promise.reject(new Error('保存菜单顺序失败'));
        }
        
        // 两个菜单都更新成功后，重新加载数据
        return this.loadMenus();
      }).then(() => {
        // 数据加载完成后，重新选中当前菜单
        this.$nextTick(() => {
          if (this.$refs.menuTree) {
            this.$refs.menuTree.setCurrentKey(menu.id);
          }
        });
      }).catch(err => {
        this.$message.error('保存菜单顺序失败: ' + (err.message || '未知错误'));
      }).finally(() => {
        // 确保无论成功失败都关闭加载状态
        this.loading = false;
      });
    },
    
    moveDown(menu) {
      // 获取同级菜单
      const siblings = menu.parent_id === 0 
        ? this.menus.filter(m => m.parent_id === 0)
        : this.menus.filter(m => m.parent_id === menu.parent_id);
        
      // 按sort_order排序
      const sortedSiblings = [...siblings].sort((a, b) => a.sort_order - b.sort_order);
      
      // 找到当前菜单的索引
      const currentIndex = sortedSiblings.findIndex(m => m.id === menu.id);
      
      // 如果是最后一个，不做任何操作
      if (currentIndex === sortedSiblings.length - 1) {
        this.$message.info('已经是最后一个菜单');
        return;
      }
      
      // 与后一个交换顺序
      const nextMenu = sortedSiblings[currentIndex + 1];
      const currentSort = menu.sort_order;
      
      // 交换sort_order
      menu.sort_order = nextMenu.sort_order;
      nextMenu.sort_order = currentSort;
      
      // 显示加载状态
      this.loading = true;
      
      // 更新第一个菜单数据
      this.$API.common.util.post('/buss/wechat/updateSort', {
        id: menu.id,
        school_id: this.schoolId,
        sort_order: menu.sort_order
      }).then(res => {
        if (res.errno !== 0) {
          this.$message.error('保存菜单顺序失败');
          return Promise.reject(new Error('保存菜单顺序失败'));
        }
        
        // 更新第二个菜单数据
        return this.$API.common.util.post('/buss/wechat/updateSort', {
          id: nextMenu.id,
          school_id: this.schoolId,
          sort_order: nextMenu.sort_order
        });
      }).then(res => {
        if (res && res.errno !== 0) {
          this.$message.error('保存菜单顺序失败');
          return Promise.reject(new Error('保存菜单顺序失败'));
        }
        
        // 两个菜单都更新成功后，重新加载数据
        return this.loadMenus();
      }).then(() => {
        // 数据加载完成后，重新选中当前菜单
        this.$nextTick(() => {
          if (this.$refs.menuTree) {
            this.$refs.menuTree.setCurrentKey(menu.id);
          }
        });
      }).catch(err => {
        this.$message.error('保存菜单顺序失败: ' + (err.message || '未知错误'));
      }).finally(() => {
        // 确保无论成功失败都关闭加载状态
        this.loading = false;
      });
    },
    

    
    editMenu(menu) {
      this.selectMenu(menu.id);
    },
    
    async deleteMenu(menu) {
      try {
        // 如果是一级菜单，需要先删除其下的所有子菜单
        if (menu.parent_id === 0) {
          const subMenus = this.getSubMenus(menu.id);
          if (subMenus.length > 0) {
            await this.$confirm('删除此菜单将同时删除其下的所有子菜单，是否继续？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });
          }
        }
        
        try {
          // 调用API删除菜单
          const res = await this.$API.common.util.post('/buss/wechat/delete', {
            id: menu.id,
            school_id: this.schoolId
          });
          
          if (res.errno === 0) {
            // 删除子菜单
            if (menu.parent_id === 0) {
              this.menus = this.menus.filter(item => item.parent_id !== menu.id);
            }
            
            // 删除当前菜单
            this.menus = this.menus.filter(item => item.id !== menu.id);
            
            // 如果正在编辑的是被删除的菜单，清空当前编辑
            if (this.currentMenu && this.currentMenu.id === menu.id) {
              this.currentMenu = null;
              this.activeMenuId = null;
            }
            
            // 重新排序
            if (menu.parent_id === 0) {
              this.topMenus.forEach((item, index) => {
                item.sort_order = index;
              });
            } else {
              const siblings = this.getSubMenus(menu.parent_id);
              siblings.forEach((item, index) => {
                item.sort_order = index;
              });
            }
            
            this.$message.success('删除成功');
          } else {
            this.$message.error('删除失败：' + res.message);
          }
        } catch (error) {
          this.$message.error('删除失败：' + error.message);
        }
      } catch (error) {
        // 用户取消删除
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message);
        }
      }
    },
    
    async deleteAllMenus() {
      try {
        await this.$confirm('确定要清空所有菜单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        try {
          // 调用API批量保存空菜单列表，实现清空效果
          const res = await this.$API.common.util.post('/buss/wechat/batchSave', {
            school_id: this.schoolId,
            menus: []
          });
          
          if (res.errno === 0) {
            this.menus = [];
            this.currentMenu = null;
            this.activeMenuId = null;
            this.$message.success('已清空所有菜单');
          } else {
            this.$message.error('清空菜单失败：' + res.message);
          }
        } catch (error) {
          this.$message.error('清空菜单失败：' + error.message);
        }
      } catch (error) {
        // 用户取消操作
      }
    },
    
    validateMenuName(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入菜单名称'));
        return;
      }
      
      const menu = this.currentMenu;
      if (!menu) {
        callback();
        return;
      }
      
      // 计算菜单名称的字符长度（中文算2个字符）
      const getLength = str => {
        let len = 0;
        for (let i = 0; i < str.length; i++) {
          if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
            len += 2;
          } else {
            len++;
          }
        }
        return len;
      };
      
      const nameLength = getLength(value);
      if (menu.parent_id === 0 && nameLength > 8) {
        callback(new Error('一级菜单名称不能超过4个汉字或8个字符'));
      } else if (menu.parent_id !== 0 && nameLength > 14) {
        callback(new Error('二级菜单名称不能超过7个汉字或14个字符'));
      } else {
        callback();
      }
    },
    
    saveMenu() {
      if (!this.currentMenu) return;
      
      this.$refs.form.validate(async valid => {
        if (!valid) return;
        
        try {
          // 保存到服务器
          const res = await this.$API.common.util.post('/buss/wechat/save', this.currentMenu);
          
          if (res.errno === 0) {
            // 更新本地菜单数据
            const index = this.menus.findIndex(item => item.id === this.currentMenu.id);
            if (index !== -1) {
              this.menus.splice(index, 1, this.currentMenu);
            } else {
              this.menus.push(this.currentMenu);
            }
            
            this.$message.success('保存成功');
          } else {
            this.$message.error('保存失败：' + res.message);
          }
        } catch (error) {
          this.$message.error('保存失败：' + error.message);
        }
      });
    },
    
    handleMenuTypeChange(value) {
      // 如果切换到课程服务类型，自动设置固定URL
      if (value === 4) {
        this.currentMenu.url = this.url;
      }
    },
    
    cancelEdit() {
      this.currentMenu = null;
      this.activeMenuId = null;
    },
    
    forceRefresh() {
      // 创建一个临时变量
      const tempMenus = [...this.menus];
      // 清空菜单数组
      this.menus = [];
      // 在下一个事件循环中恢复菜单数组，强制Vue重新渲染
      this.$nextTick(() => {
        this.menus = tempMenus;
      });
    },
    
    async publishMenu() {
      if (this.topMenus.length === 0) {
        this.$message.warning('没有可发布的菜单');
        return;
      }
      
      // 验证菜单数据是否符合微信规范
      const validationError = this.validateMenusForPublish();
      if (validationError) {
        this.$message.error(validationError);
        return;
      }
      
      try {
        this.publishing = true;
        
        // 构建微信菜单格式
        const menuData = {
          button: this.topMenus
            .sort((a, b) => a.sort_order - b.sort_order)
            .map(menu => {
              const subMenus = this.getSubMenus(menu.id);
              const menuItem = {
                name: menu.name
              };
            
            if (subMenus.length > 0) {
              // 有子菜单，按排序顺序
              menuItem.sub_button = subMenus
                .sort((a, b) => a.sort_order - b.sort_order)
                .map(sub => {
                  // 确保子菜单有名称
                  if (!sub.name) {
                    this.$message.warning(`子菜单缺少名称，已自动设置为"菜单项"`);
                    sub.name = "菜单项";
                  }
                  return this.formatMenuButton(sub);
                });
            } else {
              // 无子菜单，直接设置类型和值
              Object.assign(menuItem, this.formatMenuButton(menu));
            }
            
            return menuItem;
          })
        };
        
        // 发送到服务器
        const res = await this.$API.common.util.post('/buss/wechat/publish', {
          school_id: this.schoolId,
          menu_data: JSON.stringify(menuData)
        });
        
        if (res.errno === 0) {
          this.$message.success('菜单发布成功，24小时内生效');
          this.$emit('success');
        } else {
          this.$message.error('发布失败：' + res.message);
        }
      } catch (error) {
        this.$message.error('发布失败：' + error.message);
      } finally {
        this.publishing = false;
      }
    },
    
    validateMenusForPublish() {
      // 验证一级菜单
      for (const menu of this.topMenus) {
        // 验证菜单名称
        if (!menu.name) {
          return `一级菜单缺少名称`;
        }
        
        // 计算菜单名称长度（中文算2个字符）
        const getLength = str => {
          let len = 0;
          for (let i = 0; i < str.length; i++) {
            if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
              len += 2;
            } else {
              len++;
            }
          }
          return len;
        };
        
        // 验证一级菜单名称长度
        if (getLength(menu.name) > 8) {
          return `一级菜单"${menu.name}"名称过长，不能超过4个汉字或8个字符`;
        }
        
        // 验证子菜单
        const subMenus = this.getSubMenus(menu.id);
        if (subMenus.length > 0) {
          // 有子菜单，验证每个子菜单
          for (const subMenu of subMenus) {
            // 验证子菜单名称
            if (!subMenu.name) {
              return `菜单"${menu.name}"的子菜单缺少名称`;
            }
            
            // 验证子菜单名称长度
            if (getLength(subMenu.name) > 14) {
              return `菜单"${menu.name}"的子菜单"${subMenu.name}"名称过长，不能超过7个汉字或14个字符`;
            }
            
            // 验证子菜单URL
            if (subMenu.menu_type === 1 && !subMenu.url) {
              return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少URL`;
            }
            
            // 验证子菜单KEY
            if (subMenu.menu_type === 2 && !subMenu.menu_key) {
              return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少事件KEY`;
            }
            
            // 验证小程序信息
            if (subMenu.menu_type === 3) {
              if (!subMenu.appid) {
                return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少小程序APPID`;
              }
              if (!subMenu.pagepath) {
                return `菜单"${menu.name}"的子菜单"${subMenu.name}"缺少小程序页面路径`;
              }
            }
          }
        } else {
          // 无子菜单，验证当前菜单
          if (menu.menu_type === 1 && !menu.url) {
            return `菜单"${menu.name}"缺少URL`;
          }
          
          if (menu.menu_type === 2 && !menu.menu_key) {
            return `菜单"${menu.name}"缺少事件KEY`;
          }
          
          if (menu.menu_type === 3) {
            if (!menu.appid) {
              return `菜单"${menu.name}"缺少小程序APPID`;
            }
            if (!menu.pagepath) {
              return `菜单"${menu.name}"缺少小程序页面路径`;
            }
          }
        }
      }
      
      return null; // 验证通过
    },
    
    formatMenuButton(menu) {
      // 确保菜单有名称
      const result = {
        name: menu.name || "菜单项" // 如果没有名称，使用默认名称
      };
      
      switch (menu.menu_type) {
        case 1: // 跳转网页
          result.type = 'view';
          result.url = menu.url;
          break;
        case 2: // 点击事件
          result.type = 'click';
          result.key = menu.menu_key;
          break;
        case 3: // 小程序
          result.type = 'miniprogram';
          result.url = menu.url || 'http://mp.weixin.qq.com'; // 兜底链接
          result.appid = menu.appid;
          result.pagepath = menu.pagepath;
          break;
        case 4: // 课程服务（本质是跳转网页，但URL固定）
          result.type = 'view';
          result.url = this.url;
          break;
      }
      
      return result;
    }
  }
}
</script>

<style scoped>
.menu-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.menu-header {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.tips {
  font-size: 12px;
  color: #909399;
}

.menu-editor {
  display: flex;
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.menu-list {
 
  border-right: 1px solid #dcdfe6;
  padding: 10px;
  overflow: auto;
}

.menu-form, .menu-form-empty {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.menu-form-empty {
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-tip {
  text-align: center;
  color: #909399;
}

.empty-tip i {
  font-size: 48px;
  margin-bottom: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.actions {
  margin-left: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.menu-preview {
  margin-bottom: 20px;
}

.phone-container {
  width: 320px;
  height: 200px;
  margin: 0 auto;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.phone-header {
  height: 40px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #dcdfe6;
}

.phone-content {
  flex: 1;
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}

.submenu-popup {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  border-top: 1px solid #dcdfe6;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  max-height: 180px;
  overflow-y: auto;
}

.submenu-item {
  padding: 10px 15px;
  font-size: 13px;
  cursor: pointer;
  text-align: center;
  border-bottom: 1px solid #ebeef5;
}

.submenu-item:last-child {
  border-bottom: none;
}

.submenu-item:hover, .submenu-item.active {
  background-color: #e6f7ff;
}

.phone-footer {
  height: 50px;
  background-color: #f5f5f5;
  border-top: 1px solid #dcdfe6;
}

.menu-bar {
  height: 100%;
  display: flex;
}

.menu-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #dcdfe6;
  cursor: pointer;
  position: relative;
}

.menu-item:last-child {
  border-right: none;
}

.menu-item.active {
  background-color: #e6f7ff;
}

.menu-name {
  font-size: 12px;
}

.menu-arrow {
  font-size: 10px;
  color: #909399;
  position: absolute;
  bottom: 2px;
  transition: transform 0.3s;
}

.arrow-up {
  transform: rotate(180deg);
}
</style>