<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="姓名" prop="name">
						<el-input v-model="form.name" placeholder="请输入姓名" clearable></el-input>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="营销编号" prop="code">
						<el-input v-model="form.code" placeholder="请输入营销编号" clearable></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		
		<!-- 二维码区域放在表单外部，不受表单禁用影响 -->
		<div v-if="mode === 'show' && form.pic" class="qrcode-section">
			<div class="section-title">二维码</div>
			<div class="qrcode-container">
				<el-image 
					:src="form.pic" 
					style="width: 200px; height: 200px"
					:preview-src-list="[form.pic]"
				></el-image>
				<el-button size="small" type="primary" @click="downloadQrCode" icon="el-icon-download">
					下载二维码
				</el-button>
			</div>
		</div>
		
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增营销人员',
				edit: '编辑营销人员',
				show: '查看营销人员'
			},
			visible: false,
			isSaveing: false,
			
			// 表单数据
			form: {
				id: undefined,
				name: "",
				code: "",
				pic: ""
			},
			
			// 验证规则
			rules: {
				name: [
					{required: true, message: '请输入姓名', trigger: 'blur'},
					{min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
				],
				code: [
					{required: true, message: '请输入营销编号', trigger: 'blur'},
					{min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}
				]
			}
		}
	},
	methods: {
		// 显示对话框
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			if (mode === 'add') {
				this.resetForm();
			}
			return this
		},
		
		// 表单提交方法
		async submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					
					try {
						const res = await this.$API.yx.save.post(this.form);
						
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode);
							this.visible = false;
							this.$message.success("操作成功");
						} else {
							this.$alert(res.message, "提示", {type: 'error'});
						}
					} catch (error) {
						console.error('保存营销人员失败', error);
						this.$alert('保存失败', "提示", {type: 'error'});
					} finally {
						this.isSaveing = false;
					}
				} else {
					return false;
				}
			});
		},
		
		// 表单注入数据
		setData(data) {
			this.form = {
				id: data.id,
				name: data.name,
				code: data.code,
				pic: data.pic
			};
			return this;
		},
		
		// 重置表单
		resetForm() {
			if (this.$refs.dialogForm) {
				this.$refs.dialogForm.resetFields();
			}
			this.form = {
				id: undefined,
				name: "",
				code: "",
				pic: ""
			};
		},
		
		// 下载二维码
		downloadQrCode() {
			if (!this.form.pic) return;
			
			// 创建一个临时的a标签用于下载
			const link = document.createElement('a');
			link.href = this.form.pic;
			link.download = `${this.form.name}_${this.form.code}_二维码.png`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	}
}
</script>

<style scoped>
.qrcode-section {
	margin-top: 20px;
	padding-top: 20px;
	border-top: 1px solid #EBEEF5;
}

.section-title {
	font-size: 14px;
	color: #606266;
	margin-bottom: 10px;
	font-weight: bold;
}

.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10px;
}
</style>