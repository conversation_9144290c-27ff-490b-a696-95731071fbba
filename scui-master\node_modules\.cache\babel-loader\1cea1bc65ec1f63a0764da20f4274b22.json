{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\n\nconst _hoisted_1 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"题干\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_2 = {\n  key: 0,\n  style: {\n    \"margin-bottom\": \"20px\"\n  }\n};\n\nconst _hoisted_3 = /*#__PURE__*/_createElementVNode(\"div\", {\n  style: {\n    \"margin-bottom\": \"5px\",\n    \"font-weight\": \"bold\",\n    \"color\": \"#666\"\n  }\n}, \"原图:\", -1\n/* HOISTED */\n);\n\nconst _hoisted_4 = [\"src\"];\nconst _hoisted_5 = {\n  key: 1\n};\n\nconst _hoisted_6 = /*#__PURE__*/_createElementVNode(\"div\", {\n  style: {\n    \"margin-bottom\": \"5px\",\n    \"font-weight\": \"bold\",\n    \"color\": \"#666\"\n  }\n}, \"裁剪后:\", -1\n/* HOISTED */\n);\n\nconst _hoisted_7 = [\"src\"];\n\nconst _hoisted_8 = /*#__PURE__*/_createElementVNode(\"div\", {\n  style: {\n    \"height\": \"800px\"\n  }\n}, null, -1\n/* HOISTED */\n);\n\nconst _hoisted_9 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"解析\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_10 = /*#__PURE__*/_createElementVNode(\"div\", {\n  class: \"card-header\"\n}, [/*#__PURE__*/_createElementVNode(\"span\", null, \"答案\")], -1\n/* HOISTED */\n);\n\nconst _hoisted_11 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"单选题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_12 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_13 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"多选题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_14 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_15 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"填空题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_16 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_17 = /*#__PURE__*/_createElementVNode(\"span\", {\n  style: {\n    \"font-size\": \"18px\"\n  }\n}, \"解答题\", -1\n/* HOISTED */\n);\n\nconst _hoisted_18 = /*#__PURE__*/_createTextVNode(\"保存并读取下一个\");\n\nconst _hoisted_19 = {\n  style: {\n    \"position\": \"absolute\",\n    \"right\": \"60px\",\n    \"top\": \"42px\"\n  }\n};\n\nconst _hoisted_20 = /*#__PURE__*/_createTextVNode(\"上一题\");\n\nconst _hoisted_21 = /*#__PURE__*/_createTextVNode(\"下一题\");\n\nconst _hoisted_22 = /*#__PURE__*/_createTextVNode(\"取 消\");\n\nconst _hoisted_23 = /*#__PURE__*/_createTextVNode(\"取 消\");\n\nconst _hoisted_24 = /*#__PURE__*/_createTextVNode(\" 保存裁剪 \");\n\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_card = _resolveComponent(\"el-card\");\n\n  const _component_el_col = _resolveComponent(\"el-col\");\n\n  const _component_el_image = _resolveComponent(\"el-image\");\n\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n\n  const _component_el_button = _resolveComponent(\"el-button\");\n\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n\n  const _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n\n  const _component_el_row = _resolveComponent(\"el-row\");\n\n  const _component_el_form = _resolveComponent(\"el-form\");\n\n  const _component_sc_cropper = _resolveComponent(\"sc-cropper\");\n\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n\n  return _openBlock(), _createBlock(_component_el_dialog, {\n    title: $data.titleMap[$data.mode],\n    modelValue: $data.visible,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.visible = $event),\n    fullscreen: \"true\",\n    \"destroy-on-close\": \"\",\n    onClosed: _cache[7] || (_cache[7] = $event => _ctx.$emit('closed'))\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[3] || (_cache[3] = $event => $options.close())\n    }, {\n      default: _withCtx(() => [_hoisted_22]),\n      _: 1\n      /* STABLE */\n\n    })]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $data.form,\n      height: $data.height,\n      rules: $data.rules,\n      ref: \"dialogForm\",\n      \"label-width\": \"80px\",\n      \"label-position\": \"left\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_row, {\n        style: _normalizeStyle($data.rowsytle)\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: _normalizeStyle($data.rowsytle)\n          }, {\n            header: _withCtx(() => [_hoisted_1]),\n            default: _withCtx(() => [_createElementVNode(\"div\", {\n              style: _normalizeStyle({\n                height: $data.height - 300 + 'px',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                border: '1px solid #ddd',\n                padding: '10px',\n                boxSizing: 'border-box'\n              })\n            }, [$data.form.tm_p ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_hoisted_3, _createElementVNode(\"img\", {\n              src: $data.form.tm_p,\n              style: {\n                \"width\": \"100%\",\n                \"height\": \"auto\",\n                \"cursor\": \"pointer\",\n                \"display\": \"block\",\n                \"max-width\": \"100%\"\n              },\n              onClick: _cache[0] || (_cache[0] = (...args) => $options.openCropper && $options.openCropper(...args))\n            }, null, 8\n            /* PROPS */\n            , _hoisted_4)])) : _createCommentVNode(\"v-if\", true), $data.form.tm_p_new ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_hoisted_6, _createElementVNode(\"img\", {\n              src: $data.form.tm_p_new,\n              style: {\n                \"width\": \"100%\",\n                \"height\": \"auto\",\n                \"cursor\": \"pointer\",\n                \"display\": \"block\",\n                \"max-width\": \"100%\"\n              }\n            }, null, 8\n            /* PROPS */\n            , _hoisted_7)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 强制内容高度，确保滚动条显示 \"), _hoisted_8], 4\n            /* STYLE */\n            )]),\n            _: 1\n            /* STABLE */\n\n          }, 8\n          /* PROPS */\n          , [\"style\"])]),\n          _: 1\n          /* STABLE */\n\n        }), _createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: {\n              \"height\": \"100%\"\n            }\n          }, {\n            header: _withCtx(() => [_hoisted_9]),\n            default: _withCtx(() => [_createVNode(_component_el_image, {\n              style: {\n                \"height\": \"100%\"\n              },\n              src: $data.form.jx_p,\n              \"zoom-rate\": 1.2,\n              \"max-scale\": 7,\n              \"min-scale\": 0.2,\n              fit: \"cover\"\n            }, null, 8\n            /* PROPS */\n            , [\"src\", \"zoom-rate\", \"min-scale\"])]),\n            _: 1\n            /* STABLE */\n\n          })]),\n          _: 1\n          /* STABLE */\n\n        }), _createVNode(_component_el_col, {\n          span: 8\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            style: {\n              \"height\": \"100%\"\n            }\n          }, {\n            header: _withCtx(() => [_hoisted_10]),\n            default: _withCtx(() => [_createVNode(_component_el_row, {\n              style: {\n                \"height\": \"33%\"\n              }\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_col, null, {\n                default: _withCtx(() => [_hoisted_11, _createVNode(_component_el_form_item, {\n                  label: \"答案\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n                    modelValue: $data.form.ans,\n                    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.form.ans = $event)\n                  }, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.dxlist, item => {\n                      return _openBlock(), _createBlock(_component_el_radio, {\n                        key: item.value,\n                        label: item.value\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(item.value), 1\n                        /* TEXT */\n                        )]),\n                        _: 2\n                        /* DYNAMIC */\n\n                      }, 1032\n                      /* PROPS, DYNAMIC_SLOTS */\n                      , [\"label\"]);\n                    }), 128\n                    /* KEYED_FRAGMENT */\n                    ))]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"modelValue\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-plus\",\n                    onClick: $options.dxadd,\n                    circle: \"\"\n                  }, null, 8\n                  /* PROPS */\n                  , [\"onClick\"]), _createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savedx2\n                  }, {\n                    default: _withCtx(() => [_hoisted_12]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_13, _createVNode(_component_el_form_item, {\n                  label: \"答案\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_checkbox_group, {\n                    modelValue: $data.form.ans2,\n                    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.form.ans2 = $event)\n                  }, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.duoxuanlist, item => {\n                      return _openBlock(), _createBlock(_component_el_checkbox, {\n                        key: item.value,\n                        label: item.value\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(item.value), 1\n                        /* TEXT */\n                        )]),\n                        _: 2\n                        /* DYNAMIC */\n\n                      }, 1032\n                      /* PROPS, DYNAMIC_SLOTS */\n                      , [\"label\"]);\n                    }), 128\n                    /* KEYED_FRAGMENT */\n                    ))]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"modelValue\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-plus\",\n                    onClick: _ctx.duoxuanadd,\n                    circle: \"\"\n                  }, null, 8\n                  /* PROPS */\n                  , [\"onClick\"]), _createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.saveduoxuan2\n                  }, {\n                    default: _withCtx(() => [_hoisted_14]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_15, _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savetk\n                  }, {\n                    default: _withCtx(() => [_hoisted_16]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                }), _createVNode(_component_el_divider), _hoisted_17, _createVNode(_component_el_form_item, {\n                  label: \"\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_el_button, {\n                    icon: \"el-icon-check\",\n                    onClick: $options.savejd2\n                  }, {\n                    default: _withCtx(() => [_hoisted_18]),\n                    _: 1\n                    /* STABLE */\n\n                  }, 8\n                  /* PROPS */\n                  , [\"onClick\"])]),\n                  _: 1\n                  /* STABLE */\n\n                })]),\n                _: 1\n                /* STABLE */\n\n              })]),\n              _: 1\n              /* STABLE */\n\n            })]),\n            _: 1\n            /* STABLE */\n\n          })]),\n          _: 1\n          /* STABLE */\n\n        })]),\n        _: 1\n        /* STABLE */\n\n      }, 8\n      /* PROPS */\n      , [\"style\"])]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"model\", \"height\", \"rules\"]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_button, {\n      icon: \"el-icon-back\",\n      onClick: $options.before\n    }, {\n      default: _withCtx(() => [_hoisted_20]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"onClick\"]), _createVNode(_component_el_button, {\n      icon: \"el-icon-right\",\n      onClick: $options.next\n    }, {\n      default: _withCtx(() => [_hoisted_21]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"onClick\"])]), _createVNode(_component_el_dialog, {\n      title: \"题干图片裁剪\",\n      modelValue: $data.cropperVisible,\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.cropperVisible = $event),\n      width: \"600px\",\n      \"destroy-on-close\": \"\",\n      \"append-to-body\": \"\"\n    }, {\n      footer: _withCtx(() => [_createVNode(_component_el_button, {\n        onClick: _cache[4] || (_cache[4] = $event => $data.cropperVisible = false)\n      }, {\n        default: _withCtx(() => [_hoisted_23]),\n        _: 1\n        /* STABLE */\n\n      }), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $options.saveCroppedImage,\n        loading: $data.uploadLoading\n      }, {\n        default: _withCtx(() => [_hoisted_24]),\n        _: 1\n        /* STABLE */\n\n      }, 8\n      /* PROPS */\n      , [\"onClick\", \"loading\"])]),\n      default: _withCtx(() => [$data.cropperVisible ? (_openBlock(), _createBlock(_component_sc_cropper, {\n        key: 0,\n        src: $data.cropperImageSrc,\n        compress: 0.8,\n        aspectRatio: NaN,\n        ref: \"cropper\"\n      }, null, 8\n      /* PROPS */\n      , [\"src\", \"compress\"])) : _createCommentVNode(\"v-if\", true)]),\n      _: 1\n      /* STABLE */\n\n    }, 8\n    /* PROPS */\n    , [\"modelValue\"])]),\n    _: 1\n    /* STABLE */\n\n  }, 8\n  /* PROPS */\n  , [\"title\", \"modelValue\"]);\n}", "map": {"version": 3, "mappings": ";;gCAoBcA,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;;;EAKKE,KAA4B,EAA5B;IAAA;EAAA;;;gCACHF,oBAA0E,KAA1E,EAA0E;EAArEE,KAA2D,EAA3D;IAAA;IAAA;IAAA;EAAA;AAAqE,CAA1E,EAAiE,KAAjE,EAAoE;AAAA;AAApE;;;;;;;gCASAF,oBAA2E,KAA3E,EAA2E;EAAtEE,KAA2D,EAA3D;IAAA;IAAA;IAAA;EAAA;AAAsE,CAA3E,EAAiE,MAAjE,EAAqE;AAAA;AAArE;;;;gCAQFF,oBAAkC,KAAlC,EAAkC;EAA7BE,KAAsB,EAAtB;IAAA;EAAA;AAA6B,CAAlC,EAA2B,IAA3B,EAA2B;AAAA;AAA3B;;gCAOAF,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;iCAiBAA,oBAEM,KAFN,EAEM;EAFDC,KAAK,EAAC;AAEL,CAFN,EAAwB,cACtBD,oBAAe,MAAf,EAAe,IAAf,EAAM,IAAN,CADsB,CAAxB;;AAAA;;iCAOGA,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAiBM;;iCAQNF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAkBM;;iCAKVF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAGQ;;iCAIRF,oBAAuC,MAAvC,EAAuC;EAAjCE,KAAsB,EAAtB;IAAA;EAAA;AAAiC,CAAvC,EAA6B,KAA7B,EAAgC;AAAA;AAAhC;;kDAMQ;;;EAcXA,KAEK,EAFL;IAAA;IAAA;IAAA;EAAA;;;kDAIa;;kDAGA;;kDAIW;;kDAmBiB;;kDACkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnLnFC,aAwLYC,oBAxLZ,EAwLY;IAvLTC,KAAK,EAAEC,eAASA,UAAT,CAuLE;gBAtLDA,aAsLC;+DAtLDA,gBAAOC,OAsLN;IArLVC,UAAU,EAAC,MAqLD;IApLV,sBAoLU;IAnLTC,QAAM,sCAAEC,WAAK,QAAL,CAAF;EAmLG,CAxLZ;IA8JaC,MAAM,WACf,MAA2C,CAA3CC,aAA2CC,oBAA3C,EAA2C;MAA/BC,OAAK,sCAAEC,gBAAF;IAA0B,CAA3C;wBAA4B,MAAG;;;;KAA/B,CAA2C,CAD5B;sBAvJjB,MA2IU,CA3IVH,aA2IUI,kBA3IV,EA2IU;MA1IPC,KAAK,EAAEX,UA0IA;MAzIPY,MAAM,EAAEZ,YAyID;MAxIPa,KAAK,EAAEb,WAwIA;MAvIRc,GAAG,EAAC,YAuII;MAtIR,eAAY,MAsIJ;MArIR,kBAAe;IAqIP,CA3IV;wBAQE,MAkIS,CAlITR,aAkISS,iBAlIT,EAkIS;QAlIAnB,KAAK,kBAAEI,cAAF;MAkIL,CAlIT;0BACE,MA6BS,CA7BTM,aA6BSU,iBA7BT,EA6BS;UA7BAC,IAAI,EAAE;QA6BN,CA7BT,EAAgB;4BACd,MA2BU,CA3BVX,aA2BUY,kBA3BV,EA2BU;YA3BAtB,KAAK,kBAAEI,cAAF;UA2BL,CA3BV;YACamB,MAAM,WACf,MAEM,CAFNC,UAEM,CAHS;8BAKjB,MAoBM,CApBN1B,oBAoBM,KApBN,EAoBM;cApBAE,KAAK;gBAAAgB,QAAYZ,eAAM,GAAlB,GAAkB,IAAlB;gBAAkBqB,iBAAlB;gBAAkBC,mBAAlB;gBAAkBC,wBAAlB;gBAAkBC,eAAlB;gBAAkBC;cAAlB;YAoBL,CApBN,GAC0CzB,WAAK0B,sBAA7CC,oBAOM,KAPN,cAOM,CANJC,UAMI,EALJlC,oBAIE,KAJF,EAIE;cAHCmC,GAAG,EAAE7B,WAAK0B,IAGX;cAFA9B,KAAoF,EAApF;gBAAA;gBAAA;gBAAA;gBAAA;gBAAA;cAAA,CAEA;cADCY,OAAK,yCAAEC,qDAAF;YACN,CAJF;;YAAA,aAKI,CAPN,wCASWT,WAAK8B,0BAAhBH,oBAMM,KANN,EAMMI,UANN,EAMM,CALJC,UAKI,EAJJtC,oBAGE,KAHF,EAGE;cAFCmC,GAAG,EAAE7B,WAAK8B,QAEX;cADAlC,KAAoF,EAApF;gBAAA;gBAAA;gBAAA;gBAAA;gBAAA;cAAA;YACA,CAHF;;YAAA,aAII,CANN,wCAQAqC,yCACAC,WAnBF;;YAAA,CAoBM;;;;WA1BR;;UAAA,YA2BU,EA5BI;;;;QAAA,CAAhB,CA6BS,EACT5B,aAgBSU,iBAhBT,EAgBS;UAhBAC,IAAI,EAAE;QAgBN,CAhBT,EAAgB;4BACd,MAcU,CAdVX,aAcUY,kBAdV,EAcU;YAdDtB,KAAoB,EAApB;cAAA;YAAA;UAcC,CAdV,EAA6B;YAChBuB,MAAM,WACf,MAEM,CAFNgB,UAEM,CAHS,CADU;8BAM3B,MAOE,CAPF7B,aAOE8B,mBAPF,EAOE;cANAxC,KAAoB,EAApB;gBAAA;cAAA,CAMA;cALCiC,GAAG,EAAE7B,WAAKqC,IAKX;cAJC,aAAW,GAIZ;cAHC,aAAW,CAGZ;cAFC,aAAW,GAEZ;cADAC,GAAG,EAAC;YACJ,CAPF;;YAAA,oCAOE,EAbyB;;;;UAAA,CAA7B,CAcU,EAfI;;;;QAAA,CAAhB,CADS,EAkBThC,aAiFSU,iBAjFT,EAiFS;UAjFAC,IAAI,EAAE;QAiFN,CAjFT,EAAgB;4BACd,MA+EU,CA/EVX,aA+EUY,kBA/EV,EA+EU;YA/EDtB,KAAoB,EAApB;cAAA;YAAA;UA+EC,CA/EV,EAA6B;YAChBuB,MAAM,WACf,MAEM,CAFNoB,WAEM,CAHS,CADU;8BAM3B,MAoES,CApETjC,aAoESS,iBApET,EAoES;cApEDnB,KAAmB,EAAnB;gBAAA;cAAA;YAoEC,CApET,EAA2B;gCACzB,MAkES,CAlETU,aAkESU,iBAlET,EAkES,IAlET,EAkES;kCAhEN,MAAuC,CAAvCwB,WAAuC,EAExClC,aAUiBmC,uBAVjB,EAUiB;kBAVHC,KAAK,EAAC;gBAUH,CAVjB,EAAwB;oCACpB,MAQiB,CARjBpC,aAQiBqC,yBARjB,EAQiB;gCARQ3C,WAAK4C,GAQb;+EARQ5C,WAAK4C,MAAG3C;kBAQhB,CARjB;sCAEI,MAAsB,oBADxB0B,oBAMWkB,SANX,EAMW,IANX,EAMWC,YALM9C,YAKN,EALF+C,IAAc,IAAV;2CADblD,aAMWmD,mBANX,EAMW;wBAJRC,GAAG,EAAEF,IAAI,CAACG,KAIF;wBAHRR,KAAK,EAAEK,IAAI,CAACG;sBAGJ,CANX;0CAKE,MAAgB,mCAAbH,IAAI,CAACG,QAAK;wBAAA;yBAAG;;;;uBALlB;;sBAAA;qBAMW,CANX;;oBAAA,CACwB;;;;mBAF1B;;kBAAA,iBAQiB,EATG;;;;gBAAA,CAAxB,CAFwC,EAatC5C,aAMemC,uBANf,EAMe;kBANDC,KAAK,EAAC;gBAML,CANf,EAAsB;oCACpB,MAAuD,CAAvDpC,aAAuDC,oBAAvD,EAAuD;oBAA5C4C,IAAI,EAAC,cAAuC;oBAAvB3C,OAAK,EAAEC,cAAgB;oBAAT2C,MAAM,EAAN;kBAAS,CAAvD;;kBAAA,cAAuD,EAEvD9C,aAECC,oBAFD,EAEC;oBAFU4C,IAAI,EAAC,eAEf;oBAFgC3C,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAFuD,EADnC;;;;gBAAA,CAAtB,CAbsC,EAsBtCH,aAAc+C,qBAAd,CAtBsC,EAyBvCC,WAzBuC,EA4BtChD,aAUemC,uBAVf,EAUe;kBAVDC,KAAK,EAAC;gBAUL,CAVf,EAAwB;oCACtB,MAQoB,CARpBpC,aAQoBiD,4BARpB,EAQoB;gCARQvD,WAAKwD,IAQb;+EARQxD,WAAKwD,OAAIvD;kBAQjB,CARpB;sCAEI,MAA2B,oBAD7B0B,oBAMckB,SANd,EAMc,IANd,EAMcC,YALG9C,iBAKH,EALL+C,IAAmB,IAAf;2CADblD,aAMc4D,sBANd,EAMc;wBAJXR,GAAG,EAAEF,IAAI,CAACG,KAIC;wBAHXR,KAAK,EAAEK,IAAI,CAACG;sBAGD,CANd;0CAKE,MAAgB,mCAAbH,IAAI,CAACG,QAAK;wBAAA;yBAAG;;;;uBALlB;;sBAAA;qBAMc,CANd;;oBAAA,CAC6B;;;;mBAF/B;;kBAAA,iBAQoB,EATE;;;;gBAAA,CAAxB,CA5BsC,EAuCtC5C,aAMemC,uBANf,EAMe;kBANDC,KAAK,EAAC;gBAML,CANf,EAAsB;oCACpB,MAA4D,CAA5DpC,aAA4DC,oBAA5D,EAA4D;oBAAjD4C,IAAI,EAAC,cAA4C;oBAA5B3C,OAAK,EAAEJ,eAAqB;oBAATgD,MAAM,EAAN;kBAAS,CAA5D;;kBAAA,cAA4D,EAE5D9C,aAECC,oBAFD,EAEC;oBAFU4C,IAAI,EAAC,eAEf;oBAFgC3C,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAF4D,EADxC;;;;gBAAA,CAAtB,CAvCsC,EA+CtDH,aAAc+C,qBAAd,CA/CsD,EAgD3CK,WAhD2C,EAiDxCpD,aAIemC,uBAJf,EAIe;kBAJDC,KAAK,EAAC;gBAIL,CAJf,EAAsB;oCACpB,MAEC,CAFDpC,aAECC,oBAFD,EAEC;oBAFU4C,IAAI,EAAC,eAEf;oBAFgC3C,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAEC,EAHmB;;;;gBAAA,CAAtB,CAjDwC,EAsDtDH,aAAc+C,qBAAd,CAtDsD,EAuD3CM,WAvD2C,EA0DxCrD,aAKemC,uBALf,EAKe;kBALDC,KAAK,EAAC;gBAKL,CALf,EAAsB;oCAEpB,MAEC,CAFDpC,aAECC,oBAFD,EAEC;oBAFU4C,IAAI,EAAC,eAEf;oBAFgC3C,OAAK,EAAEC;kBAEvC,CAFD;sCACG,MAAQ;;;;mBADX;;kBAAA,cAEC,EAJmB;;;;gBAAA,CAAtB,CA1DwC,EAgEjC;;;;cAAA,CAlET,CAkES,EAnEgB;;;;YAAA,CAA3B,CAoES,EA1EkB;;;;UAAA,CAA7B,CA+EU,EAhFI;;;;QAAA,CAAhB,CAlBS;;;;OA9BX;;MAAA,YAkIS;;;;KA1IX;;IAAA,+BA2IU,EAEVf,oBASM,KATN,eASM,CANAY,aAEWC,oBAFX,EAEW;MAFA4C,IAAI,EAAC,cAEL;MAFqB3C,OAAK,EAAEC;IAE5B,CAFX;wBACa,MAAG;;;;KADhB;;IAAA,cAMA,EAHYH,aAEDC,oBAFC,EAED;MAFY4C,IAAI,EAAC,eAEjB;MAFkC3C,OAAK,EAAEC;IAEzC,CAFC;wBACC,MAAG;;;;KADJ;;IAAA,cAGZ,CATN,CAFU,EAiBVH,aAoBYR,oBApBZ,EAoBY;MAnBVC,KAAK,EAAC,QAmBI;kBAlBDC,oBAkBC;iEAlBDA,uBAAcC,OAkBb;MAjBV2D,KAAK,EAAC,OAiBI;MAhBV,sBAgBU;MAfV;IAeU,CApBZ;MAcavD,MAAM,WACf,MAA0D,CAA1DC,aAA0DC,oBAA1D,EAA0D;QAA9CC,OAAK,sCAAER,uBAAc,KAAhB;MAAyC,CAA1D;0BAA2C,MAAG;;;;OAA9C,CAA0D,EAC1DM,aAEYC,oBAFZ,EAEY;QAFDsD,IAAI,EAAC,SAEJ;QAFerD,OAAK,EAAEC,yBAEtB;QAFyCqD,OAAO,EAAE9D;MAElD,CAFZ;0BAA6E,MAE7E;;;;OAFA;;MAAA,yBAD0D,CAD3C;wBAPjB,MAME,CADMA,sCALRH,aAMEkE,qBANF,EAME;cAAA;QALClC,GAAG,EAAE7B,qBAKN;QAJCgE,QAAQ,EAAE,GAIX;QAHCC,WAAW,EAAEC,GAGd;QAFApD,GAAG,EAAC;MAEJ,CANF;;MAAA,2DAME;;;;KAbJ;;IAAA,iBAjBU;;;;GAlJZ;;EAAA", "names": ["_createElementVNode", "class", "style", "_createBlock", "_component_el_dialog", "title", "$data", "$event", "fullscreen", "onClosed", "_ctx", "footer", "_createVNode", "_component_el_button", "onClick", "$options", "_component_el_form", "model", "height", "rules", "ref", "_component_el_row", "_component_el_col", "span", "_component_el_card", "header", "_hoisted_1", "overflowY", "overflowX", "border", "padding", "boxSizing", "tm_p", "_createElementBlock", "_hoisted_3", "src", "tm_p_new", "_hoisted_5", "_hoisted_6", "_createCommentVNode", "_hoisted_8", "_hoisted_9", "_component_el_image", "jx_p", "fit", "_hoisted_10", "_hoisted_11", "_component_el_form_item", "label", "_component_el_radio_group", "ans", "_Fragment", "_renderList", "item", "_component_el_radio", "key", "value", "icon", "circle", "_component_el_divider", "_hoisted_13", "_component_el_checkbox_group", "ans2", "_component_el_checkbox", "_hoisted_15", "_hoisted_17", "width", "type", "loading", "_component_sc_cropper", "compress", "aspectRatio", "NaN"], "sourceRoot": "", "sources": ["C:\\jsjy\\jsjy\\scui-master\\src\\views\\buss\\tiku\\save.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"titleMap[mode]\"\n    v-model=\"visible\"\n    fullscreen=\"true\"\n    destroy-on-close\n    @closed=\"$emit('closed')\"\n  >\n    <el-form\n      :model=\"form\"\n      :height=\"height\"\n      :rules=\"rules\"\n      ref=\"dialogForm\"\n      label-width=\"80px\"\n      label-position=\"left\"\n    >\n      <el-row :style=\"rowsytle\">\n        <el-col :span=\"8\">\n          <el-card :style=\"rowsytle\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>题干</span>\n              </div>\n            </template>\n            <div :style=\"{height: (height - 300) + 'px', overflowY: 'auto', overflowX: 'hidden', border: '1px solid #ddd', padding: '10px', boxSizing: 'border-box'}\">\n              <div style=\"margin-bottom: 20px;\" v-if=\"form.tm_p\">\n                <div style=\"margin-bottom: 5px; font-weight: bold; color: #666;\">原图:</div>\n                <img\n                  :src=\"form.tm_p\"\n                  style=\"width: 100%; height: auto; cursor: pointer; display: block; max-width: 100%;\"\n                  @click=\"openCropper\"\n                />\n              </div>\n\n              <div v-if=\"form.tm_p_new\">\n                <div style=\"margin-bottom: 5px; font-weight: bold; color: #666;\">裁剪后:</div>\n                <img\n                  :src=\"form.tm_p_new\"\n                  style=\"width: 100%; height: auto; cursor: pointer; display: block; max-width: 100%;\"\n                />\n              </div>\n\n              <!-- 强制内容高度，确保滚动条显示 -->\n              <div style=\"height: 800px;\"></div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>解析</span>\n              </div>\n            </template>\n            <el-image\n              style=\"height: 100%\"\n              :src=\"form.jx_p\"\n              :zoom-rate=\"1.2\"\n              :max-scale=\"7\"\n              :min-scale=\"0.2\"\n              fit=\"cover\"\n            />\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card style=\"height: 100%\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>答案</span>\n              </div>\n            </template>\n            <el-row style=\"height: 33%\">\n              <el-col>\n\n                 <span style=\"font-size:18px\">单选题</span>\n\n                <el-form-item label=\"答案\">\n                    <el-radio-group v-model=\"form.ans\">\n                      <el-radio\n                        v-for=\"item in dxlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-radio>\n                    </el-radio-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"dxadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"savedx2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n\n                  <el-divider />\n\n\n                 <span style=\"font-size:18px\">多选题</span>\n\n                \n                  <el-form-item label=\"答案\">\n                    <el-checkbox-group v-model=\"form.ans2\">\n                      <el-checkbox\n                        v-for=\"item in duoxuanlist\"\n                        :key=\"item.value\"\n                        :label=\"item.value\"\n                      >\n                        {{ item.value }}\n                      </el-checkbox>\n                    </el-checkbox-group>\n                  </el-form-item>\n                  <el-form-item label=\"\">\n                    <el-button icon=\"el-icon-plus\" @click=\"duoxuanadd\" circle />\n\n                    <el-button icon=\"el-icon-check\" @click=\"saveduoxuan2\"\n                      >保存并读取下一个</el-button\n                    >\n                  </el-form-item>\n\n  <el-divider />\n             <span style=\"font-size:18px\">填空题</span>\n                <el-form-item label=\"\">\n                  <el-button icon=\"el-icon-check\" @click=\"savetk\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n  <el-divider />\n             <span style=\"font-size:18px\">解答题</span>\n\n        \n                <el-form-item label=\"\">\n                 \n                  <el-button icon=\"el-icon-check\" @click=\"savejd2\"\n                    >保存并读取下一个</el-button\n                  >\n                </el-form-item>\n              </el-col>\n            </el-row>\n\n         \n\n        \n          </el-card>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <div  style=\"    position: absolute;\n    right: 60px;\n    top: 42px;\">\n          <el-button icon=\"el-icon-back\" @click=\"before\"\n                      >上一题</el-button\n                    >\n                      <el-button icon=\"el-icon-right\" @click=\"next\"\n                      >下一题</el-button\n                    >\n    </div>\n    <template #footer>\n      <el-button @click=\"close()\">取 消</el-button>\n    </template>\n\n    <!-- 裁剪对话框 -->\n    <el-dialog \n      title=\"题干图片裁剪\" \n      v-model=\"cropperVisible\" \n      width=\"600px\" \n      destroy-on-close\n      append-to-body\n    >\n      <sc-cropper \n        :src=\"cropperImageSrc\" \n        :compress=\"0.8\" \n        :aspectRatio=\"NaN\"\n        ref=\"cropper\"\n        v-if=\"cropperVisible\"\n      />\n      <template #footer>\n        <el-button @click=\"cropperVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveCroppedImage\" :loading=\"uploadLoading\">\n          保存裁剪\n        </el-button>\n      </template>\n    </el-dialog>\n  </el-dialog>\n</template>\n\n\n\n\n<script>\nimport scCropper from '@/components/scCropper'\n\nexport default {\n  emits: [\"success\", \"closed\"],\n  components: {\n    scCropper\n  },\n  data() {\n    return {\n      array: [1],\n      height: \"\",\n      rowsytle: {\n        height: \"\",\n      },\n      mode: \"add\",\n      titleMap: {\n        add: \"上传\",\n        edit: \"编辑\",\n      },\n      url: \"/static/upload/26b6ea56-7049-425a-a659-64defef541bf/t4.png\",\n      btnloading: false,\n      visible: false,\n      isSaveing: false,\n      menuList: [],\n      fileViewList: [],\n      uploadApi: this.$API.common.upload,\n\n      ifprocess: false,\n      dxlist: [{ value: \"A\" }, { value: \"B\" }, { value: \"C\" }, { value: \"D\" }],\n\n      duoxuanlist: [\n        { value: \"A\" },\n        { value: \"B\" },\n        { value: \"C\" },\n        { value: \"D\" },\n      ],\n\n      cropperVisible: false,\n      cropperImageSrc: '',\n      uploadLoading: false,\n      form: {\n        id: \"\",\n        tm_p: \"\",\n        jx_p: \"\",\n        score: \"\",\n        tm_p_new: \"\",\n        ans: \"\",\n        ans2: [],\n        value: [],\n        lessonid: 0,\n      },\n      rules: {\n        name: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n\n        parent: [{ required: true, trigger: \"blur\", message: \"请选择\" }],\n      },\n      dict: [],\n      dicProps: {\n        value: \"id\",\n        label: \"name\",\n        checkStrictly: true,\n        emitPath: false,\n      },\n      props: {\n        menu: {\n          type: Object,\n          default: () => {},\n        },\n      },\n      selectConfig: {\n        score: {\n          label: \"name\",\n          value: \"name\",\n        },\n      },\n      menuProps: {\n        value: \"id\",\n        emitPath: false,\n        label: \"title\",\n        checkStrictly: true,\n      },\n    };\n  },\n  mounted() {\n    this.getDic();\n    this.getOffice();\n  },\n  created() {\n    this.height = document.documentElement.clientHeight - 180;\n\n    this.rowsytle.height = this.height + \"px\";\n  },\n  methods: {\n    async savedx() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savedx2() {\n      if (!this.$TOOL.isEmpty(this.form.ans)) {\n        await this.$API.tk.savedx.post({\n          id: this.form.id,\n          ans: this.form.ans,\n        });\n        await this.next();\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async saveduoxuan2() {\n      if (!this.$TOOL.isEmpty(this.form.ans2)) {\n        await this.$API.tk.saveduoxuan.post({\n          id: this.form.id,\n          ans: this.form.ans2.join(\",\"),\n        });\n        await  this.next();\n        this.form.ans2 = [];\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请选择正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    async savetk() {\n      await this.$API.tk.savetk.post({ id: this.form.id, ans: \"\" });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n    async savetk2() {\n      if (!this.$TOOL.isEmpty(this.form.value)) {\n        await this.$API.tk.savetk.post({\n          id: this.form.id,\n          ans: JSON.stringify(this.form.value),\n        });\n\n        this.$message.success(\"保存成功\");\n      } else {\n        this.$alert(\"请填写正确答案\", \"提示\", { type: \"error\" });\n      }\n    },\n\n    close() {\n      this.$emit(\"success\", this.form, this.mode);\n      this.visible = false;\n    },\n\n    async savejd() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      console.log(\"====\", this.form.value);\n      this.$message.success(\"保存成功\");\n    },\n    async savejd2() {\n      await this.$API.tk.savejd.post({ id: this.form.id });\n      await  this.next();\n      this.$message.success(\"保存成功\");\n    },\n\n    async getnext() {\n      let next = await this.$API.tk.getnext2.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num\n      });\n      if (next.length == 0) {\n        this.$alert(\"本分类无待处理内容\", \"提示\", { type: \"error\" });\n      } else {\n        this.form.ans = \"\";\n        this.array = [1];\n        this.form.value = [];\n        this.form.id = next[0].id;\n        this.form.tm_p = next[0].tm_p;\n        this.form.jx_p = next[0].jx_p;\n        this.form.num=next[0].num\n      }\n    },\n\nasync before(){\n\n\n         let next = await this.$API.tk.before.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    async next(){\n\n\n         let next = await this.$API.tk.next.get({\n        lessonid: this.form.lessonid,\n        num:this.form.num,\n      });\n      if (next.length == 0) {\n        this.$alert(\"已到最后！\", \"提示\", { type: \"error\" });\n      } else {\n       this.setData(next);\n      }\n    },\n\n    dxadd() {\n      console.log(this.form.ans);\n      // 生成新元素的value值\n      const newValue = String.fromCharCode(65 + this.dxlist.length);\n\n      // 添加新元素\n      this.dxlist.push({ value: newValue });\n    },\n    async process() {\n      this.btnloading = true;\n      let res = await this.$API.tk.process.post({ file: this.form.zip });\n      this.form.buss_id = res.data.buss_id;\n      this.$message.success(\"解析完成\");\n      this.ifprocess = true;\n      this.btnloading = false;\n    },\n    fileSuccess(response) {\n      const suffix = response.data.file_name.substr(\n        response.data.file_name.lastIndexOf(\".\") + 1\n      ); // 文件后缀\n      this.fileViewList.push({\n        suffix: suffix,\n        name: response.data.file_name,\n        url: response.data.src,\n        new_name: response.data.new_name,\n        id: response.data.new_name,\n      });\n      this.$message.success(`文件上传成功`);\n      this.ifprocess = false;\n      return false;\n    },\n    beforeRemove(file) {\n      this.fileViewList.map((r, index) => {\n        if (r.name == file.name) {\n          this.form.files = this.form.files.replace(\n            \"/static/upload/\" + file.name + \",\",\n            \"\"\n          );\n          this.fileViewList.splice(index, 1);\n        }\n      });\n    },\n    async getOffice() {\n      var res = await this.$API.lesson.list.get();\n      this.menuList = res;\n    },\n\n    addtk() {\n      this.array.push(1); //通过添加array的值，增加input的个数\n    },\n    del(index) {\n      this.form.value.splice(index, 1); //先删除form中value对应索引的值\n      this.array.splice(index, 1); //然后删除array对应索引的值，实现点击删除按钮，减少input框效果\n    },\n\n    //显示\n    open(mode = \"add\") {\n      this.mode = mode;\n      this.visible = true;\n      return this;\n    },\n    //获取字典列表\n    async getDic() {\n      var res = await this.$API.dict.list.get();\n      this.dict = res.data;\n    },\n    //表单提交方法\n    submit() {\n      if (this.ifprocess) {\n        this.$refs.dialogForm.validate(async (valid) => {\n          if (valid) {\n            this.isSaveing = true;\n\n            let res = await this.$API.tk.saveimp.post(this.form);\n            this.isSaveing = false;\n            if (res.state == 1) {\n              this.$emit(\"success\", this.form, this.mode);\n              this.visible = false;\n              this.$message.success(\"操作成功\");\n            } else {\n              this.$alert(res.msg, \"提示\", { type: \"error\" });\n            }\n          }\n        });\n      } else {\n        this.$alert(\"请先解析压缩包内容！\", \"提示\", { type: \"error\" });\n      }\n    },\n    //表单注入数据\n    setData(data, mode) {\n      //可以和上面一样单个注入，也可以像下面一样直接合并进去\n      this.titleMap.edit = \"题号：\" + data.no+\"类型：\"+data.type;\n      this.form.ans = \"\";\n      this.form.ans2 = [];\n      this.form.value = [];\n      this.array = [1];\n      Object.assign(this.form, data);\n\n   \n\n      if (data.type == \"单选\") {\n        console.log(data);\n        this.form.ans = data.ans;\n      }\n      if (data.type == \"多选\") {\n        this.form.ans2 = data.ans.split(\",\");\n      }\n    },\n\n    // 打开裁剪器\n    openCropper() {\n      if (this.form.tm_p) {\n        this.cropperImageSrc = this.form.tm_p;\n        this.cropperVisible = true;\n      } else {\n        this.$message.warning('没有题干图片可以裁剪');\n      }\n    },\n\n    // 保存裁剪后的图片\n    async saveCroppedImage() {\n      if (!this.$refs.cropper) {\n        this.$message.error('裁剪器未初始化');\n        return;\n      }\n\n      this.uploadLoading = true;\n      try {\n        // 获取裁剪后的文件\n        this.$refs.cropper.getCropFile(async (file) => {\n          try {\n            // 创建FormData上传\n            const formData = new FormData();\n            formData.append('file', file);\n            \n            // 上传到服务器\n            const response = await this.$API.common.upload.post(formData);\n\n            console.log(response);\n            \n            if (response && response.data && response.data.src) {\n              // 更新题干图片路径\n              this.form.tm_p_new = response.data.src;\n              \n\n  await this.$API.common.util.post('/sys/tk/updatetmnew',{id:this.form.id,tm_p_new:this.form.tm_p_new});\n\n              this.cropperVisible = false;\n            } else {\n              this.$message.error('上传失败，请重试');\n            }\n          } catch (error) {\n            console.error('上传失败:', error);\n            this.$message.error('上传失败: ' + (error.message || '未知错误'));\n          } finally {\n            this.uploadLoading = false;\n          }\n        }, 'cropped_image.jpg', 'image/jpeg');\n      } catch (error) {\n        console.error('裁剪失败:', error);\n        this.$message.error('裁剪失败: ' + (error.message || '未知错误'));\n        this.uploadLoading = false;\n      }\n    },\n  },\n};\n</script>\n\n<style></style>\n"]}, "metadata": {}, "sourceType": "module"}