<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="330" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" ref="dialogForm" label-width="80px" label-position="left">
			<el-form-item label="字典类型" prop="type">
				<el-input v-model="form.type" clearable placeholder="字典类型"></el-input>
			</el-form-item>
			<el-form-item label="字典名称" prop="name">
				<el-input v-model="form.name" clearable placeholder="字典显示名称"></el-input>
			</el-form-item>
			<el-form-item label="父路径" prop="parentId">
				<el-cascader v-model="form.pid" :options="dict" :props="dicProps" :show-all-levels="false"
				             clearable></el-cascader>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增字典',
				edit: '编辑字典'
			},
			visible: false,
			isSaveing: false,
			form: {
				id: "",
				name: "",
				pid: "",
				type: ""
			},
			rules: {
				type: [
					{required: true, message: '请输入字典类型'}
				],
				name: [
					{required: true, message: '请输入字典名称'}
				]
			},
			dict: [],
			dicProps: {
				value: "id",
				label: "name",
				checkStrictly: true,
				emitPath: false
			}
		}
	},
	mounted() {
		this.getDic()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this;
		},
		//获取字典列表
		async getDic() {
			var res = await this.$API.dict.list.get();
			this.dict = res.data;
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					let res;
					if (this.mode == "add") {
						res = await this.$API.dict.add.post(this.form);
					} else {
						res = await this.$API.dict.save.post(this.form);
					}
					this.isSaveing = false;
					if (res.code == 200) {
						await this.$TOOL.dictInit();
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.msg, "提示", {type: 'error'})
					}
				}
			})
		},
		//表单注入数据
		setData(data, mode) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			this.mode = mode;
		}
	}
}
</script>

<style>
</style>
