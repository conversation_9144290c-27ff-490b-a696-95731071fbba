const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {

    async pageAction() {
        let respData = {};
        const page = this.get('page') ? this.get('page') : 1;
        const rows = this.get('pageSize') ? this.get('pageSize') : 20;
        const where = this.get('where') ? JSON.parse(this.get('where')) : {};
        const learnLogModel = this.model('jianyi');
        where['p.del_flag'] = 0;
        
        //let dataScopeWhere = await this.dataScope('p');
    
        const response = await learnLogModel
          .alias('p')
          .field('p.*,u.name AS create_name')
          .join('buss_student u ON p.`userid`=u.`id`')
          .page(page, rows).where(where)
          .order('p.create_date desc').countSelect();
    
        respData = {
          code: 200,
          count: response.count,
          data: response.data,
          message: ''
        };


        for(let i=0;i<respData.data.length;i++){
            respData.data[i].pic = respData.data[i].pic.split(',');
        }

        return this.json(respData);
      }

    async deleteAction() {
        let respData = {};
        const id = this.post('id') ? this.post('id') : null;
        if (think.isEmpty(id)) {
          respData = {
            code: 400,
            data: {},
            message: '缺少必要的参数'
          };
        } else {
          const model = this.model('jianyi');
          await model.where({id: id}).update(await this.deleteData());
          respData = {
            code: 200,
            data: {},
            message: '成功'
          };
        }
        return this.json(respData);
      }

}