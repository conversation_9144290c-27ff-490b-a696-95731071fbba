const BaseRest = require('../rest.js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

module.exports = class extends BaseRest {



  async listselectAction(){
    let respData = {};
   let where = {};
     
    const userInfo = await this.session('userInfo');
            

    if(userInfo.name!="系统管理员"){

      let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

      if(think.isEmpty(schoolres)){
          where["p.school_id"]=0
         
      }else{

          where["p.school_id"]=["in",schoolres.map(item => item.id)];
      }

   
    }

    

    const model = this.model('yx');
    respData = await model.where(where).select();
    return this.json(respData);
  }






  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const yxModel = this.model('yx');
    where['p.del_flag'] = 0;
    const userInfo = await this.session('userInfo');
            

    if(userInfo.name!="系统管理员"){

      let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

      if(think.isEmpty(schoolres)){
          where["p.school_id"]=0
         
      }else{

          where["p.school_id"]=["in",schoolres.map(item => item.id)];
      }

   
    }

    
    const response = await yxModel
      .alias('p')
      .field('p.*,u.name AS create_name,o.name AS office_name')
      .join('sys_user u ON p.`create_by`=u.`id`')
      .join('buss_school  o ON p.`office_id`=o.`id`')
      .page(page, rows).where(where)
      .order('p.create_date desc').countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }

  async infoAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    const model = this.model('yx');
    respData = await model.where({id: id}).find();
    
    return this.json(respData);
  }

  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('yx');
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async listselectAction() {
    let respData = {};
    const model = this.model('yx');
    respData = await model.where({del_flag:"0"}).select();
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const model = this.model('yx');
    const id = this.post('id') ? this.post('id') : null;
    const data = this.post();
    
    let userInfo=await this.session('userInfo');
    let schoolid=userInfo.schoolid;

    let gzhconfig=think.config(schoolid+'_wechat');
    console.log("schoolid======",gzhconfig)

    if(think.isEmpty(gzhconfig)){
      return this.json({
        code: 400,
        state: 0,
        data: {},
        message: '公众号配置不存在'
      });
    }


    // 检查重名/重复编号问题
    const checkResult = await this.checkDuplicate(id, data.name, data.code);
    if (!checkResult.success) {
      return this.json({
        code: 400,
        state: 0,
        data: {},
        message: checkResult.message
      });
    }
    
    // 生成微信公众号场景二维码
    try {
      const qrcodePath = await this.generateWechatQRCode(data.code,gzhconfig);
      data.pic = qrcodePath;
    } catch (error) {
      think.logger.error('生成微信二维码失败:', error);
      return this.json({
        code: 400,
        state: 0,
        data: {},
        message: '生成微信二维码失败: ' + error.message
      });
    }


     
            
           
    data.office_id = userInfo.schoolid;


    if (think.isEmpty(id)) {
      await model.add(await this.addDataNoId(data));
      respData = {
        code: 200,
        state: 1,
        data: {},
        message: '成功'
      };
    } else {
      await model.where({id: id}).update(await this.updateData(data));
      respData = {
        code: 200,
        state: 1,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }
  
  /**
   * 检查用户名和营销编号是否重复
   * @param {number|null} id 当前记录ID（编辑时）
   * @param {string} name 姓名
   * @param {string} code 营销编号
   * @return {Object} 检查结果
   */
  async checkDuplicate(id, name, code) {
    const model = this.model('yx');
    
    // 构建条件
    let whereCode = {
      code: code,
      del_flag: '0'
    };
    let whereName = {
      name: name,
      del_flag: '0'
    };
    
    // 如果是编辑状态，排除当前记录
    if (id) {
      whereCode.id = ['!=', id];
      whereName.id = ['!=', id];
    }
    
    // 检查编号是否重复
    const codeCount = await model.where(whereCode).count();
    if (codeCount > 0) {
      return {
        success: false,
        message: `营销编号 "${code}" 已存在，请更换`
      };
    }
    
    // 检查姓名是否重复
    const nameCount = await model.where(whereName).count();
    if (nameCount > 0) {
      return {
        success: false,
        message: `姓名 "${name}" 已存在，请更换`
      };
    }
    
    return {
      success: true
    };
  }
  
  /**
   * 获取微信公众号接口调用凭证
   * @return {string} 接口调用凭证
   */
  async getWechatAccessToken(gzhconfig) {
    // 获取配置的appid和secret
    const { appid, secret } = gzhconfig;
    
    // 检查是否已有缓存的token
    let accessToken = await this.cache('wechat_access_token');
    
    if (!accessToken) {
      // 从微信服务器获取新的token
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
      
      try {
        const response = await axios.get(url);
        
        if (response.data && response.data.access_token) {
          accessToken = response.data.access_token;
          // 缓存token（微信token有效期为7200秒，这里设置7000秒）
          await this.cache('wechat_access_token', accessToken, {
            timeout: 7000 * 1000
          });
        } else {
          throw new Error('获取access_token失败: ' + JSON.stringify(response.data));
        }
      } catch (error) {
        think.logger.error('获取微信access_token失败:', error);
        throw error;
      }
    }
    
    return accessToken;
  }
  
  /**
   * 生成微信公众号场景二维码
   * @param {string} code 营销编号
   * @return {string} 二维码图片路径
   */
  async generateWechatQRCode(code,gzhconfig) {
    try {
      // 获取接口调用凭证
      const accessToken = await this.getWechatAccessToken(gzhconfig);
      
      // 创建永久二维码
      const createUrl = `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`;
      
      // 场景值可以使用字符串，设置为营销编号
      const postData = {
        action_name: 'QR_LIMIT_STR_SCENE',
        action_info: {
          scene: {
            scene_str: `yx_${code}`
          }
        }
      };
      
      const createResponse = await axios.post(createUrl, postData);
      
      if (!createResponse.data || !createResponse.data.ticket) {
        throw new Error('创建二维码失败: ' + JSON.stringify(createResponse.data));
      }
      
      // 获取二维码图片
      const ticket = createResponse.data.ticket;
      const qrcodeUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(ticket)}`;
      
      // 下载二维码图片
      const imageResponse = await axios.get(qrcodeUrl, { responseType: 'arraybuffer' });
      
      // 确保上传目录存在
      const uploadDir = path.join(think.ROOT_PATH, 'www/static/upload/qrcode');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      // 生成文件名并确保唯一性
      const timestamp = new Date().getTime();
      const fileName = `yx_${code}_${timestamp}.jpg`;
      const filePath = path.join(uploadDir, fileName);
      
      // 保存图片
      fs.writeFileSync(filePath, Buffer.from(imageResponse.data));
      
      // 返回相对路径，用于存储和访问
      return `/static/upload/qrcode/${fileName}`;
    } catch (error) {
      think.logger.error('生成微信二维码失败:', error);
      throw error;
    }
  }
}