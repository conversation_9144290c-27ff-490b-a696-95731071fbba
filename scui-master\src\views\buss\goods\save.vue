<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">

	 	<el-form-item label="科目" prop="lesson">
					<el-cascader
						v-model="form.lesson"
						:options="menuList"
						:props="menuProps"
						:show-all-levels="false"
						 
						:check-strictly="true"
						clearable
					></el-cascader>
					<div class="el-form-item-msg">

					</div>
				</el-form-item>




				<el-col :span="12">
					<el-form-item label="商品名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入商品名称" clearable></el-input>
					</el-form-item>
				</el-col>

                <el-col :span="12">
					<el-form-item label="商品价格" prop="price">
						<el-input-number v-model="form.price" :precision="2" :min="0" :step="0.01" style="width: 100%"></el-input-number>
					</el-form-item>
				</el-col>
            
				<el-col :span="12">
					<el-form-item label="购买时长(天)" prop="duration">
						<el-input-number v-model="form.duration" :min="1" :step="1" style="width: 100%"></el-input-number>
					</el-form-item>
				</el-col>
				
				<el-col :span="12">
					<el-form-item label="商品数量" prop="quantity">
						<el-input-number v-model="form.quantity" :min="0" :step="1" style="width: 100%"></el-input-number>
					</el-form-item>
				</el-col>
				
		 
				
				<el-col :span="12">
					<el-form-item label="商品状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio :label="1">上架</el-radio>
							<el-radio :label="0">下架</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

               <el-col :span="12">
					<el-form-item label="是否需要填写地址" prop="ifaddress">
						<el-radio-group v-model="form.ifaddress">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="0">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				    <el-col :span="12">
					<el-form-item label="是否计算工资" prop="ifjs">
						<el-radio-group v-model="form.ifjs">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="0">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				
				<el-col :span="24">
					<el-form-item label="商品描述" prop="description">
						<el-input type="textarea" v-model="form.description" rows="4" placeholder="请输入商品描述"></el-input>
					</el-form-item>
				</el-col>
				
	 
			</el-row>
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增商品',
				edit: '编辑商品',
				show: '查看商品'
			},
			visible: false,
			isSaveing: false,
			
			// 图片预览相关
			fileList: [],
			menuList: [],
			dialogVisible: false,
			
			// 分类选项
			categoryOptions: [],
			
			// 表单数据
			form: {
				name: "",
				price: 0,
				duration: 1,
				quantity: 0,
				ifaddress:0,
				ifjs:0,
				status: 1,
				lesson:0,
				description: "",
				images: []
			},
			 menuProps: {
				value: "id",
			 
				label: "title",
			 
			 
        emitPath: false,     // 只返回选中节点的值
          leaf: 'leaf',  
			},
			// 验证规则
			rules: {
				name: [
					{required: true, message: '请输入商品名称', trigger: 'blur'},
					{min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur'}
				],
				price: [
					{required: true, message: '请输入商品价格', trigger: 'blur'}
				],
				duration: [
					{required: true, message: '请输入购买时长', trigger: 'blur'}
				],
				quantity: [
					{required: true, message: '请输入商品数量', trigger: 'blur'}
				],

				lesson: [
					{required: true, message: '请选择科目', trigger: 'blur'}
				],
				 
			}
		}
	},
	async mounted() {
	   var res = await this.$API.common.util.post('/buss/lesson/lessonselect2',{});
    this.menuList = res;
	},
	methods: {
		// 显示对话框
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		
	 
		
		
		// 移除图片
		handleRemove(file, fileList) {
			this.fileList = fileList;
			this.form.images = this.fileList.map(item => {
				if (item.response) {
					return item.response.data;
				} else {
					return item.url;
				}
			});
		},
		
		// 文件变更
		handleFileChange(file, fileList) {
			this.fileList = fileList;
		},
		
		// 上传图片
		async uploadImages() {
			if (this.fileList.length === 0) {
				return [];
			}
			
			const uploadPromises = this.fileList.filter(file => !file.url).map(file => {
				const formData = new FormData();
				formData.append('file', file.raw);
				return this.$API.upload.image.post(formData);
			});
			
			if (uploadPromises.length === 0) {
				return this.fileList.map(file => file.url);
			}
			
			try {
				const results = await Promise.all(uploadPromises);
				const uploadedUrls = results.map(res => {
					if (res.code === 200) {
						return res.data;
					}
					return null;
				}).filter(url => url);
				
				const existingUrls = this.fileList.filter(file => file.url).map(file => file.url);
				return [...existingUrls, ...uploadedUrls];
			} catch (error) {
				console.error('上传图片失败', error);
				this.$message.error('上传图片失败');
				return [];
			}
		},
		
		// 表单提交方法
		async submit() {
 	if(this.form.lesson==0){
						this.$message.error("请选择科目");
						return false;
					}

			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;

				

			 

					 
					
					try {
						// 先上传图片
						const imageUrls = await this.uploadImages();
						this.form.images = imageUrls;
						
						// 提交表单
						//const api = this.mode === 'add' ?  : this.$API.product.update;
						const res = await this.$API.product.save.post(this.form);
						
						if (res.code === 200) {
							this.$emit('success', this.form, this.mode);
							this.visible = false;
							this.$message.success("操作成功");
						} else {
							this.$alert(res.message, "提示", {type: 'error'});
						}
					} catch (error) {
						console.error('保存商品失败', error);
						this.$alert('保存商品失败', "提示", {type: 'error'});
					} finally {
						this.isSaveing = false;
					}
				} else {
					return false;
				}
			});
		},
		
		// 表单注入数据
		setData(data) {
			this.form.id = data.id;
			this.form.name = data.name;
			this.form.price = data.price;
			this.form.duration = data.duration;
			this.form.quantity = data.quantity;
			this.form.lesson = data.lesson;
			this.form.status = data.status;
			this.form.ifaddress = data.ifaddress;
			this.form.ifjs = data.ifjs;
			this.form.description = data.description;
			
			// 处理图片
			if (data.images && data.images.length > 0) {
				this.fileList = data.images.map((url, index) => {
					return {
						name: `图片${index + 1}`,
						url: url
					};
				});
				this.form.images = data.images;
			} else {
				this.fileList = [];
				this.form.images = [];
			}
		},
		
		// 重置表单
		resetForm() {
			if (this.$refs.dialogForm) {
				this.$refs.dialogForm.resetFields();
			}
			this.form = {
				name: "",
				price: 0,
				duration: 1,
				quantity: 0,
		 
				status: 1,
				description: "",
				images: []
			};
			this.fileList = [];
		}
	}
}
</script>

<style scoped>
.el-input-number {
	width: 100%;
}

.el-upload {
	width: 100%;
}

.el-upload-list--picture-card .el-upload-list__item {
	width: 100px;
	height: 100px;
}

.el-upload--picture-card {
	width: 100px;
	height: 100px;
	line-height: 100px;
}
</style>
