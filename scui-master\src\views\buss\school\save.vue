<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				 
				<el-col :span="12">
					<el-form-item label="学校名称" prop="name">
						<el-input v-model="form.name" placeholder="学校名称" clearable></el-input>
					</el-form-item>
				</el-col>

                <el-col :span="12">
					<el-form-item label="是否公开" prop="type">
					    <el-switch v-model="form.type" />
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">

		<el-col :span="12">
		<el-form-item label="负责人" prop="master">
							<sc-table-select v-model="form.master" :apiObj="apiObj" :props="props">

								<el-table-column prop="name" label="姓名"></el-table-column>

								

							</sc-table-select>

						</el-form-item>
		</el-col>
<el-col :span="12">


				<el-form-item label="学校简称" prop="shortname">
							<el-input v-model="form.shortname" placeholder="学校简称" clearable></el-input>
							

								
 

						</el-form-item>
		</el-col>
            
		
			</el-row>
			 


			 
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	 
	data() {
		return {
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
			mode: "add",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			menuList: null,
			visible: false,
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true
			},
			isSaveing: false,
		 
			//表单数据
			form: {
			   master:"",
				name: "",
				type: 0,
                remarks:""
				 
			},
			//验证规则
			rules: {
			 
				name: [
					{required: true, message: '请输入学校名称'}
				],
				shortname: [
					{required: true, message: '请输入学校简称'}
				],
			 
			 master: [
					{required: true, message: "请选择", trigger: "blur"}
				],
			
				
				 
			},
			props: {
				label: 'name',
				value: 'id'


			},
			apiObj: this.$API.user.listselect,
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		//加载树数据
		async getGroup() {
			//加载角色树
			var role = await this.$API.role.select.get();
			this.groups = role;
			this.menuList= await this.$API.office.list.get();
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => { 
				console.log(this.form.master)
             if(this.form.master==''||this.form.master.id==null||this.form.master.id==''){
				this.$message.error("请设置负责人")
				return;
			 }

				if (valid) {

 
					this.form['uid'] = this.form.master.id;

					this.isSaveing = true;

                    if(this.form.type){
                    this.form.type=1;
                    }else{
                        this.form.type=0;
                    }

                    

					var res = await this.$API.school.save.post(this.form);
					this.isSaveing = false;
					if (res.code == 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				} else {
					return false;
				}
			})
		},
		//表单注入数据
	async	setData(data) {
			this.form.id = data.id
			this.form=   await this.$API.school.info.post({id:data.id});

		}
	}
}
</script>

<style>
</style>
