

let sql="INSERT INTO `lottery_site`.`sys_column` (`id`, `table_id`, `label`, `prop`, `width`, `sortable`, `fixed`, `no`, `showOverflowTooltip`, `dic`, `custom`, `office_id`, `create_by`, `create_date`, `update_by`, `update_date`, `del_flag`) VALUES (1, 1, '1', '1', '1', 1, 1, 1, 0, '1', 0, '1', '1', '2022-09-30 11:15:10', '1', '2022-09-30 11:15:16', '0');"

let col=[
  {
    "Field": "sell_area",
    "Type": "varchar(255)",
    "Collation": "utf8_general_ci",
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "地区"
  },
  {
    "Field": "sell_site_info_id",
    "Type": "varchar(255)",
    "Collation": "utf8_general_ci",
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "站点"
  },
  {
    "Field": "sell_type",
    "Type": "varchar(255)",
    "Collation": "utf8_general_ci",
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "销售类型"
  },
  {
    "Field": "sell_date",
    "Type": "date",
    "Collation": null,
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "销售时间"
  },
  {
    "Field": "sell_no",
    "Type": "varchar(255)",
    "Collation": "utf8_general_ci",
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "期号"
  },
  {
    "Field": "sell_all_money",
    "Type": "decimal(10,2)",
    "Collation": null,
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "销售额"
  },
  {
    "Field": "sell_valid_money",
    "Type": "decimal(10,2)",
    "Collation": null,
    "Null": "YES",
    "Key": "",
    "Default": null,
    "Extra": "",
    "Privileges": "select,insert,update,references",
    "Comment": "有效销售额"
  }
]
col.map(r=>{
  let newsql="INSERT INTO `lottery_site`.`sys_column` (`table_id`, `label`, `prop`, `width`, `sortable`, `fixed`, `no`, `showOverflowTooltip`, `dic`, `custom`, `office_id`, `create_by`," +
    " `create_date`, `update_by`, `update_date`, `del_flag`) " +
    "VALUES (11, '"+r.Comment+"', '"+r.Field+"', '150', 0, 0, 0, 0, '1'" +
    ", 0, " +
    "'1', '1', '2022-09-30 11:15:10', '1', '2022-09-30 11:15:16', '0');"
  console.log(newsql)
})
