const BaseRest = require('./rest.js');
const {copyFile} = require('fs/promises');
const path = require('path');
const fs = require('fs');
module.exports = class extends BaseRest {
  /**
   * 分页查询数据
   * @returns {Promise<void>}
   */
  async findpageAction() {
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const field = this.get('field') ? this.get('field') : null;
    const fieldReverse = this.get('fieldReverse') ? this.get('fieldReverse') : null;
    const join = this.get('join') ? JSON.parse(this.get('join')) : null;
    const union = this.get('union') ? this.get('union') : null;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const order = this.get('order') ? this.get('order') : 'update_date desc';
    const having = this.get('having') ? this.get('having') : null;
    const modelName = this.get('modelName') ? this.get('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        count: 0,
        data: null,
        msg: '无模型名参数'
      };
    } else {
      const response = await model.alias('a').join(join).union(union).field(field).fieldReverse(fieldReverse).page(page, rows).where(where).having(having).order(order).countSelect();
      respData = {
        code: 200,
        count: response.count,
        data: response.data,
        msg: '查询成功'
      };
    }
    return this.json(respData);
  }

  /**
   * 查询多条数据
   * @returns {Promise<void>}
   */
  async findlistAction() {
    const field = this.post('field') ? this.post('field') : null;
    const fieldReverse = this.post('fieldReverse') ? this.post('fieldReverse') : null;
    const union = this.post('union') ? this.post('union') : null;
    const join = this.post('join') ? this.post('join') : null;
    const where = this.post('where') ? this.post('where') : {};
    const order = this.post('order') ? this.post('order') : 'update_date desc';
    const having = this.post('having') ? this.post('having') : null;
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else {
      const response = await model.alias('a').join(join).union(union).field(field).fieldReverse(fieldReverse).where(where).having(having).order(order).select();
      respData = {
        code: 200,
        data: response,
        msg: '查询成功'
      };
    }
    return this.json(respData);
  }

  /**
   * 查询一条数据
   * @returns {Promise<void>}
   */
  async findoneAction() {
    const field = this.post('field') ? this.post('field') : null;
    const fieldReverse = this.post('fieldReverse') ? this.post('fieldReverse') : null;
    const join = this.post('join') ? this.post('join') : null;
    const union = this.post('union') ? this.post('union') : null;
    const where = this.post('where') ? this.post('where') : {};
    const order = this.post('order') ? this.post('order') : 'update_date desc';
    const having = this.post('having') ? this.post('having') : null;
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else {
      const response = await model.alias('a').join(join).union(union).field(field).fieldReverse(fieldReverse).where(where).having(having).order(order).find();
      respData = {
        code: 200,
        data: response,
        msg: ''
      };
    }
    return this.json(respData);
  }

  /**
   * 添加一条数据
   * @returns {Promise<*>}
   */
  async addAction() {
    const addData = this.post('addData') ? this.addData(this.post('addData')) : '';
    const where = this.post('where') ? this.post('where') : null;
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else if (addData == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无添加数据'
      };
    } else {
      const response = await model.where(where).thenAdd(await this.addData(addData));
      if (response.type == 'add') {
        respData = {
          code: 200,
          data: null,
          msg: '添加数据成功'
        };
      } else {
        respData = {
          code: 401,
          data: null,
          msg: '添加数据失败'
        };
      }
    }
    return this.json(respData);
  }

  /**
   * 添加多条数据
   * @returns {Promise<*>}
   */
  async addManyAction() {
    const replace = this.post('replace') ? this.post('replace') : false;
    const ignore = this.post('ignore') ? this.post('ignore') : false;
    const addDatas = this.addData(this.post('addDatas')) ? this.post('addDatas') : '';
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else if (!think.isArray(addDatas)) {
      respData = {
        code: 401,
        data: null,
        msg: '数据必须是数组'
      };
    } else if (addDatas.length < 1) {
      respData = {
        code: 402,
        data: null,
        msg: '无添加数据'
      };
    } else {
      try {
        await this.startTrans();
        const response = await model.addMany(await this.addData(addDatas), {
          replace: replace, // 使用 REPLACE INTO,
          ignore: ignore // 使用 INSERT IGNORE INTO
        });
        await this.commit();
        respData = {
          code: 200,
          data: response,
          msg: '成功'
        };
      } catch (e) {
        await this.rollback();
        respData = {
          code: 402,
          data: null,
          msg: '失败'
        };
      }
    }
    return this.json(respData);
  }

  /**
   * 更新一条数据
   * @returns {Promise<void>}
   */
  async updateAction() {
    const updateData = this.post('updateData') ? this.updateData(this.post('updateData')) : '';
    const where = this.post('where') ? this.post('where') : null;
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else if (updateData == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无添加数据'
      };
    } else if (!updateData.id) {
      respData = {
        code: 402,
        data: null,
        msg: '无数据主键'
      };
    } else {
      const response = await model.where(where).update(await this.updateData(updateData));
      if (response) {
        respData = {
          code: 200,
          data: null,
          msg: '更新数据成功'
        };
      } else {
        respData = {
          code: 401,
          data: null,
          msg: '更新数据失败'
        };
      }
    }
    return this.json(respData);
  }

  /**
   * 删除数据
   */
  async deleteAction() {
    const where = this.post('where') ? this.post('where') : null;
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else if (where == null) {
      respData = {
        code: 401,
        data: null,
        msg: '无添加数据'
      };
    } else {
      try {
        await this.startTrans();
        const response = await model.where(where).update(await this.deleteData());
        await this.commit();
        respData = {
          code: 200,
          data: null,
          msg: '删除数据成功'
        };
      } catch (e) {
        await this.rollback();
        respData = {
          code: 402,
          data: null,
          msg: '删除数据失败'
        };
      }
    }
    this.json(respData);
  }

  /**
   * 上传数据
   */
  async uploadAction() {
    let respData = {};
    const file = this.file('file');
    const filepath = file.path;
    const fileobj = {};
    const filename = file.name;
    const uploadpath = think.ROOT_PATH + '/www/static/upload/';
    // 创建该目录
    think.mkdir(uploadpath);
    // 提取出用 ‘/' 隔开的path的最后一部分。
    const suffix = filename.substr(filename.lastIndexOf('.') + 1); // 文件后缀
    const name = filename.substr(0, filename.lastIndexOf('.')); // 文件后缀
    fileobj['name'] = name;
    const new_name = think.uuid();
    const newfilename = new_name + '.' + suffix;
    const basename = file.originalFilename;// 因为本系统不允许上传同名主题，所以文件名就直接使用主题名
    // 将上传的文件（路径为filepath的文件）移动到第二个参数所在的路径，并改为第二个参数的文件名。

    // 读取文件
    let fileObiect = fs.readFileSync(filepath);
    // 将文件存到指定位置
    fs.writeFileSync(uploadpath + '/' + newfilename, fileObiect);
    const fileModel = this.model('file');
    const addData = await this.addData({
      file_name: name,
      path: '/static/upload/' + newfilename,
      suffix: suffix
    });
    addData.id = new_name;
    await fileModel.add(addData);
    respData = {
      code: 200,
      data: {
        file_name: name + '.' + suffix,
        new_name: new_name,
        src: '/static/upload/' + newfilename
      },
      uploaded: true,
      message: 'Upload'
    };
    this.json(respData);
  }

  async uploadueAction() {
    const param = this.get();
    const action = this.get('action');

    if (param.action === 'config') {
      this.json(this.config('ueditor').default);
    }

    if (param.action === 'uploadimage' || param.action === 'uploadfile' || param.action === 'uploadvideo') {
      const file = this.file('upfile');
      const filepath = file.path;

      const nameArr = file.name.split('.');
      const basename = think.uuid() + '.' + nameArr[nameArr.length - 1];
      const YYYYMMDD = think.datetime(new Date(), 'YYYY-MM-DD');
      const staticPath = path.resolve(think.ROOT_PATH, 'www/static');
      const uploadPath = path.resolve(staticPath, 'upload');
      const relativePath = path.resolve(uploadPath, YYYYMMDD);

      // 文件夹不存在则创建
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath);
      }

      if (!fs.existsSync(relativePath)) {
        fs.mkdirSync(relativePath);
      }

      await copyFile(filepath, path.resolve(relativePath, `${basename}`));
      const ip = this.getIPAdress();
      const port = this.config('port');
      this.json({
        state: 'SUCCESS',
        url: `/static/upload/${YYYYMMDD}/${basename}`,
        title: basename,
        original: file.name
      });
    }

    if (param.action === 'uploadscrawl') {
      const request = require('request');
      const result = await request.get('http://www.baidu.com');
      let source = '';
      if (!think.isEmpty(this.get('source'))) {
        source = this.get('source');
      } else {
        source = this.post('source');
      }
    }
  }

  // 获取本机电脑IP
  getIPAdress() {
    const interfaces = require('os').networkInterfaces();
    for (const devName in interfaces) {
      const iface = interfaces[devName];
      for (let i = 0; i < iface.length; i++) {
        const alias = iface[i];
        if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
          // console.log(alias.address);

          return alias.address;
        }
      }
    }
  }

  async dictAction() {
    const key = this.post('key') ? this.post('key') : 'id';
    const name = this.post('name') ? this.post('name') : 'name';
    const where = this.post('where') ? this.post('where') : {};
    const order = this.post('order') ? this.post('order') : key + ' desc';
    const modelName = this.post('modelName') ? this.post('modelName') : '';
    const model = this.model(modelName);
    let respData = {};
    if (modelName == '') {
      respData = {
        code: 401,
        data: null,
        msg: '无模型名参数'
      };
    } else {
      const fieldSql = key + ' AS `key`,' + name + ' AS `name`';
      try {
        const response = await model.field(fieldSql).where(where).order(order).select();
        respData = {
          code: 200,
          data: response,
          msg: ''
        };
      } catch (e) {
        respData = {
          code: 500,
          data: {},
          msg: '参数错误'
        };
      }
    }
    return this.json(respData);
  }
};
