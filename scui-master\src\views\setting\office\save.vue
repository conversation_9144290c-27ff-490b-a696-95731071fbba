<template>
	<el-row>
		<el-col :xl="12" :lg="16">
			<h2>{{ form.name || "新增菜单" }}</h2>
			<el-form
				:model="form"
				:rules="rules"

				ref="dialogForm"
				label-width="120px"
				label-position="left"
			>
				<el-form-item label="名称" prop="name">
					<el-input
						v-model="form.name"
						clearable
						placeholder="名称"
					></el-input>

				</el-form-item>
				<el-form-item label="上级" prop="parent">
					<el-cascader
						v-model="form.parent"
						:options="menu"
						:props="menuProps"
						:show-all-levels="false"
						clearable
					></el-cascader>
					<div class="el-form-item-msg">

					</div>
				</el-form-item>

				<el-form-item label="负责人" prop="master">
					<sc-table-select v-model="form.master" :apiObj="apiObj" :props="props">

						<el-table-column prop="name" label="姓名"></el-table-column>

						<el-table-column label="操作" fixed="right" align="right" width="200">
							<template #default="scope">

								<el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
								</el-button>

							</template>
						</el-table-column>

					</sc-table-select>

				</el-form-item>

		

				<el-form-item label="排序" prop="sort">
					<el-input-number v-model="form.sort" controls-position="right" :min="1"
					                 style="width: 100%;"></el-input-number>
				</el-form-item>

				<el-form-item>
					<el-button type="primary" @click="submitForm('dialogForm')">保 存</el-button>
				</el-form-item>
			</el-form>
		</el-col>
	</el-row>


</template>

<script>

export default {

	components: {},
	props: {
		menu: {
			type: Object, default: () => {
			}
		}
	},
	data() {
		return {
			apiObj: this.$API.user.listselect,
			disabled: false,
			dialog: {
				save: false
			},
			selectConfig: {
				level: {
					label: 'name',
					value: 'key'
				},
			},
			form: {
				level:"",
				parent: "",
				name: "",
				type: "",
				master: "",
				people_number: 0,
				people_outside: 0,
				sort: 1
			},
			rules: {
				name: [
					{required: true, message: "请输入", trigger: "blur"}
				],
			
			
				"parent": [
					{required: true, trigger: "blur", message: "请选择"}
				]
			},
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true
			},

			props: {
				label: 'name',
				value: 'id'


			},
			views: []
		};
	},
	mounted() {
		this.views = this.getViews();
	},

	methods: {
		async submitForm(formName) {

			this.$refs[formName].validate(async (valid) => {
				if (valid) {
					this.form['masterid'] = this.form.master.id;
					console.log(this.form)
					let res = await this.$API.office.save.post(this.form);
					if (res.state == 1) {
						this.$message.success("保存成功")
						this.$emit("getOffice");
					} else {
						this.$message.error("保存失败")
					}

				} else {

					return false;
				}
			});
		},
		//表单注入数据
		setData(data, pid) {
			this.form = data;
			this.form.level =data.level+"";
			this.form.parent = pid;
		},

		table_edit(row) {
			//this.dialog.save = true
			this.$nextTick(() => {
				//this.$refs.saveDialog.open('save').setData(row)
			})
		},
		//获取所有视图组件
		getViews() {
			const filesUrl = [];

			return filesUrl;
		},
		querySearch(queryString, cb) {
			var results = this.getViews();
			results = results.filter(
				(item) => item.value.indexOf(queryString) !== -1
			);
			cb(results);
		}
	}
};
</script>

<style scoped>
h2 {
	font-size: 17px;
	color: #3c4a54;
	padding: 0 0 30px 0;
}
</style>
