import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/other/info/page`,
		name: "其他数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	add: {
		url: `${config.API_URL}/other/info/add`,
		name: "其他数据添加",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	save: {
		url: `${config.API_URL}/other/info/save`,
		name: "其他数据修改",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	info: {
		url: `${config.API_URL}/other/info/info`,
		name: "其他数据详情",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	list: {
		url: `${config.API_URL}/other/info/list`,
		name: "其他数据列表",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/other/info/delete`,
		name: "删除其他数据",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}
