import config from "@/config";
import http from "@/utils/request";

const tk = {
	jxpage: {
		url: `${config.API_URL}/sys/tk/jxpage`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	page: {
		url: `${config.API_URL}/sys/tk/page`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	page2: {
		url: `${config.API_URL}/sys/tk/page2`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	getnum: {
		url: `${config.API_URL}/sys/tk/getnum`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	getnext: {
		url: `${config.API_URL}/sys/tk/getnext`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	getnext2: {
		url: `${config.API_URL}/sys/tk/getnext2`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	before: {
		url: `${config.API_URL}/sys/tk/before`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},


	next: {
		url: `${config.API_URL}/sys/tk/next`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	savedx :{
		url: `${config.API_URL}/sys/tk/savedx`,
		name: "savedx",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},
	saveduoxuan :{
		url: `${config.API_URL}/sys/tk/saveduoxuan`,
		name: "saveduoxuan",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

	updatepic:{
		url: `${config.API_URL}/sys/tk/updatepic`,
		name: "updatepic",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

	
	savetk:{
		url: `${config.API_URL}/sys/tk/savetk`,
		name: "savetk",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

	savejd:{
		url: `${config.API_URL}/sys/tk/savejd`,
		name: "savejd",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},


    remove: {
		url: `${config.API_URL}/sys/tk/remove`,
		name: "删除",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

    process: {
		url: `${config.API_URL}/sys/tk/process`,
		name: "处理zip",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params, 60 * 60 * 1000,);

		}
	},
	saveimp: {
		url: `${config.API_URL}/sys/tk/saveimp`,
		name: "导入",
		post: async function (params) {
			// 这里接口对象偷懒重复了登录接口
			return await http.post(this.url, this.name, params);

		}
	},

	
}

export default tk;