<template>
	<div class="user-bar">
		<div class="panel-item hidden-sm-and-down" @click="search">
			<el-icon>
				<el-icon-search />
			</el-icon>
		</div>
		<div class="screen panel-item hidden-sm-and-down" @click="screen">
			<el-icon>
				<el-icon-full-screen />
			</el-icon>
		</div>
		<!-- <div class="tasks panel-item" @click="tasks">
			<el-icon>
				<el-icon-sort />
			</el-icon>
		</div> -->
		
		<el-dropdown class="user panel-item" trigger="click" @command="handleUser">
			<div class="user-avatar">
				<el-avatar :size="30" :src="userNameF"></el-avatar>
				<label>{{ userName }}</label>
				<el-icon class="el-icon--right">
					<el-icon-arrow-down />
				</el-icon>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="uc">帐号信息</el-dropdown-item>
					<el-dropdown-item command="clearCache">清除缓存</el-dropdown-item>
					<el-dropdown-item divided command="outLogin">退出登录</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
	</div>

	<el-dialog v-model="searchVisible" :width="700" title="搜索" custom-class="drawerBG" center destroy-on-close>
		<search @success="searchVisible = false"></search>
	</el-dialog>

	<el-drawer v-model="tasksVisible" :size="450" title="任务中心" custom-class="drawerBG" destroy-on-close>
		<tasks></tasks>
	</el-drawer>

</template>

<script>
import search from './search.vue'
import tasks from './tasks.vue'

export default {
	components: {
		search,
		tasks
	},
	data() {
		return {
			socket:null,
			userName: "",
			userNameF: "",
			userInfo:null,
			searchVisible: false,
			tasksVisible: false,
			msg: false
		}
	},
	 mounted() {
    window.getbuss = this.getbuss;
    window.showbuss = this.showbuss;

    
  },
	async created() {
		var userInfo = this.$TOOL.data.get("USER_INFO");
		this.userName = userInfo.userName;
		this.userNameF = userInfo.avatar;
		this.msgList = await this.$API.notice.getweb.get();
	},
	methods: {
		//个人信息
		handleUser(command) {
			if (command == "uc") {
				this.$router.push({ path: '/usercenter' });
			}
			if (command == "cmd") {
				this.$router.push({ path: '/cmd' });
			}
			if (command == "clearCache") {
				this.$confirm('清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？', '提示', {
					type: 'info',
				}).then(() => {
					const loading = this.$loading()
					this.$TOOL.data.clear()
					this.$router.replace({ path: '/login' })
					setTimeout(() => {
						loading.close()
						location.reload()
					}, 1000)
				}).catch(() => {
					//取消
				})
			}
			if (command == "outLogin") {
				this.$confirm('确认是否退出当前用户？', '提示', {
					type: 'warning',
					confirmButtonText: '退出',
					confirmButtonClass: 'el-button--danger'
				}).then(() => {
					this.$router.replace({ path: '/login' });
				}).catch(() => {
					//取消退出
				})
			}
		},



	 
		//跳转
		async gourl(menuname, bussid, id) {

			this.msg = false;

			this.$router.push({ name: menuname, params: { id: bussid } });

			this.$API.notice.updateone.get({ id: id });

			this.list = await this.$API.notice.getweb.get();
		},
		//全屏
		screen() {
			var element = document.documentElement;
			this.$TOOL.screen(element)
		},
		//显示短消息
		showMsg() {
			this.msg = true
		},
		//标记已读
		async markRead() {
			await this.$API.notice.updateall.get();
			this.msgList = await this.$API.notice.getweb.get();
		},
		//搜索
		search() {
			this.searchVisible = true
		},
		//任务
		tasks() {
			this.tasksVisible = true
		}
	}
}
</script>

<style scoped>
.user-bar {
	display: flex;
	align-items: center;
	height: 100%;
}

.user-bar .panel-item {
	padding: 0 10px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
}

.user-bar .panel-item i {
	font-size: 16px;
}

.user-bar .panel-item:hover {
	background: rgba(0, 0, 0, 0.1);
}

.user-bar .user-avatar {
	height: 49px;
	display: flex;
	align-items: center;
}

.user-bar .user-avatar label {
	display: inline-block;
	margin-left: 5px;
	font-size: 12px;
	cursor: pointer;
}

.msg-list li {
	border-top: 1px solid #eee;
}

.msg-list li a {
	display: flex;
	padding: 20px;
}

.msg-list li a:hover {
	background: #ecf5ff;
}

.msg-list__icon {
	width: 40px;
	margin-right: 15px;
}

.msg-list__main {
	flex: 1;
}

.msg-list__main h2 {
	font-size: 15px;
	font-weight: normal;
	color: #333;
}

.msg-list__main p {
	font-size: 12px;
	color: #999;
	line-height: 1.8;
	margin-top: 5px;
}

.msg-list__time {
	width: 100px;
	text-align: right;
	color: #999;
}

.dark .msg-list__main h2 {
	color: #d0d0d0;
}

.dark .msg-list li {
	border-top: 1px solid #363636;
}

.dark .msg-list li a:hover {
	background: #383838;
}
</style>
