<template>
	<el-container>
		<el-container>
			<el-header>
				<div class="right-panel">
				
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" stripe remoteSort remoteFilter>
			
					<el-table-column label="反馈内容" prop="content" min-width="300"></el-table-column>
					<el-table-column label="添加人" prop="create_name" width="120"></el-table-column>
					<el-table-column label="添加时间" prop="create_date" width="160" sortable='custom'></el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="80">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_show(scope.row)">查看</el-button>
                                   <el-popconfirm 
            title="确定删除该反馈吗？" 
            @confirm="table_del(scope.row, scope.$index)"
        >
            <template #reference>
                <el-button type="text" size="small" style="color: #F56C6C">删除</el-button>
            </template>
        </el-popconfirm>
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<detail-dialog v-if="dialog.detail" ref="detailDialog" @closed="dialog.detail=false"></detail-dialog>
</template>

<script>
import detailDialog from './detail'

export default {
	name: 'feedback',
	components: {
		detailDialog
	},
	data() {
		return {
			dialog: {
				detail: false
			},
			apiObj: this.$API.wtfk2.page,
			search: {
				type: null,
				content: null
			},
			// 定义类型枚举
			
		}
	},
	methods: {


          async table_del(row, index) {
            try {
                const res = await this.$API.wtfk2.delete.post({ id: row.id })
                if (res.code == 200) {
                    // 从表格中移除该行数据
                    this.$refs.table.tableData.splice(index, 1)
                    this.$message.success("删除成功")
                } else {
                    this.$alert(res.message, "提示", { type: 'error' })
                }
            } catch (error) {
                this.$alert("删除失败", "提示", { type: 'error' })
            }
        },
		//查看详情
		table_show(row) {
			this.dialog.detail = true
			this.$nextTick(() => {
				this.$refs.detailDialog.open().setData(row)
			})
		},
		//搜索
		upsearch() {
			this.$refs.table.upData(this.search)
		},
		//获取标签类型
		getTagType(type) {
			const typeMap = {
				'bug反馈': 'danger',
				'使用建议': 'warning',
				'内容建议': 'success',
				'内容错误': 'info'
			}
			return typeMap[type] || ''
		}
	}
}
</script>