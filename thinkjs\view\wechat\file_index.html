<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
        <title>附件上传</title>
        <!-- 引入 WeUI CDN 链接 -->
        <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/2.3.0/weui.min.css"/>
        <link rel="stylesheet" href="/static/css/we.css"/>
        <style>
.page__hd {
    padding: 40px;
}

        </style>
    </head>
    <body ontouchstart>
        <div class="container" id="container"><div class="page uploader js_show" tabindex="-1">
        
            <div class="page__hd">
                <h1 class="page__title">移动端文件上传助手</h1>
                <p id="x123" class="page__desc">
                  <input class="weui-hidden_abs" readonly style="width:1px;height:1px;" aria-labelledby="x123" role="option">
                  您的文件或照片在手机端上传完成后，就可以在办公系统内进行使用了。(^_^)
                </p>
            </div>
            <div class="page__bd">
                <div role="dialog" aria-hidden="true" aria-modal="true" class="weui-gallery" id="gallery">
                    <span role="img" tabindex="0" class="weui-gallery__img" id="galleryImg"></span>
                    <div class="weui-gallery__opr">
                        <a role="button" aria-label="删除" href="javascript:" class="weui-gallery__del">
                            <i class="weui-icon-delete weui-icon_gallery-delete"></i>
                        </a>
                    </div>
                </div>
        
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell  weui-cell_uploader">
                        <div class="weui-cell__bd">
                            <div class="weui-uploader">
                                <div class="weui-uploader__hd" role="option" aria-labelledby="js_uploader_title js_a11y_comma js_uploader_current_num js_uploader_unit js_a11y_comma js_uploader_max_tips js_uploader_max_num js_uploader_unit">
                                    <p id="js_uploader_title" class="weui-uploader__title">上传</p>
                                    
                                </div>
                                <div class="weui-uploader__bd">
                                    <ul class="weui-uploader__files" id="uploaderFiles">
                                   
                                    </ul>
                                    <div class="weui-uploader__input-box">
                                        <input id="uploaderInput" class="weui-uploader__input" type="file" accept="image/* " multiple/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <script src="/static/js/jquery.min.js"></script>
        <script type="text/javascript">
            $(function(){
                var tmpl = '<li class="weui-uploader__file" role="img" tabindex="0" style="background-image:url(#url#)"></li>',
                    $gallery = $("#gallery"), $galleryImg = $("#galleryImg"),
                    $uploaderInput = $("#uploaderInput"),
                    $uploaderFiles = $("#uploaderFiles")
                    ;
        
                $uploaderInput.on("change", function(e){
                    var src, url = window.URL || window.webkitURL || window.mozURL, files = e.target.files;
                    for (var i = 0, len = files.length; i < len; ++i) {
                        var file = files[i];
        
                        if (url) {
                            src = url.createObjectURL(file);
                        } else {
                            src = e.target.result;
                        }

                        var formData = new FormData();
            formData.append("file", e.target.files[0]);
            formData.append("id",'{{ id}}');
            $.ajax({
                url: "/wechat/file/upload",
                type: "post",
                data: formData,
                processData: false, // 告诉jQuery不要去处理发送的数据
                contentType: false, // 告诉jQuery不要去设置Content-Type请求头
                dataType: 'text',
                success: function(data) {
                    var res=JSON.parse(data);
                    console.log(res)
                   if(res.code==200){
                    $uploaderFiles.append($(tmpl.replace('#url#', src)));
                   }else{
                       alert("文件上传出错，请联系管理员 （*＾-＾*）")
                   }

                   
                },
                error: function(data) {
                    
                }
            });


        
                      
                    }
                });
                var currentImg;
                $uploaderFiles.on("click", "li", function(){
                    $galleryImg.attr("style", this.getAttribute("style"));
                    $gallery.attr('aria-hidden','false');
                    $gallery.attr('aria-modal','true');
                    $gallery.fadeIn(100);
                    setTimeout(function(){
                      $galleryImg.attr("tabindex", '-1').trigger('focus');
                    },200);
                    currentImg = this;
                });
                $gallery.on("click", function(){
                    $gallery.attr('aria-modal','false');
                    $gallery.attr('aria-hidden','true');
                    $gallery.fadeOut(100);
                    setTimeout(function(){
                      $galleryImg.removeAttr("tabindex");
                    },200);
                    currentImg.focus();
                });
            });
        </script>
       

       
    </body>
</html>