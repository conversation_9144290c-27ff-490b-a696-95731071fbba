const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {
  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const model = this.model('dict_info');
    where['del_flag'] = 0;
    const response = await model.page(page, rows).where(where).order('`key` asc').countSelect();
    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      msg: ''
    };
    return this.json(respData);
  }

  async dictAction() {
    let respData = {};
    const dictInfo = this.model('dict_info');
    const infoData = await dictInfo
      .alias('i')
      .field('i.id,i.name,i.key,i.tree_id,t.type,t.name as typeName')
      .join({
        table: 'dict_tree',
        join: 'left', // join 方式，有 left, right, inner 3 种方式
        as: 't', // 表别名
        on: ['tree_id', 'id'] // ON 条件
      })
      .where({'i.del_flag': 0, 't.del_flag': 0, 'i.enable': true})
      .order('i.`key` asc')
      .select();
    const data = {};
    infoData.map(r => {
      if (!data[r.type]) {
        data[r.type] = [];
        data[r.type].push(r);
      } else {
        data[r.type].push(r);
      }
    });
    respData = {
      code: 200,
      data: data,
      msg: ''
    };
    return this.json(respData);
  }

  async listAction() {
    let respData = {};
    const treeWhere = this.post('where') ? this.post('where') : {};
    const infoWhere = {'del_flag': 0, enable: true};
    treeWhere['del_flag'] = 0;
    const dictInfo = this.model('dict_info');
    const dictTree = this.model('dict_tree');
    const treeData = await dictTree.where(treeWhere).find();
    if (think.isEmpty(treeData)) {
      respData = {
        code: 200,
        data: [],
        msg: ''
      };
    } else {
      infoWhere.tree_id = treeData.id;
      const infoData = await dictInfo.where(infoWhere).order('`key` asc').select();
      respData = {
        code: 200,
        data: infoData,
        msg: ''
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const model = this.model('dict_info');
      this.post()['update_date'] = think.datetime();
      await model.where({id: id}).update(this.post());
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }

  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        msg: '缺少必要的参数'
      };
    } else {
      const model = this.model('dict_info');
      await model.where({id: id}).update({del_flag: 1, update_date: think.datetime()});
      respData = {
        code: 200,
        data: {},
        msg: '成功'
      };
    }
    return this.json(respData);
  }

  async addAction() {
    let respData = {};
    const model = this.model('dict_info');
    this.post()['create_date'] = think.datetime();
    this.post()['id'] = think.uuid();
    this.post()['del_flag'] = 0;
    await model.add(this.post());
    respData = {
      code: 200,
      data: {},
      msg: '成功'
    };
    return this.json(respData);
  }
};
