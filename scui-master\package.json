{"name": "scui", "version": "1.6.4", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --report", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "2.0.6", "@tinymce/tinymce-vue": "5.0.0", "axios": "0.27.2", "codemirror": "5.65.5", "core-js": "3.22.8", "cropperjs": "1.5.12", "crypto-js": "4.1.1", "dingtalk-jsapi": "^3.0.9", "driver.js": "^0.9.8", "echarts": "5.3.2", "element-plus": "^2.2.12", "file-saver": "^2.0.5", "less": "^4.1.3", "less-loader": "^11.0.0", "lodash": "^4.17.21", "mathjs": "^10.6.4", "nprogress": "0.2.0", "qrcodejs2": "0.0.2", "socket.io-client": "2.0.1", "sortablejs": "1.15.0", "tinymce": "6.0.3", "v-viewer": "^3.0.10", "vue": "3.2.36", "vue-i18n": "9.1.10", "vue-router": "4.0.15", "vue-ueditor-wrap": "^3.0.8", "vuedraggable": "4.0.3", "vuex": "4.0.2", "xgplayer": "2.31.6", "xgplayer-hls": "2.5.2", "xlsx": "^0.17.1"}, "devDependencies": {"@babel/core": "7.18.2", "@babel/eslint-parser": "7.18.2", "@vue/cli-plugin-babel": "5.0.4", "@vue/cli-plugin-eslint": "5.0.4", "@vue/cli-service": "5.0.4", "eslint": "8.17.0", "eslint-plugin-vue": "9.1.0", "sass": "1.37.5", "sass-loader": "10.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"APP_CONFIG": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"indent": 0, "no-tabs": 0, "no-mixed-spaces-and-tabs": 0, "vue/no-unused-components": 0, "vue/multi-word-component-names": 0, "no-unused-vars": 1, "no-empty": 0, "vue/no-reserved-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}