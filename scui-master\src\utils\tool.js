/*
 * @Descripttion: 工具集
 * @version: 1.2
 * @LastEditors: sakuya
 * @LastEditTime: 2022年5月24日00:28:56
 */

import CryptoJS from 'crypto-js';
import sysConfig from "@/config";
import _ from 'lodash';
import api from '@/api/index'
 
import { ElMessage } from 'element-plus'

const tool = {}

/* 加密localStorage */
tool.data = {
	set(key, data, datetime = 0) {
		//加密
		if(sysConfig.LS_ENCRYPTION == "AES"){
			data = tool.crypto.AES.encrypt(JSON.stringify(data), sysConfig.LS_ENCRYPTION_key)
		}
        let cacheValue = {
            content: data,
            datetime: parseInt(datetime) === 0 ? 0 : new Date().getTime() + parseInt(datetime) * 1000
        }
        return localStorage.setItem(key, JSON.stringify(cacheValue))
	},
	get(key) {
        try {
            const value = JSON.parse(localStorage.getItem(key))
            if (value) {
                let nowTime = new Date().getTime()
                if (nowTime > value.datetime && value.datetime != 0) {
                    localStorage.removeItem(key)
                    return null;
                }
				//解密
				if(sysConfig.LS_ENCRYPTION == "AES"){
					value.content = JSON.parse(tool.crypto.AES.decrypt(value.content, sysConfig.LS_ENCRYPTION_key))
				}
                return value.content
            }
            return null
        } catch (err) {
            return null
        }
	},
	remove(key) {
		return localStorage.removeItem(key)
	},
	clear() {
		return localStorage.clear()
	}
}

/*sessionStorage*/
tool.session = {
	set(table, settings) {
		var _set = JSON.stringify(settings)
		return sessionStorage.setItem(table, _set);
	},
	get(table) {
		var data = sessionStorage.getItem(table);
		try {
			data = JSON.parse(data)
		} catch (err) {
			return null
		}
		return data;
	},
	remove(table) {
		return sessionStorage.removeItem(table);
	},
	clear() {
		return sessionStorage.clear();
	}
}

/*cookie*/
tool.cookie = {
	set(name, value, config={}) {
		var cfg = {
			expires: null,
			path: null,
			domain: null,
			secure: false,
			httpOnly: false,
			...config
		}
		var cookieStr = `${name}=${escape(value)}`
		if(cfg.expires){
			var exp = new Date()
			exp.setTime(exp.getTime() + parseInt(cfg.expires) * 1000)
			cookieStr += `;expires=${exp.toGMTString()}`
		}
		if(cfg.path){
			cookieStr += `;path=${cfg.path}`
		}
		if(cfg.domain){
			cookieStr += `;domain=${cfg.domain}`
		}
		document.cookie = cookieStr
	},
	get(name){
		var arr = document.cookie.match(new RegExp("(^| )"+name+"=([^;]*)(;|$)"))
		if(arr != null){
			return unescape(arr[2])
		}else{
			return null
		}
	},
	remove(name){
		var exp = new Date()
		exp.setTime(exp.getTime() - 1)
		document.cookie = `${name}=;expires=${exp.toGMTString()}`
	}
}

/* Fullscreen */
tool.screen = function (element) {
	var isFull = !!(document.webkitIsFullScreen || document.mozFullScreen || document.msFullscreenElement || document.fullscreenElement);
	if(isFull){
		if(document.exitFullscreen) {
			document.exitFullscreen();
		}else if (document.msExitFullscreen) {
			document.msExitFullscreen();
		}else if (document.mozCancelFullScreen) {
			document.mozCancelFullScreen();
		}else if (document.webkitExitFullscreen) {
			document.webkitExitFullscreen();
		}
	}else{
		if(element.requestFullscreen) {
			element.requestFullscreen();
		}else if(element.msRequestFullscreen) {
			element.msRequestFullscreen();
		}else if(element.mozRequestFullScreen) {
			element.mozRequestFullScreen();
		}else if(element.webkitRequestFullscreen) {
			element.webkitRequestFullscreen();
		}
	}
}

/* 复制对象 */
tool.objCopy = function (obj) {
	return JSON.parse(JSON.stringify(obj));
}

/* 日期格式化 */
tool.dateFormat = function (date, fmt='yyyy-MM-dd hh:mm:ss') {
	date = new Date(date)
	var o = {
		"M+" : date.getMonth()+1,                 //月份
		"d+" : date.getDate(),                    //日
		"h+" : date.getHours(),                   //小时
		"m+" : date.getMinutes(),                 //分
		"s+" : date.getSeconds(),                 //秒
		"q+" : Math.floor((date.getMonth()+3)/3), //季度
		"S"  : date.getMilliseconds()             //毫秒
	};
	if(/(y+)/.test(fmt)) {
		fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length));
	}
	for(var k in o) {
		if(new RegExp("("+ k +")").test(fmt)){
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
		}
	}
	return fmt;
}

/* 千分符 */
tool.groupSeparator = function (num) {
	num = num + '';
	if(!num.includes('.')){
		num += '.'
	}
	return num.replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
		return $1 + ',';
	}).replace(/\.$/, '');
}

/* 常用加解密 */
tool.crypto = {
	//MD5加密
	MD5(data){
		return CryptoJS.MD5(data).toString()+ "jsxdev"
	},
	//BASE64加解密
	BASE64: {
		encrypt(data){
			return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data))
		},
		decrypt(cipher){
			return CryptoJS.enc.Base64.parse(cipher).toString(CryptoJS.enc.Utf8)
		}
	},
	//AES加解密
	AES: {
		encrypt(data, secretKey, config={}){
			if(secretKey.length % 8 != 0){
				console.warn("[SCUI error]: 秘钥长度需为8的倍数，否则解密将会失败。")
			}
			const result = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
				iv: CryptoJS.enc.Utf8.parse(config.iv || ""),
				mode: CryptoJS.mode[config.mode || "ECB"],
				padding: CryptoJS.pad[config.padding || "Pkcs7"]
			})
			return result.toString()
		},
		decrypt(cipher, secretKey, config={}){
			const result = CryptoJS.AES.decrypt(cipher, CryptoJS.enc.Utf8.parse(secretKey), {
				iv: CryptoJS.enc.Utf8.parse(config.iv || ""),
				mode: CryptoJS.mode[config.mode || "ECB"],
				padding: CryptoJS.pad[config.padding || "Pkcs7"]
			})
			return CryptoJS.enc.Utf8.stringify(result);
		}
	}
}
/**
 * 判断是否未null，undefined,[],{},0
 * @param val
 * @returns {boolean}
 */
 tool.isEmpty = function (val) {
	// null or undefined
	if (val == null) {
		return true;
	}
	if (val == undefined) {
		return true;
	}
	if (typeof val === 'boolean') {
		return false;
	}
	if (typeof val === 'number') {
		if (val == 0) {
			return false;
		} else {
			return !val;
		}
	}
	if (val instanceof Error) {
		return val.message === '';
	}
	switch (Object.prototype.toString.call(val)) {
		case '[object String]':
		case '[object Array]':
			return !val.length;
		case '[object File]':
		case '[object Map]':
		case '[object Set]': {
			return !val.size;
		}
		case '[object Object]': {
			return !Object.keys(val).length;
		}
	}
	return false;
}

/**
 * 判断是否未null，undefined,[],{},0
 * @param val
 * @returns {boolean}
 */
tool.isNotEmpty = function (val) {
	return !tool.isEmpty(val);
}



tool.callModelFun = function (funcName, that) {
	let methods = that.$options.methods;

	const _this = that;
	methods[funcName](_this);
}
/**
 * 阿拉伯数字转换为简写汉字
 * @param str
 * @returns string
 */
tool.arabiaToSimplifiedChinese = function (str) {
	var num = parseFloat(str);
	var strOutput = "", strUnit = '仟佰拾亿仟佰拾万仟佰拾元角分';
	num += "00";
	var intPos = num.indexOf('.');
	if (intPos >= 0) {
		num = num.substring(0, intPos) + num.substr(intPos + 1, 2);
	}
	strUnit = strUnit.substr(strUnit.length - num.length);
	for (var i = 0; i < num.length; i++) {
		strOutput += '零壹贰叁肆伍陆柒捌玖'.substr(num.substr(i, 1), 1) + strUnit.substr(i, 1);
	}
	return strOutput.replace(/零角零分$/, '整').replace(/零[仟佰拾]/g, '零').replace(/零{2,}/g, '零').replace(/零([亿|万])/g, '$1').replace(/零+元/, '元').replace(/亿零{0,3}万/, '亿').replace(/^元/, "零元")
};

tool.dictData = {};

tool.tableData = {};

/**
 * 表格字典显示
 * @param row element row参数
 * @param column element column参数
 * @param dictName 字典表type值
 * @returns {string}
 */
tool.tableDict = function (row, column, dictName) {
	let name = ""
	let dictData = this.dictData[dictName]


	const col = column.property

	const dictArr = dictData
	if (!dictArr) {
		return
	}
	for (let item of dictArr) {
		if (item.key == row[col]) {
			name = item.name
		}
	}
	return name;
}
/**
 * 字典表格添加 如有重复type，覆盖掉前面的字典
 * @param dictName 字典type
 * @param data 字典具体数据.例如[{key:1,name:"xxx"}]
 */
tool.saveDict = function (dictName, data) {
	this.dictData[dictName] = data
}
/**
 *获取单个字典值
 */
tool.getDictName = function (dictName, key) {
	let dict = _.find(this.dictData[dictName], ['key', key]);
	return tool.isNotEmpty(dict)?dict.name:null;

}
/**
 * 字典数据初始化
 */
tool.dictInit = async function () {
	let response = await api.dict.infoDict.post();
	this.dictData = response.data;
}
/**
 * 表格初始化
 */
tool.tableInit = async function () {
	// let response = await api.table.infoColmun.post();
	// for (let element of response.data) {
	// 	for (let col of element.column) {
	// 		col.fixed=col.fixed === 1;
	// 		col.sortable=col.sortable === 1;
	// 		col.showOverflowTooltip=col.showOverflowTooltip === 1;
	// 		col.custom=col.custom === 1;
	// 	}
	// 	this.tableData[element.table_name]=element;

	// }
}
tool.getDingCode=function (callback){
	 
}
export default tool
