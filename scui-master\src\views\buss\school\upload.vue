<template>

	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" append-to-body="true" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
				 label-position="right">


	        <el-row :gutter="24">
				<el-col :span="24">
					<el-form-item label="图片上传" prop="img">
							<sc-upload v-model="form.img" :apiObj="uploadApi"  :cropper="true" :compress="1" :aspectRatio="ratio"   icon="el-icon-picture" title="上传缩略图">

							 
						    

                            </sc-upload>
					</el-form-item>
				</el-col>
			</el-row>

           

		
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">确定</el-button>
		</template>
	</el-dialog>
</template>

<script>


export default {
	emits: ['success', 'closed'],

	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
              ratio:4/3,
             uploadApi:this.$API.common.upload,
			visible: false,
			isSaveing: false,
			form: {
				id: "",
				img: ""		 
			},
			//验证规则
			rules: {

				img: [
					{required: true, message: '请上传图片'}
				],
				
				
			},
			
		}
	},
	mounted() {
        
        
		
	},
	methods: {
	
		open(mode) {

			this.mode = mode.mode;
            this.form.cid=mode.cid;
			this.visible = true;
			return this
		},
	
	
		async getUsers() {
			const jsuser = await this.$API.user.select.get();
			this.users = jsuser;
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					    this.visible = false;
						this.$emit('success', this.form, this.mode)
					
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		async setData(data) {
			this.form.id = data.id
		
		}
	}
}
</script>
<style scoped>
	.el-card+.el-card {margin-top: 15px;}

	.imglist {margin-bottom:0;}
	.imglist .el-col+.el-col {margin-left: 10px;}
	.custom-empty {width: 100%;height: 100%;line-height: 1;display: flex;flex-direction: column;align-items: center;justify-content: center;}
	.custom-empty i {font-size: 40px;color: #8c939d;}
	.custom-empty p {font-size: 12px;font-weight: normal;color: #8c939d;margin-top: 10px;}
</style>
