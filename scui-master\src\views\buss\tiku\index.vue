<template>
	<el-container>
		<el-aside width="300px" v-loading="showDicloading">
			<el-container>
				<el-header>
					<el-input placeholder="输入关键字进行过滤" v-model="dicFilterText" clearable></el-input>
				</el-header>
				<el-main class="nopadding">
					<el-tree ref="dic" class="menu" node-key="id" :data="dicList" :props="dicProps" :highlight-current="true" :expand-on-click-node="false" :filter-node-method="dicFilterNode" @node-click="dicClick">
						<template #default="{node,data}">
							<span class="custom-tree-node">
								<span class="label">{{ node.label }}</span>
								<span class="label my-badge"  style="">{{ data.num1 }}</span>
							</span>
						</template>


						
					</el-tree>
				</el-main>
				<el-footer style="height:51px;">
				 
				</el-footer>
			</el-container>
		</el-aside>



		<el-container class="is-vertical">
			<el-header>
				<div class="left-panel">
	
      <el-radio-group v-model="state" aria-label="label position" @change="changestate"> 
        <el-radio-button label="全部"></el-radio-button>
        <el-radio-button label="未处理"></el-radio-button>
        <el-radio-button label="已处理"></el-radio-button>
      </el-radio-group>
	  <div style="margin-left:20px">
	 <el-link type="success" style="padding-left:8px">总数：{{num1}}</el-link>
    <el-link type="warning"  style="padding-left:8px">未处理：{{num1-num2}}</el-link>
    <el-link type="danger"  style="padding-left:8px">已处理：{{num2}}</el-link></div>
					
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="listApi" row-key="id" :params="listApiParams" stripe
          remoteSort
          remoteFilter
				         @selection-change="selectionChange"   :paginationLayout="'prev, pager, next'">
					<el-table-column type="selection" width="50"></el-table-column>
			

          <el-table-column
            label="编号"
            prop="no"
            width="150" sortable='custom'
             
          ></el-table-column>
		  	  <el-table-column label="类型" prop="type" width="150"></el-table-column> 
		   <el-table-column label="分数" prop="score" width="100" sortable='custom'></el-table-column>
                    
				
					<el-table-column label="题目" prop="tm" width="500"></el-table-column>
				 
                   
					<el-table-column label="操作" fixed="right" align="right" width="260">
						<template #default="scope">
							
							<el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
							</el-button>

							<el-button type="text" size="small" @click="table_updatejx(scope.row, scope.$index)">更新解析
							</el-button>

							<el-button type="text" size="small" @click="table_updatepic(scope.row, scope.$index)">更新题干
							</el-button>
						 
						</template>
					</el-table-column>
				</scTable>
			</el-main>
		</el-container>
	</el-container>

  
				 <edit-dialog
    v-if="dialog.edit"
    ref="editDialog"
    @success="handleSuccess"
    @closed="handleSuccess2"
  ></edit-dialog>




	<jx-dialog    v-if="dialog.jx"
    ref="jxDialog"
    @success="handleSuccess"
    @closed="handleSuccess2"></jx-dialog>


	<pic-dialog    v-if="dialog.pic"
    ref="picDialog"
    @success="handleSuccess"
    @closed="handleSuccess2"></pic-dialog>


</template>

<script>

import Sortable from 'sortablejs'
import editDialog from "./save";
import picDialog from "./updatepic"
 
import jxDialog from "./jx";
export default {
	name: 'tk2',
	components: {
		editDialog,
		jxDialog,
		picDialog
		 
		 
	},
	data() {
		return {
			dialog: {
				edit: false,
				zsd:false,
				jx:false,
				pic:false
			},
			num1:0,
			num2:0,
			isadmin:false,
			showDicloading: true,
			dicList: [],
			dicFilterText: '',
			dicProps: {
				label: 'name',

			},
			state:"全部",
			listApi: this.$API.tk.page,
			listApiParams: {},
			selection: []
		}
	},
	watch: {
		dicFilterText(val) {
			this.$refs.dic.filter(val);
		}
	},
 async	mounted() {
		this.getDic()
		this.rowDrop()
		let user= await this.$API.user.currinfo.post();
		if(user.data.login_name=="admin"){
			this.isadmin=true;
		}
	},
	methods: {
changestate(){

 
	if(this.state=="已处理"){
		this.listApiParams['state']=1;
	}
	if(this.state=="未处理"){
		this.listApiParams['state']=0;
	}

if(this.state=="全部"){
	this.listApiParams.state=2;
	}

	this.$refs.table.upData(this.listApiParams);
	 
},
		 //本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    handleSuccess2() {
      this.dialog.edit = false;
      this.$refs.table.refresh();
    },
		//加载树数据
		async getDic() {

            var res = await this.$API.lesson.list.get();
				
	
			this.showDicloading = false;
			this.dicList = res;
			console.log(res);
			//获取第一个节点,设置选中 & 加载明细列表
			const firstNode = this.dicList[0];
			if (firstNode) {
				this.$nextTick(() => {
					this.$refs.dic.setCurrentKey(firstNode.id)
				})
				this.listApiParams = {
					//where: JSON.stringify({tree_id: firstNode.id,})
				}
				this.listApi = this.$API.tk.page;
			}
		},
		//树过滤
		dicFilterNode(value, data) {
			if (!value) return true;
			let targetText = data.name + data.code;
			return targetText.indexOf(value) !== -1;
		},
		//树增加
		 
		//编辑树
		dicEdit(data) {
			this.dialog.dic = true
			this.$nextTick(() => {
				let editNode = this.$refs.dic.getNode(data.id);
				let editNodeParentId = editNode.level == 1 ? undefined : editNode.parent.data.id
				data.parentId = editNodeParentId
				this.$refs.dicDialog.open('edit').setData(data)
			})
		},
		//树点击事件
		dicClick(data) {

			this.num1=data.num1;
			this.num2=data.num2;

			this.$refs.table.upData({
				where: JSON.stringify({
					lessonid: data.id
				})
			})
		},
		//删除树
		dicDel(node, data) {
			this.$confirm(`确定删除 ${data.name} 项吗？`, '提示', {
				type: 'warning'
			}).then(() => {
				this.showDicloading = true;
				//删除节点是否为高亮当前 是的话 设置第一个节点高亮
				//let dicCurrentKey = this.$refs.dic.getCurrentKey();

				this.$API.dict.delete.post({id: data.id})
				this.getDic();
				 
				this.showDicloading = false;
				this.$message.success("操作成功")
			}).catch(() => {

			})
		},
		//行拖拽
		rowDrop() {
			const _this = this
			const tbody = this.$refs.table.$el.querySelector('.el-table__body-wrapper tbody')
			Sortable.create(tbody, {
				handle: ".move",
				animation: 300,
				ghostClass: "ghost",
				onEnd({newIndex, oldIndex}) {
					const tableData = _this.$refs.table.tableData
					const currRow = tableData.splice(oldIndex, 1)[0]
					tableData.splice(newIndex, 0, currRow)
					_this.$message.success("排序成功")
				}
			})
		},
	 
		table_edit(row) {
			this.dialog.edit = true
			this.$nextTick(() => {
				this.$refs.editDialog.open('edit').setData(row)
			})
		},

	table_updatejx(row) {
			this.dialog.jx = true
			this.$nextTick(() => {
				this.$refs.jxDialog.open().setData({"id":row.id});
			});
		},

	table_updatepic(row) {
			this.dialog.pic = true
			this.$nextTick(() => {
				this.$refs.picDialog.open("add").setData({"id":row.id});
			});
		},
		
		
		//删除明细
		async table_del(row, index) {
			let reqData = {id: row.id}
			let res = await await this.$API.tk.remove.post(reqData);
			if (res.code == 200) {
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},
		//批量删除
		async batch_del() {
			this.$confirm(`确定删除选中的 ${this.selection.length} 项吗？`, '提示', {
				type: 'warning'
			}).then(() => {
				const loading = this.$loading();
				this.selection.forEach(item => {
					this.$refs.table.tableData.forEach(async (itemI, indexI) => {
						if (item.id === itemI.id) {
							this.$refs.table.tableData.splice(indexI, 1)
                            await this.$API.tk.remove.post({id:itemI.id});
						}
					})
				})
				loading.close();
				this.$message.success("操作成功")
			}).catch(() => {

			})
		},
 
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//表格内开关事件
		async changeSwitch(val, row) {
			//1.还原数据
			row.enable = row.enable == '1' ? '0' : '1'
			//2.执行加载
			row.$switch_enable = true;
			//3.等待接口返回后改变值
			let response = await this.$API.dict.infoSave.post({
				enable: val,
				id: row.id
			})
			if (response.code == "200") {
				delete row.$switch_enable;
				row.enable = val;
				this.$message.success(`操作成功id:${row.id} val:${val}`)
			} else {
				this.$message.error(`操作失败id:${row.id} val:${val}`)
			}


		},
		//远程更新数据
		handleDicSuccess() {
			// if (mode == 'add') {
			this.getDic();
			
		},
		//本地更新数据
		handleListSuccess(data, mode) {
			if (mode == 'add') {
				data.id = new Date().getTime()
				this.$refs.table.tableData.push(data)
			} else if (mode == 'update') {
				this.$refs.table.tableData.filter(item => item.id === data.id).forEach(item => {
					Object.assign(item, data)
				})

				this.$refs.listDialog.getDic();
			}
		}
	}
}
</script>

<style scoped>

 .my-badge {
	right: 10px;position: absolute;
        color: #fff;
        background: #99a9bf;
        padding: 3px 8px;
        font-size: 12px;
        line-height: 12px;
        border-radius: 20px;
        text-align: center;
 }
.custom-tree-node {display: flex;flex: 1;align-items: center;justify-content: space-between;font-size: 14px;padding-right: 24px;height:100%;}
.custom-tree-node .code {font-size: 12px;color: #999;}
.custom-tree-node .do {display: none;}
.custom-tree-node:hover .code {display: none;}
.custom-tree-node:hover .do {display: inline-block;}
</style>
