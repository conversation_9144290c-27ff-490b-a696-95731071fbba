import config from "@/config";
import http from "@/utils/request";

const flow = {
	todolist: {
		url: `${config.API_URL}/sys/flow/todolist`,
		name: "待办列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	flowlog: {
		url: `${config.API_URL}/sys/flow/flowlog`,
		name: "流程记录",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	doflow: {
		url: `${config.API_URL}/sys/flow/doflow`,
		name: "同意流程",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	backlist: {
		url: `${config.API_URL}/sys/flow/backlist`,
		name: "回退步骤列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	backflow: {
		url: `${config.API_URL}/sys/flow/backflow`,
		name: "回退",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},


	muser: {
		url: `${config.API_URL}/sys/flow/muser`,
		name: "获取聊天用户列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	savemuser:{
		url: `${config.API_URL}/sys/flow/savemuser`,
		name: "获取聊天用户列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	savemess:{
		url: `${config.API_URL}/sys/flow/savemess`,
		name: "保存聊天记录",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	getmess:{
		url: `${config.API_URL}/sys/flow/getmess`,
		name: "读取",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	}
	,


	removemuser:{
		url: `${config.API_URL}/sys/flow/removemuser`,
		name: "删除聊天内用户",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},


	unread:{
		url: `${config.API_URL}/sys/flow/unread`,
		name: "消息未读列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	getflowattr:{
		url: `${config.API_URL}/sys/flow/getflowattr`,
		name: "获取流程参数",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},


}
export default flow;