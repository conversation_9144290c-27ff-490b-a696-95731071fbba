<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="330" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" ref="dialogForm" label-width="100px" label-position="left">
			<el-form-item label="所属字典" prop="tree_id">
				<el-cascader v-model="form.tree_id" :options="dic" :props="dicProps" :show-all-levels="false"
				             clearable></el-cascader>
			</el-form-item>
			<el-form-item label="键值" prop="key">
				<el-input v-model="form.key" clearable></el-input>
			</el-form-item>
			<el-form-item label="名称" prop="name">
				<el-input v-model="form.name" clearable></el-input>
			</el-form-item>
			<el-form-item label="是否有效" prop="enable">
				<el-switch v-model="form.enable" active-value="1" inactive-value="1"></el-switch>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>
<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增项',
				edit: '编辑项'
			},
			visible: false,
			isSaveing: false,
			form: {
				id: "",
				tree_id: "",
				name: "",
				key: "",
				enable: "1"
			},
			rules: {
				dic: [
					{required: true, message: '请选择所属字典'}
				],
				name: [
					{required: true, message: '请输入名称'}
				],
				key: [
					{required: true, message: '请输入键值'}
				]
			},
			dic: [],
			dicProps: {
				value: "id",
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		if (this.params) {
			this.form.tree_id = this.params.code
		}
		this.getDic()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this;
		},
		//获取字典列表
		async getDic() {
			var res = await this.$API.dict.list.get();
			this.dic = res.data;
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					let res;
					if (this.mode == "add") {
						res = await this.$API.dict.infoAdd.post(this.form);
					} else {
						res = await this.$API.dict.infoSave.post(this.form);
					}
					this.isSaveing = false;
					if (res.code == 200) {
						await this.$TOOL.dictInit();
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				}
			})
		},
		//表单注入数据
		setData(data, mode) {
			this.form.id = data.id
			this.form.name = data.name
			this.form.key = data.key
			this.form.enable = data.enable
			this.form.tree_id = data.tree_id
			this.mode = mode
		}
	}
}
</script>

<style>
</style>
