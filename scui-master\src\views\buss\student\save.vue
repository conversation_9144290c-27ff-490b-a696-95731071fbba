<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				 
				<el-col :span="12">
					<el-form-item label="学校名称" prop="name">
						<el-input v-model="form.name" placeholder="学校名称" clearable></el-input>
					</el-form-item>
				</el-col>

                <el-col :span="12">
					<el-form-item label="是否公开" prop="type">
					    <el-switch v-model="form.type" />
					</el-form-item>
				</el-col>
         
		
			</el-row>
			 


			 
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
			mode: "add",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			menuList: null,
			visible: false,
			isSaveing: false,
			//菜单树配置
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true,
			},
			//表单数据
			form: {
			 
				name: "",
				type: 0,
                remarks:""
				 
			},
			//验证规则
			rules: {
			 
				name: [
					{required: true, message: '请输入学校名称'}
				],
			 
				
				 
			},
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		//加载树数据
		async getGroup() {
			//加载角色树
			var role = await this.$API.role.select.get();
			this.groups = role;
			this.menuList= await this.$API.office.list.get();
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {

             

				if (valid) {
					this.isSaveing = true;

                    if(this.form.type){
                    this.form.type=1;
                    }else{
                        this.form.type=0;
                    }

                    

					var res = await this.$API.school.save.post(this.form);
					this.isSaveing = false;
					if (res.code == 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			this.form.id = data.id
			this.form.login_name = data.login_name
			this.form.name = data.name
			this.form.role = data.group
			this.form.office_id = parseInt(data.office_id);
			//this.menuList = data.office;


		}
	}
}
</script>

<style>
</style>
