
module.exports = class extends think.Controller {

    async loginAction() {
         
    
        let model = this.model('student');
    
        const res = await model.where({'del_flag': 0, 'phone': this.post("phone"), 'password': this.post("password")}).find();
        if (!think.isEmpty(res)) {

            const user = Object.assign({}, res, {'userName': res['name']});

    
            const token = await this.session('userInfo', user);
            console.log(token);
            user['token'] = token;
            user['state'] = 1;
            return this.json({state:1,data:user,token:token})

        }else{

            return this.json({state:0,data:null})

        }
    }



    async checkyxAction(){

        let model=this.model("yx_scan");
        let openid=this.post("openid");
        let res=await model.where({"openid":openid}).select();
        if(think.isEmpty(res)){
            return this.json({state:0})
        }else{
            return this.json({state:1})
        }

    }

    async getcode2Action() {
        const phone = this.post("phone");
       
       
        let model=this.model("student");

 

        
        if(think.isEmpty(phone)){
            return this.json({state:0,data:null})
        }


        let smsservice=this.service('sms');

        console.log(phone);
        //生成4位随机验证码
        const code = Math.floor(100000 + Math.random() * 900000).toString();

        let res=await smsservice.sendVerificationCode(phone,code,"login");
        console.log(res);
        return this.json({state:1,data:null})

    }




    async getcodeAction() {
        const phone = this.post("phone");
       
       
        let model=this.model("student");


        let student=await model.where({'del_flag': 0, 'phone': phone}).find();
        if(!think.isEmpty(student)){
          return this.json({state:0,msg:"手机号已注册"})
        }

        
        if(think.isEmpty(phone)){
            return this.json({state:0,data:null})
        }


        let smsservice=this.service('sms');

        console.log(phone);
        //生成4位随机验证码
        const code = Math.floor(100000 + Math.random() * 900000).toString();

        let res=await smsservice.sendVerificationCode(phone,code,"login");
        console.log(res);
        return this.json({state:1,data:null})

    }

    async loginbyopenidAction(){
        console.log(this.post())
        let openid=this.post("openid");
        let model=this.model("student");
        if(think.isEmpty(openid)){
            return this.json({state:0,data:null})
        }

        let res=await model.where({'del_flag': 0, 'openid': openid}).find();
        if (!think.isEmpty(res)) {

            const user = Object.assign({}, res, {'userName': res['name']});

    
            const token = await this.session('userInfo', user);
            console.log(token);
            user['token'] = token;
            user['state'] = 1;
            return this.json({state:1,data:user,token:token})

        }else{

            return this.json({state:0,data:null})

        }
    }

    async getopenidAction(){
        let code=this.post("code");
        let id=this.post("id");
        console.log(code);
        let res=await this.service('wechat').getOpenidByCode(code,id);
        let para={};
        para.openid=res.openid;
        return this.json({state:1,data:res});
    }
    

    async updatepasswordAction(){
        let code=this.post("code");
        let password=this.post("password");
        let phone=this.post("phone");  

        let model=this.model("student");
        let student=await model.where({'del_flag': 0, 'phone': phone}).find();
        let smsservice=this.service('sms');
        let res=await smsservice.verifyCode(phone,code,"login");
        if(!res){
            return this.json({state:0,msg:"验证码错误"})
        }
       
      
        await model.where({'del_flag': 0, 'phone': phone}).update({'password': password});
        return this.json({state:1,msg:"密码修改成功"})
        
        
        
        
    }

    async regAction(){

        console.log("sdfasdfsssssssss");


        let code=this.post("code");
        let password=this.post("password");
        let phone=this.post("phone");

        let level=this.post("grade");

        if(think.isEmpty(code) || think.isEmpty(password) || think.isEmpty(phone)){
            return this.json({state:0,msg:"参数错误"})
        }  
        let smsservice=this.service('sms');
        let res=await smsservice.verifyCode(phone,code,"login");
        if(!res){
            return this.json({state:0,msg:"验证码错误"})
        }


      let data={};
      let model=this.model("student");


      let student=await model.where({'del_flag': 0, 'phone': phone}).find();
      if(!think.isEmpty(student)){
        return this.json({state:0,msg:"手机号已注册"})
      }

      let code2=this.post("code2");

      let keyrecord={};
      let key={};
      let model2 = this.model("key");
      if(!think.isEmpty(code2)){
     //激活码校验
      
         key = await model2.where({key:code2,"del_flag":0}).find();
  
       
        if(think.isEmpty(key)){
          return this.json({state:0,msg:"激活码不存在"})
            
        }
  
        let keyrecordmodel=this.model("keyrecord");
         keyrecord=await keyrecordmodel.where({id:key.keyrecord,"del_flag":0}).find();
  
  
        if(think.isEmpty(keyrecord)){
          return this.json({state:0,msg:"激活码不存在"})
          
        }
  
        if(key.status == 1){
         
            return this.json({state:0,msg:"激活码已使用"})
        }
  
        if(keyrecord.yxq<think.datetime()){
           
            return this.json({state:0,msg:"激活码已过期"})
        }


  
  


      }

  





  let yxmodel=this.model("yx_scan");
  let yx=await yxmodel.alias("sc").join(["sys_yx  as y on sc.yx_id=y.id"]).where({openid:this.post("openid")}).field("y.office_id as schoolid").find();
  if(!think.isEmpty(yx)){
    data.school=yx.schoolid;
  }else{

    data.school=this.post("schoolid")
  }



    
      data.phone=this.post("phone");
      data.del_flag=0;
      data.create_date=think.datetime();
      data.password=this.post("password");
  
      data.id=think.uuid();
      data.type="免费";
      data.level=level;
      //日期增加七日以后的日期
      let date=new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000);







      data.name="匿名用户";
      data.todate=think.datetime(date);
      data.openid=this.post("openid");








     let insertId = await model.add(data);

     let vipService = think.service('vip');

     let lessson=await this.model("lesson").where({"type":data.level,parent_id:0}).order(["sort asc"]).field("id,name").select();
    for(let item of lessson){



        let vipres=     await vipService.addVipRecord({
            userid: insertId,
            lessson: item.id,
            nj: data.level,
            allday: 7,
            orderid: "0",
            type:"free"
          });




    }



      if(!think.isEmpty(code2)){
        

        await model2.where({id:key.id}).update({state:1,state2:1,"uid":insertId});
  

        
 
      
        
        let userModel=this.model("student");
        let user=await userModel.where({id:insertId}).find();

        ///如果使用了激活码，则更新用户信息




        for(let item of lessson){



               await vipService.addVipRecord({
                userid: insertId,
                lessson: item.id,
                nj: keyrecord.nj,
                allday: keyrecord.usetime,
                orderid: "0",
                type:"key",
                code:code2
              });
    
    
    
    
        }

        // if(user.type=="免费"){

            

        //     let date1    =new Date();
            
        //     let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
          

        //      await userModel.where({id:insertId}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss'),school:keyrecord.schoolid});
        // }else{
        //     let date1    =new Date(user.todate);
        //     if(date1<new Date()){
        //         date1=new Date();
        //     }
        //     let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
        //     await userModel.where({id:insertId}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});

        // }
      }



  
  
      this.json({"state":1});

    


    }



    async verifykeyAction(code){
       
        let model = this.model("key");
        let key = await model.where({key:code,"del_flag":0}).find();

       
        if(think.isEmpty(key)){
            return {"flag":false,msg:"激活码不存在"};
        }

        let keyrecordmodel=this.model("keyrecord");
        let keyrecord=await keyrecordmodel.where({id:key.keyrecord,"del_flag":0}).find();


        if(think.isEmpty(keyrecord)){
            return {"flag":false,msg:"激活码不存在"};
        }

        if(key.status == 1){
            return {"flag":false,msg:"激活码已使用"};
             
        }

        if(keyrecord.yxq<think.datetime()){
            return {"flag":false,msg:"激活码已过期"};
            
        }






        await model.where({id:key.id}).update({state:1,state2:1,"uid":userinfo.id});
  


 
      
        
        let userModel=this.model("student");
        let user=await userModel.where({id:userinfo.id}).find();
        if(user.type=="免费"){

            

            let date1    =new Date();
            
            let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
          

             await userModel.where({id:userinfo.id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});
        }else{
            let date1    =new Date(user.todate);
            if(date1<new Date()){
                date1=new Date();
            }
            let date2=new Date(date1.getTime() + keyrecord.usetime * 24 * 60 * 60 * 1000);
            await userModel.where({id:userinfo.id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});

        }
        return {"flag":true};
    }
    
  };
  