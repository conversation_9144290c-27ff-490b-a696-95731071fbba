const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {


    async listAction() {
        const page = this.get('page');
        const rows = this.get('pageSize');
        const keyword = this.get('keyword');
        const model = this.model('help');
        const where = {del_flag: 0};
        if (!think.isEmpty(keyword)) {
          where['name'] = ['like', '%' + keyword + '%'];
        }
        const res = await model.alias('c').page(page, rows).where(where).order('name ,s asc').countSelect();
        const data = {};

        data.code = 200;
        data.count = res.count;
        data.data = res.data;
        data.msg = '';

        return this.json(data);
      }

      async selectAction() {
        const model = this.model('help');
        const where = {del_flag: 0};
        const res = await model.where(where).select();

        return this.json(res);
      }


      async updatestateAction(){
        let name=this.post("name");
        let model2=this.model("help_user");
        const user = await this.session('userInfo');
        let data={};
        data.id=think.uuid();
        data.userid=user.id;
        data.name=name;
        await model2.add(data);

        return this.json({});
      }

      async gethelpAction() {
        let name=this.get("name")
        const user = await this.session('userInfo');
        let model2=this.model("help_user");
        let count=await model2.where({name:name,"userid":user.id}).count();

        if(count == 0){
            const model = this.model('help');
            const where = {del_flag: 0};
            const res = await model.where({name:name}).order('name ,s asc').select();
            let arr=[];
            for(var item of res){
                let one ={};
                one.element=item.domid;
                let para={};
                para.title="使用引导";
                para.description=item.content;
                para.position=item.pos
                one.popover=para;
                arr.push(one);
            }

            return this.json(arr);


        }else{

            return this.json([]);
        }


      }

      async removeAction() {
        const id = this.get('id');

        const model = this.model('help');
        await model.where({'id': id}).update({'del_flag': 1});

        return this.json({'code': 200});
      }

      async saveAction() {
        const model = this.model('help');
        const data = this.post();

        if (think.isEmpty(data.id)) {
          data.id = think.uuid();
          await model.add(data);
        } else {
          await model.where({'id': data.id}).update(data);
        }

        this.json({code: 200});
      }



}
