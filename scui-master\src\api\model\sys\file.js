import config from "@/config";
import http from "@/utils/request";

const file = {
	list: {
		url: `${config.API_URL}/sys/file/list`,
		name: "附件列表",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},

	list2: {
		url: `${config.API_URL}/sys/file/list2`,
		name: "附件列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	}
	,
	compress: {
		url: `${config.API_URL}/sys/file/compress`,
		name: "压缩",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},

	
	rotate: {
		url: `${config.API_URL}/sys/file/rotate`,
		name: "旋转",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
	topdf: {
		url: `${config.API_URL}/sys/file/topdf`,
		name: "topdf",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	}
};

export default file;
