<template>
  <el-dialog
    :title="titleMap[mode]"
    v-model="visible"
    :width="600"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      :model="form"
      :rules="rules"
      :disabled="mode == 'show'"
      ref="dialogForm"
      label-width="100px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="" clearable></el-input>
          </el-form-item>
        </el-col>

				<el-col :span="24">
					<el-form-item label="科目" prop="schoolid">
						<el-cascader
							v-model="form.toplesson"
							:options="lessonlist"
							:props="lessonProps"
							:show-all-levels="false"
							clearable
							style="width: 100%;"
						></el-cascader>
					</el-form-item>
				</el-col>
		




          <el-col :span="24">
          <el-form-item label="统计时间段" prop="datescope">
           <el-select v-model="form.datescope" placeholder="时间段选择" style="width: 100%" @change="datechange"  >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
    </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="学科章节" prop="lesson">
            <el-cascader
              v-model="form.lesson"    popper-class="popper-select" :disabled="distree" :show-all-levels="false" 
              :options="menuList"
              :props="menuProps"
               @change="handleChange"  ref="cascader" style="width: 100%;"
              clearable
            ></el-cascader>
          </el-form-item>
        </el-col>




        <el-col :span="24">
          <el-form-item label="上课时间" prop="skdate">
            <el-date-picker   type="datetime" placeholder="选择日期" :editable="false" v-model="form.skdate"   format="YYYY-MM-DD HH:mm:ss"
       
        time-format="HH:mm" value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%;"></el-date-picker>
           
          </el-form-item>
        </el-col>
      </el-row>

          <el-row >
				<el-col :span="24">
					<el-form-item label="备注说明" prop="remarks">
					   <el-input type="textarea" v-model="form.remarks"></el-input>	 
					</el-form-item>
				</el-col>
			
			</el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="mode != 'show'"
        type="primary"
        :loading="isSaveing"
        @click="submit()"
        >保 存</el-button
      >
    </template>
  </el-dialog>


</template>

<script>

export default {
  components: {
    
  },
  emits: ["success", "closed"],
  data() {
    return {
      selectConfig: {
        userLevel: {
          label: "name",
          value: "name",
        },
      },
     
      mode: "add",
      titleMap: {
        add: "新增备课",
        edit: "编辑备课",
        show: "查看",
      },
      schoolList: null,
      visible: false,
      distree:true,
      isSaveing: false,
      //菜单树配置
     lessonlist:[],
      //表单数据
      form: {
        id: "",
        title: "",
        skdate: "",
        lesson: "",
        lessonname:"",
        schoolid: "",
        remarks:"",
        datescope:"",
        toplesson:"",
      },
      //验证规则
      rules: {
        title: [{ required: true, message: "请输入标题" }],
        skdate: [{ required: true, message: "请输入上课时间" }],
toplesson:[{ required: true, message: "请选择" }],
        lesson: [{ required: true, message: "请选择章节" }],
      },
      //所需数据选项
      groups: [],
      menuList:[],
      //规则树配置
      groupsProps: {
        value: "id",
        multiple: true,
        label: "name",
        checkStrictly: true,
      },

      lessonProps:{
   
				value: "id",
				emitPath: false,
				label: "name",
				checkStrictly: true,
	
      
      },

        options : [
  {
    value: '1d',
    label: '当天',
  },
  {
    value: '7d',
    label: '一周',
  },
  {
    value: '30d',
    label: '30天',
  },
  
  ],


      menuProps: {
        value: "id",
        emitPath: false,
      
        label: "title",
        multiple: true,
        checkStrictly: true,
      },
    };
  },
async  mounted() {
    this.getGroup();

    this.lessonlist= await this.$API.bk.gettoplesson.get();
  },
  methods: {
    //显示

    handleChange(value) {
        let arr=[];
        for(var item of this.$refs["cascader"].getCheckedNodes()){

            let  tmparr=item.pathLabels;

            let str=tmparr.join('');
            arr.push(str);
        }

     

       

        this.form.lessonname=arr.join(',');
     
  },


async datechange(){


    this.distree=false;
    var res = await this.$API.lesson.listbyschool.get({"datescope":this.form.datescope});
    this.menuList = res;

},
    open(mode = "add") {
      this.mode = mode;
      this.visible = true;
      return this;
    },
    //加载树数据
    async getGroup() {
      var res = await this.$API.lesson.listbyschool.get();
      this.menuList = res;
    },
    //表单提交方法
    submit() {
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          
            this.form.lesson=this.form.lesson.join(",")
              console.log(this.form)
          this.isSaveing = true;
          var res = await this.$API.bk.save.post(this.form);
          this.isSaveing = false;
          if (res.code == 200) {
            this.$emit("success", this.form, this.mode);
            this.visible = false;
            this.$message.success("操作成功");
          } else {
            this.$alert(res.message, "提示", { type: "error" });
          }
        } else {
          return false;
        }
      });
    },
    //表单注入数据
  async  setData(data) {
      this.form.id = data.id;


      let info  = await this.$API.bk.info.post({"id":data.id});
      this.form.title = data.title;
       this.form.toplesson = data.toplesson;
      this.form.skdate = info.skdate;
      this.form.lesson = data.lesson.split(',').map(Number)
        this.form.remarks=data.remarks;
        this.form.lessonname=data.lessonname;
       
    },
  },
};
</script>

<style>
.popper-select {
        li[aria-haspopup="true"] {
                .el-checkbox {
                        display: none;
                }
        }
}
</style>
