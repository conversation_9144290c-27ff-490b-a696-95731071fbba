const crypto = require('crypto');
const xml2js = require('xml2js');

module.exports = class extends think.Controller {
  /**
   * 微信服务器认证接口
   */
  async indexAction() {
    let id=this.get('id');
    let gzhconfig=think.config(id+'_wechat');
    console.log(gzhconfig);
   
    // 微信加密签名
    const signature = this.get('signature');
    // 时间戳
    const timestamp = this.get('timestamp');
    // 随机数
    const nonce = this.get('nonce');
    // 随机字符串
    const echostr = this.get('echostr');
    
    // 如果是GET请求，说明是微信服务器在进行URL验证
    if (this.ctx.method === 'GET') {
      if (this.checkSignature(signature, timestamp, nonce,gzhconfig)) {
        return this.body = echostr;
      } else {
         
        return this.body = 'fail';
      }
    }
    
    // 如果是POST请求，说明是微信服务器转发用户消息
    if (this.ctx.method === 'POST') {
      try {
        // 接收消息体
        think.logger.info('接收微信消息体');
        
        // 获取原始XML数据
        const xmlData = this.post();
    

        // 解密消息
        const decryptedData = this.decryptMessage(xmlData, gzhconfig);
        think.logger.info('解密后数据:', decryptedData);


      

        // 解析XML消息
        let messageData;
        if (typeof decryptedData === 'string') {
          // 如果是解密后的XML字符串，需要解析
          messageData = await this.parseXML(decryptedData);
        } else {
          // 如果是明文模式，直接使用原始数据
          if (typeof xmlData === 'string') {
            messageData = await this.parseXML(xmlData);
          } else {
            messageData = xmlData;
          }
        }
        
        think.logger.info('解析后的消息数据:', messageData);
        
        // 根据消息类型处理
        const result = await this.handleMessage(messageData);
        think.logger.info('处理结果:', result );
        
        // 如果有返回消息，根据配置决定是否加密
        let finalResult = result || 'success';
        if (result && gzhconfig.encodingAESKey) {
          // 如果配置了加密密钥，对回复消息进行加密
          finalResult = this.encryptReplyMessage(result, gzhconfig);
          console.log("finalResult==========",finalResult);
          think.logger.info('回复消息已加密');
        }
        console.log("finalResult==========",finalResult);
        // 设置正确的Content-Type
        this.ctx.type = 'application/xml';
        return this.body = finalResult;
      } catch (error) {
        think.logger.error('处理微信消息异常:', error);
        return this.body = 'success';
      }
    }
  }
  

  decryptMessage(xmlData, gzhconfig){
    const { appid, secret, token, encodingAESKey } = gzhconfig;
 
    console.log("appid=========",appid)
    // 如果没有配置加密密钥，直接返回原始数据
    if (!encodingAESKey || encodingAESKey.length !== 43) {
      think.logger.info('未配置EncodingAESKey或长度不正确，使用明文模式');
      return xmlData;
    }
    
    try {
      // 从XML中提取加密信息
      const encryptData = this.extractEncryptData(xmlData);


      console.log("sdafasfdaf    ",encryptData);
      if (!encryptData) {
        think.logger.info('未找到加密数据，使用明文模式');
        return xmlData;
      }
      
      // 验证消息签名（可选，增强安全性）
      if (encryptData.msgSignature && encryptData.timeStamp && encryptData.nonce) {
        const isValidSignature = this.verifyMsgSignature(
          encryptData.msgSignature,
          encryptData.timeStamp,
          encryptData.nonce,
          encryptData.encrypt,
          token
        ); 
        
        if (!isValidSignature) {
          think.logger.warn('消息签名验证失败，但继续处理');
          // 注意：某些情况下微信的签名可能不一致，这里只警告不阻断
        } else {
          think.logger.info('消息签名验证成功');
        }
      }
      
      // 执行AES解密
      const decryptedXml = this.aesDecrypt(encryptData, encodingAESKey, appid);
      think.logger.info('消息解密成功');
      return decryptedXml;
      
    } catch (error) {
      think.logger.error('消息解密失败:', error);
      // 解密失败时返回原始数据
      return xmlData;
    }
  }

  /**
   * 从XML中提取加密数据
   */
  extractEncryptData(xmlData) {
    try {

     
      // 如果xmlData是字符串，需要先解析
      if (typeof xmlData === 'string') {
        console.log(1)
        const parser = require('xml2js');
        let encryptInfo = null;
        
        parser.parseString(xmlData, { trim: true, explicitArray: false }, (err, result) => {

       
          if (!err && result && result.xml) {
            encryptInfo = {
              encrypt: result.xml.Encrypt[0],
              msgSignature: result.xml.MsgSignature[0] || '',
              timeStamp: result.xml.TimeStamp[0] || '',
              nonce: result.xml.Nonce[0] || ''
            };
          }
        });
        
        return encryptInfo;
      }
    
    console.log("=============",xmlData.xml.Encrypt)
      // 如果xmlData已经是解析后的对象
      if (xmlData && xmlData.xml.Encrypt) {
        console.log(2)
        return {
          encrypt: xmlData.xml.Encrypt[0],
          msgSignature: xmlData.xml.MsgSignature|| '',
          timeStamp: xmlData.xml.TimeStamp || '',
          nonce: xmlData.xml.Nonce || ''
        };
      }
      
      return null;
    } catch (error) {
      think.logger.error('提取加密数据失败:', error);
      return null;
    }
  }

  /**
   * AES-256-CBC解密
   */
  aesDecrypt(encryptData, encodingAESKey, appid) {
    try {
      // 检查加密数据格式
      let encryptedText = '';
      if (typeof encryptData === 'string') {
        encryptedText = encryptData;
      } else if (encryptData && encryptData.encrypt) {
        encryptedText = encryptData.encrypt;
      } else {
        throw new Error('无效的加密数据格式');
      }
      
      // Base64解码EncodingAESKey，得到32字节的密钥
      const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
      
      // Base64解码加密数据
      const encryptedBuffer = Buffer.from(encryptedText, 'base64');
      
      // 确保数据长度足够
      if (encryptedBuffer.length <= 16) {
        throw new Error('加密数据长度不足，无法提取IV');
      }
      
      // 提取16字节的IV (前16字节)
      const iv = encryptedBuffer.slice(0, 16);
      
      // 提取密文数据 (16字节之后的数据)
      const ciphertext = encryptedBuffer.slice(16);
      
      // 创建解密器
      const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
      decipher.setAutoPadding(false);
      
      // 执行解密
      let decrypted = Buffer.concat([
        decipher.update(ciphertext),
        decipher.final()
      ]);
      
      // 移除PKCS7填充
      decrypted = this.removePKCS7Padding(decrypted);
      
      // 确保解密后的数据长度足够
      if (decrypted.length < 20) {
        throw new Error('解密后数据长度不足，无法读取消息长度');
      }
      
      // 解析解密后的数据结构
      // 格式: [random(16字节)] + [msg_len(4字节)] + [msg] + [appid]
      const msgLen = decrypted.readUInt32BE(16);
      
      // 提取消息内容
      let msg = decrypted.slice(20, 20 + msgLen).toString('utf8');
      console.log("解密后的消息内容:", msg);
      
      // 修复XML格式问题
      if (msg.startsWith('>')) {
        msg = '<xml><ToUserName' + msg;
      }
      
      // 确保XML完整
      if (!msg.includes('</xml>')) {
        msg += '</xml>';
      }
      
      // 寻找完整的XML部分
      const xmlStart = msg.indexOf('<xml');
      const xmlEnd = msg.lastIndexOf('</xml>');
      
      if (xmlStart !== -1 && xmlEnd !== -1) {
        // 提取完整的XML内容
        const completeXml = msg.substring(xmlStart, xmlEnd + 6);
        console.log("提取的完整XML:", completeXml);
        return completeXml;
      }
      
      // 如果没有找到完整的XML，返回原始消息
      return msg;
      
    } catch (error) {
      think.logger.error('AES解密失败:', error);
      throw error;
    }
  }

  /**
   * 移除PKCS7填充
   */
  removePKCS7Padding(buffer) {
    if (buffer.length === 0) {
      return buffer;
    }
    
    const paddingLength = buffer[buffer.length - 1];
    
    // 验证填充是否有效
    if (paddingLength < 1 || paddingLength > 32) {
      throw new Error('无效的PKCS7填充');
    }
    
    // 检查填充字节是否一致
    for (let i = buffer.length - paddingLength; i < buffer.length; i++) {
      if (buffer[i] !== paddingLength) {
        throw new Error('PKCS7填充验证失败');
      }
    }
    
    return buffer.slice(0, buffer.length - paddingLength);
  }

  /**
   * 验证消息签名
   */
  verifyMsgSignature(msgSignature, timeStamp, nonce, encryptMsg, token) {
    try {
      // 按字典序排序
      const tmpArr = [token, timeStamp, nonce, encryptMsg].sort();
      const tmpStr = tmpArr.join('');
      
      // SHA1加密
      const signature = crypto.createHash('sha1').update(tmpStr).digest('hex');
      
      return signature === msgSignature;
    } catch (error) {
      think.logger.error('消息签名验证失败:', error);
      return false;
    }
  }

  /**
   * 加密回复消息
   * @param {string} replyMsg - 要加密的回复消息XML
   * @param {object} gzhconfig - 公众号配置
   * @returns {string} - 加密后的XML
   */
  encryptReplyMessage(replyMsg, gzhconfig) {
    const { appid, token, encodingAESKey } = gzhconfig;
    
    // 如果没有配置加密密钥，直接返回明文
    if (!encodingAESKey || encodingAESKey.length !== 43) {
      return replyMsg;
    }
    
    try {
      // AES加密
      const encryptedMsg = this.aesEncrypt(replyMsg, encodingAESKey, appid);
      
      // 生成时间戳和随机数
      // 使用当前时间戳，确保每次生成的签名不同
      const timeStamp = parseInt(Date.now() / 1000).toString();
      
      // 生成随机数，确保在0~4294967295之间
      const nonce = this.generateNonce();
      
      // 记录用于调试
      console.log("加密参数:", {
        token,
        timeStamp,
        nonce,
        encryptedMsg: encryptedMsg.substring(0, 20) + "..." // 只记录部分加密消息
      });
      
      // 生成签名
      const msgSignature = this.generateMsgSignature(token, timeStamp, nonce, encryptedMsg);
      
      // 构建加密XML
      const encryptedXml = `<xml>
<Encrypt><![CDATA[${encryptedMsg}]]></Encrypt>
<MsgSignature><![CDATA[${msgSignature}]]></MsgSignature>
<TimeStamp>${timeStamp}</TimeStamp>
<Nonce><![CDATA[${nonce}]]></Nonce>
</xml>`;
      
      // 记录最终生成的XML
      console.log("生成的加密XML结构:", encryptedXml.substring(0, 100) + "...");
      
      return encryptedXml;
      
    } catch (error) {
      think.logger.error('回复消息加密失败:', error.message);
      return replyMsg; // 加密失败时返回明文
    }
  }

  /**
   * AES-256-CBC加密
   * 严格按照微信加密规范实现
   */
  aesEncrypt(msg, encodingAESKey, appid) {
    try {
      // 1. AESKey = Base64_Decode(EncodingAESKey + "=")
      const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
      if (aesKey.length !== 32) {
        throw new Error(`AESKey长度错误，期望32字节，实际${aesKey.length}字节`);
      }
      
      // 2. 生成16字节随机数
      const random = crypto.randomBytes(16);
      
      // 3. 消息长度（4字节网络字节序，大端序）
      const msgBuffer = Buffer.from(msg, 'utf8');
      const msgLenBuffer = Buffer.alloc(4);
      msgLenBuffer.writeUInt32BE(msgBuffer.length, 0);
      
      // 4. 拼接数据: random(16B) + msg_len(4B) + msg + appid
      const appidBuffer = Buffer.from(appid, 'utf8');
      const dataBuffer = Buffer.concat([random, msgLenBuffer, msgBuffer, appidBuffer]);
      
      // 5. 添加PKCS7填充
      const paddedData = this.addPKCS7Padding(dataBuffer);
      
      // 6. 使用AESKey作为密钥，使用AESKey的前16字节作为IV
      const iv = aesKey.slice(0, 16);
      
      // 7. 创建加密器
      const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
      cipher.setAutoPadding(false); // 我们已经手动添加了填充
      
      // 8. 执行加密
      const encrypted = Buffer.concat([
        cipher.update(paddedData),
        cipher.final()
      ]);
      
      // 9. Base64编码加密结果
      return encrypted.toString('base64');
      
    } catch (error) {
      think.logger.error('AES加密失败:', error.message);
      throw error;
    }
  }

  /**
   * 添加PKCS7填充
   */
  addPKCS7Padding(buffer) {
    const blockSize = 32;
    const paddingLength = blockSize - (buffer.length % blockSize);
    const padding = Buffer.alloc(paddingLength, paddingLength);
    return Buffer.concat([buffer, padding]);
  }

  /**
   * 生成随机数字Nonce
   * 返回0~4294967295之间的整数字符串
   * 微信小程序要求Nonce必须为范围0~4294967295之间的数字
   */
  generateNonce() {
    // 生成0到4294967295之间的随机整数
    // 使用位运算确保生成的是32位无符号整数
    const randomNum = (Math.random() * 0x100000000) >>> 0;
    
    // 确保数字在有效范围内并转为字符串
    return randomNum.toString();
  }

  /**
   * 生成消息签名
   */
  generateMsgSignature(token, timeStamp, nonce, encryptMsg) {
    const tmpArr = [token, timeStamp, nonce, encryptMsg].sort();
    const tmpStr = tmpArr.join('');
    return crypto.createHash('sha1').update(tmpStr).digest('hex');
  }

  /**
   * 验证微信签名
   */
  checkSignature(signature, timestamp, nonce,gzhconfig) {
    // 从配置中获取令牌
    const token = gzhconfig.token || 'jsjywechat';
    
    // 按字典序排序token、timestamp、nonce
    const tmpArr = [token, timestamp, nonce].sort();
    // 拼接成一个字符串
    const tmpStr = tmpArr.join('');
    // 进行sha1加密
    const sha1Str = crypto.createHash('sha1').update(tmpStr).digest('hex');
    
    // 与signature对比，相同则通过验证
    return sha1Str === signature;
  }
  
  /**
   * 获取原始请求内容
   */
  async getPayload() {
    return new Promise((resolve, reject) => {
      let data = '';
      this.ctx.req.on('data', chunk => {
        data += chunk;
      });
      this.ctx.req.on('end', () => {
        resolve(data);
      });
      this.ctx.req.on('error', err => {
        reject(err);
      });
    });
  }
  
  /**
   * 解析XML消息
   */
  async parseXML(xml) {
    return new Promise((resolve, reject) => {
      xml2js.parseString(xml, { trim: true, explicitArray: false }, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result.xml);
        }
      });
    });
  }
  
  /**
   * 构建XML回复
   */
  buildXML(data) {
    const builder = new xml2js.Builder({
      rootName: 'xml',
      cdata: true,
      headless: true
    });
    return builder.buildObject(data);
  }
  
  /**
   * 处理微信消息
   */
  async handleMessage(message) {
    think.logger.info('收到微信消息:', message);
    
    try {
      // 如果message是xml解析结果，需要进一步处理
      if (message.xml) {
        message = this.normalizeMessage(message.xml);
      }
      
      // 基本回复信息
      const baseReply = {
        ToUserName: message.FromUserName,
        FromUserName: message.ToUserName,
        CreateTime: parseInt(Date.now() / 1000)
      };
      
      switch (message.MsgType) {
        // 文本消息
        case 'text':
          return this.handleTextMessage(message, baseReply);
        
        // 图片消息
        case 'image':
          return this.handleImageMessage(message, baseReply);
        
        // 事件消息
        case 'event':
          return this.handleEventMessage(message, baseReply);
          
        // 其他类型消息
        default:
          return this.buildXML({
            ...baseReply,
            MsgType: 'text',
            Content: '感谢您的关注，目前暂不支持此类消息的处理。'
          });
      }
    } catch (error) {
      think.logger.error('处理微信消息出错:', error);
      return this.buildXML({
        ToUserName: message.FromUserName || '',
        FromUserName: message.ToUserName || '',
        MsgType: 'text',
        CreateTime: parseInt(Date.now() / 1000),
        Content: '系统繁忙，请稍后再试。'
      });
    }
  }
  
  /**
   * 标准化消息对象
   * 将数组形式的属性值转换为单个值
   */
  normalizeMessage(xmlObj) {
    const result = {};
    
    // 遍历xml对象的所有属性
    for (const key in xmlObj) {
      // 如果属性值是数组，取第一个元素
      if (Array.isArray(xmlObj[key]) && xmlObj[key].length > 0) {
        result[key] = xmlObj[key][0];
      } else {
        result[key] = xmlObj[key];
      }
    }
    
    return result;
  }
  
  /**
   * 处理文本消息
   */
  async handleTextMessage(message, baseReply) {
    // 保存用户消息到数据库
    await this.saveUserMessage(message);
    
    // 智能回复逻辑可以在这里实现
    let replyContent = '感谢您的留言，我们会尽快回复您。';
    
    // 如果是特定关键词，可以返回特定内容
    if (message.Content.includes('优惠') || message.Content.includes('活动')) {
      replyContent = '近期活动信息请关注公众号通知，谢谢支持！';
    }
    
    return this.buildXML({
      ...baseReply,
      MsgType: 'text',
      Content: replyContent
    });
  }
  
  /**
   * 处理图片消息
   */
  async handleImageMessage(message, baseReply) {
    // 保存用户消息到数据库
    await this.saveUserMessage(message);
    
    return this.buildXML({
      ...baseReply,
      MsgType: 'text',
      Content: '感谢您的图片，我们已收到。'
    });
  }
  
  /**
   * 处理事件消息
   */
  async handleEventMessage(message, baseReply) {
    const event = message.Event.toUpperCase(); // 确保大写比较
    
    switch (event) {
      // 关注事件
      case 'SUBSCRIBE':
        // 如果是扫描带参数二维码关注
        if (message.EventKey && message.EventKey.startsWith('qrscene_')) {
          const sceneStr = message.EventKey.substring(8); // 去掉 'qrscene_' 前缀
          return this.handleScanSubscribe(message, baseReply, sceneStr);
        }
        // 普通关注
        return this.handleSubscribe(message, baseReply);
      
      // 取消关注
      case 'UNSUBSCRIBE':
        return this.handleUnsubscribe(message, baseReply);
      
      // 已关注用户扫描带参数二维码
      case 'SCAN':
        return this.handleScan(message, baseReply, message.EventKey);
      
      // 点击菜单
      case 'CLICK':
        return this.handleMenuClick(message, baseReply);
        
      // 其他事件类型
      default:
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: '感谢您的互动。'
        });
    }
  }
  
  /**
   * 处理普通关注事件
   */
  async handleSubscribe(message, baseReply) {
    // 保存或更新用户信息
    await this.saveWechatUser(message.FromUserName);
    
    // 欢迎消息
    return this.buildXML({
      ...baseReply,
      MsgType: 'text',
      Content: '感谢您的关注！我们将为您提供最优质的服务。'
    });
  }
  
  /**
   * 处理扫描带参数二维码关注事件
   */
  async handleScanSubscribe(message, baseReply, sceneStr) {
    // 保存或更新用户信息
    await this.saveWechatUser(message.FromUserName);
    
    // 处理场景值，sceneStr格式为 "yx_营销码"
    if (sceneStr.startsWith('yx_')) {
      const yxCode = sceneStr.substring(3);
      
      // 记录扫码事件
      await this.recordYxScan(yxCode, message.FromUserName);
      
      // 查询营销人员信息
      const yxInfo = await this.model('yx').where({
        code: yxCode,
        del_flag: '0'
      }).find();
      
      if (!think.isEmpty(yxInfo)) {
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: `感谢您的关注！您通过${yxInfo.name}的推广二维码关注了我们，我们将为您提供最优质的服务。`
        });
      }
    }
    
    // 默认欢迎消息
    return this.buildXML({
      ...baseReply,
      MsgType: 'text',
      Content: '感谢您的关注！我们将为您提供最优质的服务。'
    });
  }
  
  /**
   * 处理已关注用户扫描带参数二维码事件
   */
  async handleScan(message, baseReply, sceneStr) {
    // 处理场景值，sceneStr格式为 "yx_营销码"
    if (sceneStr.startsWith('yx_')) {
      const yxCode = sceneStr.substring(3);
      
      // 记录扫码事件
      await this.recordYxScan(yxCode, message.FromUserName);
      
      // 查询营销人员信息
      const yxInfo = await this.model('yx').where({
        code: yxCode,
        del_flag: '0'
      }).find();
      
      if (!think.isEmpty(yxInfo)) {
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: `您扫描了${yxInfo.name}的推广二维码，感谢您的支持！`
        });
      }
    }
    
    return this.buildXML({
      ...baseReply,
      MsgType: 'text',
      Content: '感谢您的扫码！'
    });
  }
  
  /**
   * 处理取消关注事件
   */
  async handleUnsubscribe(message, baseReply) {
    // 更新用户状态为未关注
    await this.model('wechat_user').where({
      openid: message.FromUserName
    }).update({
      subscribe: 0,
      update_time: think.datetime()
    });
    
    // 取消关注不需要回复消息
    return 'success';
  }
  
  /**
   * 处理菜单点击事件
   */
  async handleMenuClick(message, baseReply) {
    const eventKey = message.EventKey;
    
    // 记录菜单点击事件
    think.logger.info(`用户 ${message.FromUserName} 点击了菜单: ${eventKey}`);
    
    // 根据不同的菜单项返回不同内容
    switch (eventKey) {
      case 'ABOUT_US':
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: '我们是一家专注于教育行业的创新企业，致力于为用户提供优质的学习服务和资源。'
        });
        
      case 'CONTACT_US':
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: '客服电话：400-123-4567\n工作时间：周一至周五 9:00-18:00\n邮箱：<EMAIL>'
        });
        
      default:
        return this.buildXML({
          ...baseReply,
          MsgType: 'text',
          Content: '感谢您的互动，我们将尽快处理您的请求。'
        });
    }
  }
  
  /**
   * 保存用户消息到数据库
   */
  async saveUserMessage(message) {
    try {
      await this.model('wechat_message').add({
        openid: message.FromUserName,
        msgtype: message.MsgType,
        content: message.MsgType === 'text' ? message.Content : '',
        media_id: message.MsgType === 'image' ? message.MediaId : '',
        msgid: message.MsgId,
        create_time: think.datetime()
      });
    } catch (error) {
      think.logger.error('保存用户消息失败:', error);
    }
  }
  
  /**
   * 保存或更新微信用户信息
   */
  async saveWechatUser(openid) {
    try {
      // 获取用户信息
      const userInfo = await this.getWechatUserInfo(openid);
      
      // 查询用户是否已存在
      const exists = await this.model('wechat_user').where({
        openid: openid
      }).find();
      
      if (think.isEmpty(exists)) {
        // 添加新用户
        await this.model('wechat_user').add({
          openid: userInfo.openid,
          nickname: userInfo.nickname,
          sex: userInfo.sex,
          headimgurl: userInfo.headimgurl,
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          subscribe: userInfo.subscribe,
          subscribe_time: userInfo.subscribe_time,
          create_time: think.datetime(),
          update_time: think.datetime()
        });
      } else {
        // 更新现有用户
        await this.model('wechat_user').where({
          openid: openid
        }).update({
          nickname: userInfo.nickname,
          sex: userInfo.sex,
          headimgurl: userInfo.headimgurl,
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          subscribe: userInfo.subscribe,
          subscribe_time: userInfo.subscribe_time,
          update_time: think.datetime()
        });
      }
    } catch (error) {
      think.logger.error('保存微信用户信息失败:', error);
    }
  }
  
  /**
   * 获取微信用户信息
   */
  async getWechatUserInfo(openid) {
    // 获取access_token
    const accessToken = await this.getWechatAccessToken();
    
    // 调用微信接口获取用户信息
    const axios = require('axios');
    const url = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;
    
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      think.logger.error('获取微信用户信息失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取微信公众号接口调用凭证
   */
  async getWechatAccessToken() {
    // 获取配置的appid和secret
    const { appid, secret } = think.config();
    
    // 检查是否已有缓存的token
    let accessToken = await this.cache('wechat_access_token');
    
    if (!accessToken) {
      // 从微信服务器获取新的token
      const axios = require('axios');
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
      
      try {
        const response = await axios.get(url);
        
        if (response.data && response.data.access_token) {
          accessToken = response.data.access_token;
          // 缓存token（微信token有效期为7200秒，这里设置7000秒）
          await this.cache('wechat_access_token', accessToken, {
            timeout: 7000 * 1000
          });
        } else {
          throw new Error('获取access_token失败: ' + JSON.stringify(response.data));
        }
      } catch (error) {
        think.logger.error('获取微信access_token失败:', error);
        throw error;
      }
    }
    
    return accessToken;
  }
  
  /**
   * 记录营销扫码事件
   */
  async recordYxScan(yxCode, openid) {
    try {
      // 查询营销人员信息
      const yxInfo = await this.model('yx').where({
        code: yxCode,
        del_flag: '0'
      }).find();
      
      let res=await this.model("yx_scan").where({        
        openid:openid,
}).select();


  if(res.length>0){ 
    return ;
  }

      if (!think.isEmpty(yxInfo)) {




        // 记录扫码事件
        await this.model('yx_scan').add({
          yx_id: yxInfo.id,
          openid: openid,
          scan_time: think.datetime(),
          status: 1, // 1: 有效扫码
          remark: '微信公众号扫码'
        });



        await this.model("student").where({openid:openid,"del_flag":0}).update({yx_id:yxInfo.id});
      }
    } catch (error) {
      think.logger.error('记录营销扫码事件失败:', error);
    }
  }
};