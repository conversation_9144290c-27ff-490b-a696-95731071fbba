const crypto = require('crypto');
module.exports = class extends think.Controller {

/**
 * 验证微信支付回调签名
 * @param {Object} data 回调数据对象
 * @param {String} apiKey 微信支付商户密钥
 * @return {Boolean} 签名是否有效
 */
async verifyWxPaySignature(data, apiKey) {
    // 1. 获取原始签名
    const originalSign = data.sign;
    if (!originalSign) {
      console.error('签名字段不存在');
      return false;
    }
    
    // 2. 按照字典序排序参数
    const sortedKeys = Object.keys(data).sort();
    
    // 3. 组装签名字符串
    let signStr = '';
    for (const key of sortedKeys) {
      // 签名字段不参与签名
      if (key !== 'sign') {
        const value = data[key];
        if (value !== undefined && value !== null && value !== '') {
          signStr += `${key}=${value}&`;
        }
      }
    }
    
    // 4. 加上API密钥
    signStr += `key=${apiKey}`;
    
    // 5. 计算MD5签名并转为大写
    const calculatedSign = crypto.createHash('md5')
      .update(signStr, 'utf8')
      .digest('hex')
      .toUpperCase();
    
    // 6. 比较签名
    const isValid = calculatedSign === originalSign;
    if (!isValid) {
      console.error('签名验证失败', {
        original: originalSign,
        calculated: calculatedSign
      });
    }
    
    return isValid;
  }





  






    async wxpayAction(){
        let data = this.post();
        console.log(data);
        const jsonData =await this.convertWxPayXmlToJson(data);


        console.log(jsonData);
        let paymodel=this.model("payment");
        await paymodel.add(jsonData);
        let ordermodel=this.model("order");
        let order=await ordermodel.where({order_no:jsonData.out_trade_no}).find();

      let product=await this.model("product").where({"id":order.product_id}).find();
        const isSignValid = this.verifyWxPaySignature(jsonData,think.config('wxpay.key') );

        try{
        if(order.pay_status==0&&isSignValid){

            if(jsonData.result_code=="SUCCESS"){





              await this.model("product").where({"id":order.product_id}).decrement("quantity",1)
              await this.model("school").where({id:order.schoolid}).decrement("canusetime",product.duration);
              await ordermodel.where({order_no:jsonData.out_trade_no}).update({pay_status:1,order_status:1,pay_time:think.datetime()});
              let vipService = think.service('vip');
              let vipres=     await vipService.addVipRecord({
                userid: order.user_id,
                lessson: product.lesson,
                nj: product.nj,
                allday: order.duration,
                orderid: order.id,
                type:"product"
              });

             console.log("params",{
              userid: order.user_id,
              lessson: product.lesson,
              nj: product.nj,
              allday: order.duration,
              orderid: order.id
            })

              console.log("vipres",vipres)


                // await ordermodel.where({order_no:jsonData.out_trade_no}).update({pay_status:1,order_status:1,pay_time:think.datetime()});
                // let userModel=this.model("student");
                // let user=await userModel.where({id:order.user_id}).find();
                // if(user.type=="免费"){





    
                //     //todate 增加天数
    
                //     let date1    =new Date();
                    
                //     let date2=new Date(date1.getTime() + order.duration * 24 * 60 * 60 * 1000);
    
    
                //      await userModel.where({id:order.user_id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});
                // }else{
                //     let date1    =new Date(user.todate);
                //     if(date1<new Date()){
                //         date1=new Date();
                //     }
                //     let date2=new Date(date1.getTime() + order.duration * 24 * 60 * 60 * 1000);
                //     await userModel.where({id:order.user_id}).update({type:"VIP会员",todate:think.datetime(date2,'YYYY-MM-DD HH:mm:ss')});
    
                // }
            }   
        }
        }catch(e){
            console.log(e);
            return this.ctx.type = 'text/xml', this.ctx.body = `<xml>
  <return_code><![CDATA[FAIL]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>`;
        }
       
         

        


       
        return this.ctx.type = 'text/xml', this.ctx.body = `<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>`;
    }


    async convertWxPayXmlToJson(xmlData){
        const xml = xmlData.xml || xmlData;
        
     
        // 创建结果对象
        const result = {};
        
        // 遍历所有属性
        for (const key in xml) {
            // 微信返回的字段都是数组形式，取第一个元素
            if (Array.isArray(xml[key]) && xml[key].length > 0) {
            result[key] = xml[key][0];
            } else {
            result[key] = xml[key];
            }
        }
        
        return result;
    }
}