import config from "@/config";
import http from "@/utils/request";


const common = {
 
	avatarUpload: {
		url: `${config.API_URL}/common/upload`,
		name: "头像上传",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	upload: {
		url: `${config.API_URL}/common/upload`,
		name: "文件上传",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	dict: {
		url: `${config.API_URL}/common/dict`,
		name: "字典树",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
	util: {
		url: ``,
		name: "post",
		post: async function (url,data) {
			return await http.post(config.API_URL+url, this.name, data);
		}
	},
	
	getconfig :{
		url: `${config.API_URL}/sys/index/getconfig`,
		name: "getconfig",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},saveconfig: {
		url: `${config.API_URL}/sys/index/saveconfig`,
		name: "saveconfig",
		post: async function (data) {
			return await http.post(this.url, this.name, data);
		}
	},
};

export default common;
