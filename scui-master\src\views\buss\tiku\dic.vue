<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="330" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" ref="dialogForm" label-width="80px" label-position="left">


            <el-form-item label="名称" prop="name">
					<el-input
						v-model="form.name"
						clearable
						placeholder="名称"
					></el-input>

				</el-form-item>

                <el-form-item label="编号" prop="code">
					<el-input
						v-model="form.code"
						clearable
						placeholder="编号"
					></el-input>

				</el-form-item>
				<el-form-item label="上级" prop="parent">
					<el-cascader
						v-model="form.parent"
						:options="menuList"
						:props="menuProps"
						:show-all-levels="false"
						clearable
					></el-cascader>
					<div class="el-form-item-msg">

					</div>
				</el-form-item>

				 

                <el-form-item label="是否关联" prop="meta.ifgl">
					<el-checkbox v-model="form.ifgl">是否关联</el-checkbox>
				</el-form-item>

				<el-form-item label="排序" prop="sort">
					<el-input-number v-model="form.sort" controls-position="right" :min="1"
					                 style="width: 100%;"></el-input-number>
				</el-form-item>


		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			mode: "add",
			titleMap: {
				add: '新增字典',
				edit: '编辑字典'
			},
			visible: false,
			isSaveing: false,
            menuList:[],
			form: {
				id: "",
			
				pid: "",
			

                level:"",
				parent: "",
				name: "",
				type: "",
				master: "",
				people_number: 0,
				people_outside: 0,
				sort: 1,
                ifgl:0,

			},
			rules: {
				name: [
					{required: true, message: "请输入", trigger: "blur"}
				],
			

                sort: [
					{required: true, message: "请输入", trigger: "blur"}
				],
                code: [
					{required: true, message: "请输入", trigger: "blur"}
				],
			
			
			
				"parent": [
					{required: true, trigger: "blur", message: "请选择"}
				]
			},
			dict: [],
			dicProps: {
				value: "id",
				label: "name",
				checkStrictly: true,
				emitPath: false
			},
            props: {
                menu: {
                    type: Object, default: () => {
                    }
                }
            }
            ,

            menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true
			},
		}
	},
	mounted() {
		this.getDic();
        this.getOffice();
	},
	methods: {

        async getOffice(){
				var res = await this.$API.lesson.list.get();
				this.menuList = res;
			},

		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this;
		},
		//获取字典列表
		async getDic() {
			var res = await this.$API.dict.list.get();
			this.dict = res.data;
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {
				if (valid) {
					this.isSaveing = true;
					 
                    let res = await this.$API.lesson.save.post(this.form);
					this.isSaveing = false;
					if (res.state == 1) {
						await this.$TOOL.dictInit();
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.msg, "提示", {type: 'error'})
					}
				}
			})
		},
		//表单注入数据
		setData(data, mode) {
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
			this.mode = mode;
		}
	}
}
</script>

<style>
</style>
