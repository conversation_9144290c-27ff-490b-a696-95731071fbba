<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="500" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px" label-position="top">
			
			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="组件name" prop="label">
						<el-input v-model="form.name" clearable></el-input>
					</el-form-item>
				</el-col>
			
			</el-row>

            	<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="DOM ID" prop="domid">
						<el-input v-model="form.domid" clearable></el-input>
					</el-form-item>
				</el-col>
			
			</el-row>

            	<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="说明" prop="content">
						<el-input
  type="textarea"
  :rows="2"
  placeholder="说明"
  v-model="form.content">
</el-input>
					</el-form-item>
				</el-col>
			
			</el-row>


	<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="提示位置" prop="pos">
		<sc-select v-model="form.pos"
											           :selectConfig="selectConfig.pos"
											           clearable dic="提示位置"
											           filterable ></sc-select>
					</el-form-item>
				</el-col>
			
			</el-row>

			<el-row :gutter="20">
				
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="form.s" controls-position="right" :min="1" style="width: 100%;"></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<el-button @click="visible=false" >取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
	export default {
		emits: ['success', 'closed'],
		data() {
			return {
                   selectConfig: {
				pos: {
					label: 'name',
					value: 'key',
				},
               
                
                },
				mode: "add",
				titleMap: {
					add: '新增',
					edit: '编辑',
					show: '查看'
				},
				visible: false,
				isSaveing: false,
				//表单数据
				form: {
					id:"",
					name: "",
					content: "",
                    pos:"",
                    domid:"",
                    s:""
				},
				//验证规则
				rules: {
					
					name: [
						{required: true, message: '请输入名称'}
					],

					
				},
				//所需数据选项
				groups: [],
				groupsProps: {
					value: "id",
					checkStrictly: true
				}
			}
		},
		mounted() {
			
		},
		methods: {
			//显示
			open(mode='add'){
				this.mode = mode;
				this.visible = true;
				return this
			},
		
			//表单提交方法
			submit(){
				this.$refs.dialogForm.validate(async (valid) => {
					if (valid) {
						this.isSaveing = true;
						var res = await this.$API.help.save.post(this.form);
						this.isSaveing = false;
						if(res.code == 200){
							this.$emit('success', this.form, this.mode)
							this.visible = false;
							this.$message.success("操作成功")
						}else{
							this.$alert(res.message, "提示", {type: 'error'})
						}
					}
				})
			},
			//表单注入数据
			setData(data){
				this.form.id = data.id
				this.form.name = data.name
                this.form.pos = data.pos
                this.form.content = data.content
                this.form.domid = data.domid
                this.form.s=data.s
				
				//可以和上面一样单个注入，也可以像下面一样直接合并进去
				//Object.assign(this.form, data)
			}
		}
	}
</script>

<style>
</style>
