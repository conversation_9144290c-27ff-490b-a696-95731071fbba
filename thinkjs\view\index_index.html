<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>New ThinkJS Application</title>
<style>
*{padding: 0;margin: 0;font-size: 16px;line-height: 20px;font-family: arial;}
a, a:visited{color:#337ab7;text-decoration: none;}
header{padding: 70px 0 70px 0;background-color: #4A6495}
h1{font-size: 36px;color:#fff;font-weight: normal;}
code{  padding: 2px 4px;font-size: 90%;color: #c7254e;background-color: #f9f2f4;border-radius: 4px;}
.content{width: 1000px;margin: auto}
.wrap{width: 1000px;margin: auto}
.content{margin-top: 80px;}
.list{width: 800px;}
.list .item{position: relative;padding-left: 70px;margin-top: 50px;}
.list .item .step{position: absolute;width: 36px;height: 36px;top:-3px;left:0;border: 5px solid #4A6495;border-radius: 23px;text-align: center;line-height: 36px;}
.list .item h2{font-size: 24px;font-weight: normal;}
.list .item p{line-height: 30px;margin-top: 10px}
</style>
</head>
<body>
  <header>
    <div class="wrap">
      <h1>A New App Created By ThinkJS</h1>
    </div>
  </header>
  <div class="content">
    <div class="list">
      <div class="item">
        <div class="step">1</div>
        <h2>Generate Files</h2>
        <p>Run <code>thinkjs</code> command to create module, controler, model, service and so on. </p>
      </div>
      <div class="item">
        <div class="step">2</div>
        <h2>Documentation</h2>
        <p>ThinkJS has online html documents. visit <a href="https://thinkjs.org/doc.html">https://thinkjs.org/doc.html</a>.</p>
      </div>
      <div class="item">
        <div class="step">3</div>
        <h2>GitHub</h2>
        <p>If you have some questions, please <a href="https://github.com/thinkjs/thinkjs/issues">new a issue</a>.</p>
      </div>
    </div>
  </div>
</body>
</html>
