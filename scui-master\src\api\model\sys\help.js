import config from "@/config";
import http from "@/utils/request";

const help = {

	list: {
		url: `${config.API_URL}/sys/help/list`,
		name: "列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

    gethelp: {
		url: `${config.API_URL}/sys/help/gethelp`,
		name: "获取参数",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

    
    updatestate: {
		url: `${config.API_URL}/sys/help/updatestate`,
		name: "完成帮助信息",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},

	remove: {
		url: `${config.API_URL}/sys/help/remove`,
		name: "删除帮助信息",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},

	save: {
		url: `${config.API_URL}/sys/help/save`,
		name: "添加帮助信息",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
	
};
export default help;
