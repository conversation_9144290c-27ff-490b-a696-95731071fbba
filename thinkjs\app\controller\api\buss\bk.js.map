{"version": 3, "sources": ["..\\..\\..\\..\\src\\controller\\api\\buss\\bk.js"], "names": ["BaseRest", "require", "module", "exports", "removeAction", "id", "post", "model", "where", "update", "delete", "json", "tkpage3Action", "respData", "userInfo", "session", "page", "get", "rows", "JSON", "parse", "think", "isEmpty", "lessonid", "code", "count", "data", "msg", "start", "parseInt", "end", "lession_id", "split", "order", "orderstr", "sql2", "ifjj", "console", "log", "schoolid", "response", "alias", "join", "field", "countSelect", "tkpage2Action", "infoAction", "find", "tkpageAction", "tmpsubmitAction", "allParams", "arr", "tkids", "para", "obj", "list", "tkid", "bkid", "bktype", "push", "addMany", "gettoplessonAction", "res", "select", "submitAction", "datetime", "getlistAction", "saveAction", "colums", "c", "lesson", "lessonname", "item", "name", "teacherid", "state", "create_date", "add", "listAction", "where2", "keyword", "prop", "replace", "schoolres", "map", "skdate", "recordAction", "recordlistAction"], "mappings": ";;AAAA,MAAMA,WAAWC,QAAQ,YAAR,CAAjB;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,QAAd,CAAuB;;AAE9BI,oBAAN,GAAqB;AAAA;;AAAA;AACjB,8BAAMC,KAAK,MAAKC,IAAL,CAAU,IAAV,CAAX;;AAEA,8BAAMC,QAAQ,MAAKA,KAAL,CAAW,IAAX,CAAd;AACA,8BAAMA,MAAMC,KAAN,CAAY,EAAC,MAAMH,EAAP,EAAZ,EAAwBI,MAAxB,CAA+B,EAAC,YAAY,CAAb,EAA/B,CAAN;;AAEA,8BAAM,MAAKF,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAC,QAAOH,EAAR,EAA5B,EAAyCK,MAAzC,EAAN;AACA,8BAAM,MAAKH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAC,QAAOH,EAAR,EAA5B,EAAyCK,MAAzC,EAAN;;AAEA,+BAAO,MAAKC,IAAL,CAAU,EAAC,QAAQ,GAAT,EAAV,CAAP;AATiB;AAUlB;;AAGKC,qBAAN,GAAsB;AAAA;;AAAA;AACpB,4BAAIC,WAAW,EAAf;;AAEA,8BAAMC,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;;AAEA,8BAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,IAAmB,OAAKA,GAAL,CAAS,MAAT,CAAnB,GAAsC,CAAnD;AACA,8BAAMC,OAAO,EAAb;AACA,8BAAMV,QAAQ,OAAKS,GAAL,CAAS,OAAT,IAAoBE,KAAKC,KAAL,CAAW,OAAKH,GAAL,CAAS,OAAT,CAAX,CAApB,GAAoD,EAAlE;AACA,8BAAMV,QAAQ,OAAKA,KAAL,CAAW,WAAX,CAAd;AACAC,8BAAM,YAAN,IAAsB,CAAtB;;AAGA,4BAAGa,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAH,EAAiC;AAC/B,oCAAKV,WAAW;AACVW,8CAAM,GADI;AAEVC,+CAAO,CAFG;AAGVC,8CAAK,EAHK;AAIVC,6CAAK;AAJK,iCAAhB;AAMI,uCAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AAEL;;AAED,4BAAG,CAACQ,MAAMC,OAAN,CAAcd,MAAMoB,KAApB,CAAJ,EAA+B;AAC3B,oCAAIA,QAAMpB,MAAMoB,KAAhB;AACA,uCAAOpB,MAAMoB,KAAb;AACApB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASD,KAAT,CAAN,CAAjB;AACH;;AAED,4BAAG,CAACP,MAAMC,OAAN,CAAcd,MAAMsB,GAApB,CAAJ,EAA6B;;AAEzB,oCAAIA,MAAItB,MAAMsB,GAAd;AACA,uCAAOtB,MAAMsB,GAAb;AACAtB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASC,GAAT,CAAN,CAAjB;AACH;;AAGD,4BAAG,CAACT,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAJ,EAAkC;;AAE9B,oCAAIQ,aAAWvB,MAAMe,QAAN,CAAeS,KAAf,CAAqB,GAArB,CAAf;AACA,uCAAOxB,MAAMe,QAAb;AACAf,sCAAM,YAAN,IAAoB,CAAC,IAAD,EAAMuB,UAAN,CAApB;AACH;;AAKD,4BAAG,OAAKd,GAAL,CAAS,OAAT,KAAmB,CAAnB,IAAsB,OAAKA,GAAL,CAAS,OAAT,KAAmB,CAA5C,EAA8C;AAC5CT,sCAAM,SAAN,IAAiB,OAAKS,GAAL,CAAS,OAAT,CAAjB;AACD;;AAED,4BAAIgB,QAAM,aAAV;AACA,4BAAIC,WAAS,OAAKjB,GAAL,CAAS,OAAT,CAAb;AACA,4BAAGiB,YAAU,YAAb,EAA0B;AACxBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,OAA7B;AAED;;AAED,4BAAGiB,YAAU,WAAb,EAAyB;AACvBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,MAA7B;AAED;;AAEDT,8BAAM,QAAN,IAAgB,CAAC,GAAD,EAAK,CAAL,CAAhB;;AAGA,4BAAI2B,OAAK,EAAT;;AAEA,4BAAG,CAACd,MAAMC,OAAN,CAAcd,MAAM4B,IAApB,CAAJ,EAA8B;AAC5BC,wCAAQC,GAAR,CAAY9B,KAAZ;;AAEA,oCAAGA,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAClBD,+CAAK,+CAAL;AACF,iCAFD,MAEO,IAAG3B,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAC1BD,+CAAK,2CAAL;AACD;AAEF;AACD,+BAAO3B,MAAM4B,IAAb;;AAGA5B,8BAAM,YAAN,IAAoBM,SAASyB,QAA7B;AACA,8BAAMC,WAAW,MAAMjC,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,yBAAtB,EAAiD1B,IAAjD,CAAsDA,IAAtD,EAA4DE,IAA5D,EAAkEV,KAAlE,CAAwEA,KAAxE,EAA+EA,KAA/E,CAAqF2B,IAArF,EAA2FQ,KAA3F,CAAiG,oBAAjG,EAAuHV,KAAvH,CAA6HA,KAA7H,EAAoIW,WAApI,EAAvB;AACA/B,mCAAW;AACTW,sCAAM,GADG;AAETC,uCAAOe,SAASf,KAFP;AAGTC,sCAAMc,SAASd,IAHN;AAITC,qCAAK;AAJI,yBAAX;AAMA,+BAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AAzFoB;AA0FrB;;AAQKgC,qBAAN,GAAsB;AAAA;;AAAA;AACpB,4BAAIhC,WAAW,EAAf;;AAEA,8BAAMC,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;;AAEA,8BAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,IAAmB,OAAKA,GAAL,CAAS,MAAT,CAAnB,GAAsC,CAAnD;AACA,8BAAMC,OAAO,EAAb;AACA,8BAAMV,QAAQ,OAAKS,GAAL,CAAS,OAAT,IAAoBE,KAAKC,KAAL,CAAW,OAAKH,GAAL,CAAS,OAAT,CAAX,CAApB,GAAoD,EAAlE;AACA,8BAAMV,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACAC,8BAAM,YAAN,IAAsB,CAAtB;;AAGA,4BAAGa,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAH,EAAiC;AAC/B,oCAAKV,WAAW;AACVW,8CAAM,GADI;AAEVC,+CAAO,CAFG;AAGVC,8CAAK,EAHK;AAIVC,6CAAK;AAJK,iCAAhB;AAMI,uCAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AAEL;;AAED,4BAAG,CAACQ,MAAMC,OAAN,CAAcd,MAAMoB,KAApB,CAAJ,EAA+B;AAC3B,oCAAIA,QAAMpB,MAAMoB,KAAhB;AACA,uCAAOpB,MAAMoB,KAAb;AACApB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASD,KAAT,CAAN,CAAjB;AACH;;AAED,4BAAG,CAACP,MAAMC,OAAN,CAAcd,MAAMsB,GAApB,CAAJ,EAA6B;;AAEzB,oCAAIA,MAAItB,MAAMsB,GAAd;AACA,uCAAOtB,MAAMsB,GAAb;AACAtB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASC,GAAT,CAAN,CAAjB;AACH;;AAGD,4BAAG,CAACT,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAJ,EAAkC;;AAE9B,oCAAIQ,aAAWvB,MAAMe,QAAN,CAAeS,KAAf,CAAqB,GAArB,CAAf;AACA,uCAAOxB,MAAMe,QAAb;AACAf,sCAAM,YAAN,IAAoB,CAAC,IAAD,EAAMuB,UAAN,CAApB;AACH;;AAGH,4BAAG,CAACV,MAAMC,OAAN,CAAcd,KAAd,CAAD,IAAuB,CAACa,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAA3B,EAAyD;AACvD;;AAED;;AAEC,4BAAG,OAAKN,GAAL,CAAS,OAAT,KAAmB,CAAnB,IAAsB,OAAKA,GAAL,CAAS,OAAT,KAAmB,CAA5C,EAA8C;AAC5CT,sCAAM,SAAN,IAAiB,OAAKS,GAAL,CAAS,OAAT,CAAjB;AACD;;AAEDT,8BAAM,YAAN,IAAoBM,SAASyB,QAA7B;;AAEA,4BAAIN,QAAM,aAAV;AACA,4BAAIC,WAAS,OAAKjB,GAAL,CAAS,OAAT,CAAb;AACA,4BAAGiB,YAAU,YAAb,EAA0B;AACxBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,OAA7B;AAED;;AAED,4BAAGiB,YAAU,WAAb,EAAyB;AACvBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,MAA7B;AAED;;AAGD,4BAAIkB,OAAK,EAAT;;AAEA,4BAAG,CAACd,MAAMC,OAAN,CAAcd,MAAM4B,IAApB,CAAJ,EAA8B;AAC5BC,wCAAQC,GAAR,CAAY9B,KAAZ;;AAEA,oCAAGA,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAClBD,+CAAK,+CAAL;AACF,iCAFD,MAEO,IAAG3B,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAC1BD,+CAAK,2CAAL;AACD;AAEF;AACD,+BAAO3B,MAAM4B,IAAb;;AAEA,8BAAMI,WAAW,MAAMjC,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,yBAAtB,EAAiD1B,IAAjD,CAAsDA,IAAtD,EAA4DE,IAA5D,EAAkEV,KAAlE,CAAwEA,KAAxE,EAA+EA,KAA/E,CAAqF2B,IAArF,EAA2FQ,KAA3F,CAAiG,mBAAjG,EAAsHV,KAAtH,CAA4HA,KAA5H,EAAmIW,WAAnI,EAAvB;AACA/B,mCAAW;AACTW,sCAAM,GADG;AAETC,uCAAOe,SAASf,KAFP;AAGTC,sCAAMc,SAASd,IAHN;AAITC,qCAAK;AAJI,yBAAX;AAMA,+BAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AA1FoB;AA2FrB;;AAKKiC,kBAAN,GAAkB;AAAA;;AAAA;;AAEhB,4BAAIjC,WAAW,EAAf;AACA,8BAAMR,KAAK,OAAKC,IAAL,CAAU,IAAV,IAAkB,OAAKA,IAAL,CAAU,IAAV,CAAlB,GAAoC,IAA/C;AACA,8BAAMC,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACAM,mCAAY,MAAMN,MAAMC,KAAN,CAAY,EAACH,IAAIA,EAAL,EAAZ,EAAsB0C,IAAtB,EAAlB;;AAEA,+BAAO,OAAKpC,IAAL,CAAUE,QAAV,CAAP;AAPgB;AAQjB;;AAMKmC,oBAAN,GAAqB;AAAA;;AAAA;AACnB,4BAAInC,WAAW,EAAf;;AAEA,8BAAMC,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;;AAEA,8BAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,IAAmB,OAAKA,GAAL,CAAS,MAAT,CAAnB,GAAsC,CAAnD;AACA,8BAAMC,OAAO,EAAb;AACA,8BAAMV,QAAQ,OAAKS,GAAL,CAAS,OAAT,IAAoBE,KAAKC,KAAL,CAAW,OAAKH,GAAL,CAAS,OAAT,CAAX,CAApB,GAAoD,EAAlE;AACA,8BAAMV,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACAC,8BAAM,YAAN,IAAsB,CAAtB;;AAGA,4BAAGa,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAH,EAAiC;AAC/B,oCAAKV,WAAW;AACVW,8CAAM,GADI;AAEVC,+CAAO,CAFG;AAGVC,8CAAK,EAHK;AAIVC,6CAAK;AAJK,iCAAhB;AAMI,uCAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AAEL;;AAKD,4BAAG,CAACQ,MAAMC,OAAN,CAAcd,MAAMoB,KAApB,CAAJ,EAA+B;AAC3B,oCAAIA,QAAMpB,MAAMoB,KAAhB;AACA,uCAAOpB,MAAMoB,KAAb;AACApB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASD,KAAT,CAAN,CAAjB;AACH;;AAED,4BAAG,CAACP,MAAMC,OAAN,CAAcd,MAAMsB,GAApB,CAAJ,EAA6B;;AAEzB,oCAAIA,MAAItB,MAAMsB,GAAd;AACA,uCAAOtB,MAAMsB,GAAb;AACAtB,sCAAM,SAAN,IAAiB,CAAC,IAAD,EAAMqB,SAASC,GAAT,CAAN,CAAjB;AACH;;AAGD,4BAAG,CAACT,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAAJ,EAAkC;;AAE9B,oCAAIQ,aAAWvB,MAAMe,QAAN,CAAeS,KAAf,CAAqB,GAArB,CAAf;AACA,uCAAOxB,MAAMe,QAAb;AACAf,sCAAM,YAAN,IAAoB,CAAC,IAAD,EAAMuB,UAAN,CAApB;AACH;;AAGH,4BAAG,CAACV,MAAMC,OAAN,CAAcd,KAAd,CAAD,IAAuB,CAACa,MAAMC,OAAN,CAAcd,MAAMe,QAApB,CAA3B,EAAyD;AACvD;;AAED;;AAEC,4BAAG,OAAKN,GAAL,CAAS,OAAT,KAAmB,CAAnB,IAAsB,OAAKA,GAAL,CAAS,OAAT,KAAmB,CAA5C,EAA8C;AAC5CT,sCAAM,SAAN,IAAiB,OAAKS,GAAL,CAAS,OAAT,CAAjB;AACD;;AAED,4BAAIgB,QAAM,aAAV;AACA,4BAAIC,WAAS,OAAKjB,GAAL,CAAS,OAAT,CAAb;AACA,4BAAGiB,YAAU,YAAb,EAA0B;AACxBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,OAA7B;AAED;;AAED,4BAAGiB,YAAU,WAAb,EAAyB;AACvBD,wCAAM,QAAM,OAAKhB,GAAL,CAAS,MAAT,CAAN,GAAuB,MAA7B;AAED;AACD,4BAAIkB,OAAK,EAAT;;AAEA,4BAAG,CAACd,MAAMC,OAAN,CAAcd,MAAM4B,IAApB,CAAJ,EAA8B;AAC5BC,wCAAQC,GAAR,CAAY9B,KAAZ;;AAEA,oCAAGA,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAClBD,+CAAK,+CAAL;AACF,iCAFD,MAEO,IAAG3B,MAAM4B,IAAN,IAAY,KAAf,EAAqB;AAC1BD,+CAAK,2CAAL;AACD;AAEF;AACD,+BAAO3B,MAAM4B,IAAb;;AAEA,8BAAMI,WAAW,MAAMjC,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,2DAAyD5B,SAASyB,QAAlE,GAA2E,oBAAjG,EAAuHvB,IAAvH,CAA4HA,IAA5H,EAAkIE,IAAlI,EAAwIV,KAAxI,CAA8IA,KAA9I,EAAqJA,KAArJ,CAA2J2B,IAA3J,EAAiKQ,KAAjK,CAAuK,YAAvK,EAAqLV,KAArL,CAA2LA,KAA3L,EAAkMW,WAAlM,EAAvB;AACA/B,mCAAW;AACTW,sCAAM,GADG;AAETC,uCAAOe,SAASf,KAFP;AAGTC,sCAAMc,SAASd,IAHN;AAITC,qCAAK;AAJI,yBAAX;AAMA,+BAAO,OAAKhB,IAAL,CAAUE,QAAV,CAAP;AAzFmB;AA0FpB;;AAIKoC,uBAAN,GAAuB;AAAA;;AAAA;AACrB,8BAAMnC,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;AACA,8BAAMmC,YAAY,OAAK5C,IAAL,EAAlB;AACA+B,gCAAQC,GAAR,CAAYY,SAAZ;AACA,4BAAI3C,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,4BAAI4C,MAAI,EAAR;AACA,4BAAIC,QAAM,OAAK9C,IAAL,CAAU,KAAV,CAAV;;AAEA,4BAAI+C,OAAKlC,KAAKC,KAAL,CAAWgC,KAAX,CAAT;;AAEA,6BAAI,IAAIE,GAAR,IAAeD,KAAKE,IAApB,EAAyB;AACvB,oCAAG,CAAClC,MAAMC,OAAN,CAAcgC,IAAIjD,EAAlB,CAAJ,EAA0B;AACxB,4CAAIqB,OAAK,EAAT;AACAW,gDAAQC,GAAR,CAAYgB,IAAIjD,EAAhB;;AAEAqB,6CAAK8B,IAAL,GAAUF,IAAIjD,EAAd;;AAIAqB,6CAAK+B,IAAL,GAAU,OAAKnD,IAAL,CAAU,IAAV,CAAV;AACAoB,6CAAKa,QAAL,GAAczB,SAASyB,QAAvB;AACAb,6CAAKgC,MAAL,GAAYJ,IAAII,MAAhB;AACAP,4CAAIQ,IAAJ,CAASjC,IAAT;AAED;AAGF;;AAEDW,gCAAQC,GAAR,CAAaa,GAAb;;AAEA,8BAAM5C,MAAMC,KAAN,CAAY,EAACiD,MAAK,OAAKnD,IAAL,CAAU,IAAV,CAAN,EAAZ,EAAoCI,MAApC,EAAN;AACA,8BAAMH,MAAMqD,OAAN,CAAcT,GAAd,CAAN;;AAEA,+BAAO,OAAKxC,IAAL,CAAU,EAAC,QAAQ,GAAT,EAAc,WAAW,OAAzB,EAAkC,cAAc,IAAhD,EAAsD,SAAS,CAA/D,EAAV,CAAP;AAlCqB;AAoCtB;;AAKKkD,0BAAN,GAA0B;AAAA;;AAAA;AACxB,8BAAM/C,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;AACA,4BAAIR,QAAQ,OAAKA,KAAL,CAAW,aAAX,CAAZ;AACA,4BAAIuD,MAAI,MAAMvD,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,mCAAtB,EAA2DC,KAA3D,CAAiE,aAAjE,EAAgFnC,KAAhF,CAAsF,EAAC,WAAUM,SAAST,EAApB,EAAtF,EAA+G0D,MAA/G,EAAd;;AAGA,+BAAO,OAAKpD,IAAL,CAAUmD,GAAV,CAAP;AANwB;AAWzB;;AAEKE,oBAAN,GAAoB;AAAA;;AAAA;AAClB,8BAAMlD,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;AACA,8BAAMmC,YAAY,OAAK5C,IAAL,EAAlB;AACA+B,gCAAQC,GAAR,CAAYY,SAAZ;AACA,4BAAI3C,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,4BAAI4C,MAAI,EAAR;AACA,4BAAIC,QAAM,OAAK9C,IAAL,CAAU,KAAV,CAAV;AACA,4BAAI+C,OAAKlC,KAAKC,KAAL,CAAWgC,KAAX,CAAT;;AAEA,6BAAI,IAAIE,GAAR,IAAeD,KAAKE,IAApB,EAAyB;AACvB,oCAAG,CAAClC,MAAMC,OAAN,CAAcgC,IAAIjD,EAAlB,CAAJ,EAA0B;AACxB,4CAAIqB,OAAK,EAAT;AACAW,gDAAQC,GAAR,CAAYgB,IAAIjD,EAAhB;;AAEAqB,6CAAK8B,IAAL,GAAUF,IAAIjD,EAAd;;AAIAqB,6CAAK+B,IAAL,GAAU,OAAKnD,IAAL,CAAU,IAAV,CAAV;AACAoB,6CAAKa,QAAL,GAAczB,SAASyB,QAAvB;AACAb,6CAAKgC,MAAL,GAAYJ,IAAII,MAAhB;AACAP,4CAAIQ,IAAJ,CAASjC,IAAT;AAED;AAGF;;AAED,8BAAMnB,MAAMC,KAAN,CAAY,EAACiD,MAAK,OAAKnD,IAAL,CAAU,IAAV,CAAN,EAAZ,EAAoCI,MAApC,EAAN;AACA,8BAAMH,MAAMqD,OAAN,CAAcT,GAAd,CAAN;AACA,8BAAM,OAAK5C,KAAL,CAAW,IAAX,EAAiBC,KAAjB,CAAuB,EAAC,MAAK,OAAKF,IAAL,CAAU,IAAV,CAAN,EAAvB,EAA+CG,MAA/C,CAAsD,EAAC,SAAQ,CAAT,EAAW,UAASY,MAAM4C,QAAN,EAApB,EAAtD,CAAN;AACA,8BAAM,OAAK1D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAC,QAAO,OAAKF,IAAL,CAAU,IAAV,CAAR,EAA5B,EAAsDI,MAAtD,EAAN;AACA,+BAAO,OAAKC,IAAL,CAAU,EAAC,QAAQ,GAAT,EAAc,WAAW,OAAzB,EAAkC,cAAc,IAAhD,EAAsD,SAAS,CAA/D,EAAV,CAAP;AAhCkB;AAkCnB;;AAGKuD,qBAAN,GAAqB;AAAA;;AAAA;AACnB,8BAAMpD,WAAW,MAAM,OAAKC,OAAL,CAAa,UAAb,CAAvB;AACA,4BAAIR,QAAM,OAAKA,KAAL,CAAW,SAAX,CAAV;AACA,4BAAIkD,OAAK,OAAKxC,GAAL,CAAS,IAAT,CAAT;AACA,4BAAIT,QAAM,EAAC,UAASiD,IAAV,EAAV;AACA,4BAAIK,MAAI,MAAMvD,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,mCAAtB,EACbA,IADa,CACR,4DAA0D5B,SAASyB,QAAnE,GAA4E,uBADpE,EAEb/B,KAFa,CAEPA,KAFO,EAEAmC,KAFA,CAEM,2CAFN,EAGboB,MAHa,EAAd;AAIA,+BAAO,OAAKpD,IAAL,CAAUmD,GAAV,CAAP;AATmB;AAYpB;;AAIGK,kBAAN,GAAmB;AAAA;;AAAA;AACf,8BAAMC,SAAS,CAAC,OAAD,EAAU,SAAV,EAAqB,YAArB,EAAmC,QAAnC,EAA6C,SAA7C,EAAwD,UAAxD,EAAoE,MAApE,EAA4E,WAA5E,EAAyF,UAAzF,EAAoG,UAApG,EAA+G,IAA/G,EAAoH,QAApH,EAA6H,WAA7H,CAAf;AACA,8BAAMlB,YAAY,QAAK5C,IAAL,EAAlB;AACA,8BAAMoB,OAAO,EAAb;AACA,6BAAK,IAAI2C,CAAT,IAAcD,MAAd,EAAsB;AACpB,oCAAIC,KAAKnB,SAAT,EAAoB;AAClBxB,6CAAK2C,CAAL,IAAUnB,UAAUmB,CAAV,CAAV;AACD;AACF;;AAGD,8BAAM9D,QAAQ,QAAKA,KAAL,CAAW,IAAX,CAAd;;AAGA,4BAAI+D,SAAO5C,KAAK4C,MAAhB;AACAjC,gCAAQC,GAAR,CAAYgC,MAAZ;;AAEA,4BAAInB,MAAImB,OAAOtC,KAAP,CAAa,GAAb,CAAR;AACA,4BAAIuC,aAAW,EAAf;AACA,6BAAI,IAAIC,IAAR,IAAgBrB,GAAhB,EAAoB;;AAElB,oCAAImB,SAAO,MAAM,QAAK/D,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B,EAACH,IAAGmE,IAAJ,EAA3B,EAAsCzB,IAAtC,EAAjB;AACAwB,2CAAWZ,IAAX,CAAgBW,OAAOG,IAAvB;AAED;;AAED/C,6BAAK6C,UAAL,GAAgBA,WAAW7B,IAAX,CAAgB,GAAhB,CAAhB;;AAGA,4BAAI,QAAKpC,IAAL,CAAU,IAAV,CAAJ,EAAqB;;AAEnB,sCAAMC,MAAMC,KAAN,CAAY,EAACH,IAAI,QAAKC,IAAL,CAAU,IAAV,CAAL,EAAZ,EAAmCG,MAAnC,CAA0CiB,IAA1C,CAAN;;AAEA,uCAAO,QAAKf,IAAL,CAAU,EAAC,QAAQ,GAAT,EAAc,WAAW,OAAzB,EAAkC,cAAc,IAAhD,EAAsD,QAAQ,CAA9D,EAAV,CAAP;AACD,yBALD,MAKO;;AAEH,sCAAMG,WAAW,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAvB;AACAW,qCAAKgD,SAAL,GAAe5D,SAAST,EAAxB;AACAqB,qCAAKa,QAAL,GAAczB,SAASyB,QAAvB;AACAb,qCAAKiD,KAAL,GAAW,CAAX;AACAjD,qCAAKkD,WAAL,GAAiBvD,MAAM4C,QAAN,EAAjB;;AAEF,sCAAM1D,MAAMsE,GAAN,CAAUnD,IAAV,CAAN;;AAEA,uCAAO,QAAKf,IAAL,CAAU,EAAC,QAAQ,GAAT,EAAc,WAAW,OAAzB,EAAkC,cAAc,IAAhD,EAAsD,QAAQ,CAA9D,EAAV,CAAP;AACD;AA7Cc;AA8ChB;;AAICmE,kBAAN,GAAmB;AAAA;;AAAA;AACjB,4BAAI7C,QAAQ,oBAAZ;AACA,8BAAMjB,OAAO,QAAKC,GAAL,CAAS,MAAT,CAAb;AACA,8BAAMC,OAAO,QAAKD,GAAL,CAAS,UAAT,CAAb;AACA,8BAAM8D,SAAS,QAAK9D,GAAL,CAAS,OAAT,IAAoBE,KAAKC,KAAL,CAAW,QAAKH,GAAL,CAAS,OAAT,CAAX,CAApB,GAAoD,EAAnE;AACA,8BAAM+D,UAAU,QAAK/D,GAAL,CAAS,MAAT,CAAhB;AACA,8BAAMgE,OAAO,QAAKhE,GAAL,CAAS,MAAT,CAAb;AACA,4BAAI,CAACI,MAAMC,OAAN,CAAc2D,IAAd,CAAD,IAAsB,CAAC5D,MAAMC,OAAN,CAAc,QAAKL,GAAL,CAAS,OAAT,CAAd,CAA3B,EAA6D;AAC3DgB,wCAAQgD,OAAO,GAAP,GAAa,QAAKhE,GAAL,CAAS,OAAT,EAAkBiE,OAAlB,CAA0B,QAA1B,EAAoC,EAApC,CAArB;AACD;;AAED,8BAAM3E,QAAQ,QAAKA,KAAL,CAAW,IAAX,CAAd;AACA,8BAAMC,QAAQ,EAAC,cAAc,CAAf,EAAd;AACA,4BAAI,CAACa,MAAMC,OAAN,CAAc0D,OAAd,CAAL,EAA6B;AAC3BxE,sCAAM,SAAN,IAAmB,CAAC,MAAD,EAAS,MAAMwE,OAAN,GAAgB,GAAzB,CAAnB;AACD;;AAED,8BAAMlE,WAAW,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAvB;;AAEAsB,gCAAQC,GAAR,CAAYxB,QAAZ;;AAEA,4BAAGA,SAAS2D,IAAT,IAAe,OAAlB,EAA0B;;AAEtB,oCAAIU,YAAU,MAAM,QAAK5E,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B,EAAC,OAAMM,SAAST,EAAhB,EAA3B,EAAgD0D,MAAhD,EAApB;;AAEA,oCAAG1C,MAAMC,OAAN,CAAc6D,SAAd,CAAH,EAA4B;AACxB3E,8CAAM,aAAN,IAAqBM,SAAST,EAA9B;AAEH,iCAHD,MAGK;;AAEDG,8CAAM,YAAN,IAAoB,CAAC,IAAD,EAAM2E,UAAUC,GAAV,CAAc;AAAA,uDAAQZ,KAAKnE,EAAb;AAAA,yCAAd,CAAN,CAApB;AACH;AAGF;;AAEH,4BAAI,CAACgB,MAAMC,OAAN,CAAc,QAAKL,GAAL,CAAS,OAAT,CAAd,CAAL,EAAuC;AACrCT,sCAAM,SAAN,IAAmB,CAAC,MAAD,EAAS,MAAM,QAAKS,GAAL,CAAS,MAAT,CAAN,GAAyB,GAAlC,CAAnB;AACD;;AAKD,8BAAM6C,MAAM,MAAMvD,MACfkC,KADe,CACT,GADS,EAEfE,KAFe,CAET,uBAFS,EAIfD,IAJe,CAIV,CAAC,kCAAD,CAJU,EAKfA,IALe,CAKV,CAAC,gCAAD,CALU,EAMf1B,IANe,CAMVA,IANU,EAMJE,IANI,EAOfV,KAPe,CAOTA,KAPS,EAQjBA,KARiB,CAQXuE,MARW,EASf9C,KATe,CASTA,KATS,EAUfW,WAVe,EAAlB;AAWA,8BAAMlB,OAAO,EAAb;;AAEA,6BAAI,IAAI8C,IAAR,IAAgBV,IAAIpC,IAApB,EAAyB;AACrB8C,qCAAKD,UAAL,GAAgBC,KAAKD,UAAL,CAAgBW,OAAhB,CAAwB,IAAxB,EAA8B,MAA9B,CAAhB;AACAV,qCAAKa,MAAL,GAAYb,KAAKa,MAAL,CAAYH,OAAZ,CAAoB,IAApB,EAA0B,MAA1B,CAAZ;AACH;;AAMDxD,6BAAKF,IAAL,GAAY,GAAZ;AACAE,6BAAKD,KAAL,GAAaqC,IAAIrC,KAAjB;AACAC,6BAAKA,IAAL,GAAYoC,IAAIpC,IAAhB;AACAA,6BAAKC,GAAL,GAAW,EAAX;;AAIA,+BAAO,QAAKhB,IAAL,CAAUe,IAAV,CAAP;AAxEiB;AAyElB;;AAGK4D,oBAAN,GAAoB;AAAA;;AAAA;;AAElB,4BAAIzE,WAAW,EAAf;AACA,8BAAMR,KAAK,QAAKC,IAAL,CAAU,IAAV,IAAkB,QAAKA,IAAL,CAAU,IAAV,CAAlB,GAAoC,IAA/C;;AAIA,8BAAMC,QAAQ,QAAKA,KAAL,CAAW,IAAX,CAAd;AACA,8BAAMO,WAAW,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAvB;AACAF,mCAAY,MAAMN,MAAMC,KAAN,CAAY,EAACH,IAAIA,EAAL,EAAZ,EAAsB0C,IAAtB,EAAlB;;AAEA,+BAAO,QAAKpC,IAAL,CAAUE,QAAV,CAAP;AAXkB;AAYnB;AACK0E,wBAAN,GAAwB;AAAA;;AAAA;AACtB,8BAAMvE,OAAO,QAAKC,GAAL,CAAS,MAAT,IAAmB,QAAKA,GAAL,CAAS,MAAT,CAAnB,GAAsC,CAAnD;AACA,8BAAMC,OAAO,EAAb;AACA,8BAAMJ,WAAW,MAAM,QAAKC,OAAL,CAAa,UAAb,CAAvB;AACA,8BAAMR,QAAQ,QAAKA,KAAL,CAAW,WAAX,CAAd;AACA,4BAAIiD,OAAK,QAAKvC,GAAL,CAAS,MAAT,CAAT;AACA,4BAAIT,QAAM,EAAV;AACAA,8BAAM,QAAN,IAAgBgD,IAAhB;AACAhD,8BAAM,YAAN,IAAoBM,SAASyB,QAA7B;AACA,8BAAMuB,MAAM,MAAMvD,MAAMkC,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB,iCAAtB,EAAyD1B,IAAzD,CAA8DA,IAA9D,EAAmEE,IAAnE,EAAyEV,KAAzE,CAA+EA,KAA/E,EAAsFmC,KAAtF,CAA4F,YAA5F,EAA0GV,KAA1G,CAAgH,mBAAhH,EAAqIW,WAArI,EAAlB;AACD,4BAAI/B,WAAW;AACZW,sCAAM,GADM;AAEZC,uCAAOqC,IAAIrC,KAFC;AAGZC,sCAAMoC,IAAIpC,IAHE;AAIZC,qCAAK;AAJO,yBAAf;AAMC,+BAAO,QAAKhB,IAAL,CAAUE,QAAV,CAAP;AAhBsB;AAiBvB;;AApkBqC,CAAxC", "file": "..\\..\\..\\..\\src\\controller\\api\\buss\\bk.js", "sourcesContent": ["const BaseRest = require('../rest.js');\r\n \r\nmodule.exports = class extends BaseRest {\r\n\r\n    async removeAction() {\r\n        const id = this.post('id');\r\n    \r\n        const model = this.model('bk');\r\n        await model.where({'id': id}).update({'del_flag': 1});\r\n\r\n        await this.model(\"bk_list\").where({\"bkid\":id}).delete();\r\n        await this.model(\"bk_user\").where({\"bkid\":id}).delete();\r\n    \r\n        return this.json({'code': 200});\r\n      }\r\n\r\n\r\n      async tkpage3Action() {\r\n        let respData = {};\r\n   \r\n        const userInfo = await this.session('userInfo');\r\n\r\n        const page = this.get('page') ? this.get('page') : 1;\r\n        const rows = 15;\r\n        const where = this.get('where') ? JSON.parse(this.get('where')) : {};\r\n        const model = this.model('tk_school');\r\n        where['c.del_flag'] = 0;\r\n\r\n\r\n        if(think.isEmpty(where.lessonid)){\r\n          let  respData = {\r\n                code: 200,\r\n                count: 0,\r\n                data:[],\r\n                msg: ''\r\n              };\r\n              return this.json(respData);\r\n\r\n        }\r\n\r\n        if(!think.isEmpty(where.start)){\r\n            let start=where.start\r\n            delete where.start\r\n            where[\"c.score\"]=[\">=\",parseInt(start)];\r\n        }\r\n        \r\n        if(!think.isEmpty(where.end)){\r\n\r\n            let end=where.end\r\n            delete where.end\r\n            where[\"c.score\"]=[\"<=\",parseInt(end)];\r\n        }\r\n\r\n\r\n        if(!think.isEmpty(where.lessonid)){\r\n\r\n            let lession_id=where.lessonid.split(\",\")\r\n            delete where.lessonid\r\n            where[\"c.lessonid\"]=[\"in\",lession_id];\r\n        }\r\n\r\n\r\n    \r\n       \r\n        if(this.get(\"state\")==1||this.get(\"state\")==0){\r\n          where['c.state']=this.get(\"state\")\r\n        }\r\n\r\n        let order='c.score asc';\r\n        let orderstr=this.get(\"order\");\r\n        if(orderstr==\"descending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" desc\";\r\n\r\n        }\r\n\r\n        if(orderstr==\"ascending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" asc\";\r\n\r\n        }\r\n\r\n        where[\"d.num2\"]=[\">\",0]\r\n\r\n\r\n        let sql2=\"\"\r\n\r\n        if(!think.isEmpty(where.ifjj)){\r\n          console.log(where)\r\n\r\n          if(where.ifjj==\"未讲解\"){\r\n             sql2=\"  c.id not in (select tkid from sys_bk_list )\"\r\n          } else if(where.ifjj==\"已讲解\"){\r\n            sql2=\"  c.id in (select tkid from sys_bk_list )\"\r\n          }\r\n            \r\n        }\r\n        delete where.ifjj\r\n\r\n\r\n        where['d.schoolid']=userInfo.schoolid;\r\n        const response = await model.alias(\"d\").join(\"sys_tk c on c.id=d.tkid\").page(page, rows).where(where).where(sql2).field(\"c.*,d.num2 as num2\").order(order).countSelect();\r\n        respData = {\r\n          code: 200,\r\n          count: response.count,\r\n          data: response.data,\r\n          msg: ''\r\n        };\r\n        return this.json(respData);\r\n      }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n      async tkpage2Action() {\r\n        let respData = {};\r\n   \r\n        const userInfo = await this.session('userInfo');\r\n\r\n        const page = this.get('page') ? this.get('page') : 1;\r\n        const rows = 15;\r\n        const where = this.get('where') ? JSON.parse(this.get('where')) : {};\r\n        const model = this.model('jj');\r\n        where['c.del_flag'] = 0;\r\n\r\n\r\n        if(think.isEmpty(where.lessonid)){\r\n          let  respData = {\r\n                code: 200,\r\n                count: 0,\r\n                data:[],\r\n                msg: ''\r\n              };\r\n              return this.json(respData);\r\n\r\n        }\r\n\r\n        if(!think.isEmpty(where.start)){\r\n            let start=where.start\r\n            delete where.start\r\n            where[\"c.score\"]=[\">=\",parseInt(start)];\r\n        }\r\n        \r\n        if(!think.isEmpty(where.end)){\r\n\r\n            let end=where.end\r\n            delete where.end\r\n            where[\"c.score\"]=[\"<=\",parseInt(end)];\r\n        }\r\n\r\n\r\n        if(!think.isEmpty(where.lessonid)){\r\n\r\n            let lession_id=where.lessonid.split(\",\")\r\n            delete where.lessonid\r\n            where[\"c.lessonid\"]=[\"in\",lession_id];\r\n        }\r\n\r\n\r\n      if(!think.isEmpty(where)&&!think.isEmpty(where.lessonid)){\r\n        //await this.updatelesson(where.lessonid);\r\n\r\n      }\r\n       \r\n        if(this.get(\"state\")==1||this.get(\"state\")==0){\r\n          where['c.state']=this.get(\"state\")\r\n        }\r\n\r\n        where['d.schoolid']=userInfo.schoolid;\r\n\r\n        let order='c.score asc';\r\n        let orderstr=this.get(\"order\");\r\n        if(orderstr==\"descending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" desc\";\r\n\r\n        }\r\n\r\n        if(orderstr==\"ascending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" asc\";\r\n\r\n        }\r\n\r\n\r\n        let sql2=\"\"\r\n\r\n        if(!think.isEmpty(where.ifjj)){\r\n          console.log(where)\r\n\r\n          if(where.ifjj==\"未讲解\"){\r\n             sql2=\"  c.id not in (select tkid from sys_bk_list )\"\r\n          } else if(where.ifjj==\"已讲解\"){\r\n            sql2=\"  c.id in (select tkid from sys_bk_list )\"\r\n          }\r\n            \r\n        }\r\n        delete where.ifjj\r\n\r\n        const response = await model.alias(\"d\").join(\"sys_tk c on c.id=d.tkid\").page(page, rows).where(where).where(sql2).field(\"c.*,d.num as num2\").order(order).countSelect();\r\n        respData = {\r\n          code: 200,\r\n          count: response.count,\r\n          data: response.data,\r\n          msg: ''\r\n        };\r\n        return this.json(respData);\r\n      }\r\n\r\n\r\n\r\n\r\n      async infoAction(){\r\n\r\n        let respData = {};\r\n        const id = this.post('id') ? this.post('id') : null;\r\n        const model = this.model('bk');\r\n        respData=   await model.where({id: id}).find();\r\n     \r\n        return this.json(respData);\r\n      }\r\n    \r\n\r\n\r\n\r\n    \r\n      async tkpageAction() {\r\n        let respData = {};\r\n   \r\n        const userInfo = await this.session('userInfo');\r\n\r\n        const page = this.get('page') ? this.get('page') : 1;\r\n        const rows = 15;\r\n        const where = this.get('where') ? JSON.parse(this.get('where')) : {};\r\n        const model = this.model('tk');\r\n        where['c.del_flag'] = 0;\r\n\r\n\r\n        if(think.isEmpty(where.lessonid)){\r\n          let  respData = {\r\n                code: 200,\r\n                count: 0,\r\n                data:[],\r\n                msg: ''\r\n              };\r\n              return this.json(respData);\r\n\r\n        }\r\n\r\n\r\n        \r\n\r\n        if(!think.isEmpty(where.start)){\r\n            let start=where.start\r\n            delete where.start\r\n            where[\"c.score\"]=[\">=\",parseInt(start)];\r\n        }\r\n        \r\n        if(!think.isEmpty(where.end)){\r\n\r\n            let end=where.end\r\n            delete where.end\r\n            where[\"c.score\"]=[\"<=\",parseInt(end)];\r\n        }\r\n\r\n\r\n        if(!think.isEmpty(where.lessonid)){\r\n\r\n            let lession_id=where.lessonid.split(\",\")\r\n            delete where.lessonid\r\n            where[\"c.lessonid\"]=[\"in\",lession_id];\r\n        }\r\n\r\n\r\n      if(!think.isEmpty(where)&&!think.isEmpty(where.lessonid)){\r\n        //await this.updatelesson(where.lessonid);\r\n\r\n      }\r\n       \r\n        if(this.get(\"state\")==1||this.get(\"state\")==0){\r\n          where['c.state']=this.get(\"state\")\r\n        }\r\n\r\n        let order='c.score asc';\r\n        let orderstr=this.get(\"order\");\r\n        if(orderstr==\"descending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" desc\";\r\n\r\n        }\r\n\r\n        if(orderstr==\"ascending\"){\r\n          order=\" c.\"+this.get(\"prop\")+\" asc\";\r\n\r\n        }\r\n        let sql2=\"\"\r\n\r\n        if(!think.isEmpty(where.ifjj)){\r\n          console.log(where)\r\n\r\n          if(where.ifjj==\"未讲解\"){\r\n             sql2=\"  c.id not in (select tkid from sys_bk_list )\"\r\n          } else if(where.ifjj==\"已讲解\"){\r\n            sql2=\"  c.id in (select tkid from sys_bk_list )\"\r\n          }\r\n            \r\n        }\r\n        delete where.ifjj\r\n\r\n        const response = await model.alias(\"c\").join(\"left join (select * from sys_tk_school where schoolid=\"+userInfo.schoolid+\") l on l.tkid=c.id\").page(page, rows).where(where).where(sql2).field(\"c.*,l.num2\").order(order).countSelect();\r\n        respData = {\r\n          code: 200,\r\n          count: response.count,\r\n          data: response.data,\r\n          msg: ''\r\n        };\r\n        return this.json(respData);\r\n      }\r\n\r\n\r\n\r\n      async tmpsubmitAction(){\r\n        const userInfo = await this.session('userInfo');\r\n        const allParams = this.post();\r\n        console.log(allParams);\r\n        let model=this.model(\"bk_list\")\r\n        let arr=[];\r\n        let tkids=this.post(\"ids\");\r\n\r\n        let para=JSON.parse(tkids);\r\n\r\n        for(let obj of para.list){\r\n          if(!think.isEmpty(obj.id)){\r\n            let data={};\r\n            console.log(obj.id)\r\n\r\n            data.tkid=obj.id;\r\n\r\n\r\n\r\n            data.bkid=this.post(\"id\");\r\n            data.schoolid=userInfo.schoolid;\r\n            data.bktype=obj.bktype;\r\n            arr.push(data);\r\n\r\n          }\r\n            \r\n\r\n        }\r\n\r\n        console.log( arr)\r\n\r\n        await model.where({bkid:this.post(\"id\")}).delete();\r\n        await model.addMany(arr);\r\n        \r\n        return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1});\r\n        \r\n      }\r\n\r\n\r\n\r\n\r\n      async gettoplessonAction(){\r\n        const userInfo = await this.session('userInfo');\r\n        let model = this.model(\"user_lesson\");\r\n        let res=await model.alias(\"c\").join(\"sys_lesson l on l.id=c.lession_id\").field(\"l.id,l.name\").where({\"user_id\":userInfo.id}).select();\r\n    \r\n    \r\n        return this.json(res);\r\n    \r\n    \r\n    \r\n    \r\n      }\r\n\r\n      async submitAction(){\r\n        const userInfo = await this.session('userInfo');\r\n        const allParams = this.post();\r\n        console.log(allParams);\r\n        let model=this.model(\"bk_list\")\r\n        let arr=[];\r\n        let tkids=this.post(\"ids\");\r\n        let para=JSON.parse(tkids);\r\n\r\n        for(let obj of para.list){\r\n          if(!think.isEmpty(obj.id)){\r\n            let data={};\r\n            console.log(obj.id)\r\n\r\n            data.tkid=obj.id;\r\n\r\n\r\n\r\n            data.bkid=this.post(\"id\");\r\n            data.schoolid=userInfo.schoolid;\r\n            data.bktype=obj.bktype;\r\n            arr.push(data);\r\n\r\n          }\r\n            \r\n\r\n        }\r\n\r\n        await model.where({bkid:this.post(\"id\")}).delete();\r\n        await model.addMany(arr);\r\n        await this.model(\"bk\").where({\"id\":this.post(\"id\")}).update({\"state\":2,\"fbdate\":think.datetime()});\r\n        await this.model(\"bk_user\").where({\"bkid\":this.post(\"id\")}).delete();\r\n        return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'state': 1});\r\n        \r\n      }\r\n\r\n\r\n      async getlistAction(){\r\n        const userInfo = await this.session('userInfo');\r\n        let model=this.model(\"bk_list\");\r\n        let bkid=this.get(\"id\");\r\n        let where={\"a.bkid\":bkid};\r\n        let res=await model.alias(\"a\").join(\"left join sys_tk c on c.id=a.tkid\")\r\n        .join(\" left join (select * from sys_tk_school where schoolid=\"+userInfo.schoolid+\") d on d.tkid=a.tkid \")\r\n        .where(where).field(\"c.*,d.num2 as num2,a.bktype,a.bkid as id2\")\r\n        .select();\r\n        return this.json(res);\r\n\r\n\r\n      }\r\n\r\n\r\n\r\n    async saveAction() {\r\n        const colums = ['title', 'reamrks', 'lessonname', 'skdate', 'remarks', 'password', 'role', 'user_type', 'birthday',\"schoolid\",\"id\",\"lesson\",\"toplesson\"];\r\n        const allParams = this.post();\r\n        const data = {};\r\n        for (var c of colums) {\r\n          if (c in allParams) {\r\n            data[c] = allParams[c];\r\n          }\r\n        }\r\n    \r\n\r\n        const model = this.model('bk');\r\n\r\n      \r\n        let lesson=data.lesson;\r\n        console.log(lesson)\r\n\r\n        let arr=lesson.split(\",\");\r\n        let lessonname=[];\r\n        for(var item of arr){\r\n\r\n          let lesson=await this.model(\"lesson\").where({id:item}).find();\r\n          lessonname.push(lesson.name);\r\n\r\n        }\r\n\r\n        data.lessonname=lessonname.join(\",\");\r\n\r\n        \r\n        if (this.post('id')) { \r\n         \r\n          await model.where({id: this.post('id')}).update(data);\r\n    \r\n          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});\r\n        } else {\r\n            \r\n            const userInfo = await this.session('userInfo');\r\n            data.teacherid=userInfo.id;\r\n            data.schoolid=userInfo.schoolid;\r\n            data.state=1;\r\n            data.create_date=think.datetime();\r\n\r\n          await model.add(data);\r\n\r\n          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});\r\n        }\r\n      }\r\n \r\n\r\n  \r\n  async listAction() {\r\n    let order = 'c.create_date desc';\r\n    const page = this.get('page');\r\n    const rows = this.get('pageSize');\r\n    const where2 = this.get('where') ? JSON.parse(this.get('where')) : {};\r\n    const keyword = this.get('name');\r\n    const prop = this.get('prop');\r\n    if (!think.isEmpty(prop)&&!think.isEmpty(this.get('order'))) {\r\n      order = prop + ' ' + this.get('order').replace('ending', '');\r\n    }\r\n\r\n    const model = this.model('bk');\r\n    const where = {'c.del_flag': 0};\r\n    if (!think.isEmpty(keyword)) {\r\n      where['c.title'] = ['like', '%' + keyword + '%'];\r\n    }\r\n\r\n    const userInfo = await this.session('userInfo');\r\n   \r\n    console.log(userInfo);\r\n\r\n    if(userInfo.name!=\"系统管理员\"){\r\n\r\n        let schoolres=await this.model(\"school\").where({\"uid\":userInfo.id}).select();\r\n\r\n        if(think.isEmpty(schoolres)){\r\n            where[\"c.teacherid\"]=userInfo.id\r\n           \r\n        }else{\r\n\r\n            where[\"c.schoolid\"]=[\"in\",schoolres.map(item => item.id)];\r\n        }\r\n\r\n     \r\n      }\r\n   \r\n    if (!think.isEmpty(this.get(\"title\"))) {\r\n      where['c.title'] = ['like', '%' + this.get(\"name\") + '%'];\r\n    }\r\n\r\n\r\n\r\n\r\n    const res = await model\r\n      .alias('c')\r\n      .field('c.*,t.name as teacher')\r\n       \r\n      .join([\"buss_school s on s.id=c.schoolid\"])\r\n      .join([\"sys_user t on t.id=c.teacherid\"])\r\n      .page(page, rows)\r\n      .where(where)\r\n    .where(where2)\r\n      .order(order)\r\n      .countSelect();\r\n    const data = {};\r\n\r\n    for(let item of res.data){\r\n        item.lessonname=item.lessonname.replace(/,/g, '<br>');\r\n        item.skdate=item.skdate.replace(/ /g, '<br>');\r\n    }\r\n\r\n\r\n\r\n\r\n    \r\n    data.code = 200;\r\n    data.count = res.count;\r\n    data.data = res.data;\r\n    data.msg = '';\r\n\r\n  \r\n \r\n    return this.json(data);\r\n  }\r\n\r\n \r\n  async recordAction(){\r\n\r\n    let respData = {};\r\n    const id = this.post('id') ? this.post('id') : null;\r\n\r\n \r\n\r\n    const model = this.model('tk');\r\n    const userInfo = await this.session('userInfo');\r\n    respData=   await model.where({id: id}).find();\r\n    \r\n    return this.json(respData);\r\n  }\r\n  async recordlistAction(){\r\n    const page = this.get('page') ? this.get('page') : 1;\r\n    const rows = 15;\r\n    const userInfo = await this.session('userInfo');\r\n    const model = this.model('tk_record');\r\n    let tkid=this.get(\"tkid\");\r\n    let where={};\r\n    where['c.tkid']=tkid;\r\n    where['c.schoolid']=userInfo.schoolid;\r\n    const res = await model.alias(\"c\").join(\"buss_student u on u.id=c.userid\").page(page,rows).where(where).field(\"c.*,u.name\").order(\"c.create_date asc\").countSelect();\r\n   let respData = {\r\n      code: 200,\r\n      count: res.count,\r\n      data: res.data,\r\n      msg: ''\r\n    };\r\n    return this.json(respData);\r\n  }\r\n\r\n}"]}