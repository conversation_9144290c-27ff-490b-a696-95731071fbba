import config from "@/config";
import http from "@/utils/request";

const order = {
 
	page: {
		url: `${config.API_URL}/buss/order/page`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

    // 删除订单
    remove: {
        url: `${config.API_URL}/buss/order/remove`,
        name: "删除订单",
        post: async function (params) {
            return await http.post(this.url, this.name, params);
        }
    }
}
    export default order;