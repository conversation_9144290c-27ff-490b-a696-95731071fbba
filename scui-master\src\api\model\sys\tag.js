import config from "@/config";
import http from "@/utils/request";

export default {
	tags: {
		url: `${config.API_URL}/sys/tag/tags`,
		name: "标签",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
	save: {
		url: `${config.API_URL}/sys/tag/save`,
		name: "更新标签",
		post: async function (params = {}) {
			return await http.post(this.url, this.name, params);
		}
	},
}
