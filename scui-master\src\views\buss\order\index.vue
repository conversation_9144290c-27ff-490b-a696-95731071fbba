<template>
  <el-container>
    <el-container>
 
     
      
      <el-header>
        <div class="left-panel">
        </div>
        <div class="right-panel">
          <div class="right-panel-search">
            
        <el-select v-model="search.yxid" placeholder="销售人员" clearable>
          <el-option v-for="item in yxlist" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
             <el-input v-model="search.phone" placeholder="用户手机号" clearable></el-input>
            <el-select v-model="search.payStatus" placeholder="支付状态" clearable>
              <el-option label="未支付" :value="0"></el-option>
              <el-option label="已支付" :value="1"></el-option>
              <el-option label="已退款" :value="2"></el-option>
            </el-select>
            <el-select v-model="search.orderStatus" placeholder="订单状态" clearable>
              <el-option label="待付款" :value="0"></el-option>
              <el-option label="已完成" :value="1"></el-option>
              <el-option label="已取消" :value="2"></el-option>
              <el-option label="已关闭" :value="3"></el-option>
            </el-select>

            <el-select v-model="search.isConsumed" placeholder="是否消耗完" clearable>
            <el-option label="已消耗完" :value="1"></el-option>
            <el-option label="未消耗完" :value="2"></el-option>
          </el-select>

            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="handleDateChange">
            </el-date-picker>
            <el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch"></el-button>
          </div>
        </div>
      </el-header>
      <el-main class="nopadding">
        <scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
          remoteFilter>
          <el-table-column type="selection" width="50"></el-table-column>
          
          <el-table-column label="订单编号" prop="order_no" width="180" sortable="order_no"></el-table-column>
            <el-table-column label="销售人员" prop="yxname" width="80" sortable="yxname"></el-table-column>
          <el-table-column label="用户名/手机号" width="100">
            <template #default="scope">
              <div>{{scope.row.uname}}</div>
              <div>{{scope.row.uphone}}</div>
            </template>
          </el-table-column>

          

            <el-table-column label="商品名称" prop="productname" width="180" sortable="productname"></el-table-column>

               <el-table-column label="订单金额" prop="pay_amount" width="50" sortable="pay_amount"></el-table-column>
         
        
          <el-table-column label="支付状态" prop="pay_status" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.pay_status === 0" type="info">未支付</el-tag>
              <el-tag v-else-if="scope.row.pay_status === 1" type="success">已支付</el-tag>
              <el-tag v-else-if="scope.row.pay_status === 2" type="danger">已退款</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="订单状态" prop="order_status" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.order_status === 0" type="info">待付款</el-tag>
              <el-tag v-else-if="scope.row.order_status === 1" type="success">已完成</el-tag>
              <el-tag v-else-if="scope.row.order_status === 2" type="warning">已取消</el-tag>
              <el-tag v-else-if="scope.row.order_status === 3" type="danger">已关闭</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="剩余天数" prop="available_days" width="100">
            <template #default="scope">
              {{ scope.row.available_days }}/{{ scope.row.allday }}
            </template>
          </el-table-column>
         
          <el-table-column label="支付时间" prop="pay_time" width="160"></el-table-column>
          <el-table-column label="退款金额" prop="refund_amount" width="100"></el-table-column>
          <el-table-column label="备注" prop="remark" min-width="150"></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="200">
            <template #default="scope">
             
              <el-button 
                v-if="scope.row.address" 
                type="text" 
                size="small" 
                @click="showAddress(scope.row)">查看地址</el-button>
              <el-button 
                v-if="scope.row.pay_status === 1" 
                type="text" 
                size="small" 
                @click="handleRefund(scope.row)">退款</el-button>
              <el-popconfirm title="确定删除吗？"   v-if="scope.row.pay_status === 0"  @confirm="table_del(scope.row, scope.$index)">
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
    
    <!-- 放在容器内部，确保只有一个根元素 -->
    <refund-dialog v-if="dialog.refund" ref="refundDialog" @success="handleSuccess" @closed="dialog.refund=false"></refund-dialog>
    
    <!-- 地址信息弹窗 -->
    <el-dialog title="收货地址信息" v-model="dialog.address" width="500px">
      <div v-if="currentAddress">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="收货人">{{ currentAddress.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentAddress.phone }}</el-descriptions-item>
          <el-descriptions-item label="所在地区">{{ currentAddress.region }}</el-descriptions-item>
          <el-descriptions-item label="详细地址">{{ currentAddress.address }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </el-container>
</template>

<script>

 
import refundDialog from './refund'
import * as echarts from 'echarts'
import { PieChart, Refresh } from '@element-plus/icons-vue'

export default {
  name: 'order',
  components: {
   
    refundDialog,
    PieChart,
    Refresh
  },
  data() {
    return {
      dialog: {
        save: false,
        pay: false,
        refund: false,
        address: false
      },
      currentAddress: null,
      apiObj: this.$API.order.page,
      selection: [],
      dateRange: [],
      yxlist: [],
      // 图表相关数据
      showStatistics: false, // 默认不显示统计图表
      chartDateRange: [],
      pieChart: null,
      chartData: [],
      search: {
        orderNo: null,
        phone: null,
        payStatus: null,
        orderStatus: null,
        startDate: null,
        isConsumed: null, // 添加此行
        endDate: null
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
     this.getYxlist();
    });
  },
  methods: {
    // 初始化饼图
    initPieChart() {
      if (this.$refs.pieChartRef) {
        this.pieChart = echarts.init(this.$refs.pieChartRef);
        window.addEventListener('resize', this.resizeChart);
      }
    },
    
    // 调整图表大小
    resizeChart() {
      if (this.pieChart) {
        this.pieChart.resize();
      }
    },
    
    // 渲染饼图
    renderPieChart() {
      if (!this.pieChart) return;
      
      const option = {
        title: {
          text: '商品销售统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.chartData.map(item => item.name)
        },
        series: [
          {
            name: '销售数量',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: this.chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      
      this.pieChart.setOption(option);
    },
    


   async getYxlist() {
       const res = await this.$API.common.util.post("/buss/yx/listselect", {});
       this.yxlist = res;
    },
    // 获取图表数据
   
    
    // 图表日期范围变化
    handleChartDateChange(val) {
      this.chartDateRange = val;
      this.fetchChartData();
    },
    
    // 刷新图表
    refreshChart() {
      this.fetchChartData();
    },
    
    // 显示地址信息
    showAddress(row) {
      if (row.address) {
        try {
          // 如果地址是字符串，则解析成对象
          this.currentAddress = typeof row.address === 'string' ? JSON.parse(row.address) : row.address;
          this.dialog.address = true;
        } catch (error) {
          this.$message.error('地址格式错误');
          console.error('解析地址出错:', error);
        }
      }
    },
   
    
    // 删除订单
    async table_del(row, index) {
      var reqData = { id: row.id }
      var res = await this.$API.order.remove.post(reqData);
      if (res.errno == 0) {
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功")
      } else {
        this.$alert(res.message, "提示", { type: 'error' })
      }
    },
    
     
    // 处理退款
    handleRefund(row) {
      this.dialog.refund = true
      this.$nextTick(() => {
        this.$refs.refundDialog.open(row)
      })
    },
      
    
    
    // 表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    
    // 日期范围变化
    handleDateChange(val) {
      if (val) {
        this.search.startDate = val[0];
        this.search.endDate = val[1];
      } else {
        this.search.startDate = null;
        this.search.endDate = null;
      }
    },
    
    // 搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    
    // 重置搜索
    resetSearch() {
      this.search = {
        orderNo: null,
        userId: null,
        payStatus: null,
        orderStatus: null,
        startDate: null,
        endDate: null,
         isConsumed: null, // 添加此行
      };
      this.dateRange = [];
      this.$refs.table.upData(this.search);
    },
    
    // 本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    },
    
    // 切换统计图表显示状态
    toggleStatistics() {
      this.showStatistics = !this.showStatistics;
      if (this.showStatistics) {
        this.$nextTick(() => {
          this.initPieChart();
          this.fetchChartData();
        });
      }
    }
  }
}
</script>

<style>
.statistics-button {
  width: 100%;
  padding: 10px 20px;
  display: flex;
  justify-content: flex-start;
}

.statistics-section {
  width: 100%;
  padding: 0 20px 15px;
}

.statistics-container {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  transition: all 0.3s;
}

.filter-bar {
  display: flex;
  margin-bottom: 15px;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.chart-container {
  width: 100%;
  height: 400px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;
}

.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: auto !important;
  padding-top: 10px;
  padding-bottom: 10px;
}

.left-panel {
  display: flex;
  align-items: center;
}

.left-panel .el-button {
  margin-right: 10px;
}

.right-panel {
  display: flex;
}

.right-panel-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.right-panel-search .el-input,
.right-panel-search .el-select {
  margin-right: 10px;
  width: 180px;
}

.right-panel-search .el-date-editor {
  margin-right: 10px;
  width: 320px;
}

.right-panel-search .el-button {
  margin-right: 10px;
}
</style>
