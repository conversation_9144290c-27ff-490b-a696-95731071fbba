const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {





  
  async menuAction() {
    const user = await this.session('userInfo');
    const model = this.model('user_role');
    const mmodel = this.model('menu');
    const roles = await model.where({'user_id': user.id}).select();
    if (think.isEmpty(roles)) {
      this.json([]);
    } else {
      let tmpsql = '';
      let flag = 1;
      for (var one of roles) {
        if (flag < roles.length) {
          tmpsql = tmpsql + "'" + one.role_id + "',";
        } else {
          tmpsql = tmpsql + "'" + one.role_id + "'";
        }
        flag++;
      }

      const tmparr = [];
      const sql = "select * from sys_menu where id in (select  DISTINCT m.id from sys_menu m join sys_role_menu rm on rm.menu_id=m.id where m.del_flag='0' and rm.role_id  in (" + tmpsql + ')) order by sort asc';
      const res = await model.query(sql);

      for (var one of res) {
        if (one.parent_id != '0') {
          console.log(one);
          let ifhava = false;
          for (var t of res) {
            if (t.id == one.parent_id) {
              ifhava = true;
            }
          }
          if (!ifhava) {
            const m = await mmodel.where({'id': one.parent_id}).find();

            res.push(m);
          }
        }
      }

      var tree = [];
      for (var t of res) {
        if (t.parent_id == '0') {
          const obj = {};
          obj.id = t.id;
          obj.name = t.name;
          obj.sort = t.sort;
          obj.title = t.title;
          obj.path = t.path;

          obj.component = t.component;
          const meta = {};
          meta.title = t.title;
          meta.icon = t.icon;
          meta.type = t.type;

          obj.meta = meta;

          await this.getchild(obj, res);
          tree.push(obj);
        }
      }
      this.json(tree);
    }
  }

  async getchild(tree, menus) {
    tree.hasChildren = false;
    var childs = [];
    for (var t of menus) {
      if (tree.id == t.parent_id) {
        const obj = {};
        obj.id = t.id;
        obj.name = t.name;
        obj.title = t.title;
        obj.sort = t.sort;
        obj.path = t.path;

        obj.component = t.component;
        const meta = {};
        meta.title = t.title;
        meta.icon = t.icon;
        meta.type = t.type;

        obj.meta = meta;
        await this.getchild(obj, menus);
        childs.push(obj);
      }
    }
    tree.children = childs;
  }

  async getrole(id) {
    const model = this.model('role');
    const where = {'r.user_id': id};
    const res = await model.alias('c').join([' sys_user_role r on c.id=r.role_id ']).where(where).field('c.name as name,c.id as id').select();
    return res;
  }

  async saveAction() {
    const colums = ['name', 'office_id', 'login_name', 'mobile', 'email', 'password', 'role', 'user_type', 'birthday'];
    const allParams = this.post();
    const data = {};
    for (var c of colums) {
      if (c in allParams) {
        data[c] = allParams[c];
      }
    }

    const rmodel = this.model('user_role');
    if (this.post('id')) {
      const model = this.model('user');

      await model.where({id: this.post('id')}).update(data);
      const arr = [];
      for (var one of data.role) {
        arr.push({'user_id': this.post('id'), 'role_id': one[0]});
      }
      await rmodel.where({'user_id': this.post('id')}).delete();
      await rmodel.addMany(arr);

      return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
    } else {
      const model = this.model('user');
      data.password = think.md5(data.password);
      data.id = think.uuid();
      data.create_date = think.datetime();
      data.avatar='/static/upload/default.png';
      await model.add(data);

      await model.where({id: this.post('id')}).update(data);
      const arr = [];
      for (var one of data.role) {
        arr.push({'user_id': data.id, 'role_id': one[0]});
      }
      await rmodel.where({'user_id': data.id}).delete();
      await rmodel.addMany(arr);

      return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
    }
  }

  async listselectAction() {
    let order = 'create_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');

    const keyword = this.get('keyword');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('user');
    const where = {del_flag: 0};
    if (!think.isEmpty(keyword)) {
      where['name'] = ['like', '%' + keyword + '%'];
    }
    const res = await model.alias('c').page(page, rows).where(where).order(order).countSelect();
    const data = {};

    data.code = 200;

    data.data = {total: res.count, rows: res.data};
    data.msg = '';

    return this.json(data);
  }



  async listAction() {
    let order = 'c.create_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');

    const keyword = this.get('keyword');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('user');
    const where = {'c.del_flag': 0};
    if (!think.isEmpty(keyword)) {
      where['c.name'] = ['like', '%' + keyword + '%'];
    }
    if (!think.isEmpty(this.get("name"))) {
      where['c.name'] = ['like', '%' + this.get("name") + '%'];
    }



    let dataScopeWhere = await this.dataScope('c');
    const res = await model
      .alias('c')
      .field('c.id,c.name,c.create_date,c.login_date,c.login_name,o.name as office_name,c.office_id')
      .join({
        table: 'office',
        join: 'left', // join 方式，有 left, right, inner 3 种方式
        as: 'o', // 表别名
        on: ['office_id', 'id'] // ON 条件
      })
      .page(page, rows)
      .where(where)
      .where(dataScopeWhere)
      .order(order)
      .countSelect();
    const data = {};

    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';

    for (var one of data.data) {
      const group = await this.getrole(one.id);
      var str = '';
      const arr = [];
      for (var tmp of group) {
        arr.push(tmp.id);
        str = str + '' + tmp.name + ',';
      }
      one['groupName'] = str;
      one['group'] = arr;
    }

    return this.json(data);
  }

  async resetpassAction() {
    const id = this.post('id');

    const model = this.model('user');
    await model.where({'id': id}).update({'password': think.md5(this.post('password'))});
    return this.json({'code': 200});
  }

  async selectAction() {
    const model = this.model('user');
    const where = { del_flag: 0 };
    const res = await model.where(where).select();
    return this.json(res);
  }

  async removeAction() {
    const id = this.post('id');

    const model = this.model('user');
    await model.where({'id': id}).update({'del_flag': 1});

    return this.json({'code': 200});
  }


};
