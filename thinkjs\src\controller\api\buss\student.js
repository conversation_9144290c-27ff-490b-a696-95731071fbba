const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {


  async pageAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const learnLogModel = this.model('student');
    where['p.del_flag'] = 0;
    
    //let dataScopeWhere = await this.dataScope('p');

    const response = await learnLogModel
      .alias('p')
    
     .join(["buss_school l on l.id=p.school"])
 
     .join(["sys_yx y on y.id=p.yxid"])
      .page(page, rows).where(where)
      .order('p.create_date desc').field("p.*,l.name as schoolname,y.name as yxname").countSelect();

    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      message: ''
    };
    return this.json(respData);
  }



  async deleteAction() {
    let respData = {};
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      respData = {
        code: 400,
        data: {},
        message: '缺少必要的参数'
      };
    } else {
      const model = this.model('student');
      await model.where({id: id}).update(await this.deleteData());
      respData = {
        code: 200,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async saveAction() {
    let respData = {};
    const model = this.model('student');
    const id = this.post('id') ? this.post('id') : null;
    if (think.isEmpty(id)) {
      await model.add(await this.addData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    } else {
      await model.where({id: id}).update(await this.updateData(this.post()));
      respData = {
        code: 200,
        state:1,
        data: {},
        message: '成功'
      };
    }
    return this.json(respData);
  }

  async listAction() {
    let respData = {};
    const where = this.post('where') ? this.post('where') : {};
    const model = this.model('student');
    where['p.del_flag'] = 0;
    const response = await model
      .alias('p')
      .field('p.*')
      .where(where)
      .select();
    respData = {
      code: 200,
      data: response,
      message: ''
    };
    return this.json(respData);
  }


 
};
