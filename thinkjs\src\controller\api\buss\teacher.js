const BaseRest = require('../rest.js');
 
module.exports = class extends BaseRest {

    async removeAction() {
        const id = this.post('id');
    
        const model = this.model('user');
        await model.where({'id': id}).update({'del_flag': 1});
    
        return this.json({'code': 200});
      }
    
    

    async saveAction() {
        const colums = ['name', 'office_id', 'login_name', 'mobile', 'email', 'password', 'role', 'user_type', 'birthday',"schoolid","id","lesson"];
        const allParams = this.post();
        const data = {};
        for (var c of colums) {
          if (c in allParams) {
            data[c] = allParams[c];
          }
        }
    



        const rmodel = this.model('user_lesson');
        const model = this.model('user');

        const rmodel2 = this.model('user_role');
        

        data.type=2;
        
        if (this.post('id')) { 
            delete data.login_name;
          await model.where({id: this.post('id')}).update(data);
          const arr = [];
          for (var one of data.lesson) {
            arr.push({'user_id': this.post('id'), 'lession_id': one[0]});
          }
          
          await rmodel.where({'user_id': this.post('id')}).delete();
          await rmodel.addMany(arr);

          await rmodel2.where({'user_id': this.post('id')}).delete();

          await rmodel2.add({'user_id': this.post('id'),"role_id":"b77cc46c-9c35-4c49-b115-79e12111783c"});
    
          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
        } else {
         
          data.password = think.md5(data.password);
          data.id = think.uuid();
          data.create_date = think.datetime();
          data.avatar='/static/upload/default.png';

          let findres=await model.where({"login_name":data.login_name}).find()

          if(!think.isEmpty(findres)){
  
              return this.json({'code': -1, 'message': '该登录名已被注册', 'resultdata': null, 'type': 1});
          }

          await model.add(data);
          
    
          await model.where({id: this.post('id')}).update(data);
          const arr = [];
         
          for (var one of data.lesson) {
            arr.push({'user_id': this.post('id'), 'lession_id': one[0]});
          }
          
          await rmodel.where({'user_id': data.id}).delete();
          await rmodel.addMany(arr);

          await rmodel2.where({'user_id': data.id}).delete();

          await rmodel2.add({'user_id': data.id,"role_id":"b77cc46c-9c35-4c49-b115-79e12111783c"});
    
          return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
        }
      }
 

  
  async listAction() {
    let order = 'c.create_date desc';
    const page = this.get('page');
    const rows = this.get('pageSize');

    const keyword = this.get('keyword');
    const prop = this.get('prop');
    if (!think.isEmpty(prop)) {
      order = prop + ' ' + this.get('order').replace('ending', '');
    }

    const model = this.model('user');
    const where = {'c.del_flag': 0};
    if (!think.isEmpty(keyword)) {
      where['c.name'] = ['like', '%' + keyword + '%'];
    }

    const userInfo = await this.session('userInfo');
   

    if(userInfo.name!="系统管理员"){

        let schoolres=await this.model("school").where({"uid":userInfo.id}).select();

        where["c.schoolid"]=["in",schoolres.map(item => item.id)];
      }
    where['c.type'] = 2;
    if (!think.isEmpty(this.get("name"))) {
      where['c.name'] = ['like', '%' + this.get("name") + '%'];
    }




    const res = await model
      .alias('c')
      .field('c.id,c.name,c.create_date,c.login_date,c.login_name,o.name as office_name,c.office_id,s.name as school,c.schoolid as schoolid')
      .join({
        table: 'office',
        join: 'left', // join 方式，有 left, right, inner 3 种方式
        as: 'o', // 表别名
        on: ['office_id', 'id'] // ON 条件
      })

      .join(["buss_school s on s.id=c.schoolid"])
      .page(page, rows)
      .where(where)

      .order(order)
      .countSelect();
    const data = {};






    
    data.code = 200;
    data.count = res.count;
    data.data = res.data;
    data.msg = '';

  
    for (var one of data.data) {
        const lesson = await this.lesson(one.id);
        var str = '';
        const arr = [];
        for (var tmp of lesson) {
          arr.push(tmp.id);
          str = str + '' + tmp.name + '-'+tmp.type+",";
        }
        one['lessonName'] = str;
        one['lesson'] = arr;
      }

    return this.json(data);
  }


  async lesson(id) {
    const model = this.model('user_lesson');
    const where = {'c.user_id': id};
    const res = await model.alias('c').join(['sys_lesson l on c.lession_id=l.id ']).where(where).field('l.name as name,l.type as type,l.id as id').select();
    return res;
  }

  

  async myinfoAction(){

    let model=this.model("user");
    const userInfo = await this.session('userInfo');

    let res=await model.where({"id":userInfo.id}).field("content,skinfo,avatar").find();



    return this.json(res);
  }

  async basicAction(){

    let model=this.model("user");
    const userInfo = await this.session('userInfo');

    let avatar=this.post("avatar")
    let content=this.post("content");
    await model.where({id:userInfo.id}).update({avatar:avatar,content:content});

    return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
  }



  async skinfoAction(){

    let model=this.model("user");
    const userInfo = await this.session('userInfo');

    
    let skinfo=this.post("skinfo");
    await model.where({id:userInfo.id}).update( {skinfo:skinfo});

    return this.json({'code': 200, 'message': '保存成功。', 'resultdata': null, 'type': 1});
  }


  async lessonAction(){
    let model=this.model("lesson");


    let res=await model.where({"del_flag":"0","parent_id":0}).field("id,concat(type,'-',name) as name").select();

    return this.json(res);




  }

}