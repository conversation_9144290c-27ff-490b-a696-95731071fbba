import config from "@/config";
import http from "@/utils/request";

const teacher = {
	list: {
		url: `${config.API_URL}/buss/teacher/list`,
		name: "获取用户列表",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
    lesson: {
		url: `${config.API_URL}/buss/teacher/lesson`,
		name: "",
		get: async function (params = {}) {
			return await http.get(this.url, this.name, params);
		}
	},
    

	save: {
		url: `${config.API_URL}/buss/teacher/save`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

    myinfo: {
		url: `${config.API_URL}/buss/teacher/myinfo`,
		name: "",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
    basic: {
		url: `${config.API_URL}/buss/teacher/basic`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

    

    skinfo: {
		url: `${config.API_URL}/buss/teacher/skinfo`,
		name: "新增编辑用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},
	del: {
		url: `${config.API_URL}/buss/teacher/remove`,
		name: "删除用户",
		post: async function (data = {}) {
			return await http.post(this.url, this.name, data);
		}
	},

}
export default teacher;
