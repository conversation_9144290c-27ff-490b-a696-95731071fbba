const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {

    async getlessonAction(){
        let model=this.model("lesson");
        let res=await model.where({"parent_id":"0","del_flag":"0"}).order("sort asc").field("id,name").select();
        console.log(res);
        return this.json(res);

    }

    async getlessonallAction(){
        let model=this.model("lesson");
        let model2=this.model("lesson_user");
        const user = await this.session('userInfo');
        
        let id=this.post("id");
        let res=await model.where({"parent_id":id,"del_flag":"0","type":user.level}).order("sort asc").field("id,name,type,ifgl").select();
        let arr=[];
        for(var item of res){
            let children=await model.where({"parent_id":item.id,"del_flag":"0",}).order("sort asc").field("id,name,type,ifgl").select();
            let one={};
            one.id=item.id;
            one.text=item.name
            if(item.ifgl==1){

                one.disabled=true;
            }

            let color=0;
            if(!think.isEmpty(children)){
                let arr2=[];
                console.log("=====================");

                let allscore=0;
                for(var item2 of children){
                  

                    let tmp={"id":item2.id,"text":item2.name}
                    tmp.flagcolor="black";


                     


                    let tmp2=await model2.where({lessonid:item2.id,userid:user.id}).find();


                    if(think.isEmpty(tmp2)){

                        tmp.score=50;
                    }else{
                        tmp.score=tmp2.score;
                    }

                    allscore=allscore+tmp.score;
                   
               

                    if(item2.ifgl==1){
                        tmp.disabled=true;
                        tmp.flagcolor="gainsboro";
                 
                        
                        if(!think.isEmpty(tmp2)&&tmp2.state==0){
                            tmp.flagcolor="black";
                            tmp.disabled=false
                        }

                        if(!think.isEmpty(tmp2)&&tmp2.state==1){

                            tmp.disabled=false
                            tmp.flagcolor="green";
                            if(color==0){
                                color=1;
                            }
                        }
                       
                    }else{

                        if(tmp2.state==1){
                           
                           

                            // 增加指定秒数
                            if(color==0){
                                color=1;
                            }
                           
                            tmp.flagcolor="green";
                            console.log("+++++++++++++++");
                            console.log(tmp2);


                            if(!think.isEmpty(tmp2.lasttime)){
                                let date = new Date(tmp2.lasttime);
                                let config=this.config("ti")
                                date.setSeconds(date.getSeconds() +parseInt(config.lessontime1));
                                console.log(date)
                                let currentDate = new Date();

                                // 比较日期
                                if (date <currentDate) {

                                    if(color==1||color==0){
                                        color=2;
                                    }

                                    tmp.flagcolor="yellow";
                                    
                                }


                                 date = new Date(tmp2.lasttime);
                               
                                date.setSeconds(date.getSeconds() +parseInt(config.lessontime2));

                                 currentDate = new Date();
                                 console.log(date)
                                // 比较日期
                                if (date < currentDate) {

                                    
                                    if(color==1||color==0||color==2){
                                        color=3;
                                    }
                                    tmp.flagcolor="red";
                                    
                                }


                            }
                        }
                    }




                    arr2.push(tmp);


                   
                }
                
                one.children=arr2;
                if(color==1){
                    one.background="green";
                }else if(color==2){
                    one.background="yellow";
                }else if(color==3){
                    one.background="red";
                }else{
                    one.background="black";
                }
               

                console.log(allscore)
                console.log(arr2.length)

                one.score=allscore/arr2.length;
            
            }else{
                one.score=50;
                
            }

            arr.push(one);
        }

   
        console.log(JSON.stringify(arr));
        return this.json(arr);

    }





}