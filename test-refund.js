const fs = require('fs');
const https = require('https');
const crypto = require('crypto');

// 证书路径和配置
const certPath = 'C:/jsjy/jsjy/thinkjs/www/static/upload/4200360b-2ce0-4ac4-b4ea-0a983d221505.p12';
const config = {
  appid: 'wx19db7a1f925ec299',
  mch_id: '1706925459',
  key: 'kH3uB8yO0jP9rL9uP8oH0jH4sI0vX6zB'
};

// 生成随机字符串
function generateNonceStr(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成签名
function generateSign(params) {
  // 1. 对参数按照key=value的格式，并按照参数名ASCII字典序排序
  const sortedParams = Object.keys(params).sort().reduce((result, key) => {
    if (params[key] !== undefined && params[key] !== '' && key !== 'sign') {
      result.push(`${key}=${params[key]}`);
    }
    return result;
  }, []);

  // 2. 拼接API密钥
  sortedParams.push(`key=${config.key}`);
  const stringSignTemp = sortedParams.join('&');

  // 3. MD5加密并转为大写
  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
}

// 将对象转换为XML
function objectToXml(obj) {
  let xml = '<xml>';
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      xml += `<${key}>${obj[key]}</${key}>`;
    }
  }
  xml += '</xml>';
  return xml;
}

// 构建退款参数
const params = {
  appid: config.appid,
  mch_id: config.mch_id,
  nonce_str: generateNonceStr(),
  out_trade_no: 'TEST' + Date.now(),
  out_refund_no: 'RF' + Date.now(),
  total_fee: 100, // 1元
  refund_fee: 100, // 1元
  refund_desc: '测试退款'
};

// 生成签名
params.sign = generateSign(params);

// 将参数转换为XML
const xmlData = objectToXml(params);

console.log('请求数据:', xmlData);

try {
  // 读取证书
  console.log('读取证书...');
  const certContent = fs.readFileSync(certPath);
  
  // 创建请求选项
  const options = {
    hostname: 'api.mch.weixin.qq.com',
    port: 443,
    path: '/secapi/pay/refund',
    method: 'POST',
    pfx: certContent,
    passphrase: config.mch_id,
    headers: {
      'Content-Type': 'text/xml',
      'Content-Length': Buffer.byteLength(xmlData)
    }
  };
  
  console.log('发送请求...');
  
  // 发送请求
  const req = https.request(options, (res) => {
    console.log('状态码:', res.statusCode);
    console.log('响应头:', res.headers);
    
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('响应数据:', data);
    });
  });
  
  req.on('error', (error) => {
    console.error('请求错误:', error);
  });
  
  req.write(xmlData);
  req.end();
  
} catch (error) {
  console.error('处理失败:', error);
} 