import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/buss/jx/page`,
		name: "数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

 
	 
	save: {
		url: `${config.API_URL}/buss/jx/save`,
		name: "保存",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
 
	list: {
		url: `${config.API_URL}/buss/jx/list`,
		name: "列表",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	delete: {
		url: `${config.API_URL}/buss/jx/delete`,
		name: "删除",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}

 