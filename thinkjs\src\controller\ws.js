
let socketList = {};
module.exports = class extends think.Controller {
  constructor(...arg) {
    super(...arg);
  }

  async openAction() {

    // this.broadcast('online', {
    //   userid: userInfo.id,
    //   username: userInfo.name,
    //   message: '上线了'
    // });
    const userInfo = await this.session('userInfo');
     // socketList[userInfo.id] = this.websocket;
  }

  closeAction() {
    console.log('ws close');
  }

  async addUserAction() {
    // console.log('addUserAction ...');
     console.log('获取客户端 addUser 事件发送的数据', this.wsData);
    // console.log('获取当前 WebSocket 对象', this.websocket);
    const userInfo = await this.session('userInfo');
    // this.emit('message', '收到了', socketList[userInfo.id]);
    // this.broadcast('what', {
    //   msg: '收到了'
    // });


    // console.log('获取当前 WebSocket 对象', this.websocket);
    // console.log('判断当前请求是否是 WebSocket 请求', this.isWebsocket);
    return this.success();
  }
};
