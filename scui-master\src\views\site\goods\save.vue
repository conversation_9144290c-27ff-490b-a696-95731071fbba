<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/10/12 10:47:30
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px" label-position="left">

		<el-form-item label="物品名称" prop="goods_name">
			<el-input v-model="form.goods_name" clearable></el-input>
		</el-form-item>

		<el-form-item label="领用时间" prop="goods_use_date">
			<el-date-picker
				v-model="form.goods_use_date
"
				type="date"
				value-format="YYYY-MM-DD"
				style="width:100%"
			>
			</el-date-picker>
		</el-form-item>

		<el-form-item label="领用人" prop="goods_use_name">
			<el-input v-model="form.goods_use_name" clearable></el-input>
		</el-form-item>

	</el-form>
</template>

<script>
export default {
	props: {
		mode: { type: String, default: "add" }
	},
	data() {
		return {
			//表单数据
			form: {
				id:"",

				goods_name: "",

				goods_use_date: "",

				goods_use_name: "",

			},
			//验证规则
			rules: {

				goods_name: [
					{required: true, message: '请输入物品名称'}
				],

				goods_use_date: [
					{required: true, message: '请输入领用时间'}
				],

				goods_use_name: [
					{required: true, message: '请输入领用人'}
				],

			},
		}
	},
	mounted(){

	},
	methods: {
		//表单提交方法
		submit(callback){
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				}else{
					return false;
				}
			})
		},
		//表单注入数据
		setData(data){

			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
