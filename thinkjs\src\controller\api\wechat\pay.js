
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {
  /**
   * 微信支付
   */
  async wxpayAction() {
    if (!this.isPost) {
      return this.fail(400, '请求方法错误');
    }

    const { orderId, orderNo, totalAmount } = this.post();

    if (!orderId || !orderNo || !totalAmount) {
      return this.fail(400, '参数不完整');
    }

    try {
      // 1. 获取用户信息
      let sessionuser = await this.session("userInfo");
      console.log(sessionuser);
      const userId = sessionuser.id;
      const user = await this.model('student').where({ id: userId }).find();
      console.log("user",user);
      if (think.isEmpty(user)) {
        return this.fail(301, '用户不存在');
      }

      if (think.isEmpty(user.openid)) {
        return this.fail(301, '用户未绑定微信，无法使用微信支付');
      }

      // 2. 验证订单
      const order = await this.model('order').where({
        id: orderId,
        order_no: orderNo,
        user_id: userId,
        pay_status: 0, // 未支付
        order_status: 0 // 待付款
      }).find();


      const goods=await this.model("product").where({id:order.product_id}).find();

      if (think.isEmpty(order)) {
        return this.fail(301, '订单不存在或已支付');
      }

      // 验证金额
      if (parseFloat(order.pay_amount) !== parseFloat(totalAmount)) {
        return this.fail(301, '订单金额不匹配');
      }

      // 3. 调用微信支付服务
      const wxpayService = think.service('wxpay');
      const payParams = await wxpayService.createPayParams({
        orderNo: orderNo,
        totalAmount: totalAmount,
        openid: user.openid,
        schoolid:order.schoolid,
        goodsname:goods.name,
        ip:this.ctx.headers['x-real-ip']
      });

      // 4. 更新订单的prepay_id
      await this.model('order').where({ id: orderId }).update({
        prepay_id: payParams.prepay_id,
        update_time: think.datetime()
      });



      // 6. 返回支付参数
      return this.success(payParams);
    } catch (error) {
      think.logger.error('微信支付失败', error);
      return this.fail(301, '微信支付失败: ' + error.message);
    }
  }

  /**
   * 查询订单支付状态
   */
  async queryAction() {
    const { orderNo } = this.get();

    if (!orderNo) {
      return this.fail(301, '订单号不能为空');
    }

    try {
      // 1. 查询本地订单状态
      const order = await this.model('order').where({ order_no: orderNo }).find();

      if (think.isEmpty(order)) {
        return this.fail(301, '订单不存在');
      }

      // 如果订单已支付，直接返回
      if (order.pay_status === 1) {
        return this.success(order);
      }

      // 2. 调用微信支付查询接口
      const wxpayService = think.service('wxpay');
      const result = await wxpayService.queryOrder(orderNo);

      // 3. 处理查询结果
      if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
        if (result.trade_state === 'SUCCESS') {
          // 支付成功，更新订单状态
          await this.updateOrderPaid(order.id, orderNo, result.transaction_id);
          
          // 重新查询订单
          const updatedOrder = await this.model('order').where({ id: order.id }).find();
          return this.success(updatedOrder);
        } else {
          // 返回当前订单状态
          return this.success(order);
        }
      } else {
        return this.fail(500, result.return_msg || result.err_code_des || '查询订单失败');
      }
    } catch (error) {
      think.logger.error('查询订单支付状态失败', error);
      return this.fail(301, '查询订单支付状态失败: ' + error.message);
    }
  }

  /**
   * 支付结果通知
   * 微信支付结果通知的接口
   */
  async notifyAction() {
    try {
      // 1. 获取通知数据
      const xmlData = await this.getPayNotifyData();

      // 2. 验证通知
      const wxpayService = think.service('wxpay');
      const verifyResult = await wxpayService.verifyNotify(xmlData);

      if (!verifyResult.verified) {
        return this.displayNotifyResult('FAIL', '签名验证失败');
      }

      const notifyData = verifyResult.data;

      // 3. 验证支付结果
      if (notifyData.return_code !== 'SUCCESS' || notifyData.result_code !== 'SUCCESS') {
        return this.displayNotifyResult('FAIL', '支付失败');
      }

      // 4. 查询订单
      const orderNo = notifyData.out_trade_no;
      const order = await this.model('order').where({ order_no: orderNo }).find();

      if (think.isEmpty(order)) {
        return this.displayNotifyResult('FAIL', '订单不存在');
      }

      // 5. 验证金额
      const totalFee = parseInt(notifyData.total_fee);
      const orderAmount = Math.round(parseFloat(order.pay_amount) * 100);

      if (totalFee !== orderAmount) {
        return this.displayNotifyResult('FAIL', '金额不匹配');
      }

      // 6. 更新订单状态
      if (order.pay_status === 0) {
        await this.updateOrderPaid(order.id, orderNo, notifyData.transaction_id);
      }

      // 7. 返回成功
      return this.displayNotifyResult('SUCCESS', 'OK');
    } catch (error) {
      think.logger.error('处理支付通知失败', error);
      return this.displayNotifyResult('FAIL', '处理通知失败');
    }
  }

  /**
   * 关闭订单
   */
  async closeAction() {
    if (!this.isPost) {
      return this.fail(400, '请求方法错误');
    }

    const { orderId } = this.post();

    if (!orderId) {
      return this.fail(400, '订单ID不能为空');
    }

    try {
      // 1. 查询订单
      const order = await this.model('order').where({ id: orderId }).find();

      if (think.isEmpty(order)) {
        return this.fail(404, '订单不存在');
      }

      // 只能关闭未支付的订单
      if (order.pay_status !== 0 || order.order_status !== 0) {
        return this.fail(400, '只能关闭未支付的订单');
      }

      // 2. 调用微信支付关闭接口
      const wxpayService = think.service('wxpay');
      const result = await wxpayService.closeOrder(order.order_no);

      if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
        // 3. 更新订单状态
        await this.model('order').where({ id: orderId }).update({
          order_status: 3, // 已关闭
          update_time: think.datetime()
        });

        // 4. 记录订单日志
        await this.model('order_log').add({
          order_id: orderId,
          order_no: order.order_no,
          action: 'close',
          status: 3,
          operator_id: this.ctx.state.user.id,
          operator_name: this.ctx.state.user.username || this.ctx.state.user.phone,
          content: '订单已关闭',
          ip: this.ctx.ip,
          create_time: think.datetime()
        });

        return this.success(null, '订单关闭成功');
      } else {
        return this.fail(500, result.return_msg || result.err_code_des || '关闭订单失败');
      }
    } catch (error) {
      think.logger.error('关闭订单失败', error);
      return this.fail(500, '关闭订单失败: ' + error.message);
    }
  }

  /**
   * 申请退款
   */
  async refundAction() {
    if (!this.isPost) {
      return this.fail(400, '请求方法错误');
    }

    const { orderId, refundAmount, refundReason } = this.post();

    if (!orderId || !refundAmount) {
      return this.fail(400, '参数不完整');
    }

    try {
      // 1. 查询订单
      const order = await this.model('order').where({ id: orderId }).find();

      if (think.isEmpty(order)) {
        return this.fail(404, '订单不存在');
      }

      // 只能退款已支付的订单
      if (order.pay_status !== 1 || order.order_status !== 1) {
        return this.fail(400, '只能退款已支付的订单');
      }

      // 验证退款金额
      if (parseFloat(refundAmount) > parseFloat(order.pay_amount)) {
        return this.fail(400, '退款金额不能大于支付金额');
      }

      // 2. 生成退款单号
      const refundNo = this.generateRefundNo();

      // 3. 调用微信支付退款接口
      const wxpayService = think.service('wxpay');
      const result = await wxpayService.refund({
        orderNo: order.order_no,
        refundNo: refundNo,
        totalAmount: order.pay_amount,
        refundAmount: refundAmount,
        refundReason: refundReason
      });

      if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
        // 4. 更新订单状态
        await this.model('order').where({ id: orderId }).update({
          pay_status: 2, // 已退款
          update_time: think.datetime(),
          refund_amount: refundAmount
        });

        // 5. 记录退款信息
        await this.model('order_payment').add({
          order_id: orderId,
          order_no: order.order_no,
          payment_no: refundNo,
          payment_type: 1, // 微信支付
          payment_amount: -refundAmount, // 负数表示退款
          payment_status: 1, // 已完成
          transaction_id: result.refund_id,
          create_time: think.datetime(),
          update_time: think.datetime()
        });

        // 6. 记录订单日志
        await this.model('order_log').add({
          order_id: orderId,
          order_no: order.order_no,
          action: 'refund',
          status: 2,
          operator_id: this.ctx.state.user.id,
          operator_name: this.ctx.state.user.username || this.ctx.state.user.phone,
          content: `订单已退款，退款金额：${refundAmount}，原因：${refundReason || '用户申请退款'}`,
          ip: this.ctx.ip,
          create_time: think.datetime()
        });
        
        return this.success(null, '退款成功');
      } else {
        return this.fail(500, result.return_msg || result.err_code_des || '申请退款失败');
      }
    } catch (error) {
      think.logger.error('申请退款失败', error);
      return this.fail(500, '申请退款失败: ' + error.message);
    }
  }

  /**
   * 获取支付通知数据
   * @returns {Promise<string>} - XML数据
   */
  async getPayNotifyData() {
    return new Promise((resolve, reject) => {
      let data = '';
      this.ctx.req.on('data', chunk => {
        data += chunk;
      });
      this.ctx.req.on('end', () => {
        resolve(data);
      });
      this.ctx.req.on('error', err => {
        reject(err);
      });
    });
  }

  /**
   * 显示通知处理结果
   * @param {string} code - 结果代码
   * @param {string} message - 结果消息
   */
  displayNotifyResult(code, message) {
    this.ctx.type = 'text/xml';
    this.ctx.body = `<xml><return_code><![CDATA[${code}]]></return_code><return_msg><![CDATA[${message}]]></return_msg></xml>`;
  }

  /**
   * 更新订单为已支付状态
   * @param {number} orderId - 订单ID
   * @param {string} orderNo - 订单号
   * @param {string} transactionId - 微信支付交易号
   */
  async updateOrderPaid(orderId, orderNo, transactionId) {
    // 开启事务
    const model = this.model();
    await model.startTrans();

    try {
      // 1. 更新订单状态
      await this.model('order').where({ id: orderId }).update({
        pay_status: 1, // 已支付
        order_status: 1, // 已完成
        pay_time: think.datetime(),
        update_time: think.datetime()
      });

      // 2. 更新支付记录
      await this.model('order_payment').where({
        order_id: orderId,
        payment_status: 0
      }).update({
        payment_status: 1, // 已支付
        transaction_id: transactionId,
        payment_time: think.datetime(),
        update_time: think.datetime()
      });

      // 3. 记录订单日志
      await this.model('order_log').add({
        order_id: orderId,
        order_no: orderNo,
        action: 'pay',
        status: 1,
        content: '订单支付成功',
        ip: this.ctx.ip,
        create_time: think.datetime()
      });

      // 4. 查询订单商品
      const orderProducts = await this.model('order_product').where({ order_id: orderId }).select();
      
      // 5. 处理会员购买逻辑
      for (const product of orderProducts) {
        if (product.product_id) {
          // 查询订单所属用户
          const order = await this.model('order').where({ id: orderId }).find();
          const userId = order.user_id;
          
          // 查询商品信息
          const productInfo = await this.model('product').where({ id: product.product_id }).find();
          
          if (!think.isEmpty(productInfo)) {
            // 更新用户会员信息
           await this.model('product').where({ id: product.product_id }).decrement("quantity",1)


           await this.model("school").where({id:order.schoolid}).increment("canusetime",productInfo.duration);
            
            // 更新用户会员信息
           
          }
        }
      }

      // 提交事务
      await model.commit();
    } catch (error) {
      // 回滚事务
      await model.rollback();
      throw error;
    }
  }

  /**
   * 生成支付流水号
   * @returns {string} - 支付流水号
   */
  generatePaymentNo() {
    return 'PAY' + Date.now() + Math.random().toString().substr(2, 6);
  }

  /**
   * 生成退款单号
   * @returns {string} - 退款单号
   */
  generateRefundNo() {
    return 'REF' + Date.now() + Math.random().toString().substr(2, 6);
  }
};