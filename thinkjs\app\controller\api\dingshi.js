function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Controller {

  //处理到期的题
  processAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      let model = _this.model("tk_user");

      let sql = "update sys_tk_user set state=0 where now() >=yxq";
      yield model.execute(sql);

      let model2 = _this.model("bk");

      //   await model.execute("UPDATE sys_bk SET state = 3 WHERE skdate < NOW()")
      yield model.execute("UPDATE sys_bk SET state = 3 WHERE skdate <= DATE_SUB(NOW(), INTERVAL 2 HOUR) and state=2");

      yield model.execute("UPDATE sys_bk SET state = 4 WHERE skdate <= NOW() and state=3");

      let list = yield model.query("SELECT * FROM buss_key where yxq <now() and state2=0");

      for (let tmp of list) {
        let keyrecord = yield _this.model("keyrecord").where({ "id": tmp.keyrecord }).find();

        yield _this.model("school").where({ "id": keyrecord.schoolid }).increment("canusetime", keyrecord.usetime);
        yield _this.model("key").where({ "id": tmp.id }).update({ "state2": 2 });
      }

      return _this.json("ok");
    })();
  }

  processscoreAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      let model = _this2.model("student");

      let model2 = _this2.model("userdaysinfo");

      let users = yield model.where({ "del_flag": 0 }).select();

      for (let u of users) {
        if (!think.isEmpty(u.level)) {
          let lessones = yield _this2.model("lesson").where({ "type": u.level, "parent_id": 0, "del_flag": 0 }).field("id").select();

          for (let l of lessones) {

            let para = {};

            para.uid = u.id;
            para.lessonid = l.id;
            para.create_date = think.datetime();

            let ifhave = yield _this2.model("userdaysinfo").where(para).find();

            if (think.isEmpty(ifhave)) {

              let child = yield _this2.model("lesson").where({ "parent_id": l.id, "del_flag": 0 }).field("id").select();
              if (!think.isEmpty(child)) {

                let tmpsql = "SELECT ifnull(avg(score),50)  as score  FROM sys_lesson_user where userid=" + u.id + " and lessonid  in (" + child.map(function (item) {
                  return item.id;
                }).join(',') + ")";
                let avg = yield model.query(tmpsql);
                para.score = avg[0]['avg'];
              } else {
                para.score = 50;
              }

              yield _this2.model("userdaysinfo").add(para);
            }
          }
        }
      }

      return _this2.json("ok");
    })();
  }

  caclscoreAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {

      let model = _this3.model("student");
      let users = yield model.where({ "del_flag": 0 }).select();
      let model2 = _this3.model("userdaysinfo");

      for (let u of users) {
        const scores = yield _this3.calculateUserAllScores(u.id);
        console.log(scores);
        let arr = [];
        for (let s of scores) {
          arr.push({
            uid: u.id,
            lessonid: s.lessonId,
            lessonname: s.name,
            score: s.score,
            create_date: think.datetime("YYYY-MM-DD")
          });
        }

        console.log("insert", arr);

        yield model2.where({ "uid": u.id, "create_date": think.datetime("YYYY-MM-DD") }).delete();
        yield model2.addMany(arr);
      }
      return _this3.json("ok");
    })();
  }

  calculateUserAllScores(userId) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      // 获取所有根科目（parent_id = 0）
      const rootLessons = yield _this4.model('lesson').where({
        parent_id: 0,
        del_flag: '0'
      }).select();

      const results = [];

      // 计算每个根科目的成绩
      for (const rootLesson of rootLessons) {
        const score = yield _this4.calculateSubjectScore(userId, rootLesson.id);
        results.push({
          lessonId: rootLesson.id,
          name: rootLesson.name,
          score: Math.round(score)
        });
      }

      return results;
    })();
  }

  /**
   * 递归计算科目成绩
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<number>} 返回计算后的成绩
   */
  calculateSubjectScore(userId, lessonId) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      // 获取当前科目的所有子科目
      const children = yield _this5.model('lesson').where({
        parent_id: lessonId,
        del_flag: '0'
      }).select();

      // 如果没有子科目，说明是最底层科目，直接从 lesson_user 表获取成绩
      if (think.isEmpty(children)) {
        const userScore = yield _this5.model('lesson_user').where({
          userid: userId,
          lessonid: lessonId
        }).field('score').find();

        return userScore.score || 50; // 如果没有成绩记录，返回默认分数50
      }

      // 如果有子科目，递归计算所有子科目的成绩
      let totalScore = 0;
      let validChildren = 0;

      for (const child of children) {
        const childScore = yield _this5.calculateSubjectScore(userId, child.id);
        totalScore += childScore;
        validChildren++;
      }

      // 返回子科目的平均分
      return validChildren > 0 ? totalScore / validChildren : 50;
    })();
  }

  /**
   * 获取完整的科目成绩树
   * @param {number} userId 用户ID
   * @param {number} lessonId 科目ID
   * @returns {Promise<Object>} 返回包含成绩的科目树
   */
  getScoreTree(userId, lessonId) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      // 获取当前科目信息
      const lesson = yield _this6.model('lesson').where({
        id: lessonId,
        del_flag: '0'
      }).find();

      if (think.isEmpty(lesson)) {
        return null;
      }

      // 计算当前科目成绩
      const score = yield _this6.calculateSubjectScore(userId, lessonId);

      // 获取子科目
      const children = yield _this6.model('lesson').where({
        parent_id: lessonId,
        del_flag: '0'
      }).select();

      const result = {
        id: lesson.id,
        name: lesson.name,
        score: Math.round(score),
        children: []
      };

      // 递归获取子科目的成绩树
      if (!think.isEmpty(children)) {
        for (const child of children) {
          const childTree = yield _this6.getScoreTree(userId, child.id);
          if (childTree) {
            result.children.push(childTree);
          }
        }
      }

      return result;
    })();
  }

  /**
   * 获取成绩树
   */
  treeAction() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        const userId = 3;
        const lessonId = _this7.post('lessonId'); // 可选，不提供则返回所有根科目的树

        if (!userId) {
          return _this7.fail('请提供用户ID');
        }

        if (lessonId) {
          // 获取指定科目的成绩树
          const tree = yield _this7.getScoreTree(userId, lessonId);
          return _this7.success(tree);
        } else {
          // 获取所有根科目的成绩树
          const rootLessons = yield _this7.model('lesson').where({
            parent_id: '0',
            del_flag: '0'
          }).select();

          const forest = [];
          for (const root of rootLessons) {
            const tree = yield _this7.getScoreTree(userId, root.id);
            if (tree) {
              forest.push(tree);
            }
          }

          // console.log(forest);
          return _this7.success(forest);
        }
      } catch (err) {
        think.logger.error('Score tree calculation error:', err);
        return _this7.fail(err.message);
      }
    })();
  }

  processvipAction() {
    var _this8 = this;

    return _asyncToGenerator(function* () {
      let auto = think.service('auto');
      yield auto.autoDeductVipDays();

      return _this8.json(1);
    })();
  }

  processschoolAction() {
    var _this9 = this;

    return _asyncToGenerator(function* () {

      yield _this9.model("school").update({ "todaycost": 0 });

      return _this9.json(1);
    })();
  }

  processorderAction() {
    var _this10 = this;

    return _asyncToGenerator(function* () {
      try {
        // 获取30分钟前的时间
        const thirtyMinutesAgo = think.datetime(Date.now() - 30 * 60 * 1000);

        // 删除超过30分钟未支付的订单
        const result = yield _this10.model('order').where({
          pay_status: 0,
          create_time: ['<', thirtyMinutesAgo]
        }).update({ "del_flag": 1 });

        think.logger.info(`已删除 ${result} 条超时未支付订单`);

        return _this10.json({
          code: 200,
          message: `成功删除${result}条超时订单`,
          data: {
            deleted: result
          }
        });
      } catch (error) {
        think.logger.error('删除超时订单失败:', error);
        return _this10.json({
          code: 500,
          message: '删除超时订单失败: ' + error.message
        });
      }
    })();
  }

  processyxAction() {
    var _this11 = this;

    return _asyncToGenerator(function* () {
      try {
        // 获取所有有扫码记录但用户yxid为空的情况
        const scanRecords = yield _this11.model('yx_scan').alias('scan').join('buss_student student ON scan.openid = student.openid').where({
          'student.del_flag': 0,
          'student.yxid': ['exp', 'IS NULL OR student.yxid = 0 OR student.yxid = ""']
        }).field('scan.openid, scan.yx_id, student.id as student_id, student.yxid').select();

        if (think.isEmpty(scanRecords)) {
          return _this11.json({
            code: 200,
            message: '没有需要更新的用户记录',
            data: {
              updated: 0
            }
          });
        }

        let updatedCount = 0;

        // 批量更新用户的yxid
        for (const record of scanRecords) {
          if (record.yx_id && record.student_id) {
            yield _this11.model('student').where({
              id: record.student_id,
              del_flag: 0
            }).update({
              yxid: record.yx_id
            });

            updatedCount++;

            think.logger.info(`更新用户yxid: 学生ID=${record.student_id}, openid=${record.openid}, yxid=${record.yx_id}`);
          }
        }

        return _this11.json({
          code: 200,
          message: `成功更新${updatedCount}条用户记录`,
          data: {
            updated: updatedCount,
            total: scanRecords.length
          }
        });
      } catch (error) {
        think.logger.error('更新用户yxid失败:', error);
        return _this11.json({
          code: 500,
          message: '更新用户yxid失败: ' + error.message,
          data: {
            updated: 0
          }
        });
      }
    })();
  }

};
//# sourceMappingURL=dingshi.js.map