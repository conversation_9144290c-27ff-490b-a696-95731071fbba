import config from "@/config";
import http from "@/utils/request";

const product = {
 
	page: {
		url: `${config.API_URL}/buss/product/page`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
    save: {
		url: `${config.API_URL}/buss/product/save`,
		name: "保存",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},  
    delete: {
		url: `${config.API_URL}/buss/product/delete`,
		name: "删除",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},
    upload: {
		url: `${config.API_URL}/buss/product/upload`,
		name: "上传",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},

    update: {
		url: `${config.API_URL}/buss/product/update`,
		name: "更新",
		post: async function (params) {
			return await http.post(this.url, this.name, params);
		}
	},

}

export default product;
