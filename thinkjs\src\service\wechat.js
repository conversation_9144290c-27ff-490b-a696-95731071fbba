const crypto = require('crypto')
const axios = require('axios')

module.exports = class extends think.Service {
  // 获取全局access_token（带缓存）
  async getAccessToken(id) {
    const cacheKey = id+'_wechat_access_token'
    let gzhconfig=await think.config(id+'_wechat');
    let token = await think.cache(cacheKey)

    const originalDate = new Date();
    let totime = await think.cache("totime")

  

    console.log("cache====",token)
    let appid=gzhconfig.appid;

  
    let secret=gzhconfig.secret;
    if (think.isEmpty(totime)||originalDate.getTime()-totime>7200000) {
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`
      const response = await axios.get(url)
      const res = response.data
      
      console.log(res);
      token = res.access_token
        await think.cache("totime",originalDate.getTime());
        console.log(token);
       await think.cache(cacheKey, token)

    //   await think.cache(cacheKey, token,{
    //     timeout: res.data.expires_in - 300
    // })


    }
    return token
  }

  // 获取jsapi_ticket
  async getJsapiTicket(id) {
    const cacheKey = id+'_wechat_jsapi_ticket'
    let ticket = await think.cache(cacheKey)
    
    if (!ticket) {
      const accessToken = await this.getAccessToken(id)
      const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`
      
      const response = await axios.get(url)
      const res = response.data
      
      ticket = res.ticket

      

      await think.cache(cacheKey, ticket,{
        timeout: res.expires_in - 300
    })

      //await think.cache(cacheKey, ticket, res.data.expires_in - 300)
    }
    return ticket
  }

  // 生成JSSDK签名
  async getJsConfig(url,id) {
    let gzhconfig=await think.config(id+'_wechat');
    const ticket = await this.getJsapiTicket(id)
    const noncestr = crypto.randomBytes(16).toString('hex')
    const timestamp = parseInt(Date.now() / 1000)
    
    const str = `jsapi_ticket=${ticket}&noncestr=${noncestr}&timestamp=${timestamp}&url=${url}`
    const signature = crypto.createHash('sha1').update(str).digest('hex')

    return {
      appId: gzhconfig.appid,
      timestamp,
      nonceStr: noncestr,
      signature,
      jsApiList: await think.config('jsapi.apiList')
    }
  }

  async  getOpenidByCode(code,id) {
    try {
      let gzhconfig=await think.config(id+'_wechat');
      console.log(id+'_wechat')
      console.log("getOpenidByCode",gzhconfig);
      const appId = gzhconfig.appid;
      const appSecret = gzhconfig.secret;
     
      const response = await axios.get(
        `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`
      );

      const data = response.data;


      console.log(data);

      return {
        openid: data.openid,
        access_token: data.access_token,
        expires_in: data.expires_in
      };
    } catch (error) {
      console.error('微信接口错误:', error);
      throw new Error('获取用户信息失败');
    }
  }


 async buildAuthUrl(redirectUri,id, options = {}) {
    const {
      scope = 'snsapi_base', // 授权类型：snsapi_base静默授权/snsapi_userinfo手动授权
      state = 'STATE',       // 用于防止CSRF攻击的状态参数
      forcePopup = false     // 是否强制弹出授权页
    } = options
  
    // 参数校验
    if (!redirectUri) throw new Error('缺少redirect_uri参数')
    if (!['snsapi_base', 'snsapi_userinfo'].includes(scope)) {
      throw new Error('非法的scope参数')
    }

    let gzhconfig=await think.config(id+'_wechat');

    let appid=gzhconfig.appid;
  
    // 双重编码redirect_uri（微信要求）
    const encodedUri = encodeURIComponent(redirectUri);
    
    // 构建基础URL
    let authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodedUri}&response_type=code&scope=${scope}&state=123#wechat_redirect`
  
     
  
    return authUrl
  }
}