<!--
  订货使用
  可以全选删除
-->
<template>
	<div class="sc-form-table">
		<el-table :data="data" ref="table" border stripe @selection-change="selectionChange"
			:row-class-name="tableRowClassName">
			<el-table-column type="selection" width="55" />
			<el-table-column type="index" width="60" fixed="left">
				<template #header>
					<el-row >

						<el-button type="primary" icon="el-icon-plus" size="mini" circle @click="rowAdd" v-if="canadd">
						</el-button>
					</el-row>
					<el-row>
					<el-button type="danger" icon="el-icon-delete" size="mini" circle @click="rowsDel()"></el-button>

					</el-row>
				</template>
				<template #default="scope">
					<div class="sc-form-table-handle">
						<span>{{ scope.$index + 1 }}</span>
						<el-button type="danger" icon="el-icon-delete" size="mini" v-if="candel" circle @click="rowDel(scope.row,
						scope.$index)"></el-button>
					</div>
				</template>
			</el-table-column>
			<slot></slot>
			<el-table-column min-width="1"></el-table-column>
			<template #empty>
				{{ placeholder }}
			</template>
		</el-table>
	</div>
</template>

<script>
export default {
	props: {
		modelValue: { type: Array, default: () => [] },
		addTemplate: { type: Object, default: () => { } },
		placeholder: { type: String, default: "暂无数据" },
		canadd: { type: Boolean, default: true },
		candel: { type: Boolean, default: true },
	},
	data() {
		return {
			data: [],
			selection: [],
			selectionItemIndexes: []
		}
	},
	mounted() {
		this.data = this.modelValue
	},
	watch: {
		modelValue() {
			this.data = this.modelValue
		},
		data: {
			handler() {
				this.$emit('update:modelValue', this.data);
			},
			deep: true
		}
	},
	methods: {
		tableRowClassName({ row, rowIndex }) {
			row.index = rowIndex;
		},
		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
			this.selectionItemIndexes = [];
			selection.forEach(item => {
				this.selectionItemIndexes.push(item.index);
			});
		},
		rowsDel() {
			if (this.selection.length > 0) {
				this.data = this.data.filter((item, index) => {
					let arrlist = this.selectionItemIndexes;
					return !arrlist.includes(index);
				});
			}
			else {
				this.$alert("至少选择一条数据", "提示", { type: 'error' })
			}
		},
		rowAdd() {
			this.data.push({ ...this.addTemplate })
		},
		rowDel(row, index) {
			this.data.splice(index, 1)
		}
	}
}
</script>

<style scoped>
.sc-form-table .sc-form-table-handle {
	text-align: center;
}

.sc-form-table .sc-form-table-handle span {
	display: inline-block;
}

.sc-form-table .sc-form-table-handle button {
	display: none;
}

.sc-form-table .hover-row .sc-form-table-handle span {
	display: none;
}

.sc-form-table .hover-row .sc-form-table-handle button {
	display: inline-block;
}
</style>
