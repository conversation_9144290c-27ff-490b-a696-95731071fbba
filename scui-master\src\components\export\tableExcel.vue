<template>
	<el-button @click="exportExcel" type="primary" icon="el-icon-download" :size="size">{{ buttonName }}</el-button>
</template>

<script>
import XLSX from "xlsx";
import FileSaver from 'file-saver'

export default {
	props: {
		refName: {
			type: String,
			default: 'table'
		},
		excelName: {
			type: String,
			default: '导出表格'
		},
		buttonName: {
			type: String,
			default: '导出'
		},
		size: {
			type: String,
			default: 'small'
		},

	},
	methods: {
		// 导出excel
		exportExcel() {
			let table = this.findTable(this)
			if (!table) {
				this.$message({message: '没找到可导出的表格', type: 'warning'})
				return
			}
			try {
				let $e = table.$el
				let fix = $e.querySelector(`*[class^='el-table__fixed']`);
				let wb;
				if (fix) { //判断要导出的节点中是否有fixed的表格，如果有，转换excel时先将该dom移除，然后append回去
					$e.querySelector(`*[class^='el-table--fit']`).removeChild(fix)
					wb=XLSX.utils.table_to_book($e, {raw: true})
					$e.querySelector(`*[class^='el-table--fit']`).appendChild(fix)
				}
				else {
					wb=XLSX.utils.table_to_book($e, {raw: true})
				}
				let wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'})
				FileSaver.saveAs(
					new Blob([wbout], {type: 'application/octet-stream'}), this.excelName + ".xlsx"
				)
			} catch (e) {
				if (typeof console !== 'undefined') {
					console.error(e)
				}
			}

		},
		findTable(x) { // 遍历实例树查找有无能打印的table
			let vm = x.$parent
			if (vm === undefined) {
				return false
			}
			if (vm.$refs[this.refName]) {
				return vm.$refs[this.refName]
			}
			return this.findTable(vm)
		}
	}
}
</script>

<style>

</style>
