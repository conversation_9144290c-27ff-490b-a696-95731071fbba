const BaseRest = require('../rest.js');
module.exports = class extends BaseRest {


    async listAction() {
        
        let where={};

        let create_by=this.post("create_by");


        where['create_by']=create_by;

        const model = this.model('phrase');
     
        const res = await model.alias('c').where(where).order('create_date desc').select();
    

        return this.json(res);
      }


  

     

      async removeAction() {
        const id = this.get('id');

        const model = this.model('phrase');
        await model.where({'id': id}).delete();

        return this.json({'code': 200});
      }

      async saveAction() {
        const model = this.model('phrase');
        const data = this.post();
        let userInfo = await this.session('userInfo');
        data.create_by=userInfo.aid;

        if (think.isEmpty(data.id)) {
        
          await model.add(data);
        } else {
          await model.where({'id': data.id}).update(data);
        }

        this.json({code: 200});
      }



}
