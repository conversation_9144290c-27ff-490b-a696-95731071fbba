module.exports = class extends think.Service {
    /**
     * 添加用户VIP记录
     * @param {Object} vipData VIP数据对象，包含userid, lessson, nj, allday, orderid等字段
     * @return {Object} 保存后的VIP记录
     */
    async addVipRecord(vipData) {
      // 确保必要字段存在
      if (!vipData.userid || !vipData.lessson || !vipData.nj || !vipData.allday || !vipData.orderid||!vipData.type) {
        return { errno: 1, message: '缺少必要参数' };
      }
  console.log("========start============",vipData)
      // 查询是否存在相同课程和学科的VIP记录
      let existingVip = await think.model('vip').query(`
        SELECT * FROM sys_vip 
        WHERE 
          userid = ${vipData.userid} 
          And available_days>0
          AND nj = '${vipData.nj}'
          AND lessson = ${vipData.lessson}
        
        ORDER BY enddate DESC,sort ASC
        LIMIT 1
      `);
      console.log("========existingVip============",existingVip)
    
      console.log("========end============")
      const now = new Date();
      let startdate = now;
      
      // 设置初始值
      vipData.create_date = now;
      vipData.useday = 0;
      vipData.available_days = vipData.allday; // 可用天数等于总天数
      vipData.type = vipData.type; // 设置类型为product
      vipData.create_date=think.datetime();


      // 判断是否存在记录且可用天数不为0
      if (!think.isEmpty(existingVip)) {
        existingVip=existingVip[0];
        console.log("enddate",existingVip);
        // 如果存在且有可用天数，新记录的开始时间为原记录的结束时间
        startdate =existingVip.enddate
      }
      
      // 设置开始时间
      vipData.startdate = think.datetime(startdate, 'YYYY-MM-DD HH:mm:ss');
      console.log(startdate )
      console.log(vipData.startdate )
      // 计算结束时间 (开始时间 + 总天数)
      let enddate = new Date(startdate);
      console.log(enddate )
      enddate.setDate(enddate.getDate() + vipData.allday);
      console.log(enddate )
      vipData.enddate = think.datetime(enddate, 'YYYY-MM-DD HH:mm:ss');

      console.log(vipData.enddate )

      // 获取同类型、同课程、同学科、同用户的最大sort值并递增
      let maxSortRecord = await think.model('vip').where({
        type: 'product',
        nj: vipData.nj,
        lessson: vipData.lessson,
        userid: vipData.userid
      }).order('sort DESC').find();
      
      if (!think.isEmpty(maxSortRecord) && maxSortRecord.sort) {
        // 如果sort是数字字符串，则转为数字并+1
        let currentSort = parseInt(maxSortRecord.sort) || 0;
        vipData.sort = (currentSort + 1).toString();
      } else {
        // 如果没有记录或sort为空，则从1开始
        vipData.sort = '1';
      }
      

      console.log("vipdata",vipData);
      // 保存VIP记录
      const vipId = await think.model('vip').add(vipData);
      
      if (vipId) {
        return { errno: 0, data: { id: vipId, ...vipData } };
      } else {
        return { errno: 1, message: '添加VIP记录失败' };
      }
    }
    

 
      
    
  };