<template>
	<el-dialog title="反馈详情" v-model="visible" :width="700" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" ref="dialogForm" label-width="100px" disabled>
	
			<el-form-item label="反馈内容">
				<el-input v-model="form.content" type="textarea" :rows="4"></el-input>
			</el-form-item>
			<el-form-item label="反馈图片" v-if="form.pic && form.pic.length">
				<div class="image-preview">
					<el-image 
						v-for="(img, index) in form.pic" 
						:key="index"
						:src="img"
						:preview-src-list="form.pic"
						fit="cover"
						class="feedback-image"
					></el-image>
				</div>
			</el-form-item>
			<el-form-item label="添加人">
				<el-input v-model="form.create_name"></el-input>
			</el-form-item>
			<el-form-item label="联系方式">
				<el-input v-model="form.contact"></el-input>
			</el-form-item>
			<el-form-item label="添加时间">
				<el-input v-model="form.create_date"></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">关 闭</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['closed'],
	data() {
		return {
			visible: false,
			form: {
				type: '',
				content: '',
				images: [],
				creator: '',
				create_time: ''
			}
		}
	},
	methods: {
		open() {
			this.visible = true
			return this
		},
		setData(data) {
			this.form = JSON.parse(JSON.stringify(data))
			return this
		},
		getTagType(type) {
			const typeMap = {
				'bug反馈': 'danger',
				'使用建议': 'warning',
				'内容建议': 'success',
				'内容错误': 'info'
			}
			return typeMap[type] || ''
		}
	}
}
</script>

<style scoped>
.image-preview {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}
.feedback-image {
	width: 100px;
	height: 100px;
	border-radius: 4px;
	cursor: pointer;
}
</style>