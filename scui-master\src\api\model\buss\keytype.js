import config from "@/config";
import http from "@/utils/request";

const keytype = {
 
	page: {
		url: `${config.API_URL}/buss/keytype/page`,
		name: "列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

    delete: {
        url: `${config.API_URL}/buss/keytype/delete`,
        name: "删除",
        post: async function (params) {
            return await http.post(this.url, this.name, params);
        }
    },
    save:{
        url: `${config.API_URL}/buss/keytype/save`,
        name: "保存",
        post: async function (params) {
            return await http.post(this.url, this.name, params);
        }
    },
    listselect:{
        url: `${config.API_URL}/buss/keytype/listselect`,
        name: "列表",
        get: async function (params) {
            return await http.get(this.url, this.name, params);
        }
    }
}
export default keytype;