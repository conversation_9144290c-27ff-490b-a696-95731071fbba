<!--
 * @Descripttion: 此文件由SCUI生成，典型的VUE增删改列表页面组件
 * @version: 1.0
 * @Author: SCUI AutoCode 模板版本 1.0.0-beta.1
 * @Date: 2022/9/28 08:58:16
 * @LastEditors: (最后更新作者)
 * @LastEditTime: (最后更新时间)
-->

<template>
	<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
	         label-position="left">
		<el-row :gutter="15">
			<el-col :lg="12">
				<el-form-item label="逻辑机号" prop="site_no">
					<el-input v-model="form.site_no" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="场所名称" prop="site_name">
					<el-input v-model="form.site_name" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="24">
				<el-form-item label="场所地址" prop="site_address">
					<el-input v-model="form.site_address" clearable></el-input>
				</el-form-item>
			</el-col>

			<el-col :lg="12">
				<el-form-item label="场所星级" prop="site_star">
					<sc-select v-model="form.site_star"
					           :selectConfig="selectConfig.siteStar"
					           clearable dic="siteStar"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="场所类型" prop="site_type">
					<sc-select v-model="form.site_type"
					           :selectConfig="selectConfig.siteType"
					           clearable dic="siteType"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="电话号码" prop="site_phone">
					<el-input v-model="form.site_phone" clearable></el-input>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="场所区域" prop="site_area">
					<sc-select v-model="form.site_area"
					           :selectConfig="selectConfig.siteArea"
					           clearable dic="siteArea"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="场所类别" prop="site_class">
					<sc-select v-model="form.site_class"
					           :selectConfig="selectConfig.siteClass"
					           clearable dic="siteClass"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="场所房屋" prop="site_hosue">
					<sc-select v-model="form.site_hosue"
					           :selectConfig="selectConfig.siteHouee"
					           clearable dic="siteHosue"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="是否使用" prop="site_enable">
					<sc-select v-model="form.site_enable"
					           :selectConfig="selectConfig.yesNo"
					           clearable dic="yesNo"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="新增日期" prop="site_add_date">
					<el-date-picker
						v-model="form.site_add_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="撤销日期" prop="site_revoke_date">
					<el-date-picker
						v-model="form.site_revoke_date"
						type="date"
						value-format="YYYY-MM-DD"
						style="width:100%"
					>
					</el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :lg="12">
				<el-form-item label="保证金退还" prop="site_bond">
					<sc-select v-model="form.site_bond"
					           :selectConfig="selectConfig.yesNo"
					           clearable dic="yesNo"
					           filterable :disabled="mode=='show'"
					           style="width: 100%"></sc-select>
				</el-form-item>
			</el-col>
			<el-col :lg="24">
				<el-form-item label="撤销原因" prop="site_revoke_note">
					<el-input v-model="form.site_revoke_note" :autosize="{ minRows: 2, maxRows: 4 }"
					          type="textarea" clearable></el-input>
				</el-form-item>
			</el-col>

		</el-row>
	</el-form>
</template>

<script>
export default {
	props: {
		mode: { type: String, default: "add" }
	},
	data() {
		return {
			//表单数据
			form: {
				id:"",

				site_no: "",

				site_name: "",

				site_address: "",

				site_phone: "",

				site_type: "",

				site_area: "",

				site_class: "",

				site_hosue: "",
				site_enable:"",
				site_add_date:"",
				site_revoke_date:"",
				site_revoke_note:"",
				site_bond:"",

			},
			//验证规则
			rules: {

				site_no: [
					{required: true, message: '请输入逻辑机号'}
				],

				site_name: [
					{required: true, message: '请输入场所名称'}
				],

				site_address: [
					{required: true, message: '请输入场所地址'}
				],

				site_phone: [
					{required: true, message: '请输入电话号码'}
				],

				site_type: [
					{required: true, message: '请输入场所类型'}
				],

				site_star: [
					{required: true, message: '请输入场所星级'}
				],

				site_area: [
					{required: true, message: '请输入场所区域'}
				],

				site_class: [
					{required: true, message: '请输入场所类别'}
				],

				site_hosue: [
					{required: true, message: '请输入场所房屋'}
				],

			},
			selectConfig:{
				siteType:{
					label: 'name',
					value: 'key'
				},
				siteStar:{
					label: 'name',
					value: 'key'
				},
				siteArea:{
					label: 'name',
					value: 'key'
				},
				siteClass:{
					label: 'name',
					value: 'key'
				},
				siteHouse:{
					label: 'name',
					value: 'key'
				},
				yesNo:{
					label: 'name',
					value: 'key'
				},
			}
		}
	},
	mounted(){

	},
	methods: {
		//表单提交方法
		submit(callback){
			this.$refs.dialogForm.validate((valid) => {
				if (valid) {
					callback(this.form)
				}else{
					return false;
				}
			})
		},
		//表单注入数据
		setData(data){
			//可以和上面一样单个注入，也可以像下面一样直接合并进去
			Object.assign(this.form, data)
		}
	}
}
</script>

<style>
</style>
