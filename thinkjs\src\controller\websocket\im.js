'use strict';

const socketList = {};
module.exports = class extends think.Controller {

  constructor(...arg) {
    super(...arg);
  }

  async sendAction(self) {
    var socket = self.http.socket;
    const resstr = this.http.data;
    const res = eval('(' + resstr + ')');
    res.data.mine.mine = false;
    res.data.mine.type = 'friend';

    if (!think.isEmpty(socketList[res.data.to.id])) {
      socketList[res.data.to.id].emit('msg', res.data.mine);
    } else {
      const offmsg = {};
      offmsg['id'] = think.uuid(32);
      offmsg['touserid'] = res.data.to.id;
      offmsg['content'] = JSON.stringify(res.data.mine);
      const offmsgmodel = this.model('imoffmsg');
      await offmsgmodel.add(offmsg);
      // console.log("离线消息");
    }
  }

  async openAction(self) {
    this.emit('opend', 'This client opened successfully!')
    this.broadcast('joined', 'There is a new client joined successfully!')
    // const userInfo = await this.session('userInfo');
    // socketList[userInfo] = socket;
    //
    // this.broadcast('online', {
    //   userid: userInfo.id,
    //   username: userInfo.name,
    //   message: '上线了'
    // });
  }

  addUserAction() {
    console.log('获取客户端 addUser 事件发送的数据', this.wsData);
    console.log('获取当前 WebSocket 对象', this.websocket);
    console.log('判断当前请求是否是 WebSocket 请求', this.isWebsocket);
  }

  async closeAction(self) {
    var socket = self.http.socket;
    const user = await this.session('userInfo');
    const model = this.model('user');
    await model.where({'id': user.id}).update({'status': 'offline'});
    console.log('sdfsdf============================ds' + user.id);
    delete socketList[user.id];
    this.broadcast('offline', {
      userid: user.id,
      username: user.name,
      message: 'offline'
    });
  }
};
