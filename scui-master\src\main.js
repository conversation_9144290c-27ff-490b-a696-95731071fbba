import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/display.css'
import scui from './scui'
import i18n from './locales'
import router from './router'
import store from './store'
import { createApp } from 'vue'
import App from './App.vue'
import tool from './utils/tool'
import VueUeditorWrap from 'vue-ueditor-wrap';
import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'
const app = createApp(App);

//数据字典初始化
tool.dictInit();

//数据字段初始化
tool.tableInit();
app.use(VueUeditorWrap);
app.use(store);
app.use(router);
app.use(ElementPlus);
app.use(i18n);
app.use(scui);
app.use(VueViewer)

//挂载app
app.mount('#app');
