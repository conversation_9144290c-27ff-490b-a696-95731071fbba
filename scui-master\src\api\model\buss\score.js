import config from "@/config";
import http from "@/utils/request";

export default {

	page: {
		url: `${config.API_URL}/buss/score/page`,
		name: "数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	records: {
		url: `${config.API_URL}/buss/score/records`,
		name: "数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},

	
	tongji: {
		url: `${config.API_URL}/buss/score/tongji`,
		name: "数据列表",
		get: async function (params) {
			return await http.get(this.url, this.name, params);
		}
	},
	info: {
		url: `${config.API_URL}/buss/score/info`,
		name: "info",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	getnexttk: {
		url: `${config.API_URL}/buss/score/getnexttk`,
		name: "获取下一题",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	updateflag: {
		url: `${config.API_URL}/buss/score/updateflag`,
		name: "更新标记",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	},
	count: {
		url: `${config.API_URL}/buss/score/count`,
		name: "统计",
		get: async function(){
			return await http.get(this.url, this.name);
		}
	},
 
	
	delete: {
		url: `${config.API_URL}/buss/score/delete`,
		name: "删除",
		post: async function(data){
			return await http.post(this.url, this.name, data);
		}
	}
}

 