<template>
  <el-container>
    <el-container>
      <el-header>
        <div class="left-panel">
          <el-button type="primary" icon="el-icon-plus" @click="add"></el-button>
        </div>
        <div class="right-panel">
          <div class="right-panel-search">
            <el-input v-model="search.name" placeholder="类型名称" clearable></el-input>
            <el-select v-model="search.allowMultipleUse" placeholder="多次使用" clearable>
              <el-option label="允许" :value="1"></el-option>
              <el-option label="不允许" :value="0"></el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
          </div>
        </div>
      </el-header>
      <el-main class="nopadding">
        <scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
          remoteFilter>
          <el-table-column type="selection" width="50"></el-table-column>
          
          <el-table-column label="类型名称" prop="name" width="150"></el-table-column>
          <el-table-column label="多次使用" prop="allowMultipleUse" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.allow_multiple_use == 1" type="success">允许</el-tag>
              <el-tag v-if="scope.row.allow_multiple_use == 0" type="info">不允许</el-tag>
            </template>
          </el-table-column>
       
          <el-table-column label="描述" prop="description" width="200" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column label="添加时间" prop="create_date" width="150" sortable='create_date'></el-table-column>
          <el-table-column label="添加人" prop="create_by" width="120"></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="140">
            <template #default="scope">
              <el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
              </el-button>
              <el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
              </el-button>
              <el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
                <template #reference>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </scTable>
      </el-main>
    </el-container>
  </el-container>

  <save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>
</template>

<script>
import saveDialog from './save'

export default {
  name: 'keytype',
  components: {
    saveDialog
  },
  data() {
    return {
      dialog: {
        save: false
      },
      apiObj: this.$API.keytype.page,
      selection: [],
      search: {
        name: null,
        allowMultipleUse: null
      }
    }
  },
  mounted() {
    // 如果需要加载初始数据，可以在这里调用
  },
  methods: {
    // 添加
    add() {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('add');
      })
    },
    // 编辑
    async table_edit(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('edit').setData(row)
      })
    },
    // 查看
    table_show(row) {
      this.dialog.save = true
      this.$nextTick(() => {
        this.$refs.saveDialog.open('show').setData(row)
      })
    },
    // 删除
    async table_del(row, index) {
      var reqData = { id: row.id }
      var res = await this.$API.keytype.delete.post(reqData);
      if (res.code == 200) {
        // 这里选择刷新整个表格 OR 插入/编辑现有表格数据
        this.$refs.table.tableData.splice(index, 1);
        this.$message.success("删除成功")
      } else {
        this.$alert(res.message, "提示", { type: 'error' })
      }
    },
    // 表格选择后回调事件
    selectionChange(selection) {
      this.selection = selection;
    },
    // 搜索
    upsearch() {
      this.$refs.table.upData(this.search);
    },
    // 本地更新数据
    handleSuccess() {
      this.$refs.table.refresh();
    }
  }
}
</script>

<style>
.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: auto !important;
  padding-top: 10px;
  padding-bottom: 10px;
}

.left-panel {
  display: flex;
  align-items: center;
}

.right-panel {
  display: flex;
}

.right-panel-search {
  display: flex;
  align-items: center;
}

.right-panel-search .el-input, .right-panel-search .el-select {
  margin-right: 10px;
  width: 180px;
}
</style>