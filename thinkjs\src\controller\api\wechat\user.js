
const BaseRest = require('./rest.js');
module.exports = class extends BaseRest {
 
    async vipsAction(){
        let user =await this.session("userInfo"); 
        let model = this.model("vip");
        let     res=await model.query("select l.`name` ,c.* from (SELECT lessson, MAX(enddate) AS enddate FROM sys_vip where userid="+user.id+" GROUP BY lessson) c right  join sys_lesson l on l.id=c.lessson where l.parent_id=0  and l.type='"+user.level+"' and l.del_flag=0")




        for(let item of res){

            if(item.type=="product"){
                item.type="正式套餐"
            }

            
            if(item.type=="free"){
                item.type="试用套餐"
            }if(item.type=="key"){
                item.type="激活码"
            }

            if(item.enddate==null){

                item.enddate="非会员";
            }

        }



        return this.success(res);

    }


    async viplistAction(){

        let user =await this.session("userInfo"); 
        let model = this.model("vip");
        let     res=await model.query("SELECT v.*,l.name as lessson_name  from sys_vip v left join sys_lesson l on l.id=v.lessson where userid="+user.id+" order by enddate desc")


        for(let item of res){

            if(item.type=="product"){
                item.type="正式套餐"
            }

            
            if(item.type=="free"){
                item.type="试用套餐"
            }if(item.type=="key"){
                item.type="激活码"
            }

        }

        return this.success(res);

    }



    async getUserInfoAction(){
         
        let user =await this.session("userInfo");
        
          

        console.log(user);
        let model = this.model("student");
        let userInfo = await model.where({id:user.id}).find();
        let data={};
        data.name=userInfo.name;
        data.phone=userInfo.phone;
        data.type=userInfo.type;
        data.level=userInfo.level;
        data.avatar=userInfo.avatar;
        data.create_date=userInfo.create_date;

        let todate="";
    
        let sql="SELECT enddate,type FROM `sys_vip` where userid="+user.id+" and nj='"+userInfo.level+"' order by enddate desc limit 1"
        let res=await model.query(sql);

        console.log(res)
        if(think.isEmpty(res)){

            data.type="免费"
        }else{
            let tmp=res[0];
            if(tmp.type=="product"|| tmp.type=="key"){
                data.type="VIP会员"
            }else if (tmp.type=="free"){
                data.type="试用会员"
            }
            data.todate= think.datetime(tmp.enddate,"YYYY-MM-DD");   

        }







        return this.success(data);
    }


    async todateAction(){
        let user =await this.session("userInfo");
        let model = this.model("student");
        let userInfo = await model.where({id:user.id}).find();

        let days=this.post("days");
        let date=new Date();

        if(!think.isEmpty(userInfo.todate)){
            let date=new Date(userInfo.todate);

        }
       
        // 计算新的到期时间
        date.setDate(date.getDate() + days);
        let newTodate = think.datetime(date, "YYYY-MM-DD");


       
        return this.success({"todate":newTodate});
    }


    async getproductAction(){

 

        let model = this.model("product");
        let user =await this.session("userInfo");
        let model2 = this.model("student");
        let userInfo = await model2.where({id:user.id}).find();


        let data = await model.alias("c").join(["sys_lesson l on l.id=c.lesson"]).where({"c.del_flag":0,"c.status":1,quantity:['>',0],"c.schoolid":user.school}).field("c.*,l.name as lessonname").select();
        return this.success(data);
    }

    async logoutAction(){
        await this.session("userInfo",null);
        return this.success("退出成功");
    }


}