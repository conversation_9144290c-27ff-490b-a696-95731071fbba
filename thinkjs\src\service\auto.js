module.exports = class extends think.Service {
    /**
     * 自动扣除VIP天数
     * @return {Object} 处理结果
     */
    async autoDeductVipDays() {
      // 获取当前日期
      const now = think.datetime();
      const today = think.datetime(now, 'YYYY-MM-DD');
      const today2 = think.datetime(now, 'YYYY-MM-DD HH:mm:ss');
      try {
        // 查询所有有效的VIP记录（可用天数大于0且未过期）
        const vipRecords = await this.model('vip').where({
          available_days: ['>', 0],
          startdate: ['<=', today2],
          enddate: ['>', today2]
        }).select();
        let successCount = 0;
        let failCount = 0;
        
        // 遍历处理每条记录
        for (const vip of vipRecords) {
          // 检查今天是否已经扣除过
          const hasDeducted = await this.model('vip_usage_log').where({
            vip_id: vip.id,
            deduct_date: ['LIKE', `${today}%`]
          }).count();
          
          // 如果今天没有扣除过，则进行扣除
          if (hasDeducted === 0) {
            try {
              // 更新VIP记录
              const newUsedDays = vip.useday + 1; // 每次扣除1天
              const newAvailableDays = vip.available_days - 1;
              
              await this.model('vip').where({ id: vip.id }).update({
                useday: newUsedDays,
                available_days: newAvailableDays
              });
              
              // 记录扣除日志
              await this.model('vip_usage_log').add({
                vip_id: vip.id,
                userid: vip.userid,
                deduct_date: now,
                remark: '系统自动扣除'
              });

              let user=await this.model("student").where({"id":vip.userid}).find();
              console.log(user);

              await this.model("school").where({"id":user.school}).increment("todaycost",1);
              
              successCount++;
            } catch (err) {
              failCount++;
              // 记录错误日志
              think.logger.error(`自动扣除VIP天数失败: VIP ID=${vip.id}, 错误=${err.message}`);
            }
          }
        }
        
        return {
          errno: 0,
          data: {
            total: vipRecords.length,
            success: successCount,
            fail: failCount
          },
          message: '自动扣除VIP天数完成'
        };
      } catch (error) {
        think.logger.error(`自动扣除VIP天数异常: ${error.message}`);
        return {
          errno: 1,
          message: `自动扣除VIP天数异常: ${error.message}`
        };
      }
    }
  };