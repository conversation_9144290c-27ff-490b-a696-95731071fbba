/**
 
 @Name: laydata
 @Author: 贤心
 
 **/
 

html #layuicss-laydate{display: none; position: absolute; width: 1989px;}

/* 初始化 */
.layui-laydate *{margin: 0; padding: 0;}

/* 主体结构 */
.layui-laydate, .layui-laydate *{box-sizing: border-box;}
.layui-laydate{position: absolute; z-index: 66666666; margin: 5px 0; border-radius: 2px; font-size: 14px; -webkit-animation-duration: 0.3s; animation-duration: 0.3s; -webkit-animation-fill-mode: both; animation-fill-mode: both;}
.layui-laydate-main{width: 272px;}
.layui-laydate-header *,
.layui-laydate-content td,
.layui-laydate-list li{transition-duration: .3s; -webkit-transition-duration: .3s;}

@-webkit-keyframes laydate-upbit{ /* 微微往上滑入 */
  from {-webkit-transform: translate3d(0, 20px, 0); opacity: 0.3;}
  to {-webkit-transform: translate3d(0, 0, 0);  opacity: 1;}
}
@keyframes laydate-upbit{
  from {transform: translate3d(0, 20px, 0);  opacity: 0.3;}
  to {transform: translate3d(0, 0, 0);  opacity: 1;}
}
.layui-laydate{-webkit-animation-name: laydate-upbit; animation-name: laydate-upbit;}
.layui-laydate-static{ position: relative; z-index: 0; display: inline-block; margin: 0; -webkit-animation: none; animation: none;}

/* 展开年月列表时 */
.laydate-ym-show .laydate-prev-m,
.laydate-ym-show .laydate-next-m{display: none !important;}
.laydate-ym-show .laydate-prev-y,
.laydate-ym-show .laydate-next-y{display: inline-block !important;}
.laydate-ym-show .laydate-set-ym span[lay-type="month"]{display: none !important;}

/* 展开时间列表时 */
.laydate-time-show .layui-laydate-header .layui-icon,
.laydate-time-show .laydate-set-ym span[lay-type="year"],
.laydate-time-show .laydate-set-ym span[lay-type="month"]{display: none !important;}

/* 头部结构 */
.layui-laydate-header{position: relative; line-height:30px; padding: 10px 70px 5px;}
.layui-laydate-header *{display: inline-block; vertical-align: bottom;}
.layui-laydate-header i{position: absolute; top: 10px; padding: 0 5px; color: #999; font-size: 18px; cursor: pointer;}
.layui-laydate-header i.laydate-prev-y{left: 15px;}
.layui-laydate-header i.laydate-prev-m{left: 45px;}
.layui-laydate-header i.laydate-next-y{right: 15px;}
.layui-laydate-header i.laydate-next-m{right: 45px;}
.laydate-set-ym{width: 100%; text-align: center; box-sizing: border-box; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;}
.laydate-set-ym span{padding: 0 5px; cursor: pointer;}
.laydate-time-text{cursor: default !important;}

/* 主体结构 */
.layui-laydate-content{position: relative; padding: 10px; -moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}
.layui-laydate-content table{border-collapse: collapse; border-spacing: 0;}
.layui-laydate-content th,
.layui-laydate-content td{width: 36px; height: 30px; padding: 5px; text-align: center;}
.layui-laydate-content th{font-weight: 400;}
.layui-laydate-content td{position: relative; cursor: pointer;}
.laydate-day-mark{position: absolute; left: 0; top: 0; width: 100%; height: 100%; line-height: 30px; font-size: 12px; overflow: hidden;}
.laydate-day-mark::after{position: absolute; content:''; right: 2px; top: 2px; width: 5px; height: 5px; border-radius: 50%;}

/* 底部结构 */
.layui-laydate-footer{position: relative; height: 46px; line-height: 26px; padding: 10px 20px;}
.layui-laydate-footer span{margin-right: 15px; display: inline-block; cursor: pointer; font-size: 12px;}
.layui-laydate-footer span:hover{color: #5FB878;}
.laydate-footer-btns{position: absolute; right: 10px; top: 10px;}
.laydate-footer-btns span{height: 26px; line-height: 26px; margin: 0 0 0 -1px; padding: 0 10px; border: 1px solid #C9C9C9; background-color: #fff;  white-space: nowrap; vertical-align: top; border-radius: 2px;}

/* 年月列表 */
.layui-laydate-list{position: absolute; left: 0; top: 0; width: 100%; height: 100%; padding: 10px; box-sizing: border-box; background-color: #fff;}
.layui-laydate-list>li{position: relative; display: inline-block; width: 33.3%; height: 36px; line-height: 36px; margin: 3px 0; vertical-align: middle; text-align: center; cursor: pointer;}
.laydate-month-list>li{width: 25%; margin: 17px 0;}
.laydate-time-list{}
.laydate-time-list>li{height: 100%; margin: 0; line-height: normal; cursor: default;}
.laydate-time-list p{position: relative; top: -4px; line-height: 29px;}
.laydate-time-list ol{height: 181px; overflow: hidden;}
.laydate-time-list>li:hover ol{overflow-y: auto;}
.laydate-time-list ol li{width: 130%; padding-left: 33px; line-height: 30px; text-align: left; cursor: pointer;}

/* 提示 */
.layui-laydate-hint{position: absolute; top: 115px; left: 50%; width: 250px; margin-left: -125px; line-height: 20px; padding: 15px; text-align: center; font-size: 12px; color: #FF5722;}


/* 双日历 */
.layui-laydate-range{width: 546px;}
.layui-laydate-range .layui-laydate-main{display: inline-block; vertical-align: middle;}
.layui-laydate-range .laydate-main-list-0 .laydate-next-m,
.layui-laydate-range .laydate-main-list-0 .laydate-next-y,
.layui-laydate-range .laydate-main-list-1 .laydate-prev-y,
.layui-laydate-range .laydate-main-list-1 .laydate-prev-m{display: none;}
.layui-laydate-range .laydate-main-list-1 .layui-laydate-content{border-left: 1px solid #e2e2e2;}


/* 默认简约主题 */
.layui-laydate, .layui-laydate-hint{border: 1px solid #d2d2d2; box-shadow: 0 2px 4px rgba(0,0,0,.12); background-color: #fff; color: #666;}
.layui-laydate-header{border-bottom: 1px solid #e2e2e2;}
.layui-laydate-header i:hover,
.layui-laydate-header span:hover{color: #5FB878;}
.layui-laydate-content{border-top: none 0; border-bottom: none 0;}
.layui-laydate-content th{color: #333;}
.layui-laydate-content td{color: #666;}
.layui-laydate-content td.laydate-selected{background-color: #00F7DE;}
.laydate-selected:hover{background-color: #00F7DE !important;}
.layui-laydate-content td:hover,
.layui-laydate-list li:hover{background-color: #eaeaea; color: #333;}
.laydate-time-list li ol{margin: 0; padding: 0; border: 1px solid #e2e2e2; border-left-width: 0;}
.laydate-time-list li:first-child ol{border-left-width: 1px;}
.laydate-time-list>li:hover{background: none;}
.layui-laydate-content .laydate-day-prev,
.layui-laydate-content .laydate-day-next{color: #d2d2d2;}
.laydate-selected.laydate-day-prev,
.laydate-selected.laydate-day-next{background-color: #f8f8f8 !important;}
.layui-laydate-footer{border-top: 1px solid #e2e2e2;}
.layui-laydate-hint{color: #FF5722;}
.laydate-day-mark::after{background-color: #5FB878;}
.layui-laydate-content td.layui-this .laydate-day-mark::after{display: none;}
.layui-laydate-footer span[lay-type="date"]{color: #5FB878;}
.layui-laydate .layui-this{background-color: #009688 !important; color: #fff !important;}
.layui-laydate .laydate-disabled,
.layui-laydate .laydate-disabled:hover{background:none !important; color: #d2d2d2 !important; cursor: not-allowed !important; -moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}

/* 墨绿/自定义背景色主题 */
.laydate-theme-molv{border: none;}
.laydate-theme-molv.layui-laydate-range{width: 548px}
.laydate-theme-molv .layui-laydate-main{width: 274px;}
.laydate-theme-molv .layui-laydate-header{border: none; background-color: #009688;}
.laydate-theme-molv .layui-laydate-header i,
.laydate-theme-molv .layui-laydate-header span{color: #f6f6f6;}
.laydate-theme-molv .layui-laydate-header i:hover,
.laydate-theme-molv .layui-laydate-header span:hover{color: #fff;}
.laydate-theme-molv .layui-laydate-content{border: 1px solid #e2e2e2; border-top: none; border-bottom: none;}
.laydate-theme-molv .laydate-main-list-1 .layui-laydate-content{border-left: none;}
.laydate-theme-molv .layui-laydate-footer{border: 1px solid #e2e2e2;}

/* 格子主题 */
.laydate-theme-grid .layui-laydate-content td,
.laydate-theme-grid .layui-laydate-content thead,
.laydate-theme-grid .laydate-year-list>li,
.laydate-theme-grid .laydate-month-list>li{border: 1px solid #e2e2e2;}
.laydate-theme-grid .laydate-selected,
.laydate-theme-grid .laydate-selected:hover{background-color: #f2f2f2 !important; color: #009688 !important;}
.laydate-theme-grid .laydate-selected.laydate-day-prev,
.laydate-theme-grid .laydate-selected.laydate-day-next{color: #d2d2d2 !important;}
.laydate-theme-grid .laydate-year-list,
.laydate-theme-grid .laydate-month-list{margin: 1px 0 0 1px;}
.laydate-theme-grid .laydate-year-list>li,
.laydate-theme-grid .laydate-month-list>li{margin: 0 -1px -1px 0;}
.laydate-theme-grid .laydate-year-list>li{height: 43px; line-height: 43px;}
.laydate-theme-grid .laydate-month-list>li{height: 71px; line-height: 71px;}

