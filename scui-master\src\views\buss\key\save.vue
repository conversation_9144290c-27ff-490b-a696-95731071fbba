<template>
	<el-dialog :title="titleMap[mode]" v-model="visible" :width="600" destroy-on-close @closed="$emit('closed')">
		<el-form :model="form" :rules="rules" :disabled="mode=='show'" ref="dialogForm" label-width="100px"
		         label-position="top">

			<el-row :gutter="20">
				 
				<el-col :span="12">
					<el-form-item label="学校名称" prop="school">
						 

						<sc-table-select v-model="form.school" :apiObj="apiObj" :props="props">

						<el-table-column prop="name" label="学校名称"></el-table-column>

						 
						</sc-table-select>

					</el-form-item>
				</el-col>

                <el-col :span="12">
					<el-form-item label="生成数量" prop="num">
						<el-input type="number" v-model="form.num"  clearable></el-input>
					</el-form-item>
				</el-col>
            
				<el-col :span="12">
					<el-form-item label="KEY前缀" prop="keystart">
						<el-input   v-model="form.keystart"  clearable></el-input>
					</el-form-item>
				</el-col>
		
			</el-row>
			 


			 
		</el-form>
		<template #footer>
			<el-button @click="visible=false">取 消</el-button>
			<el-button v-if="mode!='show'" type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
		</template>
	</el-dialog>
</template>

<script>
export default {
	emits: ['success', 'closed'],
	data() {
		return {
			selectConfig: {
				userLevel: {
					label: 'name',
					value: 'name'
				},
			},
			apiObj: this.$API.school.listselect,
			mode: "add",
			titleMap: {
				add: '新增',
				edit: '编辑',
				show: '查看'
			},
			menuList: null,
			visible: false,
			isSaveing: false,
			//菜单树配置
			menuProps: {
				value: "id",
				emitPath: false,
				label: "title",
				checkStrictly: true,
			},
			props: {
				label: 'name',
				value: 'id'


			},
			//表单数据
			form: {
				school:"",
				num:0,
                keystart:""
				 
			},
			//验证规则
			rules: {
			 
				school: [
					{required: true, message: '请选择学校'}
				],
			 
				
				 
			},
			//所需数据选项
			groups: [],
			//规则树配置
			groupsProps: {
				value: "id",
				multiple: true,
				label: "name",
				checkStrictly: true
			}
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//显示
		open(mode = 'add') {
			this.mode = mode;
			this.visible = true;
			return this
		},
		//加载树数据
		async getGroup() {
			//加载角色树
			var role = await this.$API.role.select.get();
			this.groups = role;
			this.menuList= await this.$API.office.list.get();
		},
		//表单提交方法
		submit() {
			this.$refs.dialogForm.validate(async (valid) => {

             

				if (valid) {
					this.isSaveing = true;

					this.form.school=this.form.school.id;
                    

					var res = await this.$API.key.save.post(this.form);
					this.isSaveing = false;
					if (res.code == 200) {
						this.$emit('success', this.form, this.mode)
						this.visible = false;
						this.$message.success("操作成功")
					} else {
						this.$alert(res.message, "提示", {type: 'error'})
					}
				} else {
					return false;
				}
			})
		},
		//表单注入数据
		setData(data) {
			this.form.id = data.id
			this.form.login_name = data.login_name
			this.form.name = data.name
			this.form.role = data.group
			this.form.office_id = parseInt(data.office_id);
			//this.menuList = data.office;


		}
	}
}
</script>

<style>
</style>
