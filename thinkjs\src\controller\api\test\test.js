const BaseRest = require('../rest.js');
const _ = require('lodash');
const Op = think.Sequel.Sequel.Op;
module.exports = class extends BaseRest {
  async testAction() {
    let respData = {};
    const page = this.get('page') ? this.get('page') : 1;
    const rows = this.get('pageSize') ? this.get('pageSize') : 20;
    const where = this.get('where') ? JSON.parse(this.get('where')) : {};
    const orderMainModel = this.model('order_main');
    where['p.del_flag'] = 0;
    where['o.del_flag'] = 0;
    where['t.del_flag'] = 0;
    const dataWhere = await this.dataScope('p');
    const response = await orderMainModel
      .alias('p')
      .field('p.*,o.name AS office_name,t.name as project_name')
      .join('sys_office o ON p.`office_id`=o.`id`')
      .join('sys_project t ON p.`project_id`=t.`id`')
      .page(page, rows)
      .where(where).where(dataWhere)
      .setRelationPage(false);
    respData = {
      code: 200,
      count: response.count,
      data: response.data,
      msg: ''
    };
    // const seqwhere = {
    //   logging: true,
    //   where: {
    //     name: 'a project',
    //     [Op.or]: [
    //       {id: [1, 2, 3]},
    //       {
    //         [Op.and]: [
    //           {id: {gt: 10}},
    //           {id: {lt: 100}}
    //         ]
    //       }
    //     ]
    //   }
    // };
    // const data = await this.sequel('seq/oa_costpay_main', 'sequel').findAll(seqwhere);
    return this.json(respData);
  }
};
