<template>
	<el-container>
		
		<el-container>
			<el-header>
				<div class="left-panel">
					<el-button type="primary" icon="el-icon-plus" @click="add"></el-button>


					<el-button type="primary" plain :disabled="selection.length!=1" @click="open()">密码重置</el-button>
				</div>
				<div class="right-panel">
					<div class="right-panel-search">
						<el-input v-model="search.name" placeholder="登录账号 / 姓名" clearable></el-input>
						<el-button type="primary" icon="el-icon-search" @click="upsearch"></el-button>
					</div>
				</div>
			</el-header>
			<el-main class="nopadding">
				<scTable ref="table" :apiObj="apiObj" @selection-change="selectionChange" stripe remoteSort
				         remoteFilter>
					<el-table-column type="selection" width="50"></el-table-column>


					<el-table-column label="登录账号" prop="login_name" width="150" sortable='custom'
					                 column-key="filterUserName"></el-table-column>
					<el-table-column label="姓名" prop="name" width="150" sortable='custom'></el-table-column>
					<el-table-column label="所属部门" prop="office_name" width="200"></el-table-column>
					<el-table-column label="所属角色" prop="groupName" width="200"></el-table-column>
					<el-table-column label="加入时间" prop="create_date" width="150" sortable='custom'></el-table-column>
					<el-table-column label="操作" fixed="right" align="right" width="140">
						<template #default="scope">
							<el-button type="text" size="small" @click="table_show(scope.row, scope.$index)">查看
							</el-button>
							<el-button type="text" size="small" @click="table_edit(scope.row, scope.$index)">编辑
							</el-button>
							<el-popconfirm title="确定删除吗？" @confirm="table_del(scope.row, scope.$index)">
								<template #reference>
									<el-button type="text" size="small">删除</el-button>
								</template>
							</el-popconfirm>
						</template>
					</el-table-column>

				</scTable>
			</el-main>
		</el-container>
	</el-container>

	<save-dialog v-if="dialog.save" ref="saveDialog" @success="handleSuccess" @closed="dialog.save=false"></save-dialog>

</template>

<script>
import saveDialog from './save'

export default {
	name: 'user',
	components: {
		saveDialog
	},
	data() {
		return {

			dialog: {
				save: false
			},
			showGrouploading: false,
			groupFilterText: '',
			group: [],
			apiObj: this.$API.user.list,
			selection: [],
			search: {
				name: null
			},
			defaultProps: {

				label: 'name'
			}
		}
	},
	watch: {
		groupFilterText(val) {
			this.$refs.group.filter(val);
		}
	},
	mounted() {
		this.getGroup()
	},
	methods: {
		//添加
	 async	add() {
			this.dialog.save = true
			//	let row={};
			
			//	row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('add');
			})
		},
		//编辑
		async table_edit(row) {
			this.dialog.save = true
			//加载部门树
			var office = await this.$API.office.list.get();
			row.office = office
			this.$nextTick(() => {
				this.$refs.saveDialog.open('edit').setData(row)
			})
		},
		//查看
		table_show(row) {
			this.dialog.save = true
			this.$nextTick(() => {
				this.$refs.saveDialog.open('show').setData(row)
			})
		},
		//删除
		async table_del(row, index) {
			var reqData = {id: row.id}
			var res = await this.$API.user.del.post(reqData);
			if (res.code == 200) {
				//这里选择刷新整个表格 OR 插入/编辑现有表格数据
				this.$refs.table.tableData.splice(index, 1);
				this.$message.success("删除成功")
			} else {
				this.$alert(res.message, "提示", {type: 'error'})
			}
		},


		//表格选择后回调事件
		selectionChange(selection) {
			this.selection = selection;
		},
		//加载树数据
		async getGroup() {
			var res = await this.$API.role.select.get();
			this.showGrouploading = false;
			///res.data.unshift(allNode);
			this.group = res;
		},
		//树过滤
		groupFilterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		//树点击事件
		groupClick(data) {
			var params = {
				roleid: data.id
			}
			this.$refs.table.upData(params)
		},
		//搜索
		upsearch() {

			this.$refs.table.upData(this.search);

		},
		//本地更新数据
		handleSuccess() {

			this.$refs.table.refresh();

		}
		,

		open() {
			this.$prompt('请输入密码', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',

			}).then(async ({value}) => {

				await this.$API.user.personalSavePassWord.post({"password": value, "id": this.selection[0].id});


				this.$message({
					type: 'success',
					message: '修改成功'
				});
			}).catch(() => {

			});
		}

	}
}
</script>

<style>
</style>
